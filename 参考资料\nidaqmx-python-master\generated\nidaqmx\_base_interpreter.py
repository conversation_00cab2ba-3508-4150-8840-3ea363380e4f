# Do not edit this file; it was automatically generated.
import abc
from typing import Optional


class BaseEventHandler(abc.ABC):
    """Interpreter-specific object that is returned from register_*_event()."""
    __slots__ = ()

    @abc.abstractmethod
    def close(self) -> None:
        """Release resources used by the event handler.

        After releasing the resources, this method may report handler-specific errors
        (e.g. gRPC stream errors) by raising an exception.
        """
        raise NotImplementedError


class BaseInterpreter(abc.ABC):
    """
    Contains signature of functions for all DAQmx APIs.
    """
    __slots__ = ()

    @abc.abstractmethod
    def add_cdaq_sync_connection(self, port_list):
        raise NotImplementedError

    @abc.abstractmethod
    def add_global_chans_to_task(self, task, channel_names):
        raise NotImplementedError

    @abc.abstractmethod
    def add_network_device(
            self, ip_address, device_name, attempt_reservation, timeout):
        raise NotImplementedError

    @abc.abstractmethod
    def are_configured_cdaq_sync_ports_disconnected(
            self, chassis_devices_ports, timeout):
        raise NotImplementedError

    @abc.abstractmethod
    def auto_configure_cdaq_sync_connections(
            self, chassis_devices_ports, timeout):
        raise NotImplementedError

    @abc.abstractmethod
    def calculate_reverse_poly_coeff(
            self, forward_coeffs, min_val_x, max_val_x, num_points_to_compute,
            reverse_poly_order):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_anlg_edge_ref_trig(
            self, task, trigger_source, pretrigger_samples, trigger_slope,
            trigger_level):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_anlg_edge_start_trig(
            self, task, trigger_source, trigger_slope, trigger_level):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_anlg_multi_edge_ref_trig(
            self, task, trigger_sources, pretrigger_samples,
            trigger_slope_array, trigger_level_array):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_anlg_multi_edge_start_trig(
            self, task, trigger_sources, trigger_slope_array,
            trigger_level_array):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_anlg_window_ref_trig(
            self, task, trigger_source, window_top, window_bottom,
            pretrigger_samples, trigger_when):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_anlg_window_start_trig(
            self, task, window_top, window_bottom, trigger_source,
            trigger_when):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_burst_handshaking_timing_export_clock(
            self, task, sample_clk_rate, sample_clk_outp_term, sample_mode,
            samps_per_chan, sample_clk_pulse_polarity, pause_when,
            ready_event_active_level):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_burst_handshaking_timing_import_clock(
            self, task, sample_clk_rate, sample_clk_src, sample_mode,
            samps_per_chan, sample_clk_active_edge, pause_when,
            ready_event_active_level):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_change_detection_timing(
            self, task, rising_edge_chan, falling_edge_chan, sample_mode,
            samps_per_chan):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_dig_edge_ref_trig(
            self, task, trigger_source, pretrigger_samples, trigger_edge):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_dig_edge_start_trig(self, task, trigger_source, trigger_edge):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_dig_pattern_ref_trig(
            self, task, trigger_source, trigger_pattern, pretrigger_samples,
            trigger_when):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_dig_pattern_start_trig(
            self, task, trigger_source, trigger_pattern, trigger_when):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_handshaking_timing(self, task, sample_mode, samps_per_chan):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_implicit_timing(self, task, sample_mode, samps_per_chan):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_pipelined_samp_clk_timing(
            self, task, rate, source, active_edge, sample_mode,
            samps_per_chan):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_samp_clk_timing(
            self, task, rate, source, active_edge, sample_mode,
            samps_per_chan):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_time_start_trig(self, task, when, timescale):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_watchdog_ao_expir_states(
            self, task, channel_names, expir_state_array, output_type_array):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_watchdog_co_expir_states(
            self, task, channel_names, expir_state_array):
        raise NotImplementedError

    @abc.abstractmethod
    def cfg_watchdog_do_expir_states(
            self, task, channel_names, expir_state_array):
        raise NotImplementedError

    @abc.abstractmethod
    def clear_task(self, task):
        raise NotImplementedError

    @abc.abstractmethod
    def clear_teds(self, physical_channel):
        raise NotImplementedError

    @abc.abstractmethod
    def configure_logging(
            self, task, file_path, logging_mode, group_name, operation):
        raise NotImplementedError

    @abc.abstractmethod
    def configure_teds(self, physical_channel, file_path):
        raise NotImplementedError

    @abc.abstractmethod
    def connect_terms(
            self, source_terminal, destination_terminal, signal_modifiers):
        raise NotImplementedError

    @abc.abstractmethod
    def control_watchdog_task(self, task, action):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_accel4_wire_dc_voltage_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, sensitivity,
            sensitivity_units, voltage_excit_source, voltage_excit_val,
            use_excit_for_scaling, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_accel_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, sensitivity,
            sensitivity_units, current_excit_source, current_excit_val,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_accel_charge_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, sensitivity,
            sensitivity_units, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_bridge_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_charge_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_current_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, shunt_resistor_loc,
            ext_shunt_resistor_val, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_current_rms_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, shunt_resistor_loc,
            ext_shunt_resistor_val, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_force_bridge_polynomial_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, forward_coeffs,
            reverse_coeffs, electrical_units, physical_units,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_force_bridge_table_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, electrical_vals,
            electrical_units, physical_vals, physical_units,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_force_bridge_two_point_lin_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance,
            first_electrical_val, second_electrical_val, electrical_units,
            first_physical_val, second_physical_val, physical_units,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_force_iepe_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, sensitivity,
            sensitivity_units, current_excit_source, current_excit_val,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_freq_voltage_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, threshold_level, hysteresis, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_microphone_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, units, mic_sensitivity, max_snd_press_level,
            current_excit_source, current_excit_val, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_pos_eddy_curr_prox_probe_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, sensitivity, sensitivity_units,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_pos_lvdt_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, sensitivity, sensitivity_units,
            voltage_excit_source, voltage_excit_val, voltage_excit_freq,
            ac_excit_wire_mode, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_pos_rvdt_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, sensitivity, sensitivity_units,
            voltage_excit_source, voltage_excit_val, voltage_excit_freq,
            ac_excit_wire_mode, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_power_chan(
            self, task, physical_channel, voltage_setpoint, current_setpoint,
            output_enable, name_to_assign_to_channel):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_pressure_bridge_polynomial_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, forward_coeffs,
            reverse_coeffs, electrical_units, physical_units,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_pressure_bridge_table_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, electrical_vals,
            electrical_units, physical_vals, physical_units,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_pressure_bridge_two_point_lin_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance,
            first_electrical_val, second_electrical_val, electrical_units,
            first_physical_val, second_physical_val, physical_units,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_resistance_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, current_excit_source,
            current_excit_val, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_rosette_strain_gage_chan(
            self, task, physical_channel, rosette_type, gage_orientation,
            rosette_meas_types, name_to_assign_to_channel, min_val, max_val,
            strain_config, voltage_excit_source, voltage_excit_val,
            gage_factor, nominal_gage_resistance, poisson_ratio,
            lead_wire_resistance):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_strain_gage_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, strain_config, voltage_excit_source,
            voltage_excit_val, gage_factor, initial_bridge_voltage,
            nominal_gage_resistance, poisson_ratio, lead_wire_resistance,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_temp_built_in_sensor_chan(
            self, task, physical_channel, name_to_assign_to_channel, units):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_thrmcpl_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, thermocouple_type, cjc_source, cjc_val,
            cjc_channel):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_thrmstr_chan_iex(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, current_excit_source,
            current_excit_val, a, b, c):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_thrmstr_chan_vex(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, voltage_excit_source,
            voltage_excit_val, a, b, c, r_1):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_torque_bridge_polynomial_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, forward_coeffs,
            reverse_coeffs, electrical_units, physical_units,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_torque_bridge_table_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, electrical_vals,
            electrical_units, physical_vals, physical_units,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_torque_bridge_two_point_lin_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance,
            first_electrical_val, second_electrical_val, electrical_units,
            first_physical_val, second_physical_val, physical_units,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_velocity_iepe_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, sensitivity,
            sensitivity_units, current_excit_source, current_excit_val,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_voltage_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_voltage_chan_with_excit(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, bridge_config,
            voltage_excit_source, voltage_excit_val, use_excit_for_scaling,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ai_voltage_rms_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_airtd_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, rtd_type, resistance_config, current_excit_source,
            current_excit_val, r_0):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ao_current_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ao_func_gen_chan(
            self, task, physical_channel, name_to_assign_to_channel, type,
            freq, amplitude, offset):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ao_voltage_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_ang_encoder_chan(
            self, task, counter, name_to_assign_to_channel, decoding_type,
            zidx_enable, zidx_val, zidx_phase, units, pulses_per_rev,
            initial_angle, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_ang_velocity_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            decoding_type, units, pulses_per_rev, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_count_edges_chan(
            self, task, counter, name_to_assign_to_channel, edge,
            initial_count, count_direction):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_duty_cycle_chan(
            self, task, counter, name_to_assign_to_channel, min_freq,
            max_freq, edge, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_freq_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units, edge, meas_method, meas_time, divisor, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_lin_encoder_chan(
            self, task, counter, name_to_assign_to_channel, decoding_type,
            zidx_enable, zidx_val, zidx_phase, units, dist_per_pulse,
            initial_pos, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_lin_velocity_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            decoding_type, units, dist_per_pulse, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_period_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units, edge, meas_method, meas_time, divisor, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_pulse_chan_freq(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_pulse_chan_ticks(
            self, task, counter, name_to_assign_to_channel, source_terminal,
            min_val, max_val):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_pulse_chan_time(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_pulse_width_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units, starting_edge, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_semi_period_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_ci_two_edge_sep_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units, first_edge, second_edge, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_cigps_timestamp_chan(
            self, task, counter, name_to_assign_to_channel, units,
            sync_method, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_co_pulse_chan_freq(
            self, task, counter, name_to_assign_to_channel, units, idle_state,
            initial_delay, freq, duty_cycle):
        raise NotImplementedError

    @abc.abstractmethod
    def create_co_pulse_chan_ticks(
            self, task, counter, source_terminal, name_to_assign_to_channel,
            idle_state, initial_delay, low_ticks, high_ticks):
        raise NotImplementedError

    @abc.abstractmethod
    def create_co_pulse_chan_time(
            self, task, counter, name_to_assign_to_channel, units, idle_state,
            initial_delay, low_time, high_time):
        raise NotImplementedError

    @abc.abstractmethod
    def create_di_chan(
            self, task, lines, name_to_assign_to_lines, line_grouping):
        raise NotImplementedError

    @abc.abstractmethod
    def create_do_chan(
            self, task, lines, name_to_assign_to_lines, line_grouping):
        raise NotImplementedError

    @abc.abstractmethod
    def create_lin_scale(
            self, name, slope, y_intercept, pre_scaled_units, scaled_units):
        raise NotImplementedError

    @abc.abstractmethod
    def create_map_scale(
            self, name, prescaled_min, prescaled_max, scaled_min, scaled_max,
            pre_scaled_units, scaled_units):
        raise NotImplementedError

    @abc.abstractmethod
    def create_polynomial_scale(
            self, name, forward_coeffs, reverse_coeffs, pre_scaled_units,
            scaled_units):
        raise NotImplementedError

    @abc.abstractmethod
    def create_table_scale(
            self, name, prescaled_vals, scaled_vals, pre_scaled_units,
            scaled_units):
        raise NotImplementedError

    @abc.abstractmethod
    def create_task(self, session_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_accel_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, current_excit_source,
            current_excit_val, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_bridge_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_current_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, shunt_resistor_loc,
            ext_shunt_resistor_val, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_force_bridge_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_force_iepe_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, current_excit_source,
            current_excit_val, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_microphone_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, units, max_snd_press_level, current_excit_source,
            current_excit_val, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_pos_lvdt_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            voltage_excit_freq, ac_excit_wire_mode, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_pos_rvdt_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            voltage_excit_freq, ac_excit_wire_mode, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_pressure_bridge_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_resistance_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, current_excit_source,
            current_excit_val, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_strain_gage_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            initial_bridge_voltage, lead_wire_resistance, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_thrmcpl_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, cjc_source, cjc_val, cjc_channel):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_thrmstr_chan_iex(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, current_excit_source,
            current_excit_val):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_thrmstr_chan_vex(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, voltage_excit_source,
            voltage_excit_val, r_1):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_torque_bridge_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_voltage_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsai_voltage_chan_with_excit(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, voltage_excit_source,
            voltage_excit_val, custom_scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def create_tedsairtd_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, current_excit_source,
            current_excit_val):
        raise NotImplementedError

    @abc.abstractmethod
    def create_watchdog_timer_task_ex(
            self, device_name, session_name, timeout):
        raise NotImplementedError

    @abc.abstractmethod
    def delete_network_device(self, device_name):
        raise NotImplementedError

    @abc.abstractmethod
    def delete_saved_global_chan(self, channel_name):
        raise NotImplementedError

    @abc.abstractmethod
    def delete_saved_scale(self, scale_name):
        raise NotImplementedError

    @abc.abstractmethod
    def delete_saved_task(self, task_name):
        raise NotImplementedError

    @abc.abstractmethod
    def device_supports_cal(self, device_name):
        raise NotImplementedError

    @abc.abstractmethod
    def disable_ref_trig(self, task):
        raise NotImplementedError

    @abc.abstractmethod
    def disable_start_trig(self, task):
        raise NotImplementedError

    @abc.abstractmethod
    def disconnect_terms(self, source_terminal, destination_terminal):
        raise NotImplementedError

    @abc.abstractmethod
    def export_signal(self, task, signal_id, output_terminal):
        raise NotImplementedError

    @abc.abstractmethod
    def get_analog_power_up_states_with_output_type(
            self, channel_names, array_size):
        raise NotImplementedError

    @abc.abstractmethod
    def get_auto_configured_cdaq_sync_connections(self):
        raise NotImplementedError

    @abc.abstractmethod
    def get_buffer_attribute_uint32(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_cal_info_attribute_bool(self, device_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_cal_info_attribute_double(self, device_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_cal_info_attribute_string(self, device_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_cal_info_attribute_uint32(self, device_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_chan_attribute_bool(self, task, channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_chan_attribute_double(self, task, channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_chan_attribute_double_array(self, task, channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_chan_attribute_int32(self, task, channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_chan_attribute_string(self, task, channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_chan_attribute_uint32(self, task, channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_device_attribute_bool(self, device_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_device_attribute_double(self, device_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_device_attribute_double_array(self, device_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_device_attribute_int32(self, device_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_device_attribute_int32_array(self, device_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_device_attribute_string(self, device_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_device_attribute_uint32(self, device_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_device_attribute_uint32_array(self, device_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_digital_logic_family_power_up_state(self, device_name):
        raise NotImplementedError

    @abc.abstractmethod
    def get_digital_power_up_states(self, device_name, channel_name):
        raise NotImplementedError

    @abc.abstractmethod
    def get_digital_pull_up_pull_down_states(self, device_name, channel_name):
        raise NotImplementedError

    @abc.abstractmethod
    def get_disconnected_cdaq_sync_ports(self):
        raise NotImplementedError

    @abc.abstractmethod
    def get_error_string(self, error_code):
        raise NotImplementedError

    @abc.abstractmethod
    def get_exported_signal_attribute_bool(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_exported_signal_attribute_double(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_exported_signal_attribute_int32(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_exported_signal_attribute_string(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_exported_signal_attribute_uint32(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_ext_cal_last_date_and_time(self, device_name):
        raise NotImplementedError

    @abc.abstractmethod
    def get_persisted_chan_attribute_bool(self, channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_persisted_chan_attribute_string(self, channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_persisted_scale_attribute_bool(self, scale_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_persisted_scale_attribute_string(self, scale_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_persisted_task_attribute_bool(self, task_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_persisted_task_attribute_string(self, task_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_physical_chan_attribute_bool(self, physical_channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_physical_chan_attribute_bytes(self, physical_channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_physical_chan_attribute_double(self, physical_channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_physical_chan_attribute_double_array(
            self, physical_channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_physical_chan_attribute_int32(self, physical_channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_physical_chan_attribute_int32_array(
            self, physical_channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_physical_chan_attribute_string(self, physical_channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_physical_chan_attribute_uint32(self, physical_channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_physical_chan_attribute_uint32_array(
            self, physical_channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_read_attribute_bool(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_read_attribute_double(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_read_attribute_int32(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_read_attribute_string(self, task, attribute, size_hint=0):
        raise NotImplementedError

    @abc.abstractmethod
    def get_read_attribute_uint32(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_read_attribute_uint64(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_scale_attribute_double(self, scale_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_scale_attribute_double_array(self, scale_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_scale_attribute_int32(self, scale_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_scale_attribute_string(self, scale_name, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_self_cal_last_date_and_time(self, device_name):
        raise NotImplementedError

    @abc.abstractmethod
    def get_system_info_attribute_string(self, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_system_info_attribute_uint32(self, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_task_attribute_bool(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_task_attribute_string(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_task_attribute_uint32(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_timing_attribute_bool(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_timing_attribute_double(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_timing_attribute_ex_bool(self, task, device_names, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_timing_attribute_ex_double(self, task, device_names, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_timing_attribute_ex_int32(self, task, device_names, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_timing_attribute_ex_string(self, task, device_names, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_timing_attribute_ex_uint32(self, task, device_names, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_timing_attribute_ex_uint64(self, task, device_names, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_timing_attribute_int32(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_timing_attribute_string(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_timing_attribute_uint32(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_timing_attribute_uint64(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_trig_attribute_bool(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_trig_attribute_double(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_trig_attribute_double_array(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_trig_attribute_int32(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_trig_attribute_int32_array(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_trig_attribute_string(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_trig_attribute_timestamp(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_trig_attribute_uint32(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_watchdog_attribute_bool(self, task, lines, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_watchdog_attribute_double(self, task, lines, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_watchdog_attribute_int32(self, task, lines, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_watchdog_attribute_string(self, task, lines, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_write_attribute_bool(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_write_attribute_double(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_write_attribute_int32(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_write_attribute_string(self, task, attribute, size_hint=0):
        raise NotImplementedError

    @abc.abstractmethod
    def get_write_attribute_uint32(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def get_write_attribute_uint64(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def internal_get_last_created_chan(self):
        raise NotImplementedError

    @abc.abstractmethod
    def is_task_done(self, task):
        raise NotImplementedError

    @abc.abstractmethod
    def load_task(self, session_name):
        raise NotImplementedError

    @abc.abstractmethod
    def perform_bridge_offset_nulling_cal_ex(
            self, task, channel, skip_unsupported_channels):
        raise NotImplementedError

    @abc.abstractmethod
    def perform_bridge_shunt_cal_ex(
            self, task, channel, shunt_resistor_value,
            shunt_resistor_location, shunt_resistor_select,
            shunt_resistor_source, bridge_resistance,
            skip_unsupported_channels):
        raise NotImplementedError

    @abc.abstractmethod
    def perform_strain_shunt_cal_ex(
            self, task, channel, shunt_resistor_value,
            shunt_resistor_location, shunt_resistor_select,
            shunt_resistor_source, skip_unsupported_channels):
        raise NotImplementedError

    @abc.abstractmethod
    def perform_thrmcpl_lead_offset_nulling_cal(
            self, task, channel, skip_unsupported_channels):
        raise NotImplementedError

    @abc.abstractmethod
    def read_analog_f64(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def read_analog_scalar_f64(self, task, timeout):
        raise NotImplementedError

    @abc.abstractmethod
    def read_binary_i16(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def read_binary_i32(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def read_binary_u16(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def read_binary_u32(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def read_counter_f64(self, task, num_samps_per_chan, timeout, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def read_counter_f64_ex(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def read_counter_scalar_f64(self, task, timeout):
        raise NotImplementedError

    @abc.abstractmethod
    def read_counter_scalar_u32(self, task, timeout):
        raise NotImplementedError

    @abc.abstractmethod
    def read_counter_u32(self, task, num_samps_per_chan, timeout, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def read_counter_u32_ex(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def read_ctr_freq(
            self, task, num_samps_per_chan, timeout, interleaved,
            read_array_frequency, read_array_duty_cycle):
        raise NotImplementedError

    @abc.abstractmethod
    def read_ctr_freq_scalar(self, task, timeout):
        raise NotImplementedError

    @abc.abstractmethod
    def read_ctr_ticks(
            self, task, num_samps_per_chan, timeout, interleaved,
            read_array_high_ticks, read_array_low_ticks):
        raise NotImplementedError

    @abc.abstractmethod
    def read_ctr_ticks_scalar(self, task, timeout):
        raise NotImplementedError

    @abc.abstractmethod
    def read_ctr_time(
            self, task, num_samps_per_chan, timeout, interleaved,
            read_array_high_time, read_array_low_time):
        raise NotImplementedError

    @abc.abstractmethod
    def read_ctr_time_scalar(self, task, timeout):
        raise NotImplementedError

    @abc.abstractmethod
    def read_digital_lines(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def read_digital_scalar_u32(self, task, timeout):
        raise NotImplementedError

    @abc.abstractmethod
    def read_digital_u16(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def read_digital_u32(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def read_digital_u8(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def read_id_pin_memory(self, device_name, id_pin_name):
        raise NotImplementedError

    @abc.abstractmethod
    def read_power_binary_i16(
            self, task, num_samps_per_chan, timeout, fill_mode,
            read_array_voltage, read_array_current):
        raise NotImplementedError

    @abc.abstractmethod
    def read_power_f64(
            self, task, num_samps_per_chan, timeout, fill_mode,
            read_array_voltage, read_array_current):
        raise NotImplementedError

    @abc.abstractmethod
    def read_power_scalar_f64(self, task, timeout):
        raise NotImplementedError

    @abc.abstractmethod
    def read_raw(self, task, num_samps_per_chan, timeout, read_array):
        raise NotImplementedError

    @abc.abstractmethod
    def register_done_event(
            self, task, options, callback_function, callback_data):
        raise NotImplementedError

    @abc.abstractmethod
    def register_every_n_samples_event(
            self, task, every_n_samples_event_type, n_samples, options,
            callback_function, callback_data):
        raise NotImplementedError

    @abc.abstractmethod
    def register_signal_event(
            self, task, signal_id, options, callback_function, callback_data):
        raise NotImplementedError

    @abc.abstractmethod
    def remove_cdaq_sync_connection(self, port_list):
        raise NotImplementedError

    @abc.abstractmethod
    def reserve_network_device(self, device_name, override_reservation):
        raise NotImplementedError

    @abc.abstractmethod
    def reset_buffer_attribute(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def reset_chan_attribute(self, task, channel, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def reset_device(self, device_name):
        raise NotImplementedError

    @abc.abstractmethod
    def reset_exported_signal_attribute(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def reset_read_attribute(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def reset_timing_attribute(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def reset_timing_attribute_ex(self, task, device_names, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def reset_trig_attribute(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def reset_watchdog_attribute(self, task, lines, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def reset_write_attribute(self, task, attribute):
        raise NotImplementedError

    @abc.abstractmethod
    def restore_last_ext_cal_const(self, device_name):
        raise NotImplementedError

    @abc.abstractmethod
    def save_global_chan(self, task, channel_name, save_as, author, options):
        raise NotImplementedError

    @abc.abstractmethod
    def save_scale(self, scale_name, save_as, author, options):
        raise NotImplementedError

    @abc.abstractmethod
    def save_task(self, task, save_as, author, options):
        raise NotImplementedError

    @abc.abstractmethod
    def self_cal(self, device_name):
        raise NotImplementedError

    @abc.abstractmethod
    def self_test_device(self, device_name):
        raise NotImplementedError

    @abc.abstractmethod
    def set_analog_power_up_states(
            self, device_name, channel_names, state, channel_type):
        raise NotImplementedError

    @abc.abstractmethod
    def set_analog_power_up_states_with_output_type(
            self, channel_names, state_array, channel_type_array):
        raise NotImplementedError

    @abc.abstractmethod
    def set_buffer_attribute_uint32(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_cal_info_attribute_bool(self, device_name, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_cal_info_attribute_double(self, device_name, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_cal_info_attribute_string(self, device_name, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_cal_info_attribute_uint32(self, device_name, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_chan_attribute_bool(self, task, channel, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_chan_attribute_double(self, task, channel, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_chan_attribute_double_array(self, task, channel, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_chan_attribute_int32(self, task, channel, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_chan_attribute_string(self, task, channel, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_chan_attribute_uint32(self, task, channel, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_digital_logic_family_power_up_state(
            self, device_name, logic_family):
        raise NotImplementedError

    @abc.abstractmethod
    def set_digital_power_up_states(self, device_name, channel_names, state):
        raise NotImplementedError

    @abc.abstractmethod
    def set_digital_pull_up_pull_down_states(
            self, device_name, channel_names, state):
        raise NotImplementedError

    @abc.abstractmethod
    def set_exported_signal_attribute_bool(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_exported_signal_attribute_double(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_exported_signal_attribute_int32(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_exported_signal_attribute_string(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_exported_signal_attribute_uint32(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_read_attribute_bool(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_read_attribute_double(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_read_attribute_int32(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_read_attribute_string(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_read_attribute_uint32(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_read_attribute_uint64(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_runtime_environment(
            self, environment, environment_version, reserved_1, reserved_2):
        raise NotImplementedError

    @abc.abstractmethod
    def set_scale_attribute_double(self, scale_name, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_scale_attribute_double_array(self, scale_name, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_scale_attribute_int32(self, scale_name, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_scale_attribute_string(self, scale_name, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_timing_attribute_bool(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_timing_attribute_double(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_timing_attribute_ex_bool(
            self, task, device_names, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_timing_attribute_ex_double(
            self, task, device_names, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_timing_attribute_ex_int32(
            self, task, device_names, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_timing_attribute_ex_string(
            self, task, device_names, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_timing_attribute_ex_uint32(
            self, task, device_names, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_timing_attribute_ex_uint64(
            self, task, device_names, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_timing_attribute_int32(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_timing_attribute_string(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_timing_attribute_uint32(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_timing_attribute_uint64(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_trig_attribute_bool(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_trig_attribute_double(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_trig_attribute_double_array(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_trig_attribute_int32(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_trig_attribute_int32_array(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_trig_attribute_string(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_trig_attribute_timestamp(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_trig_attribute_uint32(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_watchdog_attribute_bool(self, task, lines, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_watchdog_attribute_double(self, task, lines, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_watchdog_attribute_int32(self, task, lines, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_watchdog_attribute_string(self, task, lines, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_write_attribute_bool(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_write_attribute_double(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_write_attribute_int32(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_write_attribute_string(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_write_attribute_uint32(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def set_write_attribute_uint64(self, task, attribute, value):
        raise NotImplementedError

    @abc.abstractmethod
    def start_new_file(self, task, file_path):
        raise NotImplementedError

    @abc.abstractmethod
    def start_task(self, task):
        raise NotImplementedError

    @abc.abstractmethod
    def stop_task(self, task):
        raise NotImplementedError

    @abc.abstractmethod
    def task_control(self, task, action):
        raise NotImplementedError

    @abc.abstractmethod
    def tristate_output_term(self, output_terminal):
        raise NotImplementedError

    @abc.abstractmethod
    def unregister_done_event(self, task):
        raise NotImplementedError

    @abc.abstractmethod
    def unregister_every_n_samples_event(
            self, task, every_n_samples_event_type):
        raise NotImplementedError

    @abc.abstractmethod
    def unregister_signal_event(self, task, signal_id):
        raise NotImplementedError

    @abc.abstractmethod
    def unreserve_network_device(self, device_name):
        raise NotImplementedError

    @abc.abstractmethod
    def wait_for_valid_timestamp(self, task, timestamp_event, timeout):
        raise NotImplementedError

    @abc.abstractmethod
    def wait_until_task_done(self, task, time_to_wait):
        raise NotImplementedError

    @abc.abstractmethod
    def write_analog_f64(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        raise NotImplementedError

    @abc.abstractmethod
    def write_analog_scalar_f64(self, task, auto_start, timeout, value):
        raise NotImplementedError

    @abc.abstractmethod
    def write_binary_i16(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        raise NotImplementedError

    @abc.abstractmethod
    def write_binary_i32(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        raise NotImplementedError

    @abc.abstractmethod
    def write_binary_u16(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        raise NotImplementedError

    @abc.abstractmethod
    def write_binary_u32(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        raise NotImplementedError

    @abc.abstractmethod
    def write_ctr_freq(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            frequency, duty_cycle):
        raise NotImplementedError

    @abc.abstractmethod
    def write_ctr_freq_scalar(
            self, task, auto_start, timeout, frequency, duty_cycle):
        raise NotImplementedError

    @abc.abstractmethod
    def write_ctr_ticks(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            high_ticks, low_ticks):
        raise NotImplementedError

    @abc.abstractmethod
    def write_ctr_ticks_scalar(
            self, task, auto_start, timeout, high_ticks, low_ticks):
        raise NotImplementedError

    @abc.abstractmethod
    def write_ctr_time(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            high_time, low_time):
        raise NotImplementedError

    @abc.abstractmethod
    def write_ctr_time_scalar(
            self, task, auto_start, timeout, high_time, low_time):
        raise NotImplementedError

    @abc.abstractmethod
    def write_digital_lines(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        raise NotImplementedError

    @abc.abstractmethod
    def write_digital_scalar_u32(self, task, auto_start, timeout, value):
        raise NotImplementedError

    @abc.abstractmethod
    def write_digital_u16(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        raise NotImplementedError

    @abc.abstractmethod
    def write_digital_u32(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        raise NotImplementedError

    @abc.abstractmethod
    def write_digital_u8(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        raise NotImplementedError

    @abc.abstractmethod
    def write_id_pin_memory(self, device_name, id_pin_name, data, format_code):
        raise NotImplementedError

    @abc.abstractmethod
    def write_raw(self, task, num_samps, auto_start, timeout, write_array):
        raise NotImplementedError

    @abc.abstractmethod
    def write_to_teds_from_array(
            self, physical_channel, bit_stream, basic_teds_options):
        raise NotImplementedError

    @abc.abstractmethod
    def write_to_teds_from_file(
            self, physical_channel, file_path, basic_teds_options):
        raise NotImplementedError

    @abc.abstractmethod
    def hash_task_handle(self, task_handle):
        raise NotImplementedError