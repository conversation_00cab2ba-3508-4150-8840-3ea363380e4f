3.14 OC输出 
 
3.14.1 
MT_Get_OC_Out_Num 
功能描述：读取板卡上OC输出通道的数量 
 
VC 
INT32 
MT_Get_OC_Out_Num 
(INT32* pValue) 
VB 
Function 
MT_Get_OC_Out_Num 
(ByRef Value As Long) As Long 
Delphi 
function 
MT_Get_OC_Out_Num 
(pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_OC_Out_Num 
(ref Int32 Value); 
输入参数 
无 
 
输出参数 
Value 
光电隔离输出通道的数量 
函数返回 
0 
函数执行成功，读取到通道数有效 
非0 
函数执行失败，读取到通道数无效 
备注 
通道的序号从0开始，N的通道，序号为0,1,2,3…N-1 
 
3.14.2 
MT_Set_OC_Out_Single 
功能描述：设置指定OC输出通道上的电平状态 
 
VC 
INT32 
MT_Set_OC_Out_Single 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_OC_Out_Single 
(ByVal AObj As Integer, ByVal 
Value As 
Long) As 
Long 
Delphi 
function 
MT_Set_OC_Out_Single 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_OC_Out_Single 
(UInt16 AObj, 
Int32 
Value); 
输入参数 
AObj 
光电隔离输出通道号 
输出参数 
Value 
指定通道上的电平状态，1为高电平，0为低电平 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
通道的序号从0开始，N的通道，序号为0,1,2,3…N-1 
 
3.14.3 MT_Set_OC_Out_All 
功能描述：设置OC输出所有通道上的电平状态 

VC 
INT32 
MT_Set_OC_Out_All 
(INT32 
Value); 
VB 
Function 
MT_Set_OC_Out_All 
(ByVal 
Value As 
Long) As Long 
Delphi 
function 
MT_Set_OC_Out_All 
(Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_OC_Out_All 
(Int32 
Value); 
输入参数 
无 
 
输出参数 
Value 
所有通道上的电平状态，1为高电平，0为低电平 
通道0为LSB(最低位) 
通道N对应第N-1位 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
通道的序号从0开始，N的通道，序号为0,1,2,3…N-1 
