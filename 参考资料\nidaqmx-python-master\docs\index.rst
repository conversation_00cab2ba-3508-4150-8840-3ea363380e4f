.. NI-DAQmx Python API documentation master file, created by
   sphinx-quickstart on Thu Dec 15 09:40:36 2016.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

NI-DAQmx Python Documentation
=============================

.. include:: ../README.rst

.. py:module:: nidaqmx

.. toctree::
   :maxdepth: 3
   :caption: API Reference:

   constants
   errors
   grpc_session_options
   scale
   stream_readers
   stream_writers
   system
   task
   types
   utils


Indices and Tables
==================

* :ref:`genindex`
* :ref:`modindex`
