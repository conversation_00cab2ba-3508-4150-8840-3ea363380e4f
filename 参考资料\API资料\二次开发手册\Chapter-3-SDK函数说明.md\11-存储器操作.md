3.11 存储器操作 

备注:当前版本如果有PLC模式，则存储器会被PLC模式占用，用户不可使用 

3.11.1 
MT_Get_Param_Mem_Len 
功能描述：读取板卡上存储器的容量，单位为字节(Byte) 
 
VC 
INT32 
MT_Get_Param_Mem_Len 
(INT32* pValue) 
VB 
Function 
MT_Get_Param_Mem_Len 
(ByRef Value As Long) As Long 
Delphi 
function 
MT_Get_Param_Mem_Len 
(pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Num 
(ref Int32 Value); 
输入参数 
无 
 
输出参数 
Value 
存储器的容量 
函数返回 
0 
函数执行成功，读取到的容量有效 
非0 
函数执行失败，读取到的容量无效 
备注 
存储器地址的序号从0开始，N的存储器，序号为0,1,2,3…N-1 
 
3.11.2 MT_Get_Param_Mem_Data 
功能描述：读取存储器指定地址上的数据，每次一个字节 
 
VC 
INT32 
MT_Get_Param_Mem_Data 
(WORD AObj,BYTE* pValue); 
VB 
Function 
MT_Get_Param_Mem_Data 
(ByVal AObj As Integer, ByRef Value As 
BYTE) As 
Long 
Delphi 
function 
MT_Get_Param_Mem_Data 
(AObj:Word;pValue:PByte):Integer; 
C# 
public static extern int 
MT_Get_Param_Mem_Data 
(UInt16 AObj, ref 
Byte 
Value); 
输入参数 
AObj 
存储器地址 
输出参数 
Value 
指定地址上的字节数据 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
存储器地址的序号从0开始，N的存储器，序号为0,1,2,3…N-1 
 
3.11.3 MT_Set_Param_Mem_Data 
功能描述：设置存储器指定地址上的数据，每次一个字节 
 
VC 
INT32 
MT_Set_Param_Mem_Data 
(WORD AObj,BYTE 
Value); 
VB 
Function 
MT_Set_Param_Mem_Data 
(ByVal AObj As 
Integer, ByVal 
Value As 
BYTE) As 
Long 
Delphi 
function 
MT_Set_Param_Mem_Data 
(AObj:Word;Value:BYTE):Integer; 
C# 
public static extern int 
MT_Set_Param_Mem_Data 
(UInt16 AObj, 
BYTE 
Value); 
输入参数 
AObj 
存储器地址 
输出参数 
Value 
指定地址上的字节数据 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
存储器地址的序号从0开始，N的存储器，序号为0,1,2,3…N-1 
 
