3.5 零位模式(开环和闭环) 

3.5.1 MT_Set_Axis_Mode_Home[兼容] 
功能描述：设置指定的轴为开环零位模式 
 
VC 
INT32 
MT_Set_Axis_Mode_Home(WORD AObj) 
VB 
Function MT_Set_Axis_Mode_Home 
(ByVal AObj As Integer) As Long 
Delphi 
function MT_Set_Axis_Mode_Home(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Mode_Home(UInt16 AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
 
3.5.2 MT_Set_Axis_Mode_Home_Home_Switch 
功能描述：设置指定的轴为开环零位模式，查找零位以零位开关和限位开关为判断依据。 
 
VC 
INT32 
MT_Set_Axis_Mode_Home_Home_Switch 
(WORD AObj) 
VB 
Function MT_Set_Axis_Mode_Home_Home_Switch 
(ByVal AObj As Integer) As Long 
Delphi 
function MT_Set_Axis_Mode_Home_Home_Switch 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Mode_Home_Home_Switch 
(UInt16 AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
 
3.5.3 MT_Set_Axis_Mode_Home_ 
Encoder_Index 
功能描述：设置指定的轴为闭环零位模式，查找零位以光栅尺零位和编码器零位为判断依据。 
 
VC 
INT32 
MT_Set_Axis_Mode_Home_ 
Encoder_Index 
(WORD AObj) 
VB 
Function MT_Set_Axis_Mode_Home_ 
Encoder_Index 
(ByVal AObj As Integer) As 
Long 
Delphi 
function MT_Set_Axis_Mode_Home_ 
Encoder_Index 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Mode_Home_ 
Encoder_Index 
(UInt16 AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
 
3.5.4 MT_Set_Axis_Mode_Home_ 
Encoder_Home_Switch 
功能描述：设置指定的轴为闭环零位模式，查找零位以零位开关和限位开关为判断依据。 
 
VC 
INT32 
MT_Set_Axis_Mode_Home_ 
Encoder_Home_Switch 
(WORD AObj) 
VB 
Function MT_Set_Axis_Mode_Home_ 
Encoder_Home_Switch 
(ByVal AObj As 
Integer) As Long 
Delphi 
function MT_Set_Axis_Mode_Home_ 
Encoder_Home_Switch 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Mode_Home_ 
Encoder_Home_Switch 
(UInt16 
AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
 
3.5.5 MT_Set_Axis_Home_V 
功能描述：设置指定轴以指定速度查找零位，速度值为正，则正向查找0位，速度为负，则负向查找零位 
 
VC 
INT32 
MT_Set_Axis_Home_V 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Axis_Home_V 
(ByVal AObj As Integer, ByVal 
Value As Long) As Long 
Delphi 
function 
MT_Set_Axis_Home_V 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Home_V 
(UInt16 AObj,Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的查找零位速度 
Hz/S 
可正可负 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
较慢的速度有利于查找的精度 
 
3.5.6 MT_Set_Axis_Home_Stop 
功能描述：停止指定轴的零位运动模式 
 
VC 
INT32 
MT_Set_Axis_Home_Stop 
(WORD AObj) 
VB 
Function 
MT_Set_Axis_Home_Stop 
(ByVal AObj As Integer) As Long 
Delphi 
function 
MT_Set_Axis_Home_Stop 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Home_Stop 
(UInt16 AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
 
3.5.7 MT_Set_Axis_Home_V_Start 
功能描述：设置指定轴的零位模式启动初始速度，默认为50Hz/s,一般无需修改。 
 
VC 
INT32 
MT_Set_Axis_Home_V_Start 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Axis_Home_V 
_Start(ByVal AObj As Integer, ByVal 
Value As Long) 
As Long 
Delphi 
function 
MT_Set_Axis_Home_V_Start 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Home_V_Start 
(UInt16 AObj,Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的启动速度 
Hz/S 
只能正 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.5.8 MT_Set_Axis_Home_Acc 
功能描述：设置指定轴的零位模式的加速度值，默认为500Hz/s2。 
 
VC 
INT32 
MT_Set_Axis_Home_Acc 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Axis_Home_Acc(ByVal AObj As 
Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function 
MT_Set_Axis_Home_Acc 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Home_Acc 
(UInt16 AObj,Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的加速度 
Hz/S2 
只能正 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.5.9 MT_Set_Axis_Home_Dec 
功能描述：设置指定轴的零位模式的减速度值，默认为500Hz/s2。 
 
VC 
INT32 
MT_Set_Axis_Home_Dec 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Axis_Home_Dec(ByVal AObj As Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function 
MT_Set_Axis_Home_Dec 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Home_Dec 
(UInt16 AObj,Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的减速度 
Hz/S2 
只能正 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.5.10 MT_Get_Axis_Home_Acc 
功能描述：读取指定控制轴零位模式的加速度 
 
VC 
INT32 
MT_Get_Axis_Home_Acc(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_ 
Home_Acc 
(ByVal AObj As Integer, ByRef Value As Long) As 
Long 
Delphi 
function 
MT_Get_Axis_ 
Home_Acc 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_ 
Home_Acc 
(UInt16 AObj, ref Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的当前加速度 
Hz/S2 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.5.11 MT_Get_Axis_Home_Dec 
功能描述：读取指定控制轴零位模式的减速度 
 
VC 
INT32 
MT_Get_Axis_Home_Dec(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_ 
Home_Dec 
(ByVal AObj As Integer, ByRef Value As Long) As 
Long 
Delphi 
function MT_Get_Axis_ 
Home_Dec 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_ 
Home_Decc 
(UInt16 AObj, ref Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的当前减速度 
Hz/S2 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
