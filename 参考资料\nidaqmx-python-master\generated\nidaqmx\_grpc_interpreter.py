# Do not edit this file; it was automatically generated.

from __future__ import annotations
import logging
import threading
import typing
import warnings
from typing import Callable, Generic, TypeVar

import google.protobuf.message
from google.protobuf.timestamp_pb2 import Timestamp as GrpcTimestamp
import grpc
import numpy

from . import errors as errors
from nidaqmx._base_interpreter import BaseEventHandler, BaseInterpreter
from nidaqmx._stubs import nidaqmx_pb2 as grpc_types
from nidaqmx._stubs import nidaqmx_pb2_grpc as nidaqmx_grpc
from nidaqmx._stubs import session_pb2 as session_grpc_types
from nidaqmx.error_codes import DAQmxErrors
from nidaqmx._grpc_time import convert_time_to_timestamp, convert_timestamp_to_time

_logger = logging.getLogger(__name__)

_UNABLE_TO_LOCATE_ERROR_RESOURCES_ERROR_MESSAGE = (
    "Error code could not be found. Reinstalling the driver might fix the issue. "
    "Otherwise, contact National Instruments technical support."
)


TEventResponse = TypeVar("TEventResponse", bound=google.protobuf.message.Message)

class GrpcEventHandler(BaseEventHandler, Generic[TEventResponse]):
    """Manage the lifetime of a gRPC event stream."""
    __slots__ = [
        "_event_name",
        "_interpreter",
        "_event_stream",
        "_event_callback",
        "_event_stream_exception",
        "_thread",
    ]

    def __init__(
        self,
        event_name: str,
        interpreter: GrpcStubInterpreter,
        event_stream: grpc._CallIterator[TEventResponse],
        event_callback: Callable[[TEventResponse], None],
    ) -> None:
        self._event_name = event_name
        self._interpreter = interpreter
        self._event_stream = event_stream
        self._event_callback = event_callback
        self._event_stream_exception: Exception | None = None
        self._thread = threading.Thread(target=self._thread_main, name=f"nidaqmx {event_name} thread")

        self._thread.start()

    def close(self) -> None:
        self._event_stream.cancel()
        self._thread.join()
        if self._event_stream_exception is not None:
            raise self._event_stream_exception

    def _thread_main(self) -> None:
        try:
            for event_response in self._event_stream:
                self._event_callback(event_response)
        except Exception as ex:
            if _is_cancelled(ex):
                return
            _logger.exception(
                "Unhandled exception raised while reading nidaqmx %s stream.", self._event_name
            )
            # Save the exception and re-raise it at the end of close().
            self._event_stream_exception = ex
            return


class GrpcStubInterpreter(BaseInterpreter):
    '''Interpreter for interacting with a gRPC Stub class'''
    # Do not add per-task state to the interpreter class.
    __slots__ = [
        '_grpc_options',
        '_client',
    ]

    def __init__(self, grpc_options):
        self._grpc_options = grpc_options
        self._client = nidaqmx_grpc.NiDAQmxStub(grpc_options.grpc_channel)

    def _invoke(self, func, request, metadata=None):
        try:
            response = func(request, metadata=metadata)
        except grpc.RpcError as rpc_error:
            self._handle_rpc_error(rpc_error)
        return response

    def _handle_rpc_error(self, rpc_error):
        error_message = rpc_error.details()
        error_code = None
        samps_per_chan_read = None
        samps_per_chan_written = None
        for entry in rpc_error.trailing_metadata() or []:
            if entry.key == 'ni-error':
                try:
                    error_code = int(typing.cast(str, entry.value))
                except ValueError:
                    error_message += f'\nError status: {entry.value}'
            elif entry.key == "ni-samps-per-chan-read":
                try:
                    samps_per_chan_read = int(typing.cast(str, entry.value))
                except ValueError:
                    error_message += f'\nSamples per channel read: {entry.value}'
            elif entry.key == "ni-samps-per-chan-written":
                try:
                    samps_per_chan_written = int(typing.cast(str, entry.value))
                except ValueError:
                    error_message += f'\nSamples per channel written: {entry.value}'
        grpc_error = rpc_error.code()
        if grpc_error == grpc.StatusCode.UNAVAILABLE:
            error_message = 'Failed to connect to server'
        elif grpc_error == grpc.StatusCode.UNIMPLEMENTED:
            error_message = (
                'This operation is not supported by the NI gRPC Device Server being used. Upgrade NI gRPC Device Server.'
            )
        if error_code is None:
            raise errors.RpcError(grpc_error, error_message) from None
        else:
            self._raise_error(error_code, error_message, samps_per_chan_written, samps_per_chan_read)

    def _check_for_error_from_response(self, error_code, samps_per_chan_written=None, samps_per_chan_read=None):
        if error_code != 0:
            # This is an optimization for the partial read operation.
            error_message = _ERROR_MESSAGES.get(error_code, None)
            if not error_message:
                error_message = self.get_error_string(error_code)
            self._raise_error(error_code, error_message, samps_per_chan_written=samps_per_chan_written, samps_per_chan_read=samps_per_chan_read)

    def _raise_error(self, error_code, error_message, samps_per_chan_written=None, samps_per_chan_read=None):
        if error_code < 0:
            if samps_per_chan_read is not None:
                raise errors.DaqReadError(error_message, error_code, samps_per_chan_read) from None
            elif samps_per_chan_written is not None:
                raise errors.DaqWriteError(error_message, error_code, samps_per_chan_written) from None
            else:
                raise errors.DaqError(error_message, error_code) from None
        elif error_code > 0:
            if not error_message:
                error_message = self.get_error_string(error_code)
            warnings.warn(errors.DaqWarning(error_message, error_code))

    def _check_for_event_registration_error(self, event_stream):
        try:
            # Wait for initial metadata to ensure that the server has received the event
            # registration request and called the event registration function. Otherwise,
            # there is no guarantee that the event registration function is called before
            # the application sends the next RPC request (e.g. start_task).
            _ = event_stream.initial_metadata()

            # When the event registration function returns an error, the server should close
            # the event stream with an error before sending initial metadata. This behavior
            # requires NI gRPC Device Server version 2.2 or later.
            if event_stream.done() and event_stream.exception() is not None:
                raise event_stream.exception()
        except grpc.RpcError as rpc_error:
            self._handle_rpc_error(rpc_error)

    def add_cdaq_sync_connection(self, port_list):
        response = self._invoke(
            self._client.AddCDAQSyncConnection,
            grpc_types.AddCDAQSyncConnectionRequest(port_list=port_list))

    def add_global_chans_to_task(self, task, channel_names):
        response = self._invoke(
            self._client.AddGlobalChansToTask,
            grpc_types.AddGlobalChansToTaskRequest(task=task, channel_names=channel_names))

    def add_network_device(
            self, ip_address, device_name, attempt_reservation, timeout):
        response = self._invoke(
            self._client.AddNetworkDevice,
            grpc_types.AddNetworkDeviceRequest(
                ip_address=ip_address, device_name=device_name,
                attempt_reservation=attempt_reservation, timeout=timeout))
        return response.device_name_out

    def are_configured_cdaq_sync_ports_disconnected(
            self, chassis_devices_ports, timeout):
        response = self._invoke(
            self._client.AreConfiguredCDAQSyncPortsDisconnected,
            grpc_types.AreConfiguredCDAQSyncPortsDisconnectedRequest(
                chassis_devices_ports=chassis_devices_ports, timeout=timeout))
        return response.disconnected_ports_exist

    def auto_configure_cdaq_sync_connections(
            self, chassis_devices_ports, timeout):
        response = self._invoke(
            self._client.AutoConfigureCDAQSyncConnections,
            grpc_types.AutoConfigureCDAQSyncConnectionsRequest(
                chassis_devices_ports=chassis_devices_ports, timeout=timeout))

    def calculate_reverse_poly_coeff(
            self, forward_coeffs, min_val_x, max_val_x, num_points_to_compute,
            reverse_poly_order):
        response = self._invoke(
            self._client.CalculateReversePolyCoeff,
            grpc_types.CalculateReversePolyCoeffRequest(
                forward_coeffs=forward_coeffs, min_val_x=min_val_x,
                max_val_x=max_val_x,
                num_points_to_compute=num_points_to_compute,
                reverse_poly_order=reverse_poly_order))
        return list(response.reverse_coeffs)

    def cfg_anlg_edge_ref_trig(
            self, task, trigger_source, pretrigger_samples, trigger_slope,
            trigger_level):
        response = self._invoke(
            self._client.CfgAnlgEdgeRefTrig,
            grpc_types.CfgAnlgEdgeRefTrigRequest(
                task=task, trigger_source=trigger_source,
                pretrigger_samples=pretrigger_samples,
                trigger_slope_raw=trigger_slope, trigger_level=trigger_level))

    def cfg_anlg_edge_start_trig(
            self, task, trigger_source, trigger_slope, trigger_level):
        response = self._invoke(
            self._client.CfgAnlgEdgeStartTrig,
            grpc_types.CfgAnlgEdgeStartTrigRequest(
                task=task, trigger_source=trigger_source,
                trigger_slope_raw=trigger_slope, trigger_level=trigger_level))

    def cfg_anlg_multi_edge_ref_trig(
            self, task, trigger_sources, pretrigger_samples,
            trigger_slope_array, trigger_level_array):
        response = self._invoke(
            self._client.CfgAnlgMultiEdgeRefTrig,
            grpc_types.CfgAnlgMultiEdgeRefTrigRequest(
                task=task, trigger_sources=trigger_sources,
                pretrigger_samples=pretrigger_samples,
                trigger_slope_array=trigger_slope_array,
                trigger_level_array=trigger_level_array))

    def cfg_anlg_multi_edge_start_trig(
            self, task, trigger_sources, trigger_slope_array,
            trigger_level_array):
        response = self._invoke(
            self._client.CfgAnlgMultiEdgeStartTrig,
            grpc_types.CfgAnlgMultiEdgeStartTrigRequest(
                task=task, trigger_sources=trigger_sources,
                trigger_slope_array=trigger_slope_array,
                trigger_level_array=trigger_level_array))

    def cfg_anlg_window_ref_trig(
            self, task, trigger_source, window_top, window_bottom,
            pretrigger_samples, trigger_when):
        response = self._invoke(
            self._client.CfgAnlgWindowRefTrig,
            grpc_types.CfgAnlgWindowRefTrigRequest(
                task=task, trigger_source=trigger_source,
                window_top=window_top, window_bottom=window_bottom,
                pretrigger_samples=pretrigger_samples,
                trigger_when_raw=trigger_when))

    def cfg_anlg_window_start_trig(
            self, task, window_top, window_bottom, trigger_source,
            trigger_when):
        response = self._invoke(
            self._client.CfgAnlgWindowStartTrig,
            grpc_types.CfgAnlgWindowStartTrigRequest(
                task=task, window_top=window_top, window_bottom=window_bottom,
                trigger_source=trigger_source, trigger_when_raw=trigger_when))

    def cfg_burst_handshaking_timing_export_clock(
            self, task, sample_clk_rate, sample_clk_outp_term, sample_mode,
            samps_per_chan, sample_clk_pulse_polarity, pause_when,
            ready_event_active_level):
        response = self._invoke(
            self._client.CfgBurstHandshakingTimingExportClock,
            grpc_types.CfgBurstHandshakingTimingExportClockRequest(
                task=task, sample_clk_rate=sample_clk_rate,
                sample_clk_outp_term=sample_clk_outp_term,
                sample_mode_raw=sample_mode, samps_per_chan=samps_per_chan,
                sample_clk_pulse_polarity_raw=sample_clk_pulse_polarity,
                pause_when_raw=pause_when,
                ready_event_active_level_raw=ready_event_active_level))

    def cfg_burst_handshaking_timing_import_clock(
            self, task, sample_clk_rate, sample_clk_src, sample_mode,
            samps_per_chan, sample_clk_active_edge, pause_when,
            ready_event_active_level):
        response = self._invoke(
            self._client.CfgBurstHandshakingTimingImportClock,
            grpc_types.CfgBurstHandshakingTimingImportClockRequest(
                task=task, sample_clk_rate=sample_clk_rate,
                sample_clk_src=sample_clk_src, sample_mode_raw=sample_mode,
                samps_per_chan=samps_per_chan,
                sample_clk_active_edge_raw=sample_clk_active_edge,
                pause_when_raw=pause_when,
                ready_event_active_level_raw=ready_event_active_level))

    def cfg_change_detection_timing(
            self, task, rising_edge_chan, falling_edge_chan, sample_mode,
            samps_per_chan):
        response = self._invoke(
            self._client.CfgChangeDetectionTiming,
            grpc_types.CfgChangeDetectionTimingRequest(
                task=task, rising_edge_chan=rising_edge_chan,
                falling_edge_chan=falling_edge_chan,
                sample_mode_raw=sample_mode, samps_per_chan=samps_per_chan))

    def cfg_dig_edge_ref_trig(
            self, task, trigger_source, pretrigger_samples, trigger_edge):
        response = self._invoke(
            self._client.CfgDigEdgeRefTrig,
            grpc_types.CfgDigEdgeRefTrigRequest(
                task=task, trigger_source=trigger_source,
                pretrigger_samples=pretrigger_samples,
                trigger_edge_raw=trigger_edge))

    def cfg_dig_edge_start_trig(self, task, trigger_source, trigger_edge):
        response = self._invoke(
            self._client.CfgDigEdgeStartTrig,
            grpc_types.CfgDigEdgeStartTrigRequest(
                task=task, trigger_source=trigger_source,
                trigger_edge_raw=trigger_edge))

    def cfg_dig_pattern_ref_trig(
            self, task, trigger_source, trigger_pattern, pretrigger_samples,
            trigger_when):
        response = self._invoke(
            self._client.CfgDigPatternRefTrig,
            grpc_types.CfgDigPatternRefTrigRequest(
                task=task, trigger_source=trigger_source,
                trigger_pattern=trigger_pattern,
                pretrigger_samples=pretrigger_samples,
                trigger_when_raw=trigger_when))

    def cfg_dig_pattern_start_trig(
            self, task, trigger_source, trigger_pattern, trigger_when):
        response = self._invoke(
            self._client.CfgDigPatternStartTrig,
            grpc_types.CfgDigPatternStartTrigRequest(
                task=task, trigger_source=trigger_source,
                trigger_pattern=trigger_pattern,
                trigger_when_raw=trigger_when))

    def cfg_handshaking_timing(self, task, sample_mode, samps_per_chan):
        response = self._invoke(
            self._client.CfgHandshakingTiming,
            grpc_types.CfgHandshakingTimingRequest(
                task=task, sample_mode_raw=sample_mode,
                samps_per_chan=samps_per_chan))

    def cfg_implicit_timing(self, task, sample_mode, samps_per_chan):
        response = self._invoke(
            self._client.CfgImplicitTiming,
            grpc_types.CfgImplicitTimingRequest(
                task=task, sample_mode_raw=sample_mode,
                samps_per_chan=samps_per_chan))

    def cfg_pipelined_samp_clk_timing(
            self, task, rate, source, active_edge, sample_mode,
            samps_per_chan):
        response = self._invoke(
            self._client.CfgPipelinedSampClkTiming,
            grpc_types.CfgPipelinedSampClkTimingRequest(
                task=task, rate=rate, source=source,
                active_edge_raw=active_edge, sample_mode_raw=sample_mode,
                samps_per_chan=samps_per_chan))

    def cfg_samp_clk_timing(
            self, task, rate, source, active_edge, sample_mode,
            samps_per_chan):
        response = self._invoke(
            self._client.CfgSampClkTiming,
            grpc_types.CfgSampClkTimingRequest(
                task=task, rate=rate, source=source,
                active_edge_raw=active_edge, sample_mode_raw=sample_mode,
                samps_per_chan=samps_per_chan))

    def cfg_time_start_trig(self, task, when, timescale):
        response = self._invoke(
            self._client.CfgTimeStartTrig,
            grpc_types.CfgTimeStartTrigRequest(
                task=task, when=convert_time_to_timestamp(when),
                timescale_raw=timescale))

    def cfg_watchdog_ao_expir_states(
            self, task, channel_names, expir_state_array, output_type_array):
        response = self._invoke(
            self._client.CfgWatchdogAOExpirStates,
            grpc_types.CfgWatchdogAOExpirStatesRequest(
                task=task, channel_names=channel_names,
                expir_state_array=expir_state_array,
                output_type_array=output_type_array))

    def cfg_watchdog_co_expir_states(
            self, task, channel_names, expir_state_array):
        response = self._invoke(
            self._client.CfgWatchdogCOExpirStates,
            grpc_types.CfgWatchdogCOExpirStatesRequest(
                task=task, channel_names=channel_names,
                expir_state_array=expir_state_array))

    def cfg_watchdog_do_expir_states(
            self, task, channel_names, expir_state_array):
        response = self._invoke(
            self._client.CfgWatchdogDOExpirStates,
            grpc_types.CfgWatchdogDOExpirStatesRequest(
                task=task, channel_names=channel_names,
                expir_state_array=expir_state_array))

    def clear_task(self, task):
        response = self._invoke(
            self._client.ClearTask,
            grpc_types.ClearTaskRequest(task=task))

    def clear_teds(self, physical_channel):
        response = self._invoke(
            self._client.ClearTEDS,
            grpc_types.ClearTEDSRequest(physical_channel=physical_channel))

    def configure_logging(
            self, task, file_path, logging_mode, group_name, operation):
        response = self._invoke(
            self._client.ConfigureLogging,
            grpc_types.ConfigureLoggingRequest(
                task=task, file_path=file_path, logging_mode_raw=logging_mode,
                group_name=group_name, operation_raw=operation))

    def configure_teds(self, physical_channel, file_path):
        response = self._invoke(
            self._client.ConfigureTEDS,
            grpc_types.ConfigureTEDSRequest(physical_channel=physical_channel, file_path=file_path))

    def connect_terms(
            self, source_terminal, destination_terminal, signal_modifiers):
        response = self._invoke(
            self._client.ConnectTerms,
            grpc_types.ConnectTermsRequest(
                source_terminal=source_terminal,
                destination_terminal=destination_terminal,
                signal_modifiers_raw=signal_modifiers))

    def control_watchdog_task(self, task, action):
        response = self._invoke(
            self._client.ControlWatchdogTask,
            grpc_types.ControlWatchdogTaskRequest(task=task, action_raw=action))

    def create_ai_accel4_wire_dc_voltage_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, sensitivity,
            sensitivity_units, voltage_excit_source, voltage_excit_val,
            use_excit_for_scaling, custom_scale_name):
        response = self._invoke(
            self._client.CreateAIAccel4WireDCVoltageChan,
            grpc_types.CreateAIAccel4WireDCVoltageChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units, sensitivity=sensitivity,
                sensitivity_units_raw=sensitivity_units,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                use_excit_for_scaling=use_excit_for_scaling,
                custom_scale_name=custom_scale_name))

    def create_ai_accel_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, sensitivity,
            sensitivity_units, current_excit_source, current_excit_val,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAIAccelChan,
            grpc_types.CreateAIAccelChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units, sensitivity=sensitivity,
                sensitivity_units_raw=sensitivity_units,
                current_excit_source_raw=current_excit_source,
                current_excit_val=current_excit_val,
                custom_scale_name=custom_scale_name))

    def create_ai_accel_charge_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, sensitivity,
            sensitivity_units, custom_scale_name):
        response = self._invoke(
            self._client.CreateAIAccelChargeChan,
            grpc_types.CreateAIAccelChargeChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units, sensitivity=sensitivity,
                sensitivity_units_raw=sensitivity_units,
                custom_scale_name=custom_scale_name))

    def create_ai_bridge_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, custom_scale_name):
        response = self._invoke(
            self._client.CreateAIBridgeChan,
            grpc_types.CreateAIBridgeChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                bridge_config_raw=bridge_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                nominal_bridge_resistance=nominal_bridge_resistance,
                custom_scale_name=custom_scale_name))

    def create_ai_charge_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, custom_scale_name):
        response = self._invoke(
            self._client.CreateAIChargeChan,
            grpc_types.CreateAIChargeChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units,
                custom_scale_name=custom_scale_name))

    def create_ai_current_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, shunt_resistor_loc,
            ext_shunt_resistor_val, custom_scale_name):
        response = self._invoke(
            self._client.CreateAICurrentChan,
            grpc_types.CreateAICurrentChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units,
                shunt_resistor_loc_raw=shunt_resistor_loc,
                ext_shunt_resistor_val=ext_shunt_resistor_val,
                custom_scale_name=custom_scale_name))

    def create_ai_current_rms_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, shunt_resistor_loc,
            ext_shunt_resistor_val, custom_scale_name):
        response = self._invoke(
            self._client.CreateAICurrentRMSChan,
            grpc_types.CreateAICurrentRMSChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units,
                shunt_resistor_loc_raw=shunt_resistor_loc,
                ext_shunt_resistor_val=ext_shunt_resistor_val,
                custom_scale_name=custom_scale_name))

    def create_ai_force_bridge_polynomial_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, forward_coeffs,
            reverse_coeffs, electrical_units, physical_units,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAIForceBridgePolynomialChan,
            grpc_types.CreateAIForceBridgePolynomialChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                bridge_config_raw=bridge_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                nominal_bridge_resistance=nominal_bridge_resistance,
                forward_coeffs=forward_coeffs, reverse_coeffs=reverse_coeffs,
                electrical_units_raw=electrical_units,
                physical_units_raw=physical_units,
                custom_scale_name=custom_scale_name))

    def create_ai_force_bridge_table_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, electrical_vals,
            electrical_units, physical_vals, physical_units,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAIForceBridgeTableChan,
            grpc_types.CreateAIForceBridgeTableChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                bridge_config_raw=bridge_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                nominal_bridge_resistance=nominal_bridge_resistance,
                electrical_vals=electrical_vals,
                electrical_units_raw=electrical_units,
                physical_vals=physical_vals,
                physical_units_raw=physical_units,
                custom_scale_name=custom_scale_name))

    def create_ai_force_bridge_two_point_lin_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance,
            first_electrical_val, second_electrical_val, electrical_units,
            first_physical_val, second_physical_val, physical_units,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAIForceBridgeTwoPointLinChan,
            grpc_types.CreateAIForceBridgeTwoPointLinChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                bridge_config_raw=bridge_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                nominal_bridge_resistance=nominal_bridge_resistance,
                first_electrical_val=first_electrical_val,
                second_electrical_val=second_electrical_val,
                electrical_units_raw=electrical_units,
                first_physical_val=first_physical_val,
                second_physical_val=second_physical_val,
                physical_units_raw=physical_units,
                custom_scale_name=custom_scale_name))

    def create_ai_force_iepe_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, sensitivity,
            sensitivity_units, current_excit_source, current_excit_val,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAIForceIEPEChan,
            grpc_types.CreateAIForceIEPEChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units, sensitivity=sensitivity,
                sensitivity_units_raw=sensitivity_units,
                current_excit_source_raw=current_excit_source,
                current_excit_val=current_excit_val,
                custom_scale_name=custom_scale_name))

    def create_ai_freq_voltage_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, threshold_level, hysteresis, custom_scale_name):
        response = self._invoke(
            self._client.CreateAIFreqVoltageChan,
            grpc_types.CreateAIFreqVoltageChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                threshold_level=threshold_level, hysteresis=hysteresis,
                custom_scale_name=custom_scale_name))

    def create_ai_microphone_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, units, mic_sensitivity, max_snd_press_level,
            current_excit_source, current_excit_val, custom_scale_name):
        response = self._invoke(
            self._client.CreateAIMicrophoneChan,
            grpc_types.CreateAIMicrophoneChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, units_raw=units,
                mic_sensitivity=mic_sensitivity,
                max_snd_press_level=max_snd_press_level,
                current_excit_source_raw=current_excit_source,
                current_excit_val=current_excit_val,
                custom_scale_name=custom_scale_name))

    def create_ai_pos_eddy_curr_prox_probe_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, sensitivity, sensitivity_units,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAIPosEddyCurrProxProbeChan,
            grpc_types.CreateAIPosEddyCurrProxProbeChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                sensitivity=sensitivity,
                sensitivity_units_raw=sensitivity_units,
                custom_scale_name=custom_scale_name))

    def create_ai_pos_lvdt_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, sensitivity, sensitivity_units,
            voltage_excit_source, voltage_excit_val, voltage_excit_freq,
            ac_excit_wire_mode, custom_scale_name):
        response = self._invoke(
            self._client.CreateAIPosLVDTChan,
            grpc_types.CreateAIPosLVDTChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                sensitivity=sensitivity,
                sensitivity_units_raw=sensitivity_units,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                voltage_excit_freq=voltage_excit_freq,
                ac_excit_wire_mode_raw=ac_excit_wire_mode,
                custom_scale_name=custom_scale_name))

    def create_ai_pos_rvdt_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, sensitivity, sensitivity_units,
            voltage_excit_source, voltage_excit_val, voltage_excit_freq,
            ac_excit_wire_mode, custom_scale_name):
        response = self._invoke(
            self._client.CreateAIPosRVDTChan,
            grpc_types.CreateAIPosRVDTChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                sensitivity=sensitivity,
                sensitivity_units_raw=sensitivity_units,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                voltage_excit_freq=voltage_excit_freq,
                ac_excit_wire_mode_raw=ac_excit_wire_mode,
                custom_scale_name=custom_scale_name))

    def create_ai_power_chan(
            self, task, physical_channel, voltage_setpoint, current_setpoint,
            output_enable, name_to_assign_to_channel):
        response = self._invoke(
            self._client.CreateAIPowerChan,
            grpc_types.CreateAIPowerChanRequest(
                task=task, physical_channel=physical_channel,
                voltage_setpoint=voltage_setpoint,
                current_setpoint=current_setpoint,
                output_enable=output_enable,
                name_to_assign_to_channel=name_to_assign_to_channel))

    def create_ai_pressure_bridge_polynomial_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, forward_coeffs,
            reverse_coeffs, electrical_units, physical_units,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAIPressureBridgePolynomialChan,
            grpc_types.CreateAIPressureBridgePolynomialChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                bridge_config_raw=bridge_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                nominal_bridge_resistance=nominal_bridge_resistance,
                forward_coeffs=forward_coeffs, reverse_coeffs=reverse_coeffs,
                electrical_units_raw=electrical_units,
                physical_units_raw=physical_units,
                custom_scale_name=custom_scale_name))

    def create_ai_pressure_bridge_table_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, electrical_vals,
            electrical_units, physical_vals, physical_units,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAIPressureBridgeTableChan,
            grpc_types.CreateAIPressureBridgeTableChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                bridge_config_raw=bridge_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                nominal_bridge_resistance=nominal_bridge_resistance,
                electrical_vals=electrical_vals,
                electrical_units_raw=electrical_units,
                physical_vals=physical_vals,
                physical_units_raw=physical_units,
                custom_scale_name=custom_scale_name))

    def create_ai_pressure_bridge_two_point_lin_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance,
            first_electrical_val, second_electrical_val, electrical_units,
            first_physical_val, second_physical_val, physical_units,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAIPressureBridgeTwoPointLinChan,
            grpc_types.CreateAIPressureBridgeTwoPointLinChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                bridge_config_raw=bridge_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                nominal_bridge_resistance=nominal_bridge_resistance,
                first_electrical_val=first_electrical_val,
                second_electrical_val=second_electrical_val,
                electrical_units_raw=electrical_units,
                first_physical_val=first_physical_val,
                second_physical_val=second_physical_val,
                physical_units_raw=physical_units,
                custom_scale_name=custom_scale_name))

    def create_ai_resistance_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, current_excit_source,
            current_excit_val, custom_scale_name):
        response = self._invoke(
            self._client.CreateAIResistanceChan,
            grpc_types.CreateAIResistanceChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                resistance_config_raw=resistance_config,
                current_excit_source_raw=current_excit_source,
                current_excit_val=current_excit_val,
                custom_scale_name=custom_scale_name))

    def create_ai_rosette_strain_gage_chan(
            self, task, physical_channel, rosette_type, gage_orientation,
            rosette_meas_types, name_to_assign_to_channel, min_val, max_val,
            strain_config, voltage_excit_source, voltage_excit_val,
            gage_factor, nominal_gage_resistance, poisson_ratio,
            lead_wire_resistance):
        response = self._invoke(
            self._client.CreateAIRosetteStrainGageChan,
            grpc_types.CreateAIRosetteStrainGageChanRequest(
                task=task, physical_channel=physical_channel,
                rosette_type_raw=rosette_type,
                gage_orientation=gage_orientation,
                rosette_meas_types=rosette_meas_types,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val,
                strain_config_raw=strain_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val, gage_factor=gage_factor,
                nominal_gage_resistance=nominal_gage_resistance,
                poisson_ratio=poisson_ratio,
                lead_wire_resistance=lead_wire_resistance))

    def create_ai_strain_gage_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, strain_config, voltage_excit_source,
            voltage_excit_val, gage_factor, initial_bridge_voltage,
            nominal_gage_resistance, poisson_ratio, lead_wire_resistance,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAIStrainGageChan,
            grpc_types.CreateAIStrainGageChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                strain_config_raw=strain_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val, gage_factor=gage_factor,
                initial_bridge_voltage=initial_bridge_voltage,
                nominal_gage_resistance=nominal_gage_resistance,
                poisson_ratio=poisson_ratio,
                lead_wire_resistance=lead_wire_resistance,
                custom_scale_name=custom_scale_name))

    def create_ai_temp_built_in_sensor_chan(
            self, task, physical_channel, name_to_assign_to_channel, units):
        response = self._invoke(
            self._client.CreateAITempBuiltInSensorChan,
            grpc_types.CreateAITempBuiltInSensorChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                units_raw=units))

    def create_ai_thrmcpl_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, thermocouple_type, cjc_source, cjc_val,
            cjc_channel):
        response = self._invoke(
            self._client.CreateAIThrmcplChan,
            grpc_types.CreateAIThrmcplChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                thermocouple_type_raw=thermocouple_type,
                cjc_source_raw=cjc_source, cjc_val=cjc_val,
                cjc_channel=cjc_channel))

    def create_ai_thrmstr_chan_iex(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, current_excit_source,
            current_excit_val, a, b, c):
        response = self._invoke(
            self._client.CreateAIThrmstrChanIex,
            grpc_types.CreateAIThrmstrChanIexRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                resistance_config_raw=resistance_config,
                current_excit_source_raw=current_excit_source,
                current_excit_val=current_excit_val, a=a, b=b, c=c))

    def create_ai_thrmstr_chan_vex(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, voltage_excit_source,
            voltage_excit_val, a, b, c, r_1):
        response = self._invoke(
            self._client.CreateAIThrmstrChanVex,
            grpc_types.CreateAIThrmstrChanVexRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                resistance_config_raw=resistance_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val, a=a, b=b, c=c, r1=r_1))

    def create_ai_torque_bridge_polynomial_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, forward_coeffs,
            reverse_coeffs, electrical_units, physical_units,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAITorqueBridgePolynomialChan,
            grpc_types.CreateAITorqueBridgePolynomialChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                bridge_config_raw=bridge_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                nominal_bridge_resistance=nominal_bridge_resistance,
                forward_coeffs=forward_coeffs, reverse_coeffs=reverse_coeffs,
                electrical_units_raw=electrical_units,
                physical_units_raw=physical_units,
                custom_scale_name=custom_scale_name))

    def create_ai_torque_bridge_table_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance, electrical_vals,
            electrical_units, physical_vals, physical_units,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAITorqueBridgeTableChan,
            grpc_types.CreateAITorqueBridgeTableChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                bridge_config_raw=bridge_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                nominal_bridge_resistance=nominal_bridge_resistance,
                electrical_vals=electrical_vals,
                electrical_units_raw=electrical_units,
                physical_vals=physical_vals,
                physical_units_raw=physical_units,
                custom_scale_name=custom_scale_name))

    def create_ai_torque_bridge_two_point_lin_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, bridge_config, voltage_excit_source,
            voltage_excit_val, nominal_bridge_resistance,
            first_electrical_val, second_electrical_val, electrical_units,
            first_physical_val, second_physical_val, physical_units,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAITorqueBridgeTwoPointLinChan,
            grpc_types.CreateAITorqueBridgeTwoPointLinChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                bridge_config_raw=bridge_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                nominal_bridge_resistance=nominal_bridge_resistance,
                first_electrical_val=first_electrical_val,
                second_electrical_val=second_electrical_val,
                electrical_units_raw=electrical_units,
                first_physical_val=first_physical_val,
                second_physical_val=second_physical_val,
                physical_units_raw=physical_units,
                custom_scale_name=custom_scale_name))

    def create_ai_velocity_iepe_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, sensitivity,
            sensitivity_units, current_excit_source, current_excit_val,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAIVelocityIEPEChan,
            grpc_types.CreateAIVelocityIEPEChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units, sensitivity=sensitivity,
                sensitivity_units_raw=sensitivity_units,
                current_excit_source_raw=current_excit_source,
                current_excit_val=current_excit_val,
                custom_scale_name=custom_scale_name))

    def create_ai_voltage_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, custom_scale_name):
        response = self._invoke(
            self._client.CreateAIVoltageChan,
            grpc_types.CreateAIVoltageChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units,
                custom_scale_name=custom_scale_name))

    def create_ai_voltage_chan_with_excit(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, bridge_config,
            voltage_excit_source, voltage_excit_val, use_excit_for_scaling,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateAIVoltageChanWithExcit,
            grpc_types.CreateAIVoltageChanWithExcitRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units,
                bridge_config_raw=bridge_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                use_excit_for_scaling=use_excit_for_scaling,
                custom_scale_name=custom_scale_name))

    def create_ai_voltage_rms_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, custom_scale_name):
        response = self._invoke(
            self._client.CreateAIVoltageRMSChan,
            grpc_types.CreateAIVoltageRMSChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units,
                custom_scale_name=custom_scale_name))

    def create_airtd_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, rtd_type, resistance_config, current_excit_source,
            current_excit_val, r_0):
        response = self._invoke(
            self._client.CreateAIRTDChan,
            grpc_types.CreateAIRTDChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                rtd_type_raw=rtd_type,
                resistance_config_raw=resistance_config,
                current_excit_source_raw=current_excit_source,
                current_excit_val=current_excit_val, r0=r_0))

    def create_ao_current_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, custom_scale_name):
        response = self._invoke(
            self._client.CreateAOCurrentChan,
            grpc_types.CreateAOCurrentChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                custom_scale_name=custom_scale_name))

    def create_ao_func_gen_chan(
            self, task, physical_channel, name_to_assign_to_channel, type,
            freq, amplitude, offset):
        response = self._invoke(
            self._client.CreateAOFuncGenChan,
            grpc_types.CreateAOFuncGenChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                type_raw=type, freq=freq, amplitude=amplitude, offset=offset))

    def create_ao_voltage_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, custom_scale_name):
        response = self._invoke(
            self._client.CreateAOVoltageChan,
            grpc_types.CreateAOVoltageChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                custom_scale_name=custom_scale_name))

    def create_ci_ang_encoder_chan(
            self, task, counter, name_to_assign_to_channel, decoding_type,
            zidx_enable, zidx_val, zidx_phase, units, pulses_per_rev,
            initial_angle, custom_scale_name):
        response = self._invoke(
            self._client.CreateCIAngEncoderChan,
            grpc_types.CreateCIAngEncoderChanRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                decoding_type_raw=decoding_type, zidx_enable=zidx_enable,
                zidx_val=zidx_val, zidx_phase_raw=zidx_phase, units_raw=units,
                pulses_per_rev=pulses_per_rev, initial_angle=initial_angle,
                custom_scale_name=custom_scale_name))

    def create_ci_ang_velocity_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            decoding_type, units, pulses_per_rev, custom_scale_name):
        response = self._invoke(
            self._client.CreateCIAngVelocityChan,
            grpc_types.CreateCIAngVelocityChanRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val,
                decoding_type_raw=decoding_type, units_raw=units,
                pulses_per_rev=pulses_per_rev,
                custom_scale_name=custom_scale_name))

    def create_ci_count_edges_chan(
            self, task, counter, name_to_assign_to_channel, edge,
            initial_count, count_direction):
        response = self._invoke(
            self._client.CreateCICountEdgesChan,
            grpc_types.CreateCICountEdgesChanRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                edge_raw=edge, initial_count=initial_count,
                count_direction_raw=count_direction))

    def create_ci_duty_cycle_chan(
            self, task, counter, name_to_assign_to_channel, min_freq,
            max_freq, edge, custom_scale_name):
        response = self._invoke(
            self._client.CreateCIDutyCycleChan,
            grpc_types.CreateCIDutyCycleChanRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_freq=min_freq, max_freq=max_freq, edge_raw=edge,
                custom_scale_name=custom_scale_name))

    def create_ci_freq_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units, edge, meas_method, meas_time, divisor, custom_scale_name):
        response = self._invoke(
            self._client.CreateCIFreqChan,
            grpc_types.CreateCIFreqChanRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                edge_raw=edge, meas_method_raw=meas_method,
                meas_time=meas_time, divisor=divisor,
                custom_scale_name=custom_scale_name))

    def create_ci_lin_encoder_chan(
            self, task, counter, name_to_assign_to_channel, decoding_type,
            zidx_enable, zidx_val, zidx_phase, units, dist_per_pulse,
            initial_pos, custom_scale_name):
        response = self._invoke(
            self._client.CreateCILinEncoderChan,
            grpc_types.CreateCILinEncoderChanRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                decoding_type_raw=decoding_type, zidx_enable=zidx_enable,
                zidx_val=zidx_val, zidx_phase_raw=zidx_phase, units_raw=units,
                dist_per_pulse=dist_per_pulse, initial_pos=initial_pos,
                custom_scale_name=custom_scale_name))

    def create_ci_lin_velocity_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            decoding_type, units, dist_per_pulse, custom_scale_name):
        response = self._invoke(
            self._client.CreateCILinVelocityChan,
            grpc_types.CreateCILinVelocityChanRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val,
                decoding_type_raw=decoding_type, units_raw=units,
                dist_per_pulse=dist_per_pulse,
                custom_scale_name=custom_scale_name))

    def create_ci_period_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units, edge, meas_method, meas_time, divisor, custom_scale_name):
        response = self._invoke(
            self._client.CreateCIPeriodChan,
            grpc_types.CreateCIPeriodChanRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                edge_raw=edge, meas_method_raw=meas_method,
                meas_time=meas_time, divisor=divisor,
                custom_scale_name=custom_scale_name))

    def create_ci_pulse_chan_freq(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units):
        response = self._invoke(
            self._client.CreateCIPulseChanFreq,
            grpc_types.CreateCIPulseChanFreqRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units))

    def create_ci_pulse_chan_ticks(
            self, task, counter, name_to_assign_to_channel, source_terminal,
            min_val, max_val):
        response = self._invoke(
            self._client.CreateCIPulseChanTicks,
            grpc_types.CreateCIPulseChanTicksRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                source_terminal=source_terminal, min_val=min_val,
                max_val=max_val))

    def create_ci_pulse_chan_time(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units):
        response = self._invoke(
            self._client.CreateCIPulseChanTime,
            grpc_types.CreateCIPulseChanTimeRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units))

    def create_ci_pulse_width_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units, starting_edge, custom_scale_name):
        response = self._invoke(
            self._client.CreateCIPulseWidthChan,
            grpc_types.CreateCIPulseWidthChanRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                starting_edge_raw=starting_edge,
                custom_scale_name=custom_scale_name))

    def create_ci_semi_period_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units, custom_scale_name):
        response = self._invoke(
            self._client.CreateCISemiPeriodChan,
            grpc_types.CreateCISemiPeriodChanRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                custom_scale_name=custom_scale_name))

    def create_ci_two_edge_sep_chan(
            self, task, counter, name_to_assign_to_channel, min_val, max_val,
            units, first_edge, second_edge, custom_scale_name):
        response = self._invoke(
            self._client.CreateCITwoEdgeSepChan,
            grpc_types.CreateCITwoEdgeSepChanRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                first_edge_raw=first_edge, second_edge_raw=second_edge,
                custom_scale_name=custom_scale_name))

    def create_cigps_timestamp_chan(
            self, task, counter, name_to_assign_to_channel, units,
            sync_method, custom_scale_name):
        response = self._invoke(
            self._client.CreateCIGPSTimestampChan,
            grpc_types.CreateCIGPSTimestampChanRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                units_raw=units, sync_method_raw=sync_method,
                custom_scale_name=custom_scale_name))

    def create_co_pulse_chan_freq(
            self, task, counter, name_to_assign_to_channel, units, idle_state,
            initial_delay, freq, duty_cycle):
        response = self._invoke(
            self._client.CreateCOPulseChanFreq,
            grpc_types.CreateCOPulseChanFreqRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                units_raw=units, idle_state_raw=idle_state,
                initial_delay=initial_delay, freq=freq, duty_cycle=duty_cycle))

    def create_co_pulse_chan_ticks(
            self, task, counter, source_terminal, name_to_assign_to_channel,
            idle_state, initial_delay, low_ticks, high_ticks):
        response = self._invoke(
            self._client.CreateCOPulseChanTicks,
            grpc_types.CreateCOPulseChanTicksRequest(
                task=task, counter=counter, source_terminal=source_terminal,
                name_to_assign_to_channel=name_to_assign_to_channel,
                idle_state_raw=idle_state, initial_delay=initial_delay,
                low_ticks=low_ticks, high_ticks=high_ticks))

    def create_co_pulse_chan_time(
            self, task, counter, name_to_assign_to_channel, units, idle_state,
            initial_delay, low_time, high_time):
        response = self._invoke(
            self._client.CreateCOPulseChanTime,
            grpc_types.CreateCOPulseChanTimeRequest(
                task=task, counter=counter,
                name_to_assign_to_channel=name_to_assign_to_channel,
                units_raw=units, idle_state_raw=idle_state,
                initial_delay=initial_delay, low_time=low_time,
                high_time=high_time))

    def create_di_chan(
            self, task, lines, name_to_assign_to_lines, line_grouping):
        response = self._invoke(
            self._client.CreateDIChan,
            grpc_types.CreateDIChanRequest(
                task=task, lines=lines,
                name_to_assign_to_lines=name_to_assign_to_lines,
                line_grouping_raw=line_grouping))

    def create_do_chan(
            self, task, lines, name_to_assign_to_lines, line_grouping):
        response = self._invoke(
            self._client.CreateDOChan,
            grpc_types.CreateDOChanRequest(
                task=task, lines=lines,
                name_to_assign_to_lines=name_to_assign_to_lines,
                line_grouping_raw=line_grouping))

    def create_lin_scale(
            self, name, slope, y_intercept, pre_scaled_units, scaled_units):
        response = self._invoke(
            self._client.CreateLinScale,
            grpc_types.CreateLinScaleRequest(
                name=name, slope=slope, y_intercept=y_intercept,
                pre_scaled_units_raw=pre_scaled_units,
                scaled_units=scaled_units))

    def create_map_scale(
            self, name, prescaled_min, prescaled_max, scaled_min, scaled_max,
            pre_scaled_units, scaled_units):
        response = self._invoke(
            self._client.CreateMapScale,
            grpc_types.CreateMapScaleRequest(
                name=name, prescaled_min=prescaled_min,
                prescaled_max=prescaled_max, scaled_min=scaled_min,
                scaled_max=scaled_max, pre_scaled_units_raw=pre_scaled_units,
                scaled_units=scaled_units))

    def create_polynomial_scale(
            self, name, forward_coeffs, reverse_coeffs, pre_scaled_units,
            scaled_units):
        response = self._invoke(
            self._client.CreatePolynomialScale,
            grpc_types.CreatePolynomialScaleRequest(
                name=name, forward_coeffs=forward_coeffs,
                reverse_coeffs=reverse_coeffs,
                pre_scaled_units_raw=pre_scaled_units,
                scaled_units=scaled_units))

    def create_table_scale(
            self, name, prescaled_vals, scaled_vals, pre_scaled_units,
            scaled_units):
        response = self._invoke(
            self._client.CreateTableScale,
            grpc_types.CreateTableScaleRequest(
                name=name, prescaled_vals=prescaled_vals,
                scaled_vals=scaled_vals,
                pre_scaled_units_raw=pre_scaled_units,
                scaled_units=scaled_units))

    def create_task(self, session_name):
        metadata = (
            ('ni-api-key', self._grpc_options.api_key),
        )
        response = self._invoke(
            self._client.CreateTask,
            grpc_types.CreateTaskRequest(
                session_name=session_name,
                initialization_behavior=self._grpc_options.initialization_behavior),
            metadata=metadata)
        return response.task, response.new_session_initialized

    def create_tedsai_accel_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, current_excit_source,
            current_excit_val, custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAIAccelChan,
            grpc_types.CreateTEDSAIAccelChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units,
                current_excit_source_raw=current_excit_source,
                current_excit_val=current_excit_val,
                custom_scale_name=custom_scale_name))

    def create_tedsai_bridge_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAIBridgeChan,
            grpc_types.CreateTEDSAIBridgeChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                custom_scale_name=custom_scale_name))

    def create_tedsai_current_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, shunt_resistor_loc,
            ext_shunt_resistor_val, custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAICurrentChan,
            grpc_types.CreateTEDSAICurrentChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units,
                shunt_resistor_loc_raw=shunt_resistor_loc,
                ext_shunt_resistor_val=ext_shunt_resistor_val,
                custom_scale_name=custom_scale_name))

    def create_tedsai_force_bridge_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAIForceBridgeChan,
            grpc_types.CreateTEDSAIForceBridgeChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                custom_scale_name=custom_scale_name))

    def create_tedsai_force_iepe_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, current_excit_source,
            current_excit_val, custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAIForceIEPEChan,
            grpc_types.CreateTEDSAIForceIEPEChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units,
                current_excit_source_raw=current_excit_source,
                current_excit_val=current_excit_val,
                custom_scale_name=custom_scale_name))

    def create_tedsai_microphone_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, units, max_snd_press_level, current_excit_source,
            current_excit_val, custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAIMicrophoneChan,
            grpc_types.CreateTEDSAIMicrophoneChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, units_raw=units,
                max_snd_press_level=max_snd_press_level,
                current_excit_source_raw=current_excit_source,
                current_excit_val=current_excit_val,
                custom_scale_name=custom_scale_name))

    def create_tedsai_pos_lvdt_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            voltage_excit_freq, ac_excit_wire_mode, custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAIPosLVDTChan,
            grpc_types.CreateTEDSAIPosLVDTChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                voltage_excit_freq=voltage_excit_freq,
                ac_excit_wire_mode_raw=ac_excit_wire_mode,
                custom_scale_name=custom_scale_name))

    def create_tedsai_pos_rvdt_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            voltage_excit_freq, ac_excit_wire_mode, custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAIPosRVDTChan,
            grpc_types.CreateTEDSAIPosRVDTChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                voltage_excit_freq=voltage_excit_freq,
                ac_excit_wire_mode_raw=ac_excit_wire_mode,
                custom_scale_name=custom_scale_name))

    def create_tedsai_pressure_bridge_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAIPressureBridgeChan,
            grpc_types.CreateTEDSAIPressureBridgeChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                custom_scale_name=custom_scale_name))

    def create_tedsai_resistance_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, current_excit_source,
            current_excit_val, custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAIResistanceChan,
            grpc_types.CreateTEDSAIResistanceChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                resistance_config_raw=resistance_config,
                current_excit_source_raw=current_excit_source,
                current_excit_val=current_excit_val,
                custom_scale_name=custom_scale_name))

    def create_tedsai_strain_gage_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            initial_bridge_voltage, lead_wire_resistance, custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAIStrainGageChan,
            grpc_types.CreateTEDSAIStrainGageChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                initial_bridge_voltage=initial_bridge_voltage,
                lead_wire_resistance=lead_wire_resistance,
                custom_scale_name=custom_scale_name))

    def create_tedsai_thrmcpl_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, cjc_source, cjc_val, cjc_channel):
        response = self._invoke(
            self._client.CreateTEDSAIThrmcplChan,
            grpc_types.CreateTEDSAIThrmcplChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                cjc_source_raw=cjc_source, cjc_val=cjc_val,
                cjc_channel=cjc_channel))

    def create_tedsai_thrmstr_chan_iex(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, current_excit_source,
            current_excit_val):
        response = self._invoke(
            self._client.CreateTEDSAIThrmstrChanIex,
            grpc_types.CreateTEDSAIThrmstrChanIexRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                resistance_config_raw=resistance_config,
                current_excit_source_raw=current_excit_source,
                current_excit_val=current_excit_val))

    def create_tedsai_thrmstr_chan_vex(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, voltage_excit_source,
            voltage_excit_val, r_1):
        response = self._invoke(
            self._client.CreateTEDSAIThrmstrChanVex,
            grpc_types.CreateTEDSAIThrmstrChanVexRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                resistance_config_raw=resistance_config,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val, r1=r_1))

    def create_tedsai_torque_bridge_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, voltage_excit_source, voltage_excit_val,
            custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAITorqueBridgeChan,
            grpc_types.CreateTEDSAITorqueBridgeChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                custom_scale_name=custom_scale_name))

    def create_tedsai_voltage_chan(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAIVoltageChan,
            grpc_types.CreateTEDSAIVoltageChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units,
                custom_scale_name=custom_scale_name))

    def create_tedsai_voltage_chan_with_excit(
            self, task, physical_channel, name_to_assign_to_channel,
            terminal_config, min_val, max_val, units, voltage_excit_source,
            voltage_excit_val, custom_scale_name):
        response = self._invoke(
            self._client.CreateTEDSAIVoltageChanWithExcit,
            grpc_types.CreateTEDSAIVoltageChanWithExcitRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                terminal_config_raw=terminal_config, min_val=min_val,
                max_val=max_val, units_raw=units,
                voltage_excit_source_raw=voltage_excit_source,
                voltage_excit_val=voltage_excit_val,
                custom_scale_name=custom_scale_name))

    def create_tedsairtd_chan(
            self, task, physical_channel, name_to_assign_to_channel, min_val,
            max_val, units, resistance_config, current_excit_source,
            current_excit_val):
        response = self._invoke(
            self._client.CreateTEDSAIRTDChan,
            grpc_types.CreateTEDSAIRTDChanRequest(
                task=task, physical_channel=physical_channel,
                name_to_assign_to_channel=name_to_assign_to_channel,
                min_val=min_val, max_val=max_val, units_raw=units,
                resistance_config_raw=resistance_config,
                current_excit_source_raw=current_excit_source,
                current_excit_val=current_excit_val))

    def create_watchdog_timer_task_ex(
            self, device_name, session_name, timeout):
        metadata = (
            ('ni-api-key', self._grpc_options.api_key),
        )
        response = self._invoke(
            self._client.CreateWatchdogTimerTaskEx,
            grpc_types.CreateWatchdogTimerTaskExRequest(
                device_name=device_name, session_name=session_name,
                timeout=timeout,
                initialization_behavior=self._grpc_options.initialization_behavior),
            metadata=metadata)
        return response.task, response.new_session_initialized

    def delete_network_device(self, device_name):
        response = self._invoke(
            self._client.DeleteNetworkDevice,
            grpc_types.DeleteNetworkDeviceRequest(device_name=device_name))

    def delete_saved_global_chan(self, channel_name):
        response = self._invoke(
            self._client.DeleteSavedGlobalChan,
            grpc_types.DeleteSavedGlobalChanRequest(channel_name=channel_name))

    def delete_saved_scale(self, scale_name):
        response = self._invoke(
            self._client.DeleteSavedScale,
            grpc_types.DeleteSavedScaleRequest(scale_name=scale_name))

    def delete_saved_task(self, task_name):
        response = self._invoke(
            self._client.DeleteSavedTask,
            grpc_types.DeleteSavedTaskRequest(task_name=task_name))

    def device_supports_cal(self, device_name):
        response = self._invoke(
            self._client.DeviceSupportsCal,
            grpc_types.DeviceSupportsCalRequest(device_name=device_name))
        return response.cal_supported

    def disable_ref_trig(self, task):
        response = self._invoke(
            self._client.DisableRefTrig,
            grpc_types.DisableRefTrigRequest(task=task))

    def disable_start_trig(self, task):
        response = self._invoke(
            self._client.DisableStartTrig,
            grpc_types.DisableStartTrigRequest(task=task))

    def disconnect_terms(self, source_terminal, destination_terminal):
        response = self._invoke(
            self._client.DisconnectTerms,
            grpc_types.DisconnectTermsRequest(
                source_terminal=source_terminal,
                destination_terminal=destination_terminal))

    def export_signal(self, task, signal_id, output_terminal):
        response = self._invoke(
            self._client.ExportSignal,
            grpc_types.ExportSignalRequest(
                task=task, signal_id_raw=signal_id,
                output_terminal=output_terminal))

    def get_analog_power_up_states_with_output_type(
            self, channel_names, array_size):
        response = self._invoke(
            self._client.GetAnalogPowerUpStatesWithOutputType,
            grpc_types.GetAnalogPowerUpStatesWithOutputTypeRequest(
                channel_names=channel_names, array_size=array_size))
        return list(response.state_array), list(response.channel_type_array)

    def get_auto_configured_cdaq_sync_connections(self):
        response = self._invoke(
            self._client.GetAutoConfiguredCDAQSyncConnections,
            grpc_types.GetAutoConfiguredCDAQSyncConnectionsRequest())
        return response.port_list

    def get_buffer_attribute_uint32(self, task, attribute):
        response = self._invoke(
            self._client.GetBufferAttributeUInt32,
            grpc_types.GetBufferAttributeUInt32Request(task=task, attribute_raw=attribute))
        return response.value

    def get_cal_info_attribute_bool(self, device_name, attribute):
        response = self._invoke(
            self._client.GetCalInfoAttributeBool,
            grpc_types.GetCalInfoAttributeBoolRequest(
                device_name=device_name, attribute_raw=attribute))
        return response.value

    def get_cal_info_attribute_double(self, device_name, attribute):
        response = self._invoke(
            self._client.GetCalInfoAttributeDouble,
            grpc_types.GetCalInfoAttributeDoubleRequest(
                device_name=device_name, attribute_raw=attribute))
        return response.value

    def get_cal_info_attribute_string(self, device_name, attribute):
        response = self._invoke(
            self._client.GetCalInfoAttributeString,
            grpc_types.GetCalInfoAttributeStringRequest(
                device_name=device_name, attribute_raw=attribute))
        return response.value

    def get_cal_info_attribute_uint32(self, device_name, attribute):
        response = self._invoke(
            self._client.GetCalInfoAttributeUInt32,
            grpc_types.GetCalInfoAttributeUInt32Request(
                device_name=device_name, attribute_raw=attribute))
        return response.value

    def get_chan_attribute_bool(self, task, channel, attribute):
        response = self._invoke(
            self._client.GetChanAttributeBool,
            grpc_types.GetChanAttributeBoolRequest(
                task=task, channel=channel, attribute_raw=attribute))
        return response.value

    def get_chan_attribute_double(self, task, channel, attribute):
        response = self._invoke(
            self._client.GetChanAttributeDouble,
            grpc_types.GetChanAttributeDoubleRequest(
                task=task, channel=channel, attribute_raw=attribute))
        return response.value

    def get_chan_attribute_double_array(self, task, channel, attribute):
        response = self._invoke(
            self._client.GetChanAttributeDoubleArray,
            grpc_types.GetChanAttributeDoubleArrayRequest(
                task=task, channel=channel, attribute_raw=attribute))
        return list(response.value)

    def get_chan_attribute_int32(self, task, channel, attribute):
        response = self._invoke(
            self._client.GetChanAttributeInt32,
            grpc_types.GetChanAttributeInt32Request(
                task=task, channel=channel, attribute_raw=attribute))
        return response.value_raw

    def get_chan_attribute_string(self, task, channel, attribute):
        response = self._invoke(
            self._client.GetChanAttributeString,
            grpc_types.GetChanAttributeStringRequest(
                task=task, channel=channel, attribute_raw=attribute))
        return response.value

    def get_chan_attribute_uint32(self, task, channel, attribute):
        response = self._invoke(
            self._client.GetChanAttributeUInt32,
            grpc_types.GetChanAttributeUInt32Request(
                task=task, channel=channel, attribute_raw=attribute))
        return response.value

    def get_device_attribute_bool(self, device_name, attribute):
        response = self._invoke(
            self._client.GetDeviceAttributeBool,
            grpc_types.GetDeviceAttributeBoolRequest(
                device_name=device_name, attribute_raw=attribute))
        return response.value

    def get_device_attribute_double(self, device_name, attribute):
        response = self._invoke(
            self._client.GetDeviceAttributeDouble,
            grpc_types.GetDeviceAttributeDoubleRequest(
                device_name=device_name, attribute_raw=attribute))
        return response.value

    def get_device_attribute_double_array(self, device_name, attribute):
        response = self._invoke(
            self._client.GetDeviceAttributeDoubleArray,
            grpc_types.GetDeviceAttributeDoubleArrayRequest(
                device_name=device_name, attribute_raw=attribute))
        return list(response.value)

    def get_device_attribute_int32(self, device_name, attribute):
        response = self._invoke(
            self._client.GetDeviceAttributeInt32,
            grpc_types.GetDeviceAttributeInt32Request(
                device_name=device_name, attribute_raw=attribute))
        return response.value_raw

    def get_device_attribute_int32_array(self, device_name, attribute):
        response = self._invoke(
            self._client.GetDeviceAttributeInt32Array,
            grpc_types.GetDeviceAttributeInt32ArrayRequest(
                device_name=device_name, attribute_raw=attribute))
        return response.value_raw

    def get_device_attribute_string(self, device_name, attribute):
        response = self._invoke(
            self._client.GetDeviceAttributeString,
            grpc_types.GetDeviceAttributeStringRequest(
                device_name=device_name, attribute_raw=attribute))
        return response.value

    def get_device_attribute_uint32(self, device_name, attribute):
        response = self._invoke(
            self._client.GetDeviceAttributeUInt32,
            grpc_types.GetDeviceAttributeUInt32Request(
                device_name=device_name, attribute_raw=attribute))
        return response.value

    def get_device_attribute_uint32_array(self, device_name, attribute):
        response = self._invoke(
            self._client.GetDeviceAttributeUInt32Array,
            grpc_types.GetDeviceAttributeUInt32ArrayRequest(
                device_name=device_name, attribute_raw=attribute))
        return list(response.value)

    def get_digital_logic_family_power_up_state(self, device_name):
        response = self._invoke(
            self._client.GetDigitalLogicFamilyPowerUpState,
            grpc_types.GetDigitalLogicFamilyPowerUpStateRequest(device_name=device_name))
        return response.logic_family

    def get_digital_power_up_states(self, device_name, channel_name):
        response = self._invoke(
            self._client.GetDigitalPowerUpStates,
            grpc_types.GetDigitalPowerUpStatesRequest(
                device_name=device_name, channel_name=channel_name))
        return response.power_up_states

    def get_digital_pull_up_pull_down_states(self, device_name, channel_name):
        response = self._invoke(
            self._client.GetDigitalPullUpPullDownStates,
            grpc_types.GetDigitalPullUpPullDownStatesRequest(
                device_name=device_name, channel_name=channel_name))
        return response.pull_up_pull_down_states

    def get_disconnected_cdaq_sync_ports(self):
        response = self._invoke(
            self._client.GetDisconnectedCDAQSyncPorts,
            grpc_types.GetDisconnectedCDAQSyncPortsRequest())
        return response.port_list

    def get_exported_signal_attribute_bool(self, task, attribute):
        response = self._invoke(
            self._client.GetExportedSignalAttributeBool,
            grpc_types.GetExportedSignalAttributeBoolRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_exported_signal_attribute_double(self, task, attribute):
        response = self._invoke(
            self._client.GetExportedSignalAttributeDouble,
            grpc_types.GetExportedSignalAttributeDoubleRequest(
                task=task, attribute_raw=attribute))
        return response.value

    def get_exported_signal_attribute_int32(self, task, attribute):
        response = self._invoke(
            self._client.GetExportedSignalAttributeInt32,
            grpc_types.GetExportedSignalAttributeInt32Request(
                task=task, attribute_raw=attribute))
        return response.value_raw

    def get_exported_signal_attribute_string(self, task, attribute):
        response = self._invoke(
            self._client.GetExportedSignalAttributeString,
            grpc_types.GetExportedSignalAttributeStringRequest(
                task=task, attribute_raw=attribute))
        return response.value

    def get_exported_signal_attribute_uint32(self, task, attribute):
        response = self._invoke(
            self._client.GetExportedSignalAttributeUInt32,
            grpc_types.GetExportedSignalAttributeUInt32Request(
                task=task, attribute_raw=attribute))
        return response.value

    def get_ext_cal_last_date_and_time(self, device_name):
        response = self._invoke(
            self._client.GetExtCalLastDateAndTime,
            grpc_types.GetExtCalLastDateAndTimeRequest(device_name=device_name))
        return response.year, response.month, response.day, response.hour, response.minute

    def get_persisted_chan_attribute_bool(self, channel, attribute):
        response = self._invoke(
            self._client.GetPersistedChanAttributeBool,
            grpc_types.GetPersistedChanAttributeBoolRequest(
                channel=channel, attribute_raw=attribute))
        return response.value

    def get_persisted_chan_attribute_string(self, channel, attribute):
        response = self._invoke(
            self._client.GetPersistedChanAttributeString,
            grpc_types.GetPersistedChanAttributeStringRequest(
                channel=channel, attribute_raw=attribute))
        return response.value

    def get_persisted_scale_attribute_bool(self, scale_name, attribute):
        response = self._invoke(
            self._client.GetPersistedScaleAttributeBool,
            grpc_types.GetPersistedScaleAttributeBoolRequest(
                scale_name=scale_name, attribute_raw=attribute))
        return response.value

    def get_persisted_scale_attribute_string(self, scale_name, attribute):
        response = self._invoke(
            self._client.GetPersistedScaleAttributeString,
            grpc_types.GetPersistedScaleAttributeStringRequest(
                scale_name=scale_name, attribute_raw=attribute))
        return response.value

    def get_persisted_task_attribute_bool(self, task_name, attribute):
        response = self._invoke(
            self._client.GetPersistedTaskAttributeBool,
            grpc_types.GetPersistedTaskAttributeBoolRequest(
                task_name=task_name, attribute_raw=attribute))
        return response.value

    def get_persisted_task_attribute_string(self, task_name, attribute):
        response = self._invoke(
            self._client.GetPersistedTaskAttributeString,
            grpc_types.GetPersistedTaskAttributeStringRequest(
                task_name=task_name, attribute_raw=attribute))
        return response.value

    def get_physical_chan_attribute_bool(self, physical_channel, attribute):
        response = self._invoke(
            self._client.GetPhysicalChanAttributeBool,
            grpc_types.GetPhysicalChanAttributeBoolRequest(
                physical_channel=physical_channel, attribute_raw=attribute))
        return response.value

    def get_physical_chan_attribute_bytes(self, physical_channel, attribute):
        response = self._invoke(
            self._client.GetPhysicalChanAttributeBytes,
            grpc_types.GetPhysicalChanAttributeBytesRequest(
                physical_channel=physical_channel, attribute_raw=attribute))
        return list(response.value)

    def get_physical_chan_attribute_double(self, physical_channel, attribute):
        response = self._invoke(
            self._client.GetPhysicalChanAttributeDouble,
            grpc_types.GetPhysicalChanAttributeDoubleRequest(
                physical_channel=physical_channel, attribute_raw=attribute))
        return response.value

    def get_physical_chan_attribute_double_array(
            self, physical_channel, attribute):
        response = self._invoke(
            self._client.GetPhysicalChanAttributeDoubleArray,
            grpc_types.GetPhysicalChanAttributeDoubleArrayRequest(
                physical_channel=physical_channel, attribute_raw=attribute))
        return list(response.value)

    def get_physical_chan_attribute_int32(self, physical_channel, attribute):
        response = self._invoke(
            self._client.GetPhysicalChanAttributeInt32,
            grpc_types.GetPhysicalChanAttributeInt32Request(
                physical_channel=physical_channel, attribute_raw=attribute))
        return response.value_raw

    def get_physical_chan_attribute_int32_array(
            self, physical_channel, attribute):
        response = self._invoke(
            self._client.GetPhysicalChanAttributeInt32Array,
            grpc_types.GetPhysicalChanAttributeInt32ArrayRequest(
                physical_channel=physical_channel, attribute_raw=attribute))
        return response.value_raw

    def get_physical_chan_attribute_string(self, physical_channel, attribute):
        response = self._invoke(
            self._client.GetPhysicalChanAttributeString,
            grpc_types.GetPhysicalChanAttributeStringRequest(
                physical_channel=physical_channel, attribute_raw=attribute))
        return response.value

    def get_physical_chan_attribute_uint32(self, physical_channel, attribute):
        response = self._invoke(
            self._client.GetPhysicalChanAttributeUInt32,
            grpc_types.GetPhysicalChanAttributeUInt32Request(
                physical_channel=physical_channel, attribute_raw=attribute))
        return response.value

    def get_physical_chan_attribute_uint32_array(
            self, physical_channel, attribute):
        response = self._invoke(
            self._client.GetPhysicalChanAttributeUInt32Array,
            grpc_types.GetPhysicalChanAttributeUInt32ArrayRequest(
                physical_channel=physical_channel, attribute_raw=attribute))
        return list(response.value)

    def get_read_attribute_bool(self, task, attribute):
        response = self._invoke(
            self._client.GetReadAttributeBool,
            grpc_types.GetReadAttributeBoolRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_read_attribute_double(self, task, attribute):
        response = self._invoke(
            self._client.GetReadAttributeDouble,
            grpc_types.GetReadAttributeDoubleRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_read_attribute_int32(self, task, attribute):
        response = self._invoke(
            self._client.GetReadAttributeInt32,
            grpc_types.GetReadAttributeInt32Request(task=task, attribute_raw=attribute))
        return response.value_raw

    def get_read_attribute_string(self, task, attribute, size_hint=0):
        response = self._invoke(
            self._client.GetReadAttributeString,
            grpc_types.GetReadAttributeStringRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_read_attribute_uint32(self, task, attribute):
        response = self._invoke(
            self._client.GetReadAttributeUInt32,
            grpc_types.GetReadAttributeUInt32Request(task=task, attribute_raw=attribute))
        return response.value

    def get_read_attribute_uint64(self, task, attribute):
        response = self._invoke(
            self._client.GetReadAttributeUInt64,
            grpc_types.GetReadAttributeUInt64Request(task=task, attribute_raw=attribute))
        return response.value

    def get_scale_attribute_double(self, scale_name, attribute):
        response = self._invoke(
            self._client.GetScaleAttributeDouble,
            grpc_types.GetScaleAttributeDoubleRequest(
                scale_name=scale_name, attribute_raw=attribute))
        return response.value

    def get_scale_attribute_double_array(self, scale_name, attribute):
        response = self._invoke(
            self._client.GetScaleAttributeDoubleArray,
            grpc_types.GetScaleAttributeDoubleArrayRequest(
                scale_name=scale_name, attribute_raw=attribute))
        return list(response.value)

    def get_scale_attribute_int32(self, scale_name, attribute):
        response = self._invoke(
            self._client.GetScaleAttributeInt32,
            grpc_types.GetScaleAttributeInt32Request(
                scale_name=scale_name, attribute_raw=attribute))
        return response.value_raw

    def get_scale_attribute_string(self, scale_name, attribute):
        response = self._invoke(
            self._client.GetScaleAttributeString,
            grpc_types.GetScaleAttributeStringRequest(
                scale_name=scale_name, attribute_raw=attribute))
        return response.value

    def get_self_cal_last_date_and_time(self, device_name):
        response = self._invoke(
            self._client.GetSelfCalLastDateAndTime,
            grpc_types.GetSelfCalLastDateAndTimeRequest(device_name=device_name))
        return response.year, response.month, response.day, response.hour, response.minute

    def get_system_info_attribute_string(self, attribute):
        response = self._invoke(
            self._client.GetSystemInfoAttributeString,
            grpc_types.GetSystemInfoAttributeStringRequest(attribute_raw=attribute))
        return response.value

    def get_system_info_attribute_uint32(self, attribute):
        response = self._invoke(
            self._client.GetSystemInfoAttributeUInt32,
            grpc_types.GetSystemInfoAttributeUInt32Request(attribute_raw=attribute))
        return response.value

    def get_task_attribute_bool(self, task, attribute):
        response = self._invoke(
            self._client.GetTaskAttributeBool,
            grpc_types.GetTaskAttributeBoolRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_task_attribute_string(self, task, attribute):
        response = self._invoke(
            self._client.GetTaskAttributeString,
            grpc_types.GetTaskAttributeStringRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_task_attribute_uint32(self, task, attribute):
        response = self._invoke(
            self._client.GetTaskAttributeUInt32,
            grpc_types.GetTaskAttributeUInt32Request(task=task, attribute_raw=attribute))
        return response.value

    def get_timing_attribute_bool(self, task, attribute):
        response = self._invoke(
            self._client.GetTimingAttributeBool,
            grpc_types.GetTimingAttributeBoolRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_timing_attribute_double(self, task, attribute):
        response = self._invoke(
            self._client.GetTimingAttributeDouble,
            grpc_types.GetTimingAttributeDoubleRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_timing_attribute_ex_bool(self, task, device_names, attribute):
        response = self._invoke(
            self._client.GetTimingAttributeExBool,
            grpc_types.GetTimingAttributeExBoolRequest(
                task=task, device_names=device_names, attribute_raw=attribute))
        return response.value

    def get_timing_attribute_ex_double(self, task, device_names, attribute):
        response = self._invoke(
            self._client.GetTimingAttributeExDouble,
            grpc_types.GetTimingAttributeExDoubleRequest(
                task=task, device_names=device_names, attribute_raw=attribute))
        return response.value

    def get_timing_attribute_ex_int32(self, task, device_names, attribute):
        response = self._invoke(
            self._client.GetTimingAttributeExInt32,
            grpc_types.GetTimingAttributeExInt32Request(
                task=task, device_names=device_names, attribute_raw=attribute))
        return response.value_raw

    def get_timing_attribute_ex_string(self, task, device_names, attribute):
        response = self._invoke(
            self._client.GetTimingAttributeExString,
            grpc_types.GetTimingAttributeExStringRequest(
                task=task, device_names=device_names, attribute_raw=attribute))
        return response.value

    def get_timing_attribute_ex_uint32(self, task, device_names, attribute):
        response = self._invoke(
            self._client.GetTimingAttributeExUInt32,
            grpc_types.GetTimingAttributeExUInt32Request(
                task=task, device_names=device_names, attribute_raw=attribute))
        return response.value

    def get_timing_attribute_ex_uint64(self, task, device_names, attribute):
        response = self._invoke(
            self._client.GetTimingAttributeExUInt64,
            grpc_types.GetTimingAttributeExUInt64Request(
                task=task, device_names=device_names, attribute_raw=attribute))
        return response.value

    def get_timing_attribute_int32(self, task, attribute):
        response = self._invoke(
            self._client.GetTimingAttributeInt32,
            grpc_types.GetTimingAttributeInt32Request(task=task, attribute_raw=attribute))
        return response.value_raw

    def get_timing_attribute_string(self, task, attribute):
        response = self._invoke(
            self._client.GetTimingAttributeString,
            grpc_types.GetTimingAttributeStringRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_timing_attribute_uint32(self, task, attribute):
        response = self._invoke(
            self._client.GetTimingAttributeUInt32,
            grpc_types.GetTimingAttributeUInt32Request(task=task, attribute_raw=attribute))
        return response.value

    def get_timing_attribute_uint64(self, task, attribute):
        response = self._invoke(
            self._client.GetTimingAttributeUInt64,
            grpc_types.GetTimingAttributeUInt64Request(task=task, attribute_raw=attribute))
        return response.value

    def get_trig_attribute_bool(self, task, attribute):
        response = self._invoke(
            self._client.GetTrigAttributeBool,
            grpc_types.GetTrigAttributeBoolRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_trig_attribute_double(self, task, attribute):
        response = self._invoke(
            self._client.GetTrigAttributeDouble,
            grpc_types.GetTrigAttributeDoubleRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_trig_attribute_double_array(self, task, attribute):
        response = self._invoke(
            self._client.GetTrigAttributeDoubleArray,
            grpc_types.GetTrigAttributeDoubleArrayRequest(task=task, attribute_raw=attribute))
        return list(response.value)

    def get_trig_attribute_int32(self, task, attribute):
        response = self._invoke(
            self._client.GetTrigAttributeInt32,
            grpc_types.GetTrigAttributeInt32Request(task=task, attribute_raw=attribute))
        return response.value_raw

    def get_trig_attribute_int32_array(self, task, attribute):
        response = self._invoke(
            self._client.GetTrigAttributeInt32Array,
            grpc_types.GetTrigAttributeInt32ArrayRequest(task=task, attribute_raw=attribute))
        return response.value_raw

    def get_trig_attribute_string(self, task, attribute):
        response = self._invoke(
            self._client.GetTrigAttributeString,
            grpc_types.GetTrigAttributeStringRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_trig_attribute_timestamp(self, task, attribute):
        response = self._invoke(
            self._client.GetTrigAttributeTimestamp,
            grpc_types.GetTrigAttributeTimestampRequest(task=task, attribute_raw=attribute))
        return convert_timestamp_to_time(response.value)

    def get_trig_attribute_uint32(self, task, attribute):
        response = self._invoke(
            self._client.GetTrigAttributeUInt32,
            grpc_types.GetTrigAttributeUInt32Request(task=task, attribute_raw=attribute))
        return response.value

    def get_watchdog_attribute_bool(self, task, lines, attribute):
        response = self._invoke(
            self._client.GetWatchdogAttributeBool,
            grpc_types.GetWatchdogAttributeBoolRequest(
                task=task, lines=lines, attribute_raw=attribute))
        return response.value

    def get_watchdog_attribute_double(self, task, lines, attribute):
        response = self._invoke(
            self._client.GetWatchdogAttributeDouble,
            grpc_types.GetWatchdogAttributeDoubleRequest(
                task=task, lines=lines, attribute_raw=attribute))
        return response.value

    def get_watchdog_attribute_int32(self, task, lines, attribute):
        response = self._invoke(
            self._client.GetWatchdogAttributeInt32,
            grpc_types.GetWatchdogAttributeInt32Request(
                task=task, lines=lines, attribute_raw=attribute))
        return response.value_raw

    def get_watchdog_attribute_string(self, task, lines, attribute):
        response = self._invoke(
            self._client.GetWatchdogAttributeString,
            grpc_types.GetWatchdogAttributeStringRequest(
                task=task, lines=lines, attribute_raw=attribute))
        return response.value

    def get_write_attribute_bool(self, task, attribute):
        response = self._invoke(
            self._client.GetWriteAttributeBool,
            grpc_types.GetWriteAttributeBoolRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_write_attribute_double(self, task, attribute):
        response = self._invoke(
            self._client.GetWriteAttributeDouble,
            grpc_types.GetWriteAttributeDoubleRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_write_attribute_int32(self, task, attribute):
        response = self._invoke(
            self._client.GetWriteAttributeInt32,
            grpc_types.GetWriteAttributeInt32Request(task=task, attribute_raw=attribute))
        return response.value_raw

    def get_write_attribute_string(self, task, attribute, size_hint=0):
        response = self._invoke(
            self._client.GetWriteAttributeString,
            grpc_types.GetWriteAttributeStringRequest(task=task, attribute_raw=attribute))
        return response.value

    def get_write_attribute_uint32(self, task, attribute):
        response = self._invoke(
            self._client.GetWriteAttributeUInt32,
            grpc_types.GetWriteAttributeUInt32Request(task=task, attribute_raw=attribute))
        return response.value

    def get_write_attribute_uint64(self, task, attribute):
        response = self._invoke(
            self._client.GetWriteAttributeUInt64,
            grpc_types.GetWriteAttributeUInt64Request(task=task, attribute_raw=attribute))
        return response.value

    def is_task_done(self, task):
        response = self._invoke(
            self._client.IsTaskDone,
            grpc_types.IsTaskDoneRequest(task=task))
        return response.is_task_done

    def load_task(self, session_name):
        metadata = (
            ('ni-api-key', self._grpc_options.api_key),
        )
        response = self._invoke(
            self._client.LoadTask,
            grpc_types.LoadTaskRequest(
                session_name=session_name,
                initialization_behavior=self._grpc_options.initialization_behavior),
            metadata=metadata)
        return response.task, response.new_session_initialized

    def perform_bridge_offset_nulling_cal_ex(
            self, task, channel, skip_unsupported_channels):
        response = self._invoke(
            self._client.PerformBridgeOffsetNullingCalEx,
            grpc_types.PerformBridgeOffsetNullingCalExRequest(
                task=task, channel=channel,
                skip_unsupported_channels=skip_unsupported_channels))

    def perform_bridge_shunt_cal_ex(
            self, task, channel, shunt_resistor_value,
            shunt_resistor_location, shunt_resistor_select,
            shunt_resistor_source, bridge_resistance,
            skip_unsupported_channels):
        response = self._invoke(
            self._client.PerformBridgeShuntCalEx,
            grpc_types.PerformBridgeShuntCalExRequest(
                task=task, channel=channel,
                shunt_resistor_value=shunt_resistor_value,
                shunt_resistor_location_raw=shunt_resistor_location,
                shunt_resistor_select_raw=shunt_resistor_select,
                shunt_resistor_source_raw=shunt_resistor_source,
                bridge_resistance=bridge_resistance,
                skip_unsupported_channels=skip_unsupported_channels))

    def perform_strain_shunt_cal_ex(
            self, task, channel, shunt_resistor_value,
            shunt_resistor_location, shunt_resistor_select,
            shunt_resistor_source, skip_unsupported_channels):
        response = self._invoke(
            self._client.PerformStrainShuntCalEx,
            grpc_types.PerformStrainShuntCalExRequest(
                task=task, channel=channel,
                shunt_resistor_value=shunt_resistor_value,
                shunt_resistor_location_raw=shunt_resistor_location,
                shunt_resistor_select_raw=shunt_resistor_select,
                shunt_resistor_source_raw=shunt_resistor_source,
                skip_unsupported_channels=skip_unsupported_channels))

    def perform_thrmcpl_lead_offset_nulling_cal(
            self, task, channel, skip_unsupported_channels):
        response = self._invoke(
            self._client.PerformThrmcplLeadOffsetNullingCal,
            grpc_types.PerformThrmcplLeadOffsetNullingCalRequest(
                task=task, channel=channel,
                skip_unsupported_channels=skip_unsupported_channels))

    def read_analog_f64(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        _validate_array_dtype(read_array, numpy.float64)
        response = self._invoke(
            self._client.ReadAnalogF64,
            grpc_types.ReadAnalogF64Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, fill_mode_raw=fill_mode,
                array_size_in_samps=read_array.size))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array, response.samps_per_chan_read

    def read_analog_scalar_f64(self, task, timeout):
        response = self._invoke(
            self._client.ReadAnalogScalarF64,
            grpc_types.ReadAnalogScalarF64Request(task=task, timeout=timeout))
        return response.value

    def read_binary_i16(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        _validate_array_dtype(read_array, numpy.int16)
        response = self._invoke(
            self._client.ReadBinaryI16,
            grpc_types.ReadBinaryI16Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, fill_mode_raw=fill_mode,
                array_size_in_samps=read_array.size))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array, response.samps_per_chan_read

    def read_binary_i32(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        _validate_array_dtype(read_array, numpy.int32)
        response = self._invoke(
            self._client.ReadBinaryI32,
            grpc_types.ReadBinaryI32Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, fill_mode_raw=fill_mode,
                array_size_in_samps=read_array.size))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array, response.samps_per_chan_read

    def read_binary_u16(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        _validate_array_dtype(read_array, numpy.uint16)
        response = self._invoke(
            self._client.ReadBinaryU16,
            grpc_types.ReadBinaryU16Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, fill_mode_raw=fill_mode,
                array_size_in_samps=read_array.size))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array, response.samps_per_chan_read

    def read_binary_u32(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        _validate_array_dtype(read_array, numpy.uint32)
        response = self._invoke(
            self._client.ReadBinaryU32,
            grpc_types.ReadBinaryU32Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, fill_mode_raw=fill_mode,
                array_size_in_samps=read_array.size))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array, response.samps_per_chan_read

    def read_counter_f64(self, task, num_samps_per_chan, timeout, read_array):
        _validate_array_dtype(read_array, numpy.float64)
        response = self._invoke(
            self._client.ReadCounterF64,
            grpc_types.ReadCounterF64Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, array_size_in_samps=read_array.size))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array, response.samps_per_chan_read

    def read_counter_f64_ex(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        _validate_array_dtype(read_array, numpy.float64)
        response = self._invoke(
            self._client.ReadCounterF64Ex,
            grpc_types.ReadCounterF64ExRequest(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, fill_mode_raw=fill_mode,
                array_size_in_samps=read_array.size))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array, response.samps_per_chan_read

    def read_counter_scalar_f64(self, task, timeout):
        response = self._invoke(
            self._client.ReadCounterScalarF64,
            grpc_types.ReadCounterScalarF64Request(task=task, timeout=timeout))
        return response.value

    def read_counter_scalar_u32(self, task, timeout):
        response = self._invoke(
            self._client.ReadCounterScalarU32,
            grpc_types.ReadCounterScalarU32Request(task=task, timeout=timeout))
        return response.value

    def read_counter_u32(self, task, num_samps_per_chan, timeout, read_array):
        _validate_array_dtype(read_array, numpy.uint32)
        response = self._invoke(
            self._client.ReadCounterU32,
            grpc_types.ReadCounterU32Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, array_size_in_samps=read_array.size))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array, response.samps_per_chan_read

    def read_counter_u32_ex(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        _validate_array_dtype(read_array, numpy.uint32)
        response = self._invoke(
            self._client.ReadCounterU32Ex,
            grpc_types.ReadCounterU32ExRequest(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, fill_mode_raw=fill_mode,
                array_size_in_samps=read_array.size))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array, response.samps_per_chan_read

    def read_ctr_freq(
            self, task, num_samps_per_chan, timeout, interleaved,
            read_array_frequency, read_array_duty_cycle):
        _validate_array_dtype(read_array_frequency, numpy.float64)
        _validate_array_dtype(read_array_duty_cycle, numpy.float64)
        response = self._invoke(
            self._client.ReadCtrFreq,
            grpc_types.ReadCtrFreqRequest(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, interleaved_raw=interleaved,
                array_size_in_samps=read_array_frequency.size))
    
        _assign_numpy_array(read_array_frequency, response.read_array_frequency)
        _assign_numpy_array(read_array_duty_cycle, response.read_array_duty_cycle)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array_frequency, read_array_duty_cycle, response.samps_per_chan_read

    def read_ctr_freq_scalar(self, task, timeout):
        response = self._invoke(
            self._client.ReadCtrFreqScalar,
            grpc_types.ReadCtrFreqScalarRequest(task=task, timeout=timeout))
        return response.frequency, response.duty_cycle

    def read_ctr_ticks(
            self, task, num_samps_per_chan, timeout, interleaved,
            read_array_high_ticks, read_array_low_ticks):
        _validate_array_dtype(read_array_high_ticks, numpy.uint32)
        _validate_array_dtype(read_array_low_ticks, numpy.uint32)
        response = self._invoke(
            self._client.ReadCtrTicks,
            grpc_types.ReadCtrTicksRequest(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, interleaved_raw=interleaved,
                array_size_in_samps=read_array_high_ticks.size))
    
        _assign_numpy_array(read_array_high_ticks, response.read_array_high_ticks)
        _assign_numpy_array(read_array_low_ticks, response.read_array_low_ticks)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array_high_ticks, read_array_low_ticks, response.samps_per_chan_read

    def read_ctr_ticks_scalar(self, task, timeout):
        response = self._invoke(
            self._client.ReadCtrTicksScalar,
            grpc_types.ReadCtrTicksScalarRequest(task=task, timeout=timeout))
        return response.high_ticks, response.low_ticks

    def read_ctr_time(
            self, task, num_samps_per_chan, timeout, interleaved,
            read_array_high_time, read_array_low_time):
        _validate_array_dtype(read_array_high_time, numpy.float64)
        _validate_array_dtype(read_array_low_time, numpy.float64)
        response = self._invoke(
            self._client.ReadCtrTime,
            grpc_types.ReadCtrTimeRequest(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, interleaved_raw=interleaved,
                array_size_in_samps=read_array_high_time.size))
    
        _assign_numpy_array(read_array_high_time, response.read_array_high_time)
        _assign_numpy_array(read_array_low_time, response.read_array_low_time)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array_high_time, read_array_low_time, response.samps_per_chan_read

    def read_ctr_time_scalar(self, task, timeout):
        response = self._invoke(
            self._client.ReadCtrTimeScalar,
            grpc_types.ReadCtrTimeScalarRequest(task=task, timeout=timeout))
        return response.high_time, response.low_time

    def read_digital_lines(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        _validate_array_dtype(read_array, bool)
        response = self._invoke(
            self._client.ReadDigitalLines,
            grpc_types.ReadDigitalLinesRequest(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, fill_mode_raw=fill_mode,
                array_size_in_bytes=read_array.nbytes))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array, response.samps_per_chan_read, response.num_bytes_per_samp

    def read_digital_scalar_u32(self, task, timeout):
        response = self._invoke(
            self._client.ReadDigitalScalarU32,
            grpc_types.ReadDigitalScalarU32Request(task=task, timeout=timeout))
        return response.value

    def read_digital_u16(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        _validate_array_dtype(read_array, numpy.uint16)
        response = self._invoke(
            self._client.ReadDigitalU16,
            grpc_types.ReadDigitalU16Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, fill_mode_raw=fill_mode,
                array_size_in_samps=read_array.size))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array, response.samps_per_chan_read

    def read_digital_u32(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        _validate_array_dtype(read_array, numpy.uint32)
        response = self._invoke(
            self._client.ReadDigitalU32,
            grpc_types.ReadDigitalU32Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, fill_mode_raw=fill_mode,
                array_size_in_samps=read_array.size))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array, response.samps_per_chan_read

    def read_digital_u8(
            self, task, num_samps_per_chan, timeout, fill_mode, read_array):
        _validate_array_dtype(read_array, numpy.uint8)
        response = self._invoke(
            self._client.ReadDigitalU8,
            grpc_types.ReadDigitalU8Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, fill_mode_raw=fill_mode,
                array_size_in_samps=read_array.nbytes))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array, response.samps_per_chan_read

    def read_power_binary_i16(
            self, task, num_samps_per_chan, timeout, fill_mode,
            read_array_voltage, read_array_current):
        _validate_array_dtype(read_array_voltage, numpy.int16)
        _validate_array_dtype(read_array_current, numpy.int16)
        response = self._invoke(
            self._client.ReadPowerBinaryI16,
            grpc_types.ReadPowerBinaryI16Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, fill_mode_raw=fill_mode,
                array_size_in_samps=read_array_voltage.size))
    
        _assign_numpy_array(read_array_voltage, response.read_array_voltage)
        _assign_numpy_array(read_array_current, response.read_array_current)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array_voltage, read_array_current, response.samps_per_chan_read

    def read_power_f64(
            self, task, num_samps_per_chan, timeout, fill_mode,
            read_array_voltage, read_array_current):
        _validate_array_dtype(read_array_voltage, numpy.float64)
        _validate_array_dtype(read_array_current, numpy.float64)
        response = self._invoke(
            self._client.ReadPowerF64,
            grpc_types.ReadPowerF64Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, fill_mode_raw=fill_mode,
                array_size_in_samps=read_array_voltage.size))
    
        _assign_numpy_array(read_array_voltage, response.read_array_voltage)
        _assign_numpy_array(read_array_current, response.read_array_current)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_per_chan_read)
        return read_array_voltage, read_array_current, response.samps_per_chan_read

    def read_power_scalar_f64(self, task, timeout):
        response = self._invoke(
            self._client.ReadPowerScalarF64,
            grpc_types.ReadPowerScalarF64Request(task=task, timeout=timeout))
        return response.voltage, response.current

    def read_raw(self, task, num_samps_per_chan, timeout, read_array):
        _validate_array_dtype(read_array, numpy.generic)
        response = self._invoke(
            self._client.ReadRaw,
            grpc_types.ReadRawRequest(
                task=task, num_samps_per_chan=num_samps_per_chan,
                timeout=timeout, array_size_in_bytes=read_array.nbytes))
    
        _assign_numpy_array(read_array, response.read_array)
        self._check_for_error_from_response(response.status, samps_per_chan_read=response.samps_read)
        return read_array, response.samps_read, response.num_bytes_per_samp

    def register_done_event(
            self, task, options, callback_function, callback_data):
        assert options == 0
        assert callback_function is not None

        event_stream = self._invoke(
            self._client.RegisterDoneEvent,
            grpc_types.RegisterDoneEventRequest(task=task))

        self._check_for_event_registration_error(event_stream)

        def invoke_callback(response):
            try:
                callback_function(
                    task, response.status, callback_data)
            except Exception:
                _logger.exception(
                    "Ignoring unhandled exception raised by event callback function: %r",
                    callback_function,
                )

        return GrpcEventHandler(
            "done event",
            self,
            event_stream,
            invoke_callback,
        )

    def register_every_n_samples_event(
            self, task, every_n_samples_event_type, n_samples, options,
            callback_function, callback_data):
        assert options == 0
        assert callback_function is not None

        event_stream = self._invoke(
            self._client.RegisterEveryNSamplesEvent,
            grpc_types.RegisterEveryNSamplesEventRequest(
                task=task,
                every_n_samples_event_type_raw=every_n_samples_event_type,
                n_samples=n_samples))

        self._check_for_event_registration_error(event_stream)

        def invoke_callback(response):
            try:
                callback_function(
                    task, response.every_n_samples_event_type_raw,
                    response.n_samples, callback_data)
            except Exception:
                _logger.exception(
                    "Ignoring unhandled exception raised by event callback function: %r",
                    callback_function,
                )

        return GrpcEventHandler(
            "every n samples event",
            self,
            event_stream,
            invoke_callback,
        )

    def register_signal_event(
            self, task, signal_id, options, callback_function, callback_data):
        assert options == 0
        assert callback_function is not None

        event_stream = self._invoke(
            self._client.RegisterSignalEvent,
            grpc_types.RegisterSignalEventRequest(task=task, signal_id_raw=signal_id))

        self._check_for_event_registration_error(event_stream)

        def invoke_callback(response):
            try:
                callback_function(
                    task, response.signal_id, callback_data)
            except Exception:
                _logger.exception(
                    "Ignoring unhandled exception raised by event callback function: %r",
                    callback_function,
                )

        return GrpcEventHandler(
            "signal event",
            self,
            event_stream,
            invoke_callback,
        )

    def remove_cdaq_sync_connection(self, port_list):
        response = self._invoke(
            self._client.RemoveCDAQSyncConnection,
            grpc_types.RemoveCDAQSyncConnectionRequest(port_list=port_list))

    def reserve_network_device(self, device_name, override_reservation):
        response = self._invoke(
            self._client.ReserveNetworkDevice,
            grpc_types.ReserveNetworkDeviceRequest(
                device_name=device_name,
                override_reservation=override_reservation))

    def reset_buffer_attribute(self, task, attribute):
        response = self._invoke(
            self._client.ResetBufferAttribute,
            grpc_types.ResetBufferAttributeRequest(task=task, attribute_raw=attribute))

    def reset_chan_attribute(self, task, channel, attribute):
        response = self._invoke(
            self._client.ResetChanAttribute,
            grpc_types.ResetChanAttributeRequest(
                task=task, channel=channel, attribute_raw=attribute))

    def reset_device(self, device_name):
        response = self._invoke(
            self._client.ResetDevice,
            grpc_types.ResetDeviceRequest(device_name=device_name))

    def reset_exported_signal_attribute(self, task, attribute):
        response = self._invoke(
            self._client.ResetExportedSignalAttribute,
            grpc_types.ResetExportedSignalAttributeRequest(task=task, attribute_raw=attribute))

    def reset_read_attribute(self, task, attribute):
        response = self._invoke(
            self._client.ResetReadAttribute,
            grpc_types.ResetReadAttributeRequest(task=task, attribute_raw=attribute))

    def reset_timing_attribute(self, task, attribute):
        response = self._invoke(
            self._client.ResetTimingAttribute,
            grpc_types.ResetTimingAttributeRequest(task=task, attribute_raw=attribute))

    def reset_timing_attribute_ex(self, task, device_names, attribute):
        response = self._invoke(
            self._client.ResetTimingAttributeEx,
            grpc_types.ResetTimingAttributeExRequest(
                task=task, device_names=device_names, attribute_raw=attribute))

    def reset_trig_attribute(self, task, attribute):
        response = self._invoke(
            self._client.ResetTrigAttribute,
            grpc_types.ResetTrigAttributeRequest(task=task, attribute_raw=attribute))

    def reset_watchdog_attribute(self, task, lines, attribute):
        response = self._invoke(
            self._client.ResetWatchdogAttribute,
            grpc_types.ResetWatchdogAttributeRequest(
                task=task, lines=lines, attribute_raw=attribute))

    def reset_write_attribute(self, task, attribute):
        response = self._invoke(
            self._client.ResetWriteAttribute,
            grpc_types.ResetWriteAttributeRequest(task=task, attribute_raw=attribute))

    def restore_last_ext_cal_const(self, device_name):
        response = self._invoke(
            self._client.RestoreLastExtCalConst,
            grpc_types.RestoreLastExtCalConstRequest(device_name=device_name))

    def save_global_chan(self, task, channel_name, save_as, author, options):
        response = self._invoke(
            self._client.SaveGlobalChan,
            grpc_types.SaveGlobalChanRequest(
                task=task, channel_name=channel_name, save_as=save_as,
                author=author, options_raw=options))

    def save_scale(self, scale_name, save_as, author, options):
        response = self._invoke(
            self._client.SaveScale,
            grpc_types.SaveScaleRequest(
                scale_name=scale_name, save_as=save_as, author=author,
                options_raw=options))

    def save_task(self, task, save_as, author, options):
        response = self._invoke(
            self._client.SaveTask,
            grpc_types.SaveTaskRequest(
                task=task, save_as=save_as, author=author,
                options_raw=options))

    def self_cal(self, device_name):
        response = self._invoke(
            self._client.SelfCal,
            grpc_types.SelfCalRequest(device_name=device_name))

    def self_test_device(self, device_name):
        response = self._invoke(
            self._client.SelfTestDevice,
            grpc_types.SelfTestDeviceRequest(device_name=device_name))

    def set_analog_power_up_states(
            self, device_name, channel_names, state, channel_type):
        power_up_states = []
        for index in range(len(channel_names)):
            power_up_states.append(grpc_types.AnalogPowerUpChannelsAndState(channel_names=channel_names[index], state=state[index], channel_type=channel_type[index]))
        response = self._invoke(
            self._client.SetAnalogPowerUpStates,
            grpc_types.SetAnalogPowerUpStatesRequest(
                device_name=device_name, power_up_states=power_up_states))

    def set_analog_power_up_states_with_output_type(
            self, channel_names, state_array, channel_type_array):
        response = self._invoke(
            self._client.SetAnalogPowerUpStatesWithOutputType,
            grpc_types.SetAnalogPowerUpStatesWithOutputTypeRequest(
                channel_names=channel_names, state_array=state_array,
                channel_type_array=channel_type_array))

    def set_buffer_attribute_uint32(self, task, attribute, value):
        response = self._invoke(
            self._client.SetBufferAttributeUInt32,
            grpc_types.SetBufferAttributeUInt32Request(
                task=task, attribute_raw=attribute, value=value))

    def set_cal_info_attribute_bool(self, device_name, attribute, value):
        response = self._invoke(
            self._client.SetCalInfoAttributeBool,
            grpc_types.SetCalInfoAttributeBoolRequest(
                device_name=device_name, attribute_raw=attribute, value=value))

    def set_cal_info_attribute_double(self, device_name, attribute, value):
        response = self._invoke(
            self._client.SetCalInfoAttributeDouble,
            grpc_types.SetCalInfoAttributeDoubleRequest(
                device_name=device_name, attribute_raw=attribute, value=value))

    def set_cal_info_attribute_string(self, device_name, attribute, value):
        response = self._invoke(
            self._client.SetCalInfoAttributeString,
            grpc_types.SetCalInfoAttributeStringRequest(
                device_name=device_name, attribute_raw=attribute, value=value))

    def set_cal_info_attribute_uint32(self, device_name, attribute, value):
        response = self._invoke(
            self._client.SetCalInfoAttributeUInt32,
            grpc_types.SetCalInfoAttributeUInt32Request(
                device_name=device_name, attribute_raw=attribute, value=value))

    def set_chan_attribute_bool(self, task, channel, attribute, value):
        response = self._invoke(
            self._client.SetChanAttributeBool,
            grpc_types.SetChanAttributeBoolRequest(
                task=task, channel=channel, attribute_raw=attribute,
                value=value))

    def set_chan_attribute_double(self, task, channel, attribute, value):
        response = self._invoke(
            self._client.SetChanAttributeDouble,
            grpc_types.SetChanAttributeDoubleRequest(
                task=task, channel=channel, attribute_raw=attribute,
                value=value))

    def set_chan_attribute_double_array(self, task, channel, attribute, value):
        response = self._invoke(
            self._client.SetChanAttributeDoubleArray,
            grpc_types.SetChanAttributeDoubleArrayRequest(
                task=task, channel=channel, attribute_raw=attribute,
                value=value))

    def set_chan_attribute_int32(self, task, channel, attribute, value):
        response = self._invoke(
            self._client.SetChanAttributeInt32,
            grpc_types.SetChanAttributeInt32Request(
                task=task, channel=channel, attribute_raw=attribute,
                value_raw=value))

    def set_chan_attribute_string(self, task, channel, attribute, value):
        response = self._invoke(
            self._client.SetChanAttributeString,
            grpc_types.SetChanAttributeStringRequest(
                task=task, channel=channel, attribute_raw=attribute,
                value=value))

    def set_chan_attribute_uint32(self, task, channel, attribute, value):
        response = self._invoke(
            self._client.SetChanAttributeUInt32,
            grpc_types.SetChanAttributeUInt32Request(
                task=task, channel=channel, attribute_raw=attribute,
                value=value))

    def set_digital_logic_family_power_up_state(
            self, device_name, logic_family):
        response = self._invoke(
            self._client.SetDigitalLogicFamilyPowerUpState,
            grpc_types.SetDigitalLogicFamilyPowerUpStateRequest(
                device_name=device_name, logic_family_raw=logic_family))

    def set_digital_power_up_states(self, device_name, channel_names, state):
        power_up_states = []
        for index in range(len(channel_names)):
            power_up_states.append(grpc_types.DigitalPowerUpChannelsAndState(channel_names=channel_names[index], state=state[index]))
        response = self._invoke(
            self._client.SetDigitalPowerUpStates,
            grpc_types.SetDigitalPowerUpStatesRequest(
                device_name=device_name, power_up_states=power_up_states))

    def set_digital_pull_up_pull_down_states(
            self, device_name, channel_names, state):
        pull_up_pull_down_states = []
        for index in range(len(channel_names)):
            pull_up_pull_down_states.append(grpc_types.DigitalPullUpPullDownChannelsAndState(channel_names=channel_names[index], state=state[index]))
        response = self._invoke(
            self._client.SetDigitalPullUpPullDownStates,
            grpc_types.SetDigitalPullUpPullDownStatesRequest(
                device_name=device_name,
                pull_up_pull_down_states=pull_up_pull_down_states))

    def set_exported_signal_attribute_bool(self, task, attribute, value):
        response = self._invoke(
            self._client.SetExportedSignalAttributeBool,
            grpc_types.SetExportedSignalAttributeBoolRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_exported_signal_attribute_double(self, task, attribute, value):
        response = self._invoke(
            self._client.SetExportedSignalAttributeDouble,
            grpc_types.SetExportedSignalAttributeDoubleRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_exported_signal_attribute_int32(self, task, attribute, value):
        response = self._invoke(
            self._client.SetExportedSignalAttributeInt32,
            grpc_types.SetExportedSignalAttributeInt32Request(
                task=task, attribute_raw=attribute, value_raw=value))

    def set_exported_signal_attribute_string(self, task, attribute, value):
        response = self._invoke(
            self._client.SetExportedSignalAttributeString,
            grpc_types.SetExportedSignalAttributeStringRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_exported_signal_attribute_uint32(self, task, attribute, value):
        response = self._invoke(
            self._client.SetExportedSignalAttributeUInt32,
            grpc_types.SetExportedSignalAttributeUInt32Request(
                task=task, attribute_raw=attribute, value=value))

    def set_read_attribute_bool(self, task, attribute, value):
        response = self._invoke(
            self._client.SetReadAttributeBool,
            grpc_types.SetReadAttributeBoolRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_read_attribute_double(self, task, attribute, value):
        response = self._invoke(
            self._client.SetReadAttributeDouble,
            grpc_types.SetReadAttributeDoubleRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_read_attribute_int32(self, task, attribute, value):
        response = self._invoke(
            self._client.SetReadAttributeInt32,
            grpc_types.SetReadAttributeInt32Request(
                task=task, attribute_raw=attribute, value_raw=value))

    def set_read_attribute_string(self, task, attribute, value):
        response = self._invoke(
            self._client.SetReadAttributeString,
            grpc_types.SetReadAttributeStringRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_read_attribute_uint32(self, task, attribute, value):
        response = self._invoke(
            self._client.SetReadAttributeUInt32,
            grpc_types.SetReadAttributeUInt32Request(
                task=task, attribute_raw=attribute, value=value))

    def set_read_attribute_uint64(self, task, attribute, value):
        response = self._invoke(
            self._client.SetReadAttributeUInt64,
            grpc_types.SetReadAttributeUInt64Request(
                task=task, attribute_raw=attribute, value=value))

    def set_scale_attribute_double(self, scale_name, attribute, value):
        response = self._invoke(
            self._client.SetScaleAttributeDouble,
            grpc_types.SetScaleAttributeDoubleRequest(
                scale_name=scale_name, attribute_raw=attribute, value=value))

    def set_scale_attribute_double_array(self, scale_name, attribute, value):
        response = self._invoke(
            self._client.SetScaleAttributeDoubleArray,
            grpc_types.SetScaleAttributeDoubleArrayRequest(
                scale_name=scale_name, attribute_raw=attribute, value=value))

    def set_scale_attribute_int32(self, scale_name, attribute, value):
        response = self._invoke(
            self._client.SetScaleAttributeInt32,
            grpc_types.SetScaleAttributeInt32Request(
                scale_name=scale_name, attribute_raw=attribute,
                value_raw=value))

    def set_scale_attribute_string(self, scale_name, attribute, value):
        response = self._invoke(
            self._client.SetScaleAttributeString,
            grpc_types.SetScaleAttributeStringRequest(
                scale_name=scale_name, attribute_raw=attribute, value=value))

    def set_timing_attribute_bool(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTimingAttributeBool,
            grpc_types.SetTimingAttributeBoolRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_timing_attribute_double(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTimingAttributeDouble,
            grpc_types.SetTimingAttributeDoubleRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_timing_attribute_ex_bool(
            self, task, device_names, attribute, value):
        response = self._invoke(
            self._client.SetTimingAttributeExBool,
            grpc_types.SetTimingAttributeExBoolRequest(
                task=task, device_names=device_names, attribute_raw=attribute,
                value=value))

    def set_timing_attribute_ex_double(
            self, task, device_names, attribute, value):
        response = self._invoke(
            self._client.SetTimingAttributeExDouble,
            grpc_types.SetTimingAttributeExDoubleRequest(
                task=task, device_names=device_names, attribute_raw=attribute,
                value=value))

    def set_timing_attribute_ex_int32(
            self, task, device_names, attribute, value):
        response = self._invoke(
            self._client.SetTimingAttributeExInt32,
            grpc_types.SetTimingAttributeExInt32Request(
                task=task, device_names=device_names, attribute_raw=attribute,
                value_raw=value))

    def set_timing_attribute_ex_string(
            self, task, device_names, attribute, value):
        response = self._invoke(
            self._client.SetTimingAttributeExString,
            grpc_types.SetTimingAttributeExStringRequest(
                task=task, device_names=device_names, attribute_raw=attribute,
                value=value))

    def set_timing_attribute_ex_uint32(
            self, task, device_names, attribute, value):
        response = self._invoke(
            self._client.SetTimingAttributeExUInt32,
            grpc_types.SetTimingAttributeExUInt32Request(
                task=task, device_names=device_names, attribute_raw=attribute,
                value=value))

    def set_timing_attribute_ex_uint64(
            self, task, device_names, attribute, value):
        response = self._invoke(
            self._client.SetTimingAttributeExUInt64,
            grpc_types.SetTimingAttributeExUInt64Request(
                task=task, device_names=device_names, attribute_raw=attribute,
                value=value))

    def set_timing_attribute_int32(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTimingAttributeInt32,
            grpc_types.SetTimingAttributeInt32Request(
                task=task, attribute_raw=attribute, value_raw=value))

    def set_timing_attribute_string(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTimingAttributeString,
            grpc_types.SetTimingAttributeStringRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_timing_attribute_uint32(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTimingAttributeUInt32,
            grpc_types.SetTimingAttributeUInt32Request(
                task=task, attribute_raw=attribute, value=value))

    def set_timing_attribute_uint64(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTimingAttributeUInt64,
            grpc_types.SetTimingAttributeUInt64Request(
                task=task, attribute_raw=attribute, value=value))

    def set_trig_attribute_bool(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTrigAttributeBool,
            grpc_types.SetTrigAttributeBoolRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_trig_attribute_double(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTrigAttributeDouble,
            grpc_types.SetTrigAttributeDoubleRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_trig_attribute_double_array(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTrigAttributeDoubleArray,
            grpc_types.SetTrigAttributeDoubleArrayRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_trig_attribute_int32(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTrigAttributeInt32,
            grpc_types.SetTrigAttributeInt32Request(
                task=task, attribute_raw=attribute, value_raw=value))

    def set_trig_attribute_int32_array(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTrigAttributeInt32Array,
            grpc_types.SetTrigAttributeInt32ArrayRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_trig_attribute_string(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTrigAttributeString,
            grpc_types.SetTrigAttributeStringRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_trig_attribute_timestamp(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTrigAttributeTimestamp,
            grpc_types.SetTrigAttributeTimestampRequest(
                task=task, attribute_raw=attribute,
                value=convert_time_to_timestamp(value)))

    def set_trig_attribute_uint32(self, task, attribute, value):
        response = self._invoke(
            self._client.SetTrigAttributeUInt32,
            grpc_types.SetTrigAttributeUInt32Request(
                task=task, attribute_raw=attribute, value=value))

    def set_watchdog_attribute_bool(self, task, lines, attribute, value):
        response = self._invoke(
            self._client.SetWatchdogAttributeBool,
            grpc_types.SetWatchdogAttributeBoolRequest(
                task=task, lines=lines, attribute_raw=attribute, value=value))

    def set_watchdog_attribute_double(self, task, lines, attribute, value):
        response = self._invoke(
            self._client.SetWatchdogAttributeDouble,
            grpc_types.SetWatchdogAttributeDoubleRequest(
                task=task, lines=lines, attribute_raw=attribute, value=value))

    def set_watchdog_attribute_int32(self, task, lines, attribute, value):
        response = self._invoke(
            self._client.SetWatchdogAttributeInt32,
            grpc_types.SetWatchdogAttributeInt32Request(
                task=task, lines=lines, attribute_raw=attribute,
                value_raw=value))

    def set_watchdog_attribute_string(self, task, lines, attribute, value):
        response = self._invoke(
            self._client.SetWatchdogAttributeString,
            grpc_types.SetWatchdogAttributeStringRequest(
                task=task, lines=lines, attribute_raw=attribute, value=value))

    def set_write_attribute_bool(self, task, attribute, value):
        response = self._invoke(
            self._client.SetWriteAttributeBool,
            grpc_types.SetWriteAttributeBoolRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_write_attribute_double(self, task, attribute, value):
        response = self._invoke(
            self._client.SetWriteAttributeDouble,
            grpc_types.SetWriteAttributeDoubleRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_write_attribute_int32(self, task, attribute, value):
        response = self._invoke(
            self._client.SetWriteAttributeInt32,
            grpc_types.SetWriteAttributeInt32Request(
                task=task, attribute_raw=attribute, value_raw=value))

    def set_write_attribute_string(self, task, attribute, value):
        response = self._invoke(
            self._client.SetWriteAttributeString,
            grpc_types.SetWriteAttributeStringRequest(
                task=task, attribute_raw=attribute, value=value))

    def set_write_attribute_uint32(self, task, attribute, value):
        response = self._invoke(
            self._client.SetWriteAttributeUInt32,
            grpc_types.SetWriteAttributeUInt32Request(
                task=task, attribute_raw=attribute, value=value))

    def set_write_attribute_uint64(self, task, attribute, value):
        response = self._invoke(
            self._client.SetWriteAttributeUInt64,
            grpc_types.SetWriteAttributeUInt64Request(
                task=task, attribute_raw=attribute, value=value))

    def start_new_file(self, task, file_path):
        response = self._invoke(
            self._client.StartNewFile,
            grpc_types.StartNewFileRequest(task=task, file_path=file_path))

    def start_task(self, task):
        response = self._invoke(
            self._client.StartTask,
            grpc_types.StartTaskRequest(task=task))

    def stop_task(self, task):
        response = self._invoke(
            self._client.StopTask,
            grpc_types.StopTaskRequest(task=task))

    def task_control(self, task, action):
        response = self._invoke(
            self._client.TaskControl,
            grpc_types.TaskControlRequest(task=task, action_raw=action))

    def tristate_output_term(self, output_terminal):
        response = self._invoke(
            self._client.TristateOutputTerm,
            grpc_types.TristateOutputTermRequest(output_terminal=output_terminal))

    def unregister_done_event(self, task):
        response = self._invoke(
            self._client.UnregisterDoneEvent,
            grpc_types.UnregisterDoneEventRequest(task=task))

    def unregister_every_n_samples_event(
            self, task, every_n_samples_event_type):
        response = self._invoke(
            self._client.UnregisterEveryNSamplesEvent,
            grpc_types.UnregisterEveryNSamplesEventRequest(
                task=task,
                every_n_samples_event_type_raw=every_n_samples_event_type))

    def unregister_signal_event(self, task, signal_id):
        response = self._invoke(
            self._client.UnregisterSignalEvent,
            grpc_types.UnregisterSignalEventRequest(task=task, signal_id_raw=signal_id))

    def unreserve_network_device(self, device_name):
        response = self._invoke(
            self._client.UnreserveNetworkDevice,
            grpc_types.UnreserveNetworkDeviceRequest(device_name=device_name))

    def wait_for_valid_timestamp(self, task, timestamp_event, timeout):
        response = self._invoke(
            self._client.WaitForValidTimestamp,
            grpc_types.WaitForValidTimestampRequest(
                task=task, timestamp_event_raw=timestamp_event,
                timeout=timeout))
        return convert_timestamp_to_time(response.timestamp)

    def wait_until_task_done(self, task, time_to_wait):
        response = self._invoke(
            self._client.WaitUntilTaskDone,
            grpc_types.WaitUntilTaskDoneRequest(task=task, time_to_wait=time_to_wait))

    def write_analog_f64(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        _validate_array_dtype(write_array, numpy.float64)
        response = self._invoke(
            self._client.WriteAnalogF64,
            grpc_types.WriteAnalogF64Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                auto_start=auto_start, timeout=timeout,
                data_layout_raw=data_layout, write_array=write_array.flat))
        return response.samps_per_chan_written

    def write_analog_scalar_f64(self, task, auto_start, timeout, value):
        response = self._invoke(
            self._client.WriteAnalogScalarF64,
            grpc_types.WriteAnalogScalarF64Request(
                task=task, auto_start=auto_start, timeout=timeout,
                value=value))

    def write_binary_i16(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        _validate_array_dtype(write_array, numpy.int16)
        response = self._invoke(
            self._client.WriteBinaryI16,
            grpc_types.WriteBinaryI16Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                auto_start=auto_start, timeout=timeout,
                data_layout_raw=data_layout, write_array=write_array.flat))
        return response.samps_per_chan_written

    def write_binary_i32(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        _validate_array_dtype(write_array, numpy.int32)
        response = self._invoke(
            self._client.WriteBinaryI32,
            grpc_types.WriteBinaryI32Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                auto_start=auto_start, timeout=timeout,
                data_layout_raw=data_layout, write_array=write_array.flat))
        return response.samps_per_chan_written

    def write_binary_u16(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        _validate_array_dtype(write_array, numpy.uint16)
        response = self._invoke(
            self._client.WriteBinaryU16,
            grpc_types.WriteBinaryU16Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                auto_start=auto_start, timeout=timeout,
                data_layout_raw=data_layout, write_array=write_array.flat))
        return response.samps_per_chan_written

    def write_binary_u32(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        _validate_array_dtype(write_array, numpy.uint32)
        response = self._invoke(
            self._client.WriteBinaryU32,
            grpc_types.WriteBinaryU32Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                auto_start=auto_start, timeout=timeout,
                data_layout_raw=data_layout, write_array=write_array.flat))
        return response.samps_per_chan_written

    def write_ctr_freq(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            frequency, duty_cycle):
        _validate_array_dtype(frequency, numpy.float64)
        _validate_array_dtype(duty_cycle, numpy.float64)
        response = self._invoke(
            self._client.WriteCtrFreq,
            grpc_types.WriteCtrFreqRequest(
                task=task, num_samps_per_chan=num_samps_per_chan,
                auto_start=auto_start, timeout=timeout,
                data_layout_raw=data_layout, frequency=frequency.flat,
                duty_cycle=duty_cycle.flat))
        return response.num_samps_per_chan_written

    def write_ctr_freq_scalar(
            self, task, auto_start, timeout, frequency, duty_cycle):
        response = self._invoke(
            self._client.WriteCtrFreqScalar,
            grpc_types.WriteCtrFreqScalarRequest(
                task=task, auto_start=auto_start, timeout=timeout,
                frequency=frequency, duty_cycle=duty_cycle))

    def write_ctr_ticks(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            high_ticks, low_ticks):
        _validate_array_dtype(high_ticks, numpy.uint32)
        _validate_array_dtype(low_ticks, numpy.uint32)
        response = self._invoke(
            self._client.WriteCtrTicks,
            grpc_types.WriteCtrTicksRequest(
                task=task, num_samps_per_chan=num_samps_per_chan,
                auto_start=auto_start, timeout=timeout,
                data_layout_raw=data_layout, high_ticks=high_ticks.flat,
                low_ticks=low_ticks.flat))
        return response.num_samps_per_chan_written

    def write_ctr_ticks_scalar(
            self, task, auto_start, timeout, high_ticks, low_ticks):
        response = self._invoke(
            self._client.WriteCtrTicksScalar,
            grpc_types.WriteCtrTicksScalarRequest(
                task=task, auto_start=auto_start, timeout=timeout,
                high_ticks=high_ticks, low_ticks=low_ticks))

    def write_ctr_time(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            high_time, low_time):
        _validate_array_dtype(high_time, numpy.float64)
        _validate_array_dtype(low_time, numpy.float64)
        response = self._invoke(
            self._client.WriteCtrTime,
            grpc_types.WriteCtrTimeRequest(
                task=task, num_samps_per_chan=num_samps_per_chan,
                auto_start=auto_start, timeout=timeout,
                data_layout_raw=data_layout, high_time=high_time.flat,
                low_time=low_time.flat))
        return response.num_samps_per_chan_written

    def write_ctr_time_scalar(
            self, task, auto_start, timeout, high_time, low_time):
        response = self._invoke(
            self._client.WriteCtrTimeScalar,
            grpc_types.WriteCtrTimeScalarRequest(
                task=task, auto_start=auto_start, timeout=timeout,
                high_time=high_time, low_time=low_time))

    def write_digital_lines(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        _validate_array_dtype(write_array, bool)
        response = self._invoke(
            self._client.WriteDigitalLines,
            grpc_types.WriteDigitalLinesRequest(
                task=task, num_samps_per_chan=num_samps_per_chan,
                auto_start=auto_start, timeout=timeout,
                data_layout_raw=data_layout,
                write_array=write_array.tobytes()))
        return response.samps_per_chan_written

    def write_digital_scalar_u32(self, task, auto_start, timeout, value):
        response = self._invoke(
            self._client.WriteDigitalScalarU32,
            grpc_types.WriteDigitalScalarU32Request(
                task=task, auto_start=auto_start, timeout=timeout,
                value=value))

    def write_digital_u16(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        _validate_array_dtype(write_array, numpy.uint16)
        response = self._invoke(
            self._client.WriteDigitalU16,
            grpc_types.WriteDigitalU16Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                auto_start=auto_start, timeout=timeout,
                data_layout_raw=data_layout, write_array=write_array.flat))
        return response.samps_per_chan_written

    def write_digital_u32(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        _validate_array_dtype(write_array, numpy.uint32)
        response = self._invoke(
            self._client.WriteDigitalU32,
            grpc_types.WriteDigitalU32Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                auto_start=auto_start, timeout=timeout,
                data_layout_raw=data_layout, write_array=write_array.flat))
        return response.samps_per_chan_written

    def write_digital_u8(
            self, task, num_samps_per_chan, auto_start, timeout, data_layout,
            write_array):
        _validate_array_dtype(write_array, numpy.uint8)
        response = self._invoke(
            self._client.WriteDigitalU8,
            grpc_types.WriteDigitalU8Request(
                task=task, num_samps_per_chan=num_samps_per_chan,
                auto_start=auto_start, timeout=timeout,
                data_layout_raw=data_layout,
                write_array=write_array.tobytes()))
        return response.samps_per_chan_written

    def write_id_pin_memory(self, device_name, id_pin_name, data, format_code):
        response = self._invoke(
            self._client.WriteIDPinMemory,
            grpc_types.WriteIDPinMemoryRequest(
                device_name=device_name, id_pin_name=id_pin_name,
                data=data.tobytes(), format_code=format_code))

    def write_raw(self, task, num_samps, auto_start, timeout, write_array):
        _validate_array_dtype(write_array, numpy.generic)
        response = self._invoke(
            self._client.WriteRaw,
            grpc_types.WriteRawRequest(
                task=task, num_samps=num_samps, auto_start=auto_start,
                timeout=timeout, write_array=write_array.tobytes()))
        return response.samps_per_chan_written

    def write_to_teds_from_array(
            self, physical_channel, bit_stream, basic_teds_options):
        response = self._invoke(
            self._client.WriteToTEDSFromArray,
            grpc_types.WriteToTEDSFromArrayRequest(
                physical_channel=physical_channel,
                bit_stream=bit_stream.tobytes(),
                basic_teds_options_raw=basic_teds_options))

    def write_to_teds_from_file(
            self, physical_channel, file_path, basic_teds_options):
        response = self._invoke(
            self._client.WriteToTEDSFromFile,
            grpc_types.WriteToTEDSFromFileRequest(
                physical_channel=physical_channel, file_path=file_path,
                basic_teds_options_raw=basic_teds_options))

    def hash_task_handle(self, task_handle):
        return hash(task_handle.name)

    def get_error_string(self, error_code):
        try:
            # Do not use self._invoke() because it may call back into self.get_error_string().
            response = self._client.GetErrorString(
                grpc_types.GetErrorStringRequest(error_code=error_code))
            if not response.error_string:
                return _UNABLE_TO_LOCATE_ERROR_RESOURCES_ERROR_MESSAGE
            return response.error_string
        except grpc.RpcError:
            _logger.exception('Failed to get error string for error code %d.', error_code)
            return 'Failed to retrieve error description.'

    def read_id_pin_memory(self, device_name, id_pin_name):
        response = self._invoke(
            self._client.ReadIDPinMemory,
            grpc_types.ReadIDPinMemoryRequest(device_name=device_name, id_pin_name=id_pin_name, array_size=0))
        if response.status <= 0:
            self._check_for_error_from_response(response.status)
        response = self._invoke(
            self._client.ReadIDPinMemory,
            grpc_types.ReadIDPinMemoryRequest(device_name=device_name, id_pin_name=id_pin_name, array_size=response.status))
        self._check_for_error_from_response(response.status)
        return list(response.data), response.data_length_read, response.format_code

    def set_runtime_environment(
            self, environment, environment_version, reserved_1, reserved_2):
        raise NotImplementedError

    def internal_get_last_created_chan(self):
        raise NotImplementedError


def _assign_numpy_array(numpy_array, grpc_array):
    """
    Assigns grpc array to numpy array maintaining the original shape.

    Checks for the instance of grpc_array with bytes, if validated to True,
    the numpy array is assigned to a 1D array of the grpc arrray.
    """
    grpc_array_size = len(grpc_array)
    if isinstance(grpc_array, bytes):
        assert numpy_array.nbytes >= grpc_array_size
        numpy_array.flat[:grpc_array_size] = numpy.frombuffer(grpc_array, dtype=numpy_array.dtype)
    else:
        assert numpy_array.size >= grpc_array_size
        numpy_array.flat[:grpc_array_size] = grpc_array

def _validate_array_dtype(numpy_array, expected_numpy_array_dtype):
    """Raises TypeError if array type doesn't match with expected numpy.dtype"""
    if expected_numpy_array_dtype != numpy.generic and numpy_array.dtype != expected_numpy_array_dtype:
        raise TypeError(f"array must have data type {expected_numpy_array_dtype}")

def _is_cancelled(ex: Exception) -> bool:
    """Returns True if the given exception is a cancelled RPC exception."""
    return (
        (isinstance(ex, grpc.RpcError) and ex.code() == grpc.StatusCode.CANCELLED)
        or (isinstance(ex, errors.RpcError) and ex.rpc_code == grpc.StatusCode.CANCELLED)
    )

_ERROR_MESSAGES = {
    DAQmxErrors.SAMPLES_NOT_YET_AVAILABLE: 'Some or all of the samples requested have not yet been acquired.\n\nTo wait for the samples to become available use a longer read timeout or read later in your program. To make the samples available sooner, increase the sample rate. If your task uses a start trigger, make sure that your start trigger is configured correctly. It is also possible that you configured the task for external timing, and no clock was supplied. If this is the case, supply an external clock.'
}
