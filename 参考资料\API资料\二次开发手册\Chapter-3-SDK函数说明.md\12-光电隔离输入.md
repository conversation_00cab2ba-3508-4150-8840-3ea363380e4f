3.12 光电隔离输入 
 
3.12.1 
MT_Get_Optic_In_Num 
功能描述：读取板卡上光电隔离输入通道的数量 
 
VC 
INT32 
MT_Get_Optic_In_Num 
(INT32* pValue) 
VB 
Function 
MT_Get_Optic_In_Num 
(ByRef Value As Long) As Long 
Delphi 
function 
MT_Get_Optic_In_Num 
(pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Optic_In_Num 
(ref Int32 Value); 
输入参数 
无 
 
输出参数 
Value 
光电隔离输入通道的数量 
函数返回 
0 
函数执行成功，读取到通道数有效 
非0 
函数执行失败，读取到通道数无效 
备注 
通道的序号从0开始，N的通道，序号为0,1,2,3…N-1 
 
3.12.2 
MT_Get_Optic_In_Single 
功能描述：读取指定光电隔离输入通道上的电平状态 
 
VC 
INT32 
MT_Get_Optic_In_Single 
(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Optic_In_Single 
(ByVal AObj As Integer, ByRef Value As 
Long) As 
Long 
Delphi 
function 
MT_Get_Optic_In_Single 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Optic_In_Single 
(UInt16 AObj, ref 
Int32 
Value); 
输入参数 
AObj 
光电隔离输入通道号 
输出参数 
Value 
指定通道上的电平状态，1为高电平，0为低电平 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
通道的序号从0开始，N的通道，序号为0,1,2,3…N-1 
 
3.12.3 MT_Get_Optic_In_All 
功能描述：读取光电隔离输入所有通道上的电平状态 
 
VC 
INT32 
MT_Get_Optic_In_All 
(INT32* pValue); 
VB 
Function 
MT_Get_Optic_In_All 
(ByRef Value As 
Long) As Long 
Delphi 
function 
MT_Get_Optic_In_All 
(pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Optic_In_All 
(ref 
Int32 
Value); 
输入参数 
无 
 
输出参数 
Value 
所有通道上的电平状态，1为高电平，0为低电平 
通道0为LSB(最低位) 
通道N对应第N-1位 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
通道的序号从0开始，N的通道，序号为0,1,2,3…N-1 
 
