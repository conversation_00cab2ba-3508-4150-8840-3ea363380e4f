name: Report test results

on:
  workflow_call:
  workflow_dispatch:

jobs:
  report_test_results:
    name: Report test results
    runs-on: ubuntu-latest
    permissions:
      contents: read
      checks: write
      pull-requests: write
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Download test results
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          path: test_results
          pattern: test_results_*
          merge-multiple: true
      - name: List downloaded files
        run: ls -lR
      - name: Publish test results
        uses: EnricoMi/publish-unit-test-result-action@3a74b2957438d0b6e2e61d67b05318aa25c9e6c6 # v2.20.0
        with:
          files: "test_results/*.xml"
