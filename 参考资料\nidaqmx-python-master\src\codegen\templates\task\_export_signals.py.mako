<%
    from codegen.utilities.attribute_helpers import get_attributes
    from codegen.utilities.attribute_helpers import get_enums_used as get_enums_used_in_attributes
    from codegen.utilities.function_helpers import get_functions
    from codegen.utilities.function_helpers import get_enums_used as get_enums_used_in_functions
    from codegen.utilities.helpers import get_enums_to_import
    from codegen.utilities.text_wrappers import wrap
    attributes = get_attributes(data,"ExportSignals")
    functions = get_functions(data,"ExportSignals")
    enums_in_attributes = get_enums_used_in_attributes(attributes)
    enums_in_functions = get_enums_used_in_functions(functions)
    enums_used = get_enums_to_import(enums_in_attributes, enums_in_functions)
%>\
# Do not edit this file; it was automatically generated.

from nidaqmx.constants import (
    ${', '.join([c for c in enums_used]) | wrap(4, 4)})


class ExportSignals:
    """
    Represents the exported signal configurations for a DAQmx task.
    """
    __slots__ = ('_handle', '_interpreter')

    def __init__(self, task_handle, interpreter):
        self._handle = task_handle
        self._interpreter = interpreter

<%namespace name="property_template" file="/property_template.py.mako"/>\
%for attribute in attributes:
${property_template.script_property(attribute)}\
%endfor
\
<%namespace name="function_template" file="/function_template.py.mako"/>\
%for function_object in functions:
${function_template.script_function(function_object)}
%endfor
