"""
sweeper400 - 协同控制NI数据采集卡和步进电机的自动化测量包

主要功能:
- measure: NI数据采集相关功能
- move: 步进电机控制相关功能
- analyze: 信号和数据处理相关功能
- sweeper: 协同调用其他子包的专用对象
- logger: 统一的日志管理系统

使用示例:
    from sweeper400.logger import get_logger
    logger = get_logger(__name__)
    logger.info("开始使用 sweeper400")
"""

# 导出日志管理系统的主要接口
from .logger import (
    get_logger,
    LogLevel,
    set_global_log_level,
    set_module_log_level,
    set_debug_mode,
    set_normal_mode,
    set_quiet_mode,
    get_log_levels,
    reset_log_levels
)

__version__ = "0.1.0"
__author__ = "sweeper400 team"

# 包级别的日志器
_package_logger = get_logger(__name__)
_package_logger.info("sweeper400 包已加载")