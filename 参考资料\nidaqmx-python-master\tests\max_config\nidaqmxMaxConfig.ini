﻿[DAQmx]
MajorVersion = 21
MinorVersion = 8

[DAQmxChannel AOTesterTask/VoltageOut_0]
AO.OutputType = Voltage
AO.Voltage.Units = Volts
AO.Max = 16
AO.Min = -16
ChanType = Analog Output
AO.TermCfg = Differential
PhysicalChanName = aoTester/ao0

[DAQmxChannel AOTesterTask/VoltageOut_1]
AO.OutputType = Voltage
AO.Voltage.Units = Volts
AO.Max = 16
AO.Min = -16
ChanType = Analog Output
AO.TermCfg = Differential
PhysicalChanName = aoTester/ao1

[DAQmxChannel AOTesterTask/VoltageOut_2]
AO.OutputType = Voltage
AO.Voltage.Units = Volts
AO.Max = 16
AO.Min = -16
ChanType = Analog Output
AO.TermCfg = Differential
PhysicalChanName = aoTester/ao2

[DAQmxTask AOTesterTask]
Channels = AOTesterTask/VoltageOut_0, AOTesterTask/VoltageOut_1, AOTesterTask/VoltageOut_2
SampQuant.SampMode = Finite Samples
SampClk.ActiveEdge = Rising
SampQuant.SampPerChan = 100
SampClk.Rate = 1000
SampTimingType = Sample Clock
RegenMode = Allow Regeneration
SampClk.Src =

[DAQmxChannel VoltageTesterChannel]
AI.MeasType = Voltage
AI.Voltage.Units = Volts
AI.TermCfg = Differential
AI.Max = 10
AI.Min = -10
ChanType = Analog Input
PhysicalChanName = tsVoltageTester1/ai0
Descr =
Author = "Test Author"
AllowInteractiveEditing = True

[DAQmxChannel VoltageTesterChannel2]
AI.MeasType = Voltage
AI.Voltage.Units = Volts
AI.TermCfg = Differential
AI.Max = 5
AI.Min = -5
ChanType = Analog Input
PhysicalChanName = tsVoltageTester1/ai1
Descr = "Another channel"
Author = "Another Test Author"
AllowInteractiveEditing = True

[DAQmxChannel VoltageTesterTask/Voltage_0]
AI.MeasType = Voltage
AI.Voltage.Units = Volts
AI.TermCfg = Differential
AI.Max = 10
AI.Min = -10
ChanType = Analog Input
PhysicalChanName = tsVoltageTester1/ai0

[DAQmxTask VoltageTesterTask]
Channels = VoltageTesterTask/Voltage_0
SampQuant.SampMode = Finite Samples
SampClk.ActiveEdge = Rising
SampQuant.SampPerChan = 100
SampClk.Rate = 1000
SampTimingType = Sample Clock
SampClk.Src =
Author = Test Author
AllowInteractiveEditing = True

[DAQmxScale double_gain_scale]
Lin.Slope = 2
Lin.YIntercept = 0
PreScaledUnits = Volts
ScaledUnits =
ScaleType = Linear
Author = Test Author
AllowInteractiveEditing = True
Descr = Twice the gain

[DAQmxScale no_scaling_scale]
Lin.Slope = 1
Lin.YIntercept = 0
PreScaledUnits = Volts
ScaledUnits =
ScaleType = Linear

[DAQmxScale polynomial_scale]
Poly.ForwardCoeff = 0, 1
Poly.ReverseCoeff = 0, 1
PreScaledUnits = Volts
ScaledUnits =
ScaleType = Polynomial

[DAQmxScale degrees_scale]
Lin.Slope = 2
Lin.YIntercept = 0
PreScaledUnits = Degrees
ScaledUnits = potatoes
ScaleType = Linear

[DAQmxDevice aoTester]
ProductType = PXIe-4322
DevSerialNum = 0x0
DevIsSimulated = 1
ProductNum = 0x76ABC4C4
BusType = PXIe
PXI.ChassisNum = 1
PXI.SlotNum = 3

[DAQmxDevice bridgeTester]
ProductType = PXIe-4331
DevSerialNum = 0x0
DevIsSimulated = 1
ProductNum = 0x74A9C4C4
BusType = PXIe
PXI.ChassisNum = 1
PXI.SlotNum = 2

[DAQmxAccessory RM-24999_A/bridgeTester/0]

[DAQmxDevice chargeTester]
ProductType = PXIe-4480
DevSerialNum = 0x0
DevIsSimulated = 1
ProductNum = 0x7844C4C4
BusType = PXIe
PXI.ChassisNum = 1
PXI.SlotNum = 5

[DAQmxDevice dmmTester]
ProductType = NI myDAQ
DevSerialNum = 0x0
DevIsSimulated = 1
ProductNum = 0x755B
BusType = USB

[DAQmxDevice dsaTester]
ProductType = PXIe-4466
DevSerialNum = 0x0
DevIsSimulated = 1
ProductNum = 0x7A8DC4C4
BusType = PXIe
PXI.ChassisNum = 1
PXI.SlotNum = 6

[DAQmxDevice nidaqmxMultithreadingTester1]
ProductType = PCIe-6363
DevSerialNum = 0x0
DevIsSimulated = 1
ProductNum = 0x7435C4C4
BusType = PCIe
PCI.BusNum = 0x0
PCI.DevNum = 0x0

[DAQmxDevice nidaqmxMultithreadingTester2]
ProductType = PCIe-6363
DevSerialNum = 0x0
DevIsSimulated = 1
ProductNum = 0x7435C4C4
BusType = PCIe
PCI.BusNum = 0x0
PCI.DevNum = 0x0

[DAQmxDevice nidaqmxMultithreadingTester3]
ProductType = PCIe-6363
DevSerialNum = 0x0
DevIsSimulated = 1
ProductNum = 0x7435C4C4
BusType = PCIe
PCI.BusNum = 0x0
PCI.DevNum = 0x0

[DAQmxDevice nidaqmxMultithreadingTester4]
ProductType = PCIe-6363
DevSerialNum = 0x0
DevIsSimulated = 1
ProductNum = 0x7435C4C4
BusType = PCIe
PCI.BusNum = 0x0
PCI.DevNum = 0x0

[DAQmxDevice hsdioTester]
ProductType = PCIe-6535
DevSerialNum = 0x0
DevIsSimulated = 1
ProductNum = 0x719F
BusType = PCIe
PCI.BusNum = 0x0
PCI.DevNum = 0x0

[DAQmxDevice positionTester]
ProductType = PXIe-4340
DevSerialNum = 0x0
DevIsSimulated = 1
ProductNum = 0x7829C4C4
BusType = PXIe
PXI.ChassisNum = 4294967295
PXI.SlotNum = 4294967295

[DAQmxDevice tempTester]
ProductType = PXIe-4353
DevSerialNum = 0x0
DevIsSimulated = 1
ProductNum = 0x74B2C4C4
BusType = PXIe
PXI.ChassisNum = 1
PXI.SlotNum = 7

[DAQmxCDAQChassis tsChassisTester]
ProductType = TS-15000
DevSerialNum = 0x0
DevIsSimulated = 1

[DAQmxCDAQModule tsPowerTester1]
ProductType = TS-15200
DevSerialNum = 0x0
DevIsSimulated = 1
CompactDAQ.ChassisDevName = tsChassisTester
CompactDAQ.SlotNum = 1

[DAQmxCDAQModule tsPowerTester2]
ProductType = TS-15200
DevSerialNum = 0x0
DevIsSimulated = 1
CompactDAQ.ChassisDevName = tsChassisTester
CompactDAQ.SlotNum = 2

[DAQmxCDAQModule tsVoltageTester1]
ProductType = TS-15100
DevSerialNum = 0x0
DevIsSimulated = 1
CompactDAQ.ChassisDevName = tsChassisTester
CompactDAQ.SlotNum = 3

[DAQmxCDAQChassis cdaqChassisTester]
ProductType = cDAQ-9189
DevSerialNum = 0x0
DevIsSimulated = 1
BusType = TCP/IP
TCPIP.Hostname =
TCPIP.EthernetIP = 0.0.0.0
TCPIP.EthernetMAC = 00:00:00:00:00:00
TCPIP.EthernetMDNSServiceInstance =
TCPIP.DevIsReserved = 0

[DAQmxCDAQModule cdaqTesterMod1]
ProductType = NI 9361
DevSerialNum = 0x0
DevIsSimulated = 1
CompactDAQ.ChassisDevName = cdaqChassisTester
CompactDAQ.SlotNum = 1

[DAQmxCDAQModule cdaqTesterMod2]
ProductType = NI 9215
DevSerialNum = 0x0
DevIsSimulated = 1
CompactDAQ.ChassisDevName = cdaqChassisTester
CompactDAQ.SlotNum = 2

[DAQmxCDAQModule cdaqTesterMod3]
ProductType = NI 9775
DevSerialNum = 0x0
DevIsSimulated = 1
CompactDAQ.ChassisDevName = cdaqChassisTester
CompactDAQ.SlotNum = 3

[DAQmxCDAQModule cdaqTesterMod4]
ProductType = NI 9401
DevSerialNum = 0x0
DevIsSimulated = 1
CompactDAQ.ChassisDevName = cdaqChassisTester
CompactDAQ.SlotNum = 4

[DAQmxCDAQModule cdaqTesterMod5]
ProductType = NI 9263
DevSerialNum = 0x0
DevIsSimulated = 1
CompactDAQ.ChassisDevName = cdaqChassisTester
CompactDAQ.SlotNum = 5

[DAQmxCDAQModule cdaqTesterMod6]
ProductType = NI 9205
DevSerialNum = 0x0
DevIsSimulated = 1
CompactDAQ.ChassisDevName = cdaqChassisTester
CompactDAQ.SlotNum = 6

[DAQmxDevice mioDAQ]
ProductType = USB-6423
DevSerialNum = 0x0
DevIsSimulated = 1
ProductNum = 0x7B40
BusType = USB
