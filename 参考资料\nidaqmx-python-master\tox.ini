# Tox (http://tox.testrun.org/) is a tool for running tests
# in multiple virtualenvs. This configuration file will run the
# test suite on all supported python versions. To use it, "pip install tox"
# and then run "tox" from this directory.

[tox]
isolated_build = true
envlist = clean, py{39,310,311,312,313}-base, py{39,310,311,312,313}-grpc, py39-base-nicaiu, py39-base-nicai_utf8, report, docs

[testenv]
skip_install = true
allowlist_externals = poetry
setenv =
   base: INSTALL_OPTS=--only main,test
   grpc: INSTALL_OPTS=--only main,test --extras grpc
   base: PYTEST_OPTS=-k "not grpc"
   grpc: PYTEST_OPTS=
   nicaiu: NIDAQMX_C_LIBRARY=nicaiu
   nicai_utf8: NIDAQMX_C_LIBRARY=nicai_utf8
platform = 
   nicaiu: win32
   nicai_utf8: win32
commands =
   poetry run python --version
   poetry install -v {env:INSTALL_OPTS}
   poetry run python -c "from nidaqmx._lib import lib_importer; print(f'Library: {lib_importer.windll._library._name}\nLibrary encoding: {lib_importer.encoding}')"
   poetry run pytest --quiet --cov=generated/nidaqmx --cov-append --cov-report= --junitxml=test_results/system-{envname}.xml {env:PYTEST_OPTS} {posargs}

[testenv:clean]
commands = poetry run coverage erase

[testenv:report]
commands =
   poetry run coverage html
   poetry run coverage report

[testenv:docs]
# base_python should match the version specified in .readthedocs.yml and the PR workflow.
base_python = python3.11
commands =
   poetry run python --version
   poetry install -v --only main,docs
   # Use -W to treat warnings as errors.
   poetry run sphinx-build -b html -W docs docs/_build
