# sweeper400 日志管理系统使用指南

## 系统概述

为 sweeper400 包设计的统一日志管理系统，专门针对中等规模项目的需求，提供清晰、可控、易用的日志功能。

## 核心特性

### ✅ 1. 仅Terminal输出
- 所有日志仅在终端显示，无本地文件保存
- 方便开发时直接查看和调试
- 节省磁盘空间，无需复杂的文件管理

### ✅ 2. 清晰的来源标识
- 明确显示日志来自哪个模块/类/方法
- 支持层级化的命名：`模块.类.方法`
- 自动简化长模块名显示（超过30字符时显示为 `...最后两级`）

### ✅ 3. 可调整的可见性
- **全局级别控制**：一键切换整个系统的日志级别
- **模块特定控制**：为不同模块设置不同的日志级别
- **便捷模式切换**：调试模式、正常模式、安静模式

### ✅ 4. 彩色输出和格式化
- 不同级别使用不同颜色（DEBUG=青色, INFO=绿色, WARNING=黄色, ERROR=红色, CRITICAL=紫色）
- 统一的时间戳格式（精确到毫秒）
- 整齐的列对齐显示

### ✅ 5. 类型安全和现代Python特性
- 完整的类型注解支持
- 枚举类型的日志级别定义
- 符合Python logging最佳实践

## 快速开始

### 基本使用
```python
from sweeper400.logger import get_logger

# 获取模块级别的日志器
logger = get_logger(__name__)

logger.debug("详细的调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误信息")
```

### 在类中使用
```python
from sweeper400.logger import get_logger

class DataCollector:
    def __init__(self):
        # 类级别的日志器
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info("DataCollector 初始化完成")
    
    def collect_data(self):
        # 方法级别的日志器（可选，用于详细跟踪）
        method_logger = get_logger(f"{__name__}.{self.__class__.__name__}.collect_data")
        method_logger.debug("开始数据采集准备工作")
        method_logger.info("正在采集数据...")
        # ... 实际的数据采集代码 ...
        method_logger.info("数据采集完成")
```

## 日志级别控制

### 全局级别控制
```python
from sweeper400.logger import set_debug_mode, set_normal_mode, set_quiet_mode

# 调试模式 - 显示所有日志（包括DEBUG）
set_debug_mode()

# 正常模式 - 显示INFO及以上级别（默认）
set_normal_mode()

# 安静模式 - 只显示WARNING及以上级别
set_quiet_mode()
```

### 模块特定级别控制
```python
from sweeper400.logger import set_module_log_level, LogLevel

# 设置特定模块为DEBUG级别
set_module_log_level("sweeper400.measure", LogLevel.DEBUG)

# 设置特定模块为WARNING级别
set_module_log_level("sweeper400.move", LogLevel.WARNING)
```

### 查看和重置配置
```python
from sweeper400.logger import get_log_levels, reset_log_levels

# 查看当前配置
levels = get_log_levels()
print(f"全局级别: {levels['global_level']}")
print(f"模块特定级别: {levels['module_levels']}")

# 重置所有配置
reset_log_levels()
```

## 日志级别说明

| 级别 | 数值 | 颜色 | 用途 |
|------|------|------|------|
| DEBUG | 10 | 青色 | 详细的调试信息、变量值、执行流程 |
| INFO | 20 | 绿色 | 一般信息、状态变化、操作完成 |
| WARNING | 30 | 黄色 | 警告信息、潜在问题、性能提醒 |
| ERROR | 40 | 红色 | 错误信息，程序可继续运行 |
| CRITICAL | 50 | 紫色 | 严重错误，程序可能无法继续 |

## 输出格式

日志输出格式为：
```
[时间戳] 级别     模块名                    | 消息内容
[14:07:07.881] INFO     sweeper400.measure.DataCollector | 开始数据采集
[14:07:07.885] WARNING  sweeper400.move.StepperMotor     | 检测到轻微阻力
[14:07:07.890] DEBUG    ...MeasureClass.collect_data     | 采集第 1 组数据
```

格式说明：
- **时间戳**: 精确到毫秒 (HH:MM:SS.mmm)
- **级别**: 8字符宽度，颜色编码
- **模块名**: 30字符宽度，超长时简化显示
- **消息**: 实际日志内容

## API 参考

### 核心函数
- `get_logger(name: str) -> logging.Logger`：获取日志器
- `set_global_log_level(level: LogLevel)`：设置全局级别
- `set_module_log_level(module_name: str, level: LogLevel)`：设置模块级别

### 便捷函数
- `set_debug_mode()`：开启调试模式
- `set_normal_mode()`：正常模式（默认）
- `set_quiet_mode()`：安静模式
- `get_log_levels() -> Dict[str, Any]`：查看当前配置
- `reset_log_levels()`：重置所有配置

### 枚举类型
- `LogLevel.DEBUG`、`LogLevel.INFO`、`LogLevel.WARNING`、`LogLevel.ERROR`、`LogLevel.CRITICAL`

## 推荐的使用模式

### 1. 模块级别日志器
在每个模块的开头创建一个日志器：
```python
logger = get_logger(__name__)
```

### 2. 类级别日志器
在类的 `__init__` 方法中创建：
```python
self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
```

### 3. 方法级别日志器（可选）
对于复杂的方法，可以创建方法级别的日志器：
```python
method_logger = get_logger(f"{__name__}.{self.__class__.__name__}.method_name")
```

### 4. 开发时的级别调整
- 开发新功能时：使用 `set_debug_mode()` 查看详细信息
- 正常运行时：使用 `set_normal_mode()` 
- 生产环境或性能测试时：使用 `set_quiet_mode()`

## 最佳实践

1. **合理使用日志级别**：
   - DEBUG: 详细的执行流程、变量值等
   - INFO: 重要的状态变化、操作完成等
   - WARNING: 可能的问题、性能警告等
   - ERROR: 错误但程序可以继续运行
   - CRITICAL: 严重错误，程序可能无法继续

2. **避免过度日志**：
   - 不要在循环中使用DEBUG级别记录每次迭代
   - 使用模块级别控制来管理详细程度

3. **有意义的消息**：
   - 包含足够的上下文信息
   - 使用清晰、简洁的描述

4. **异常处理**：
   ```python
   try:
       # 一些操作
       pass
   except Exception as e:
       logger.error(f"操作失败: {e}", exc_info=True)
   ```

## 完整示例

```python
from sweeper400.logger import get_logger, set_debug_mode

class StepperMotor:
    def __init__(self, motor_id: str):
        self.motor_id = motor_id
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info(f"步进电机 {motor_id} 初始化完成")
    
    def move_to_position(self, x: float, y: float, z: float):
        method_logger = get_logger(f"{__name__}.{self.__class__.__name__}.move_to_position")
        method_logger.info(f"开始移动到位置 ({x}, {y}, {z})")
        
        try:
            method_logger.debug("检查电机状态")
            # ... 实际的移动代码 ...
            method_logger.info("移动完成")
        except Exception as e:
            method_logger.error(f"移动失败: {e}")
            raise

# 使用示例
if __name__ == "__main__":
    set_debug_mode()  # 开启详细日志
    motor = StepperMotor("motor_1")
    motor.move_to_position(10.0, 20.0, 5.0)
```

这样的日志系统将帮助你在开发过程中更好地跟踪程序的执行状态和调试问题。
