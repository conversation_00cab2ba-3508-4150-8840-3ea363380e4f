
//---------------------------------------------------------------------
// This file is generated from NI-DAQMX API metadata version 23.0.0
//---------------------------------------------------------------------
// Proto file for the NI-DAQMX Metadata
//---------------------------------------------------------------------
syntax = "proto3";

option cc_enable_arenas = true;
option java_multiple_files = true;
option java_package = "com.ni.grpc.nidaqmx";
option java_outer_classname = "NiDAQmx";
option csharp_namespace = "NationalInstruments.Grpc.NiDAQmx";

package nidaqmx_grpc;

import "session.proto";
import "data_moniker.proto";
import "google/protobuf/timestamp.proto";

service NiDAQmx {
  rpc AddCDAQSyncConnection(AddCDAQSyncConnectionRequest) returns (AddCDAQSyncConnectionResponse);
  rpc AddGlobalChansToTask(AddGlobalChansToTaskRequest) returns (AddGlobalChansToTaskResponse);
  rpc AddNetworkDevice(AddNetworkDeviceRequest) returns (AddNetworkDeviceResponse);
  rpc AreConfiguredCDAQSyncPortsDisconnected(AreConfiguredCDAQSyncPortsDisconnectedRequest) returns (AreConfiguredCDAQSyncPortsDisconnectedResponse);
  rpc AutoConfigureCDAQSyncConnections(AutoConfigureCDAQSyncConnectionsRequest) returns (AutoConfigureCDAQSyncConnectionsResponse);
  rpc CalculateReversePolyCoeff(CalculateReversePolyCoeffRequest) returns (CalculateReversePolyCoeffResponse);
  rpc CfgAnlgEdgeRefTrig(CfgAnlgEdgeRefTrigRequest) returns (CfgAnlgEdgeRefTrigResponse);
  rpc CfgAnlgEdgeStartTrig(CfgAnlgEdgeStartTrigRequest) returns (CfgAnlgEdgeStartTrigResponse);
  rpc CfgAnlgMultiEdgeRefTrig(CfgAnlgMultiEdgeRefTrigRequest) returns (CfgAnlgMultiEdgeRefTrigResponse);
  rpc CfgAnlgMultiEdgeStartTrig(CfgAnlgMultiEdgeStartTrigRequest) returns (CfgAnlgMultiEdgeStartTrigResponse);
  rpc CfgAnlgWindowRefTrig(CfgAnlgWindowRefTrigRequest) returns (CfgAnlgWindowRefTrigResponse);
  rpc CfgAnlgWindowStartTrig(CfgAnlgWindowStartTrigRequest) returns (CfgAnlgWindowStartTrigResponse);
  rpc CfgBurstHandshakingTimingExportClock(CfgBurstHandshakingTimingExportClockRequest) returns (CfgBurstHandshakingTimingExportClockResponse);
  rpc CfgBurstHandshakingTimingImportClock(CfgBurstHandshakingTimingImportClockRequest) returns (CfgBurstHandshakingTimingImportClockResponse);
  rpc CfgChangeDetectionTiming(CfgChangeDetectionTimingRequest) returns (CfgChangeDetectionTimingResponse);
  rpc CfgDigEdgeRefTrig(CfgDigEdgeRefTrigRequest) returns (CfgDigEdgeRefTrigResponse);
  rpc CfgDigEdgeStartTrig(CfgDigEdgeStartTrigRequest) returns (CfgDigEdgeStartTrigResponse);
  rpc CfgDigPatternRefTrig(CfgDigPatternRefTrigRequest) returns (CfgDigPatternRefTrigResponse);
  rpc CfgDigPatternStartTrig(CfgDigPatternStartTrigRequest) returns (CfgDigPatternStartTrigResponse);
  rpc CfgHandshakingTiming(CfgHandshakingTimingRequest) returns (CfgHandshakingTimingResponse);
  rpc CfgImplicitTiming(CfgImplicitTimingRequest) returns (CfgImplicitTimingResponse);
  rpc CfgInputBuffer(CfgInputBufferRequest) returns (CfgInputBufferResponse);
  rpc CfgOutputBuffer(CfgOutputBufferRequest) returns (CfgOutputBufferResponse);
  rpc CfgPipelinedSampClkTiming(CfgPipelinedSampClkTimingRequest) returns (CfgPipelinedSampClkTimingResponse);
  rpc CfgSampClkTiming(CfgSampClkTimingRequest) returns (CfgSampClkTimingResponse);
  rpc CfgTimeStartTrig(CfgTimeStartTrigRequest) returns (CfgTimeStartTrigResponse);
  rpc CfgWatchdogAOExpirStates(CfgWatchdogAOExpirStatesRequest) returns (CfgWatchdogAOExpirStatesResponse);
  rpc CfgWatchdogCOExpirStates(CfgWatchdogCOExpirStatesRequest) returns (CfgWatchdogCOExpirStatesResponse);
  rpc CfgWatchdogDOExpirStates(CfgWatchdogDOExpirStatesRequest) returns (CfgWatchdogDOExpirStatesResponse);
  rpc ClearTEDS(ClearTEDSRequest) returns (ClearTEDSResponse);
  rpc ClearTask(ClearTaskRequest) returns (ClearTaskResponse);
  rpc ConfigureLogging(ConfigureLoggingRequest) returns (ConfigureLoggingResponse);
  rpc ConfigureTEDS(ConfigureTEDSRequest) returns (ConfigureTEDSResponse);
  rpc ConnectTerms(ConnectTermsRequest) returns (ConnectTermsResponse);
  rpc ControlWatchdogTask(ControlWatchdogTaskRequest) returns (ControlWatchdogTaskResponse);
  rpc CreateAIAccel4WireDCVoltageChan(CreateAIAccel4WireDCVoltageChanRequest) returns (CreateAIAccel4WireDCVoltageChanResponse);
  rpc CreateAIAccelChan(CreateAIAccelChanRequest) returns (CreateAIAccelChanResponse);
  rpc CreateAIAccelChargeChan(CreateAIAccelChargeChanRequest) returns (CreateAIAccelChargeChanResponse);
  rpc CreateAIBridgeChan(CreateAIBridgeChanRequest) returns (CreateAIBridgeChanResponse);
  rpc CreateAIChargeChan(CreateAIChargeChanRequest) returns (CreateAIChargeChanResponse);
  rpc CreateAICurrentChan(CreateAICurrentChanRequest) returns (CreateAICurrentChanResponse);
  rpc CreateAICurrentRMSChan(CreateAICurrentRMSChanRequest) returns (CreateAICurrentRMSChanResponse);
  rpc CreateAIForceBridgePolynomialChan(CreateAIForceBridgePolynomialChanRequest) returns (CreateAIForceBridgePolynomialChanResponse);
  rpc CreateAIForceBridgeTableChan(CreateAIForceBridgeTableChanRequest) returns (CreateAIForceBridgeTableChanResponse);
  rpc CreateAIForceBridgeTwoPointLinChan(CreateAIForceBridgeTwoPointLinChanRequest) returns (CreateAIForceBridgeTwoPointLinChanResponse);
  rpc CreateAIForceIEPEChan(CreateAIForceIEPEChanRequest) returns (CreateAIForceIEPEChanResponse);
  rpc CreateAIFreqVoltageChan(CreateAIFreqVoltageChanRequest) returns (CreateAIFreqVoltageChanResponse);
  rpc CreateAIMicrophoneChan(CreateAIMicrophoneChanRequest) returns (CreateAIMicrophoneChanResponse);
  rpc CreateAIPosEddyCurrProxProbeChan(CreateAIPosEddyCurrProxProbeChanRequest) returns (CreateAIPosEddyCurrProxProbeChanResponse);
  rpc CreateAIPosLVDTChan(CreateAIPosLVDTChanRequest) returns (CreateAIPosLVDTChanResponse);
  rpc CreateAIPosRVDTChan(CreateAIPosRVDTChanRequest) returns (CreateAIPosRVDTChanResponse);
  rpc CreateAIPowerChan(CreateAIPowerChanRequest) returns (CreateAIPowerChanResponse);
  rpc CreateAIPressureBridgePolynomialChan(CreateAIPressureBridgePolynomialChanRequest) returns (CreateAIPressureBridgePolynomialChanResponse);
  rpc CreateAIPressureBridgeTableChan(CreateAIPressureBridgeTableChanRequest) returns (CreateAIPressureBridgeTableChanResponse);
  rpc CreateAIPressureBridgeTwoPointLinChan(CreateAIPressureBridgeTwoPointLinChanRequest) returns (CreateAIPressureBridgeTwoPointLinChanResponse);
  rpc CreateAIRTDChan(CreateAIRTDChanRequest) returns (CreateAIRTDChanResponse);
  rpc CreateAIResistanceChan(CreateAIResistanceChanRequest) returns (CreateAIResistanceChanResponse);
  rpc CreateAIRosetteStrainGageChan(CreateAIRosetteStrainGageChanRequest) returns (CreateAIRosetteStrainGageChanResponse);
  rpc CreateAIStrainGageChan(CreateAIStrainGageChanRequest) returns (CreateAIStrainGageChanResponse);
  rpc CreateAITempBuiltInSensorChan(CreateAITempBuiltInSensorChanRequest) returns (CreateAITempBuiltInSensorChanResponse);
  rpc CreateAIThrmcplChan(CreateAIThrmcplChanRequest) returns (CreateAIThrmcplChanResponse);
  rpc CreateAIThrmstrChanIex(CreateAIThrmstrChanIexRequest) returns (CreateAIThrmstrChanIexResponse);
  rpc CreateAIThrmstrChanVex(CreateAIThrmstrChanVexRequest) returns (CreateAIThrmstrChanVexResponse);
  rpc CreateAITorqueBridgePolynomialChan(CreateAITorqueBridgePolynomialChanRequest) returns (CreateAITorqueBridgePolynomialChanResponse);
  rpc CreateAITorqueBridgeTableChan(CreateAITorqueBridgeTableChanRequest) returns (CreateAITorqueBridgeTableChanResponse);
  rpc CreateAITorqueBridgeTwoPointLinChan(CreateAITorqueBridgeTwoPointLinChanRequest) returns (CreateAITorqueBridgeTwoPointLinChanResponse);
  rpc CreateAIVelocityIEPEChan(CreateAIVelocityIEPEChanRequest) returns (CreateAIVelocityIEPEChanResponse);
  rpc CreateAIVoltageChan(CreateAIVoltageChanRequest) returns (CreateAIVoltageChanResponse);
  rpc CreateAIVoltageChanWithExcit(CreateAIVoltageChanWithExcitRequest) returns (CreateAIVoltageChanWithExcitResponse);
  rpc CreateAIVoltageRMSChan(CreateAIVoltageRMSChanRequest) returns (CreateAIVoltageRMSChanResponse);
  rpc CreateAOCurrentChan(CreateAOCurrentChanRequest) returns (CreateAOCurrentChanResponse);
  rpc CreateAOFuncGenChan(CreateAOFuncGenChanRequest) returns (CreateAOFuncGenChanResponse);
  rpc CreateAOVoltageChan(CreateAOVoltageChanRequest) returns (CreateAOVoltageChanResponse);
  rpc CreateCIAngEncoderChan(CreateCIAngEncoderChanRequest) returns (CreateCIAngEncoderChanResponse);
  rpc CreateCIAngVelocityChan(CreateCIAngVelocityChanRequest) returns (CreateCIAngVelocityChanResponse);
  rpc CreateCICountEdgesChan(CreateCICountEdgesChanRequest) returns (CreateCICountEdgesChanResponse);
  rpc CreateCIDutyCycleChan(CreateCIDutyCycleChanRequest) returns (CreateCIDutyCycleChanResponse);
  rpc CreateCIFreqChan(CreateCIFreqChanRequest) returns (CreateCIFreqChanResponse);
  rpc CreateCIGPSTimestampChan(CreateCIGPSTimestampChanRequest) returns (CreateCIGPSTimestampChanResponse);
  rpc CreateCILinEncoderChan(CreateCILinEncoderChanRequest) returns (CreateCILinEncoderChanResponse);
  rpc CreateCILinVelocityChan(CreateCILinVelocityChanRequest) returns (CreateCILinVelocityChanResponse);
  rpc CreateCIPeriodChan(CreateCIPeriodChanRequest) returns (CreateCIPeriodChanResponse);
  rpc CreateCIPulseChanFreq(CreateCIPulseChanFreqRequest) returns (CreateCIPulseChanFreqResponse);
  rpc CreateCIPulseChanTicks(CreateCIPulseChanTicksRequest) returns (CreateCIPulseChanTicksResponse);
  rpc CreateCIPulseChanTime(CreateCIPulseChanTimeRequest) returns (CreateCIPulseChanTimeResponse);
  rpc CreateCIPulseWidthChan(CreateCIPulseWidthChanRequest) returns (CreateCIPulseWidthChanResponse);
  rpc CreateCISemiPeriodChan(CreateCISemiPeriodChanRequest) returns (CreateCISemiPeriodChanResponse);
  rpc CreateCITwoEdgeSepChan(CreateCITwoEdgeSepChanRequest) returns (CreateCITwoEdgeSepChanResponse);
  rpc CreateCOPulseChanFreq(CreateCOPulseChanFreqRequest) returns (CreateCOPulseChanFreqResponse);
  rpc CreateCOPulseChanTicks(CreateCOPulseChanTicksRequest) returns (CreateCOPulseChanTicksResponse);
  rpc CreateCOPulseChanTime(CreateCOPulseChanTimeRequest) returns (CreateCOPulseChanTimeResponse);
  rpc CreateDIChan(CreateDIChanRequest) returns (CreateDIChanResponse);
  rpc CreateDOChan(CreateDOChanRequest) returns (CreateDOChanResponse);
  rpc CreateLinScale(CreateLinScaleRequest) returns (CreateLinScaleResponse);
  rpc CreateMapScale(CreateMapScaleRequest) returns (CreateMapScaleResponse);
  rpc CreatePolynomialScale(CreatePolynomialScaleRequest) returns (CreatePolynomialScaleResponse);
  rpc CreateTEDSAIAccelChan(CreateTEDSAIAccelChanRequest) returns (CreateTEDSAIAccelChanResponse);
  rpc CreateTEDSAIBridgeChan(CreateTEDSAIBridgeChanRequest) returns (CreateTEDSAIBridgeChanResponse);
  rpc CreateTEDSAICurrentChan(CreateTEDSAICurrentChanRequest) returns (CreateTEDSAICurrentChanResponse);
  rpc CreateTEDSAIForceBridgeChan(CreateTEDSAIForceBridgeChanRequest) returns (CreateTEDSAIForceBridgeChanResponse);
  rpc CreateTEDSAIForceIEPEChan(CreateTEDSAIForceIEPEChanRequest) returns (CreateTEDSAIForceIEPEChanResponse);
  rpc CreateTEDSAIMicrophoneChan(CreateTEDSAIMicrophoneChanRequest) returns (CreateTEDSAIMicrophoneChanResponse);
  rpc CreateTEDSAIPosLVDTChan(CreateTEDSAIPosLVDTChanRequest) returns (CreateTEDSAIPosLVDTChanResponse);
  rpc CreateTEDSAIPosRVDTChan(CreateTEDSAIPosRVDTChanRequest) returns (CreateTEDSAIPosRVDTChanResponse);
  rpc CreateTEDSAIPressureBridgeChan(CreateTEDSAIPressureBridgeChanRequest) returns (CreateTEDSAIPressureBridgeChanResponse);
  rpc CreateTEDSAIRTDChan(CreateTEDSAIRTDChanRequest) returns (CreateTEDSAIRTDChanResponse);
  rpc CreateTEDSAIResistanceChan(CreateTEDSAIResistanceChanRequest) returns (CreateTEDSAIResistanceChanResponse);
  rpc CreateTEDSAIStrainGageChan(CreateTEDSAIStrainGageChanRequest) returns (CreateTEDSAIStrainGageChanResponse);
  rpc CreateTEDSAIThrmcplChan(CreateTEDSAIThrmcplChanRequest) returns (CreateTEDSAIThrmcplChanResponse);
  rpc CreateTEDSAIThrmstrChanIex(CreateTEDSAIThrmstrChanIexRequest) returns (CreateTEDSAIThrmstrChanIexResponse);
  rpc CreateTEDSAIThrmstrChanVex(CreateTEDSAIThrmstrChanVexRequest) returns (CreateTEDSAIThrmstrChanVexResponse);
  rpc CreateTEDSAITorqueBridgeChan(CreateTEDSAITorqueBridgeChanRequest) returns (CreateTEDSAITorqueBridgeChanResponse);
  rpc CreateTEDSAIVoltageChan(CreateTEDSAIVoltageChanRequest) returns (CreateTEDSAIVoltageChanResponse);
  rpc CreateTEDSAIVoltageChanWithExcit(CreateTEDSAIVoltageChanWithExcitRequest) returns (CreateTEDSAIVoltageChanWithExcitResponse);
  rpc CreateTableScale(CreateTableScaleRequest) returns (CreateTableScaleResponse);
  rpc CreateTask(CreateTaskRequest) returns (CreateTaskResponse);
  rpc CreateWatchdogTimerTask(CreateWatchdogTimerTaskRequest) returns (CreateWatchdogTimerTaskResponse);
  rpc CreateWatchdogTimerTaskEx(CreateWatchdogTimerTaskExRequest) returns (CreateWatchdogTimerTaskExResponse);
  rpc DeleteNetworkDevice(DeleteNetworkDeviceRequest) returns (DeleteNetworkDeviceResponse);
  rpc DeleteSavedGlobalChan(DeleteSavedGlobalChanRequest) returns (DeleteSavedGlobalChanResponse);
  rpc DeleteSavedScale(DeleteSavedScaleRequest) returns (DeleteSavedScaleResponse);
  rpc DeleteSavedTask(DeleteSavedTaskRequest) returns (DeleteSavedTaskResponse);
  rpc DeviceSupportsCal(DeviceSupportsCalRequest) returns (DeviceSupportsCalResponse);
  rpc DisableRefTrig(DisableRefTrigRequest) returns (DisableRefTrigResponse);
  rpc DisableStartTrig(DisableStartTrigRequest) returns (DisableStartTrigResponse);
  rpc DisconnectTerms(DisconnectTermsRequest) returns (DisconnectTermsResponse);
  rpc ExportSignal(ExportSignalRequest) returns (ExportSignalResponse);
  rpc GetAIChanCalCalDate(GetAIChanCalCalDateRequest) returns (GetAIChanCalCalDateResponse);
  rpc GetAIChanCalExpDate(GetAIChanCalExpDateRequest) returns (GetAIChanCalExpDateResponse);
  rpc GetAnalogPowerUpStates(GetAnalogPowerUpStatesRequest) returns (GetAnalogPowerUpStatesResponse);
  rpc GetAnalogPowerUpStatesWithOutputType(GetAnalogPowerUpStatesWithOutputTypeRequest) returns (GetAnalogPowerUpStatesWithOutputTypeResponse);
  rpc GetArmStartTrigTimestampVal(GetArmStartTrigTimestampValRequest) returns (GetArmStartTrigTimestampValResponse);
  rpc GetArmStartTrigTrigWhen(GetArmStartTrigTrigWhenRequest) returns (GetArmStartTrigTrigWhenResponse);
  rpc GetAutoConfiguredCDAQSyncConnections(GetAutoConfiguredCDAQSyncConnectionsRequest) returns (GetAutoConfiguredCDAQSyncConnectionsResponse);
  rpc GetBufferAttributeUInt32(GetBufferAttributeUInt32Request) returns (GetBufferAttributeUInt32Response);
  rpc GetCalInfoAttributeBool(GetCalInfoAttributeBoolRequest) returns (GetCalInfoAttributeBoolResponse);
  rpc GetCalInfoAttributeDouble(GetCalInfoAttributeDoubleRequest) returns (GetCalInfoAttributeDoubleResponse);
  rpc GetCalInfoAttributeString(GetCalInfoAttributeStringRequest) returns (GetCalInfoAttributeStringResponse);
  rpc GetCalInfoAttributeUInt32(GetCalInfoAttributeUInt32Request) returns (GetCalInfoAttributeUInt32Response);
  rpc GetChanAttributeBool(GetChanAttributeBoolRequest) returns (GetChanAttributeBoolResponse);
  rpc GetChanAttributeDouble(GetChanAttributeDoubleRequest) returns (GetChanAttributeDoubleResponse);
  rpc GetChanAttributeDoubleArray(GetChanAttributeDoubleArrayRequest) returns (GetChanAttributeDoubleArrayResponse);
  rpc GetChanAttributeInt32(GetChanAttributeInt32Request) returns (GetChanAttributeInt32Response);
  rpc GetChanAttributeString(GetChanAttributeStringRequest) returns (GetChanAttributeStringResponse);
  rpc GetChanAttributeUInt32(GetChanAttributeUInt32Request) returns (GetChanAttributeUInt32Response);
  rpc GetDeviceAttributeBool(GetDeviceAttributeBoolRequest) returns (GetDeviceAttributeBoolResponse);
  rpc GetDeviceAttributeDouble(GetDeviceAttributeDoubleRequest) returns (GetDeviceAttributeDoubleResponse);
  rpc GetDeviceAttributeDoubleArray(GetDeviceAttributeDoubleArrayRequest) returns (GetDeviceAttributeDoubleArrayResponse);
  rpc GetDeviceAttributeInt32(GetDeviceAttributeInt32Request) returns (GetDeviceAttributeInt32Response);
  rpc GetDeviceAttributeInt32Array(GetDeviceAttributeInt32ArrayRequest) returns (GetDeviceAttributeInt32ArrayResponse);
  rpc GetDeviceAttributeString(GetDeviceAttributeStringRequest) returns (GetDeviceAttributeStringResponse);
  rpc GetDeviceAttributeUInt32(GetDeviceAttributeUInt32Request) returns (GetDeviceAttributeUInt32Response);
  rpc GetDeviceAttributeUInt32Array(GetDeviceAttributeUInt32ArrayRequest) returns (GetDeviceAttributeUInt32ArrayResponse);
  rpc GetDigitalLogicFamilyPowerUpState(GetDigitalLogicFamilyPowerUpStateRequest) returns (GetDigitalLogicFamilyPowerUpStateResponse);
  rpc GetDigitalPowerUpStates(GetDigitalPowerUpStatesRequest) returns (GetDigitalPowerUpStatesResponse);
  rpc GetDigitalPullUpPullDownStates(GetDigitalPullUpPullDownStatesRequest) returns (GetDigitalPullUpPullDownStatesResponse);
  rpc GetDisconnectedCDAQSyncPorts(GetDisconnectedCDAQSyncPortsRequest) returns (GetDisconnectedCDAQSyncPortsResponse);
  rpc GetErrorString(GetErrorStringRequest) returns (GetErrorStringResponse);
  rpc GetExportedSignalAttributeBool(GetExportedSignalAttributeBoolRequest) returns (GetExportedSignalAttributeBoolResponse);
  rpc GetExportedSignalAttributeDouble(GetExportedSignalAttributeDoubleRequest) returns (GetExportedSignalAttributeDoubleResponse);
  rpc GetExportedSignalAttributeInt32(GetExportedSignalAttributeInt32Request) returns (GetExportedSignalAttributeInt32Response);
  rpc GetExportedSignalAttributeString(GetExportedSignalAttributeStringRequest) returns (GetExportedSignalAttributeStringResponse);
  rpc GetExportedSignalAttributeUInt32(GetExportedSignalAttributeUInt32Request) returns (GetExportedSignalAttributeUInt32Response);
  rpc GetExtCalLastDateAndTime(GetExtCalLastDateAndTimeRequest) returns (GetExtCalLastDateAndTimeResponse);
  rpc GetFirstSampClkWhen(GetFirstSampClkWhenRequest) returns (GetFirstSampClkWhenResponse);
  rpc GetFirstSampTimestampVal(GetFirstSampTimestampValRequest) returns (GetFirstSampTimestampValResponse);
  rpc GetNthTaskChannel(GetNthTaskChannelRequest) returns (GetNthTaskChannelResponse);
  rpc GetNthTaskDevice(GetNthTaskDeviceRequest) returns (GetNthTaskDeviceResponse);
  rpc GetNthTaskReadChannel(GetNthTaskReadChannelRequest) returns (GetNthTaskReadChannelResponse);
  rpc GetPersistedChanAttributeBool(GetPersistedChanAttributeBoolRequest) returns (GetPersistedChanAttributeBoolResponse);
  rpc GetPersistedChanAttributeString(GetPersistedChanAttributeStringRequest) returns (GetPersistedChanAttributeStringResponse);
  rpc GetPersistedScaleAttributeBool(GetPersistedScaleAttributeBoolRequest) returns (GetPersistedScaleAttributeBoolResponse);
  rpc GetPersistedScaleAttributeString(GetPersistedScaleAttributeStringRequest) returns (GetPersistedScaleAttributeStringResponse);
  rpc GetPersistedTaskAttributeBool(GetPersistedTaskAttributeBoolRequest) returns (GetPersistedTaskAttributeBoolResponse);
  rpc GetPersistedTaskAttributeString(GetPersistedTaskAttributeStringRequest) returns (GetPersistedTaskAttributeStringResponse);
  rpc GetPhysicalChanAttributeBool(GetPhysicalChanAttributeBoolRequest) returns (GetPhysicalChanAttributeBoolResponse);
  rpc GetPhysicalChanAttributeBytes(GetPhysicalChanAttributeBytesRequest) returns (GetPhysicalChanAttributeBytesResponse);
  rpc GetPhysicalChanAttributeDouble(GetPhysicalChanAttributeDoubleRequest) returns (GetPhysicalChanAttributeDoubleResponse);
  rpc GetPhysicalChanAttributeDoubleArray(GetPhysicalChanAttributeDoubleArrayRequest) returns (GetPhysicalChanAttributeDoubleArrayResponse);
  rpc GetPhysicalChanAttributeInt32(GetPhysicalChanAttributeInt32Request) returns (GetPhysicalChanAttributeInt32Response);
  rpc GetPhysicalChanAttributeInt32Array(GetPhysicalChanAttributeInt32ArrayRequest) returns (GetPhysicalChanAttributeInt32ArrayResponse);
  rpc GetPhysicalChanAttributeString(GetPhysicalChanAttributeStringRequest) returns (GetPhysicalChanAttributeStringResponse);
  rpc GetPhysicalChanAttributeUInt32(GetPhysicalChanAttributeUInt32Request) returns (GetPhysicalChanAttributeUInt32Response);
  rpc GetPhysicalChanAttributeUInt32Array(GetPhysicalChanAttributeUInt32ArrayRequest) returns (GetPhysicalChanAttributeUInt32ArrayResponse);
  rpc GetReadAttributeBool(GetReadAttributeBoolRequest) returns (GetReadAttributeBoolResponse);
  rpc GetReadAttributeDouble(GetReadAttributeDoubleRequest) returns (GetReadAttributeDoubleResponse);
  rpc GetReadAttributeInt32(GetReadAttributeInt32Request) returns (GetReadAttributeInt32Response);
  rpc GetReadAttributeString(GetReadAttributeStringRequest) returns (GetReadAttributeStringResponse);
  rpc GetReadAttributeUInt32(GetReadAttributeUInt32Request) returns (GetReadAttributeUInt32Response);
  rpc GetReadAttributeUInt64(GetReadAttributeUInt64Request) returns (GetReadAttributeUInt64Response);
  rpc GetRealTimeAttributeBool(GetRealTimeAttributeBoolRequest) returns (GetRealTimeAttributeBoolResponse);
  rpc GetRealTimeAttributeInt32(GetRealTimeAttributeInt32Request) returns (GetRealTimeAttributeInt32Response);
  rpc GetRealTimeAttributeUInt32(GetRealTimeAttributeUInt32Request) returns (GetRealTimeAttributeUInt32Response);
  rpc GetRefTrigTimestampVal(GetRefTrigTimestampValRequest) returns (GetRefTrigTimestampValResponse);
  rpc GetScaleAttributeDouble(GetScaleAttributeDoubleRequest) returns (GetScaleAttributeDoubleResponse);
  rpc GetScaleAttributeDoubleArray(GetScaleAttributeDoubleArrayRequest) returns (GetScaleAttributeDoubleArrayResponse);
  rpc GetScaleAttributeInt32(GetScaleAttributeInt32Request) returns (GetScaleAttributeInt32Response);
  rpc GetScaleAttributeString(GetScaleAttributeStringRequest) returns (GetScaleAttributeStringResponse);
  rpc GetSelfCalLastDateAndTime(GetSelfCalLastDateAndTimeRequest) returns (GetSelfCalLastDateAndTimeResponse);
  rpc GetStartTrigTimestampVal(GetStartTrigTimestampValRequest) returns (GetStartTrigTimestampValResponse);
  rpc GetStartTrigTrigWhen(GetStartTrigTrigWhenRequest) returns (GetStartTrigTrigWhenResponse);
  rpc GetSyncPulseTimeWhen(GetSyncPulseTimeWhenRequest) returns (GetSyncPulseTimeWhenResponse);
  rpc GetSystemInfoAttributeString(GetSystemInfoAttributeStringRequest) returns (GetSystemInfoAttributeStringResponse);
  rpc GetSystemInfoAttributeUInt32(GetSystemInfoAttributeUInt32Request) returns (GetSystemInfoAttributeUInt32Response);
  rpc GetTaskAttributeBool(GetTaskAttributeBoolRequest) returns (GetTaskAttributeBoolResponse);
  rpc GetTaskAttributeString(GetTaskAttributeStringRequest) returns (GetTaskAttributeStringResponse);
  rpc GetTaskAttributeUInt32(GetTaskAttributeUInt32Request) returns (GetTaskAttributeUInt32Response);
  rpc GetTimingAttributeBool(GetTimingAttributeBoolRequest) returns (GetTimingAttributeBoolResponse);
  rpc GetTimingAttributeDouble(GetTimingAttributeDoubleRequest) returns (GetTimingAttributeDoubleResponse);
  rpc GetTimingAttributeExBool(GetTimingAttributeExBoolRequest) returns (GetTimingAttributeExBoolResponse);
  rpc GetTimingAttributeExDouble(GetTimingAttributeExDoubleRequest) returns (GetTimingAttributeExDoubleResponse);
  rpc GetTimingAttributeExInt32(GetTimingAttributeExInt32Request) returns (GetTimingAttributeExInt32Response);
  rpc GetTimingAttributeExString(GetTimingAttributeExStringRequest) returns (GetTimingAttributeExStringResponse);
  rpc GetTimingAttributeExTimestamp(GetTimingAttributeExTimestampRequest) returns (GetTimingAttributeExTimestampResponse);
  rpc GetTimingAttributeExUInt32(GetTimingAttributeExUInt32Request) returns (GetTimingAttributeExUInt32Response);
  rpc GetTimingAttributeExUInt64(GetTimingAttributeExUInt64Request) returns (GetTimingAttributeExUInt64Response);
  rpc GetTimingAttributeInt32(GetTimingAttributeInt32Request) returns (GetTimingAttributeInt32Response);
  rpc GetTimingAttributeString(GetTimingAttributeStringRequest) returns (GetTimingAttributeStringResponse);
  rpc GetTimingAttributeTimestamp(GetTimingAttributeTimestampRequest) returns (GetTimingAttributeTimestampResponse);
  rpc GetTimingAttributeUInt32(GetTimingAttributeUInt32Request) returns (GetTimingAttributeUInt32Response);
  rpc GetTimingAttributeUInt64(GetTimingAttributeUInt64Request) returns (GetTimingAttributeUInt64Response);
  rpc GetTrigAttributeBool(GetTrigAttributeBoolRequest) returns (GetTrigAttributeBoolResponse);
  rpc GetTrigAttributeDouble(GetTrigAttributeDoubleRequest) returns (GetTrigAttributeDoubleResponse);
  rpc GetTrigAttributeDoubleArray(GetTrigAttributeDoubleArrayRequest) returns (GetTrigAttributeDoubleArrayResponse);
  rpc GetTrigAttributeInt32(GetTrigAttributeInt32Request) returns (GetTrigAttributeInt32Response);
  rpc GetTrigAttributeInt32Array(GetTrigAttributeInt32ArrayRequest) returns (GetTrigAttributeInt32ArrayResponse);
  rpc GetTrigAttributeString(GetTrigAttributeStringRequest) returns (GetTrigAttributeStringResponse);
  rpc GetTrigAttributeTimestamp(GetTrigAttributeTimestampRequest) returns (GetTrigAttributeTimestampResponse);
  rpc GetTrigAttributeUInt32(GetTrigAttributeUInt32Request) returns (GetTrigAttributeUInt32Response);
  rpc GetWatchdogAttributeBool(GetWatchdogAttributeBoolRequest) returns (GetWatchdogAttributeBoolResponse);
  rpc GetWatchdogAttributeDouble(GetWatchdogAttributeDoubleRequest) returns (GetWatchdogAttributeDoubleResponse);
  rpc GetWatchdogAttributeInt32(GetWatchdogAttributeInt32Request) returns (GetWatchdogAttributeInt32Response);
  rpc GetWatchdogAttributeString(GetWatchdogAttributeStringRequest) returns (GetWatchdogAttributeStringResponse);
  rpc GetWriteAttributeBool(GetWriteAttributeBoolRequest) returns (GetWriteAttributeBoolResponse);
  rpc GetWriteAttributeDouble(GetWriteAttributeDoubleRequest) returns (GetWriteAttributeDoubleResponse);
  rpc GetWriteAttributeInt32(GetWriteAttributeInt32Request) returns (GetWriteAttributeInt32Response);
  rpc GetWriteAttributeString(GetWriteAttributeStringRequest) returns (GetWriteAttributeStringResponse);
  rpc GetWriteAttributeUInt32(GetWriteAttributeUInt32Request) returns (GetWriteAttributeUInt32Response);
  rpc GetWriteAttributeUInt64(GetWriteAttributeUInt64Request) returns (GetWriteAttributeUInt64Response);
  rpc IsTaskDone(IsTaskDoneRequest) returns (IsTaskDoneResponse);
  rpc LoadTask(LoadTaskRequest) returns (LoadTaskResponse);
  rpc PerformBridgeOffsetNullingCalEx(PerformBridgeOffsetNullingCalExRequest) returns (PerformBridgeOffsetNullingCalExResponse);
  rpc PerformBridgeShuntCalEx(PerformBridgeShuntCalExRequest) returns (PerformBridgeShuntCalExResponse);
  rpc PerformStrainShuntCalEx(PerformStrainShuntCalExRequest) returns (PerformStrainShuntCalExResponse);
  rpc PerformThrmcplLeadOffsetNullingCal(PerformThrmcplLeadOffsetNullingCalRequest) returns (PerformThrmcplLeadOffsetNullingCalResponse);
  rpc ReadAnalogF64(ReadAnalogF64Request) returns (ReadAnalogF64Response);
  rpc BeginReadAnalogF64(BeginReadAnalogF64Request) returns (BeginReadAnalogF64Response);
  rpc ReadAnalogScalarF64(ReadAnalogScalarF64Request) returns (ReadAnalogScalarF64Response);
  rpc BeginReadAnalogScalarF64(BeginReadAnalogScalarF64Request) returns (BeginReadAnalogScalarF64Response);
  rpc ReadBinaryI16(ReadBinaryI16Request) returns (ReadBinaryI16Response);
  rpc BeginReadBinaryI16(BeginReadBinaryI16Request) returns (BeginReadBinaryI16Response);
  rpc ReadBinaryI32(ReadBinaryI32Request) returns (ReadBinaryI32Response);
  rpc BeginReadBinaryI32(BeginReadBinaryI32Request) returns (BeginReadBinaryI32Response);
  rpc ReadBinaryU16(ReadBinaryU16Request) returns (ReadBinaryU16Response);
  rpc BeginReadBinaryU16(BeginReadBinaryU16Request) returns (BeginReadBinaryU16Response);
  rpc ReadBinaryU32(ReadBinaryU32Request) returns (ReadBinaryU32Response);
  rpc BeginReadBinaryU32(BeginReadBinaryU32Request) returns (BeginReadBinaryU32Response);
  rpc ReadCounterF64(ReadCounterF64Request) returns (ReadCounterF64Response);
  rpc BeginReadCounterF64(BeginReadCounterF64Request) returns (BeginReadCounterF64Response);
  rpc ReadCounterF64Ex(ReadCounterF64ExRequest) returns (ReadCounterF64ExResponse);
  rpc BeginReadCounterF64Ex(BeginReadCounterF64ExRequest) returns (BeginReadCounterF64ExResponse);
  rpc ReadCounterScalarF64(ReadCounterScalarF64Request) returns (ReadCounterScalarF64Response);
  rpc BeginReadCounterScalarF64(BeginReadCounterScalarF64Request) returns (BeginReadCounterScalarF64Response);
  rpc ReadCounterScalarU32(ReadCounterScalarU32Request) returns (ReadCounterScalarU32Response);
  rpc BeginReadCounterScalarU32(BeginReadCounterScalarU32Request) returns (BeginReadCounterScalarU32Response);
  rpc ReadCounterU32(ReadCounterU32Request) returns (ReadCounterU32Response);
  rpc BeginReadCounterU32(BeginReadCounterU32Request) returns (BeginReadCounterU32Response);
  rpc ReadCounterU32Ex(ReadCounterU32ExRequest) returns (ReadCounterU32ExResponse);
  rpc BeginReadCounterU32Ex(BeginReadCounterU32ExRequest) returns (BeginReadCounterU32ExResponse);
  rpc ReadCtrFreq(ReadCtrFreqRequest) returns (ReadCtrFreqResponse);
  rpc BeginReadCtrFreq(BeginReadCtrFreqRequest) returns (BeginReadCtrFreqResponse);
  rpc ReadCtrFreqScalar(ReadCtrFreqScalarRequest) returns (ReadCtrFreqScalarResponse);
  rpc BeginReadCtrFreqScalar(BeginReadCtrFreqScalarRequest) returns (BeginReadCtrFreqScalarResponse);
  rpc ReadCtrTicks(ReadCtrTicksRequest) returns (ReadCtrTicksResponse);
  rpc BeginReadCtrTicks(BeginReadCtrTicksRequest) returns (BeginReadCtrTicksResponse);
  rpc ReadCtrTicksScalar(ReadCtrTicksScalarRequest) returns (ReadCtrTicksScalarResponse);
  rpc BeginReadCtrTicksScalar(BeginReadCtrTicksScalarRequest) returns (BeginReadCtrTicksScalarResponse);
  rpc ReadCtrTime(ReadCtrTimeRequest) returns (ReadCtrTimeResponse);
  rpc BeginReadCtrTime(BeginReadCtrTimeRequest) returns (BeginReadCtrTimeResponse);
  rpc ReadCtrTimeScalar(ReadCtrTimeScalarRequest) returns (ReadCtrTimeScalarResponse);
  rpc BeginReadCtrTimeScalar(BeginReadCtrTimeScalarRequest) returns (BeginReadCtrTimeScalarResponse);
  rpc ReadDigitalLines(ReadDigitalLinesRequest) returns (ReadDigitalLinesResponse);
  rpc BeginReadDigitalLines(BeginReadDigitalLinesRequest) returns (BeginReadDigitalLinesResponse);
  rpc ReadDigitalScalarU32(ReadDigitalScalarU32Request) returns (ReadDigitalScalarU32Response);
  rpc BeginReadDigitalScalarU32(BeginReadDigitalScalarU32Request) returns (BeginReadDigitalScalarU32Response);
  rpc ReadDigitalU16(ReadDigitalU16Request) returns (ReadDigitalU16Response);
  rpc BeginReadDigitalU16(BeginReadDigitalU16Request) returns (BeginReadDigitalU16Response);
  rpc ReadDigitalU32(ReadDigitalU32Request) returns (ReadDigitalU32Response);
  rpc BeginReadDigitalU32(BeginReadDigitalU32Request) returns (BeginReadDigitalU32Response);
  rpc ReadDigitalU8(ReadDigitalU8Request) returns (ReadDigitalU8Response);
  rpc BeginReadDigitalU8(BeginReadDigitalU8Request) returns (BeginReadDigitalU8Response);
  rpc ReadIDPinMemory(ReadIDPinMemoryRequest) returns (ReadIDPinMemoryResponse);
  rpc ReadPowerBinaryI16(ReadPowerBinaryI16Request) returns (ReadPowerBinaryI16Response);
  rpc BeginReadPowerBinaryI16(BeginReadPowerBinaryI16Request) returns (BeginReadPowerBinaryI16Response);
  rpc ReadPowerF64(ReadPowerF64Request) returns (ReadPowerF64Response);
  rpc BeginReadPowerF64(BeginReadPowerF64Request) returns (BeginReadPowerF64Response);
  rpc ReadPowerScalarF64(ReadPowerScalarF64Request) returns (ReadPowerScalarF64Response);
  rpc BeginReadPowerScalarF64(BeginReadPowerScalarF64Request) returns (BeginReadPowerScalarF64Response);
  rpc ReadRaw(ReadRawRequest) returns (ReadRawResponse);
  rpc BeginReadRaw(BeginReadRawRequest) returns (BeginReadRawResponse);
  rpc RegisterDoneEvent(RegisterDoneEventRequest) returns (stream RegisterDoneEventResponse);
  rpc RegisterEveryNSamplesEvent(RegisterEveryNSamplesEventRequest) returns (stream RegisterEveryNSamplesEventResponse);
  rpc RegisterSignalEvent(RegisterSignalEventRequest) returns (stream RegisterSignalEventResponse);
  rpc RemoveCDAQSyncConnection(RemoveCDAQSyncConnectionRequest) returns (RemoveCDAQSyncConnectionResponse);
  rpc ReserveNetworkDevice(ReserveNetworkDeviceRequest) returns (ReserveNetworkDeviceResponse);
  rpc ResetBufferAttribute(ResetBufferAttributeRequest) returns (ResetBufferAttributeResponse);
  rpc ResetChanAttribute(ResetChanAttributeRequest) returns (ResetChanAttributeResponse);
  rpc ResetDevice(ResetDeviceRequest) returns (ResetDeviceResponse);
  rpc ResetExportedSignalAttribute(ResetExportedSignalAttributeRequest) returns (ResetExportedSignalAttributeResponse);
  rpc ResetReadAttribute(ResetReadAttributeRequest) returns (ResetReadAttributeResponse);
  rpc ResetRealTimeAttribute(ResetRealTimeAttributeRequest) returns (ResetRealTimeAttributeResponse);
  rpc ResetTimingAttribute(ResetTimingAttributeRequest) returns (ResetTimingAttributeResponse);
  rpc ResetTimingAttributeEx(ResetTimingAttributeExRequest) returns (ResetTimingAttributeExResponse);
  rpc ResetTrigAttribute(ResetTrigAttributeRequest) returns (ResetTrigAttributeResponse);
  rpc ResetWatchdogAttribute(ResetWatchdogAttributeRequest) returns (ResetWatchdogAttributeResponse);
  rpc ResetWriteAttribute(ResetWriteAttributeRequest) returns (ResetWriteAttributeResponse);
  rpc RestoreLastExtCalConst(RestoreLastExtCalConstRequest) returns (RestoreLastExtCalConstResponse);
  rpc SaveGlobalChan(SaveGlobalChanRequest) returns (SaveGlobalChanResponse);
  rpc SaveScale(SaveScaleRequest) returns (SaveScaleResponse);
  rpc SaveTask(SaveTaskRequest) returns (SaveTaskResponse);
  rpc SelfCal(SelfCalRequest) returns (SelfCalResponse);
  rpc SelfTestDevice(SelfTestDeviceRequest) returns (SelfTestDeviceResponse);
  rpc SetAIChanCalCalDate(SetAIChanCalCalDateRequest) returns (SetAIChanCalCalDateResponse);
  rpc SetAIChanCalExpDate(SetAIChanCalExpDateRequest) returns (SetAIChanCalExpDateResponse);
  rpc SetAnalogPowerUpStates(SetAnalogPowerUpStatesRequest) returns (SetAnalogPowerUpStatesResponse);
  rpc SetAnalogPowerUpStatesWithOutputType(SetAnalogPowerUpStatesWithOutputTypeRequest) returns (SetAnalogPowerUpStatesWithOutputTypeResponse);
  rpc SetArmStartTrigTrigWhen(SetArmStartTrigTrigWhenRequest) returns (SetArmStartTrigTrigWhenResponse);
  rpc SetBufferAttributeUInt32(SetBufferAttributeUInt32Request) returns (SetBufferAttributeUInt32Response);
  rpc SetCalInfoAttributeBool(SetCalInfoAttributeBoolRequest) returns (SetCalInfoAttributeBoolResponse);
  rpc SetCalInfoAttributeDouble(SetCalInfoAttributeDoubleRequest) returns (SetCalInfoAttributeDoubleResponse);
  rpc SetCalInfoAttributeString(SetCalInfoAttributeStringRequest) returns (SetCalInfoAttributeStringResponse);
  rpc SetCalInfoAttributeUInt32(SetCalInfoAttributeUInt32Request) returns (SetCalInfoAttributeUInt32Response);
  rpc SetChanAttributeBool(SetChanAttributeBoolRequest) returns (SetChanAttributeBoolResponse);
  rpc SetChanAttributeDouble(SetChanAttributeDoubleRequest) returns (SetChanAttributeDoubleResponse);
  rpc SetChanAttributeDoubleArray(SetChanAttributeDoubleArrayRequest) returns (SetChanAttributeDoubleArrayResponse);
  rpc SetChanAttributeInt32(SetChanAttributeInt32Request) returns (SetChanAttributeInt32Response);
  rpc SetChanAttributeString(SetChanAttributeStringRequest) returns (SetChanAttributeStringResponse);
  rpc SetChanAttributeUInt32(SetChanAttributeUInt32Request) returns (SetChanAttributeUInt32Response);
  rpc SetDigitalLogicFamilyPowerUpState(SetDigitalLogicFamilyPowerUpStateRequest) returns (SetDigitalLogicFamilyPowerUpStateResponse);
  rpc SetDigitalPowerUpStates(SetDigitalPowerUpStatesRequest) returns (SetDigitalPowerUpStatesResponse);
  rpc SetDigitalPullUpPullDownStates(SetDigitalPullUpPullDownStatesRequest) returns (SetDigitalPullUpPullDownStatesResponse);
  rpc SetExportedSignalAttributeBool(SetExportedSignalAttributeBoolRequest) returns (SetExportedSignalAttributeBoolResponse);
  rpc SetExportedSignalAttributeDouble(SetExportedSignalAttributeDoubleRequest) returns (SetExportedSignalAttributeDoubleResponse);
  rpc SetExportedSignalAttributeInt32(SetExportedSignalAttributeInt32Request) returns (SetExportedSignalAttributeInt32Response);
  rpc SetExportedSignalAttributeString(SetExportedSignalAttributeStringRequest) returns (SetExportedSignalAttributeStringResponse);
  rpc SetExportedSignalAttributeUInt32(SetExportedSignalAttributeUInt32Request) returns (SetExportedSignalAttributeUInt32Response);
  rpc SetFirstSampClkWhen(SetFirstSampClkWhenRequest) returns (SetFirstSampClkWhenResponse);
  rpc SetReadAttributeBool(SetReadAttributeBoolRequest) returns (SetReadAttributeBoolResponse);
  rpc SetReadAttributeDouble(SetReadAttributeDoubleRequest) returns (SetReadAttributeDoubleResponse);
  rpc SetReadAttributeInt32(SetReadAttributeInt32Request) returns (SetReadAttributeInt32Response);
  rpc SetReadAttributeString(SetReadAttributeStringRequest) returns (SetReadAttributeStringResponse);
  rpc SetReadAttributeUInt32(SetReadAttributeUInt32Request) returns (SetReadAttributeUInt32Response);
  rpc SetReadAttributeUInt64(SetReadAttributeUInt64Request) returns (SetReadAttributeUInt64Response);
  rpc SetRealTimeAttributeBool(SetRealTimeAttributeBoolRequest) returns (SetRealTimeAttributeBoolResponse);
  rpc SetRealTimeAttributeInt32(SetRealTimeAttributeInt32Request) returns (SetRealTimeAttributeInt32Response);
  rpc SetRealTimeAttributeUInt32(SetRealTimeAttributeUInt32Request) returns (SetRealTimeAttributeUInt32Response);
  rpc SetScaleAttributeDouble(SetScaleAttributeDoubleRequest) returns (SetScaleAttributeDoubleResponse);
  rpc SetScaleAttributeDoubleArray(SetScaleAttributeDoubleArrayRequest) returns (SetScaleAttributeDoubleArrayResponse);
  rpc SetScaleAttributeInt32(SetScaleAttributeInt32Request) returns (SetScaleAttributeInt32Response);
  rpc SetScaleAttributeString(SetScaleAttributeStringRequest) returns (SetScaleAttributeStringResponse);
  rpc SetStartTrigTrigWhen(SetStartTrigTrigWhenRequest) returns (SetStartTrigTrigWhenResponse);
  rpc SetSyncPulseTimeWhen(SetSyncPulseTimeWhenRequest) returns (SetSyncPulseTimeWhenResponse);
  rpc SetTimingAttributeBool(SetTimingAttributeBoolRequest) returns (SetTimingAttributeBoolResponse);
  rpc SetTimingAttributeDouble(SetTimingAttributeDoubleRequest) returns (SetTimingAttributeDoubleResponse);
  rpc SetTimingAttributeExBool(SetTimingAttributeExBoolRequest) returns (SetTimingAttributeExBoolResponse);
  rpc SetTimingAttributeExDouble(SetTimingAttributeExDoubleRequest) returns (SetTimingAttributeExDoubleResponse);
  rpc SetTimingAttributeExInt32(SetTimingAttributeExInt32Request) returns (SetTimingAttributeExInt32Response);
  rpc SetTimingAttributeExString(SetTimingAttributeExStringRequest) returns (SetTimingAttributeExStringResponse);
  rpc SetTimingAttributeExTimestamp(SetTimingAttributeExTimestampRequest) returns (SetTimingAttributeExTimestampResponse);
  rpc SetTimingAttributeExUInt32(SetTimingAttributeExUInt32Request) returns (SetTimingAttributeExUInt32Response);
  rpc SetTimingAttributeExUInt64(SetTimingAttributeExUInt64Request) returns (SetTimingAttributeExUInt64Response);
  rpc SetTimingAttributeInt32(SetTimingAttributeInt32Request) returns (SetTimingAttributeInt32Response);
  rpc SetTimingAttributeString(SetTimingAttributeStringRequest) returns (SetTimingAttributeStringResponse);
  rpc SetTimingAttributeTimestamp(SetTimingAttributeTimestampRequest) returns (SetTimingAttributeTimestampResponse);
  rpc SetTimingAttributeUInt32(SetTimingAttributeUInt32Request) returns (SetTimingAttributeUInt32Response);
  rpc SetTimingAttributeUInt64(SetTimingAttributeUInt64Request) returns (SetTimingAttributeUInt64Response);
  rpc SetTrigAttributeBool(SetTrigAttributeBoolRequest) returns (SetTrigAttributeBoolResponse);
  rpc SetTrigAttributeDouble(SetTrigAttributeDoubleRequest) returns (SetTrigAttributeDoubleResponse);
  rpc SetTrigAttributeDoubleArray(SetTrigAttributeDoubleArrayRequest) returns (SetTrigAttributeDoubleArrayResponse);
  rpc SetTrigAttributeInt32(SetTrigAttributeInt32Request) returns (SetTrigAttributeInt32Response);
  rpc SetTrigAttributeInt32Array(SetTrigAttributeInt32ArrayRequest) returns (SetTrigAttributeInt32ArrayResponse);
  rpc SetTrigAttributeString(SetTrigAttributeStringRequest) returns (SetTrigAttributeStringResponse);
  rpc SetTrigAttributeTimestamp(SetTrigAttributeTimestampRequest) returns (SetTrigAttributeTimestampResponse);
  rpc SetTrigAttributeUInt32(SetTrigAttributeUInt32Request) returns (SetTrigAttributeUInt32Response);
  rpc SetWatchdogAttributeBool(SetWatchdogAttributeBoolRequest) returns (SetWatchdogAttributeBoolResponse);
  rpc SetWatchdogAttributeDouble(SetWatchdogAttributeDoubleRequest) returns (SetWatchdogAttributeDoubleResponse);
  rpc SetWatchdogAttributeInt32(SetWatchdogAttributeInt32Request) returns (SetWatchdogAttributeInt32Response);
  rpc SetWatchdogAttributeString(SetWatchdogAttributeStringRequest) returns (SetWatchdogAttributeStringResponse);
  rpc SetWriteAttributeBool(SetWriteAttributeBoolRequest) returns (SetWriteAttributeBoolResponse);
  rpc SetWriteAttributeDouble(SetWriteAttributeDoubleRequest) returns (SetWriteAttributeDoubleResponse);
  rpc SetWriteAttributeInt32(SetWriteAttributeInt32Request) returns (SetWriteAttributeInt32Response);
  rpc SetWriteAttributeString(SetWriteAttributeStringRequest) returns (SetWriteAttributeStringResponse);
  rpc SetWriteAttributeUInt32(SetWriteAttributeUInt32Request) returns (SetWriteAttributeUInt32Response);
  rpc SetWriteAttributeUInt64(SetWriteAttributeUInt64Request) returns (SetWriteAttributeUInt64Response);
  rpc StartNewFile(StartNewFileRequest) returns (StartNewFileResponse);
  rpc StartTask(StartTaskRequest) returns (StartTaskResponse);
  rpc StopTask(StopTaskRequest) returns (StopTaskResponse);
  rpc TaskControl(TaskControlRequest) returns (TaskControlResponse);
  rpc TristateOutputTerm(TristateOutputTermRequest) returns (TristateOutputTermResponse);
  rpc UnregisterDoneEvent(UnregisterDoneEventRequest) returns (UnregisterDoneEventResponse);
  rpc UnregisterEveryNSamplesEvent(UnregisterEveryNSamplesEventRequest) returns (UnregisterEveryNSamplesEventResponse);
  rpc UnregisterSignalEvent(UnregisterSignalEventRequest) returns (UnregisterSignalEventResponse);
  rpc UnreserveNetworkDevice(UnreserveNetworkDeviceRequest) returns (UnreserveNetworkDeviceResponse);
  rpc WaitForNextSampleClock(WaitForNextSampleClockRequest) returns (WaitForNextSampleClockResponse);
  rpc BeginWaitForNextSampleClock(BeginWaitForNextSampleClockRequest) returns (BeginWaitForNextSampleClockResponse);
  rpc WaitForValidTimestamp(WaitForValidTimestampRequest) returns (WaitForValidTimestampResponse);
  rpc WaitUntilTaskDone(WaitUntilTaskDoneRequest) returns (WaitUntilTaskDoneResponse);
  rpc WriteAnalogF64(WriteAnalogF64Request) returns (WriteAnalogF64Response);
  rpc BeginWriteAnalogF64(BeginWriteAnalogF64Request) returns (BeginWriteAnalogF64Response);
  rpc WriteAnalogScalarF64(WriteAnalogScalarF64Request) returns (WriteAnalogScalarF64Response);
  rpc BeginWriteAnalogScalarF64(BeginWriteAnalogScalarF64Request) returns (BeginWriteAnalogScalarF64Response);
  rpc WriteBinaryI16(WriteBinaryI16Request) returns (WriteBinaryI16Response);
  rpc BeginWriteBinaryI16(BeginWriteBinaryI16Request) returns (BeginWriteBinaryI16Response);
  rpc WriteBinaryI32(WriteBinaryI32Request) returns (WriteBinaryI32Response);
  rpc BeginWriteBinaryI32(BeginWriteBinaryI32Request) returns (BeginWriteBinaryI32Response);
  rpc WriteBinaryU16(WriteBinaryU16Request) returns (WriteBinaryU16Response);
  rpc BeginWriteBinaryU16(BeginWriteBinaryU16Request) returns (BeginWriteBinaryU16Response);
  rpc WriteBinaryU32(WriteBinaryU32Request) returns (WriteBinaryU32Response);
  rpc BeginWriteBinaryU32(BeginWriteBinaryU32Request) returns (BeginWriteBinaryU32Response);
  rpc WriteCtrFreq(WriteCtrFreqRequest) returns (WriteCtrFreqResponse);
  rpc BeginWriteCtrFreq(BeginWriteCtrFreqRequest) returns (BeginWriteCtrFreqResponse);
  rpc WriteCtrFreqScalar(WriteCtrFreqScalarRequest) returns (WriteCtrFreqScalarResponse);
  rpc BeginWriteCtrFreqScalar(BeginWriteCtrFreqScalarRequest) returns (BeginWriteCtrFreqScalarResponse);
  rpc WriteCtrTicks(WriteCtrTicksRequest) returns (WriteCtrTicksResponse);
  rpc BeginWriteCtrTicks(BeginWriteCtrTicksRequest) returns (BeginWriteCtrTicksResponse);
  rpc WriteCtrTicksScalar(WriteCtrTicksScalarRequest) returns (WriteCtrTicksScalarResponse);
  rpc BeginWriteCtrTicksScalar(BeginWriteCtrTicksScalarRequest) returns (BeginWriteCtrTicksScalarResponse);
  rpc WriteCtrTime(WriteCtrTimeRequest) returns (WriteCtrTimeResponse);
  rpc BeginWriteCtrTime(BeginWriteCtrTimeRequest) returns (BeginWriteCtrTimeResponse);
  rpc WriteCtrTimeScalar(WriteCtrTimeScalarRequest) returns (WriteCtrTimeScalarResponse);
  rpc BeginWriteCtrTimeScalar(BeginWriteCtrTimeScalarRequest) returns (BeginWriteCtrTimeScalarResponse);
  rpc WriteDigitalLines(WriteDigitalLinesRequest) returns (WriteDigitalLinesResponse);
  rpc BeginWriteDigitalLines(BeginWriteDigitalLinesRequest) returns (BeginWriteDigitalLinesResponse);
  rpc WriteDigitalScalarU32(WriteDigitalScalarU32Request) returns (WriteDigitalScalarU32Response);
  rpc BeginWriteDigitalScalarU32(BeginWriteDigitalScalarU32Request) returns (BeginWriteDigitalScalarU32Response);
  rpc WriteDigitalU16(WriteDigitalU16Request) returns (WriteDigitalU16Response);
  rpc BeginWriteDigitalU16(BeginWriteDigitalU16Request) returns (BeginWriteDigitalU16Response);
  rpc WriteDigitalU32(WriteDigitalU32Request) returns (WriteDigitalU32Response);
  rpc BeginWriteDigitalU32(BeginWriteDigitalU32Request) returns (BeginWriteDigitalU32Response);
  rpc WriteDigitalU8(WriteDigitalU8Request) returns (WriteDigitalU8Response);
  rpc BeginWriteDigitalU8(BeginWriteDigitalU8Request) returns (BeginWriteDigitalU8Response);
  rpc WriteIDPinMemory(WriteIDPinMemoryRequest) returns (WriteIDPinMemoryResponse);
  rpc WriteRaw(WriteRawRequest) returns (WriteRawResponse);
  rpc BeginWriteRaw(BeginWriteRawRequest) returns (BeginWriteRawResponse);
  rpc WriteToTEDSFromArray(WriteToTEDSFromArrayRequest) returns (WriteToTEDSFromArrayResponse);
  rpc WriteToTEDSFromFile(WriteToTEDSFromFileRequest) returns (WriteToTEDSFromFileResponse);
}

enum BufferUInt32Attribute {
  BUFFER_UINT32_ATTRIBUTE_UNSPECIFIED = 0;
  BUFFER_ATTRIBUTE_INPUT_BUF_SIZE = 6252;
  BUFFER_ATTRIBUTE_OUTPUT_BUF_SIZE = 6253;
  BUFFER_ATTRIBUTE_INPUT_ONBRD_BUF_SIZE = 8970;
  BUFFER_ATTRIBUTE_OUTPUT_ONBRD_BUF_SIZE = 8971;
}

enum BufferResetAttribute {
  BUFFER_RESET_ATTRIBUTE_UNSPECIFIED = 0;
  BUFFER_RESET_ATTRIBUTE_INPUT_BUF_SIZE = 6252;
  BUFFER_RESET_ATTRIBUTE_OUTPUT_BUF_SIZE = 6253;
  BUFFER_RESET_ATTRIBUTE_OUTPUT_ONBRD_BUF_SIZE = 8971;
}

enum CalibrationInfoBoolAttribute {
  CALIBRATIONINFO_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  CALIBRATIONINFO_ATTRIBUTE_SELF_CAL_SUPPORTED = 6240;
}

enum CalibrationInfoStringAttribute {
  CALIBRATIONINFO_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  CALIBRATIONINFO_ATTRIBUTE_CAL_USER_DEFINED_INFO = 6241;
}

enum CalibrationInfoDoubleAttribute {
  CALIBRATIONINFO_DOUBLE_ATTRIBUTE_UNSPECIFIED = 0;
  CALIBRATIONINFO_ATTRIBUTE_SELF_CAL_LAST_TEMP = 6244;
  CALIBRATIONINFO_ATTRIBUTE_EXT_CAL_LAST_TEMP = 6247;
  CALIBRATIONINFO_ATTRIBUTE_CAL_DEV_TEMP = 8763;
}

enum CalibrationInfoUInt32Attribute {
  CALIBRATIONINFO_UINT32_ATTRIBUTE_UNSPECIFIED = 0;
  CALIBRATIONINFO_ATTRIBUTE_EXT_CAL_RECOMMENDED_INTERVAL = 6248;
  CALIBRATIONINFO_ATTRIBUTE_CAL_USER_DEFINED_INFO_MAX_SIZE = 6428;
  CALIBRATIONINFO_ATTRIBUTE_CAL_ACC_CONNECTION_COUNT = 12267;
  CALIBRATIONINFO_ATTRIBUTE_CAL_RECOMMENDED_ACC_CONNECTION_COUNT_LIMIT = 12268;
}

enum ChannelInt32Attribute {
  CHANNEL_INT32_ATTRIBUTE_UNSPECIFIED = 0;
  CHANNEL_ATTRIBUTE_AI_RAW_SAMP_JUSTIFICATION = 80;
  CHANNEL_ATTRIBUTE_AI_COUPLING = 100;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_CFG = 135;
  CHANNEL_ATTRIBUTE_AO_DAC_REF_SRC = 306;
  CHANNEL_ATTRIBUTE_AO_DATA_XFER_MECH = 308;
  CHANNEL_ATTRIBUTE_CI_CTR_TIMEBASE_ACTIVE_EDGE = 322;
  CHANNEL_ATTRIBUTE_CI_FREQ_MEAS_METH = 324;
  CHANNEL_ATTRIBUTE_CI_OUTPUT_STATE = 329;
  CHANNEL_ATTRIBUTE_CI_DATA_XFER_MECH = 512;
  CHANNEL_ATTRIBUTE_CO_OUTPUT_STATE = 660;
  CHANNEL_ATTRIBUTE_CO_CTR_TIMEBASE_ACTIVE_EDGE = 833;
  CHANNEL_ATTRIBUTE_AI_ACCEL_UNITS = 1651;
  CHANNEL_ATTRIBUTE_AI_MEAS_TYPE = 1685;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_DIR = 1686;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_ACTIVE_EDGE = 1687;
  CHANNEL_ATTRIBUTE_AI_CURRENT_UNITS = 1793;
  CHANNEL_ATTRIBUTE_CI_FREQ_STARTING_EDGE = 1945;
  CHANNEL_ATTRIBUTE_AI_FREQ_UNITS = 2054;
  CHANNEL_ATTRIBUTE_CI_PULSE_WIDTH_UNITS = 2083;
  CHANNEL_ATTRIBUTE_CI_PULSE_WIDTH_STARTING_EDGE = 2085;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_EDGE = 2099;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_EDGE = 2100;
  CHANNEL_ATTRIBUTE_CI_PERIOD_STARTING_EDGE = 2130;
  CHANNEL_ATTRIBUTE_AI_RVDT_UNITS = 2167;
  CHANNEL_ATTRIBUTE_CI_ENCODER_Z_INDEX_PHASE = 2185;
  CHANNEL_ATTRIBUTE_AI_LVDT_UNITS = 2320;
  CHANNEL_ATTRIBUTE_AI_RESISTANCE_UNITS = 2389;
  CHANNEL_ATTRIBUTE_AI_STRAIN_UNITS = 2433;
  CHANNEL_ATTRIBUTE_AI_STRAIN_GAGE_CFG = 2434;
  CHANNEL_ATTRIBUTE_AI_RTD_TYPE = 4146;
  CHANNEL_ATTRIBUTE_AI_TEMP_UNITS = 4147;
  CHANNEL_ATTRIBUTE_AI_THRMCPL_CJC_SRC = 4149;
  CHANNEL_ATTRIBUTE_AI_THRMCPL_TYPE = 4176;
  CHANNEL_ATTRIBUTE_CI_GPS_SYNC_METHOD = 4242;
  CHANNEL_ATTRIBUTE_AI_VOLTAGE_UNITS = 4244;
  CHANNEL_ATTRIBUTE_AI_TERM_CFG = 4247;
  CHANNEL_ATTRIBUTE_AO_OUTPUT_TYPE = 4360;
  CHANNEL_ATTRIBUTE_AO_CURRENT_UNITS = 4361;
  CHANNEL_ATTRIBUTE_DO_OUTPUT_DRIVE_TYPE = 4407;
  CHANNEL_ATTRIBUTE_CO_PULSE_IDLE_STATE = 4464;
  CHANNEL_ATTRIBUTE_AO_VOLTAGE_UNITS = 4484;
  CHANNEL_ATTRIBUTE_AI_SOUND_PRESSURE_UNITS = 5416;
  CHANNEL_ATTRIBUTE_AI_AUTO_ZERO_MODE = 5984;
  CHANNEL_ATTRIBUTE_AI_RESOLUTION_UNITS = 5988;
  CHANNEL_ATTRIBUTE_AI_VOLTAGE_ACRMS_UNITS = 6114;
  CHANNEL_ATTRIBUTE_AI_CURRENT_ACRMS_UNITS = 6115;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_BALANCE_COARSE_POT = 6129;
  CHANNEL_ATTRIBUTE_AI_CURRENT_SHUNT_LOC = 6130;
  CHANNEL_ATTRIBUTE_AI_EXCIT_SRC = 6132;
  CHANNEL_ATTRIBUTE_AI_EXCIT_VOLTAGE_OR_CURRENT = 6134;
  CHANNEL_ATTRIBUTE_AI_EXCIT_D_COR_AC = 6139;
  CHANNEL_ATTRIBUTE_AI_HIGHPASS_TYPE = 6152;
  CHANNEL_ATTRIBUTE_AI_BANDPASS_TYPE = 6157;
  CHANNEL_ATTRIBUTE_AI_NOTCH_TYPE = 6161;
  CHANNEL_ATTRIBUTE_AI_DATA_XFER_MECH = 6177;
  CHANNEL_ATTRIBUTE_AO_RESOLUTION_UNITS = 6187;
  CHANNEL_ATTRIBUTE_AO_DATA_XFER_REQ_COND = 6204;
  CHANNEL_ATTRIBUTE_CHAN_TYPE = 6271;
  CHANNEL_ATTRIBUTE_AI_RESISTANCE_CFG = 6273;
  CHANNEL_ATTRIBUTE_AI_LOWPASS_SWITCH_CAP_CLK_SRC = 6276;
  CHANNEL_ATTRIBUTE_AI_DATA_XFER_REQ_COND = 6283;
  CHANNEL_ATTRIBUTE_AO_TERM_CFG = 6286;
  CHANNEL_ATTRIBUTE_CI_MEAS_TYPE = 6304;
  CHANNEL_ATTRIBUTE_CI_FREQ_UNITS = 6305;
  CHANNEL_ATTRIBUTE_CI_PERIOD_UNITS = 6307;
  CHANNEL_ATTRIBUTE_CI_ANG_ENCODER_UNITS = 6310;
  CHANNEL_ATTRIBUTE_CI_LIN_ENCODER_UNITS = 6313;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_UNITS = 6316;
  CHANNEL_ATTRIBUTE_CI_SEMI_PERIOD_UNITS = 6319;
  CHANNEL_ATTRIBUTE_CO_OUTPUT_TYPE = 6325;
  CHANNEL_ATTRIBUTE_AI_AC_EXCIT_WIRE_MODE = 6349;
  CHANNEL_ATTRIBUTE_CO_PULSE_FREQ_UNITS = 6357;
  CHANNEL_ATTRIBUTE_CO_PULSE_TIME_UNITS = 6358;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_BALANCE_FINE_POT = 6388;
  CHANNEL_ATTRIBUTE_CI_PERIOD_MEAS_METH = 6444;
  CHANNEL_ATTRIBUTE_AI_LVDT_SENSITIVITY_UNITS = 8602;
  CHANNEL_ATTRIBUTE_AI_RVDT_SENSITIVITY_UNITS = 8603;
  CHANNEL_ATTRIBUTE_AI_ACCEL_SENSITIVITY_UNITS = 8604;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SELECT = 8661;
  CHANNEL_ATTRIBUTE_CI_ENCODER_DECODING_TYPE = 8678;
  CHANNEL_ATTRIBUTE_AO_IDLE_OUTPUT_BEHAVIOR = 8768;
  CHANNEL_ATTRIBUTE_AO_DAC_OFFSET_SRC = 8787;
  CHANNEL_ATTRIBUTE_DI_DATA_XFER_MECH = 8803;
  CHANNEL_ATTRIBUTE_DI_DATA_XFER_REQ_COND = 8804;
  CHANNEL_ATTRIBUTE_DO_DATA_XFER_MECH = 8806;
  CHANNEL_ATTRIBUTE_DO_DATA_XFER_REQ_COND = 8807;
  CHANNEL_ATTRIBUTE_AI_CHAN_CAL_SCALE_TYPE = 8860;
  CHANNEL_ATTRIBUTE_CI_TIMESTAMP_UNITS = 8883;
  CHANNEL_ATTRIBUTE_AI_RAW_DATA_COMPRESSION_TYPE = 8920;
  CHANNEL_ATTRIBUTE_CI_SEMI_PERIOD_STARTING_EDGE = 8958;
  CHANNEL_ATTRIBUTE_DI_ACQUIRE_ON = 10598;
  CHANNEL_ATTRIBUTE_DO_LINE_STATES_PAUSED_STATE = 10599;
  CHANNEL_ATTRIBUTE_DO_LINE_STATES_DONE_STATE = 10600;
  CHANNEL_ATTRIBUTE_DO_GENERATE_ON = 10601;
  CHANNEL_ATTRIBUTE_DI_LOGIC_FAMILY = 10605;
  CHANNEL_ATTRIBUTE_DO_LOGIC_FAMILY = 10606;
  CHANNEL_ATTRIBUTE_DO_LINE_STATES_START_STATE = 10610;
  CHANNEL_ATTRIBUTE_AI_THRMCPL_SCALE_TYPE = 10704;
  CHANNEL_ATTRIBUTE_CO_CONSTRAINED_GEN_MODE = 10738;
  CHANNEL_ATTRIBUTE_AI_ADC_TIMING_MODE = 10745;
  CHANNEL_ATTRIBUTE_AO_FUNC_GEN_TYPE = 10776;
  CHANNEL_ATTRIBUTE_AO_FUNC_GEN_MODULATION_TYPE = 10786;
  CHANNEL_ATTRIBUTE_AI_EDDY_CURRENT_PROX_PROBE_SENSITIVITY_UNITS = 10943;
  CHANNEL_ATTRIBUTE_AI_EDDY_CURRENT_PROX_PROBE_UNITS = 10944;
  CHANNEL_ATTRIBUTE_CO_DATA_XFER_MECH = 11980;
  CHANNEL_ATTRIBUTE_CO_DATA_XFER_REQ_COND = 11981;
  CHANNEL_ATTRIBUTE_CI_DATA_XFER_REQ_COND = 12027;
  CHANNEL_ATTRIBUTE_CI_PULSE_FREQ_START_EDGE = 12037;
  CHANNEL_ATTRIBUTE_CI_PULSE_FREQ_UNITS = 12043;
  CHANNEL_ATTRIBUTE_CI_PULSE_TIME_START_EDGE = 12045;
  CHANNEL_ATTRIBUTE_CI_PULSE_TIME_UNITS = 12051;
  CHANNEL_ATTRIBUTE_CI_PULSE_TICKS_START_EDGE = 12053;
  CHANNEL_ATTRIBUTE_AI_FORCE_UNITS = 12149;
  CHANNEL_ATTRIBUTE_AI_PRESSURE_UNITS = 12150;
  CHANNEL_ATTRIBUTE_AI_TORQUE_UNITS = 12151;
  CHANNEL_ATTRIBUTE_AI_FORCE_IEPE_SENSOR_SENSITIVITY_UNITS = 12162;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_ELECTRICAL_UNITS = 12167;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_PHYSICAL_UNITS = 12168;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_SCALE_TYPE = 12169;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_UNITS = 12178;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_ACTIVE_EDGE = 12210;
  CHANNEL_ATTRIBUTE_AI_VELOCITY_UNITS = 12276;
  CHANNEL_ATTRIBUTE_AI_VELOCITY_IEPE_SENSOR_SENSITIVITY_UNITS = 12279;
  CHANNEL_ATTRIBUTE_AI_ROSETTE_STRAIN_GAGE_ROSETTE_MEAS_TYPE = 12285;
  CHANNEL_ATTRIBUTE_AI_ROSETTE_STRAIN_GAGE_ROSETTE_TYPE = 12286;
  CHANNEL_ATTRIBUTE_CI_TIMESTAMP_EDGE = 12346;
  CHANNEL_ATTRIBUTE_CI_TIMESTAMP_TIMESCALE = 12347;
  CHANNEL_ATTRIBUTE_NAV_MEAS_TYPE = 12349;
  CHANNEL_ATTRIBUTE_NAV_ALT_UNITS = 12350;
  CHANNEL_ATTRIBUTE_NAV_LAT_UNITS = 12351;
  CHANNEL_ATTRIBUTE_NAV_LONG_UNITS = 12352;
  CHANNEL_ATTRIBUTE_NAV_SPEED_OVER_GROUND_UNITS = 12353;
  CHANNEL_ATTRIBUTE_NAV_TRACK_UNITS = 12354;
  CHANNEL_ATTRIBUTE_NAV_VERT_VELOCITY_UNITS = 12355;
  CHANNEL_ATTRIBUTE_NAV_TIMESTAMP_UNITS = 12356;
  CHANNEL_ATTRIBUTE_NAV_TIMESTAMP_TIMESCALE = 12357;
  CHANNEL_ATTRIBUTE_AI_FILTER_DELAY_UNITS = 12401;
  CHANNEL_ATTRIBUTE_AO_FILTER_DELAY_UNITS = 12406;
  CHANNEL_ATTRIBUTE_CI_DUTY_CYCLE_STARTING_EDGE = 12434;
  CHANNEL_ATTRIBUTE_CI_SAMP_CLK_OVERRUN_BEHAVIOR = 12435;
  CHANNEL_ATTRIBUTE_CI_SAMP_CLK_OVERRUN_SENTINEL_VAL = 12436;
  CHANNEL_ATTRIBUTE_CI_FREQ_TERM_CFG = 12439;
  CHANNEL_ATTRIBUTE_CI_FREQ_LOGIC_LVL_BEHAVIOR = 12440;
  CHANNEL_ATTRIBUTE_CI_PERIOD_TERM_CFG = 12441;
  CHANNEL_ATTRIBUTE_CI_PERIOD_LOGIC_LVL_BEHAVIOR = 12442;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_TERM_CFG = 12443;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_LOGIC_LVL_BEHAVIOR = 12444;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_TERM_CFG = 12445;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_LOGIC_LVL_BEHAVIOR = 12446;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_TERM_CFG = 12447;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_LOGIC_LVL_BEHAVIOR = 12448;
  CHANNEL_ATTRIBUTE_CI_DUTY_CYCLE_TERM_CFG = 12449;
  CHANNEL_ATTRIBUTE_CI_DUTY_CYCLE_LOGIC_LVL_BEHAVIOR = 12450;
  CHANNEL_ATTRIBUTE_CI_ENCODER_A_INPUT_TERM_CFG = 12451;
  CHANNEL_ATTRIBUTE_CI_ENCODER_A_INPUT_LOGIC_LVL_BEHAVIOR = 12452;
  CHANNEL_ATTRIBUTE_CI_ENCODER_B_INPUT_TERM_CFG = 12453;
  CHANNEL_ATTRIBUTE_CI_ENCODER_B_INPUT_LOGIC_LVL_BEHAVIOR = 12454;
  CHANNEL_ATTRIBUTE_CI_ENCODER_Z_INPUT_TERM_CFG = 12455;
  CHANNEL_ATTRIBUTE_CI_ENCODER_Z_INPUT_LOGIC_LVL_BEHAVIOR = 12456;
  CHANNEL_ATTRIBUTE_CI_PULSE_WIDTH_TERM_CFG = 12457;
  CHANNEL_ATTRIBUTE_CI_PULSE_WIDTH_LOGIC_LVL_BEHAVIOR = 12458;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_TERM_CFG = 12459;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_LOGIC_LVL_BEHAVIOR = 12460;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_TERM_CFG = 12461;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_LOGIC_LVL_BEHAVIOR = 12462;
  CHANNEL_ATTRIBUTE_CI_SEMI_PERIOD_TERM_CFG = 12463;
  CHANNEL_ATTRIBUTE_CI_SEMI_PERIOD_LOGIC_LVL_BEHAVIOR = 12464;
  CHANNEL_ATTRIBUTE_CI_PULSE_FREQ_TERM_CFG = 12465;
  CHANNEL_ATTRIBUTE_CI_PULSE_FREQ_LOGIC_LVL_BEHAVIOR = 12466;
  CHANNEL_ATTRIBUTE_CI_PULSE_TIME_TERM_CFG = 12467;
  CHANNEL_ATTRIBUTE_CI_PULSE_TIME_LOGIC_LVL_BEHAVIOR = 12468;
  CHANNEL_ATTRIBUTE_CI_PULSE_TICKS_TERM_CFG = 12469;
  CHANNEL_ATTRIBUTE_CI_PULSE_TICKS_LOGIC_LVL_BEHAVIOR = 12470;
  CHANNEL_ATTRIBUTE_AI_EXCIT_IDLE_OUTPUT_BEHAVIOR = 12472;
  CHANNEL_ATTRIBUTE_AI_DIG_FLTR_TYPE = 12478;
  CHANNEL_ATTRIBUTE_AI_DIG_FLTR_RESPONSE = 12479;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SHUNT_CAL_A_SRC = 12490;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ANG_ENCODER_UNITS = 12504;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_LIN_ENCODER_UNITS = 12506;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_DECODING_TYPE = 12508;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_TERM_CFG = 12510;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_LOGIC_LVL_BEHAVIOR = 12511;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_TERM_CFG = 12517;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_LOGIC_LVL_BEHAVIOR = 12518;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_GATE_TERM_CFG = 12527;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_GATE_LOGIC_LVL_BEHAVIOR = 12528;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_GATE_WHEN = 12533;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SHUNT_CAL_B_SRC = 12535;
  CHANNEL_ATTRIBUTE_AI_EXCIT_SENSE = 12541;
  CHANNEL_ATTRIBUTE_AI_CHARGE_UNITS = 12562;
  CHANNEL_ATTRIBUTE_AI_ACCEL_CHARGE_SENSITIVITY_UNITS = 12564;
  CHANNEL_ATTRIBUTE_AI_ACCEL_4_WIRE_DC_VOLTAGE_SENSITIVITY_UNITS = 12566;
  CHANNEL_ATTRIBUTE_CHAN_SYNC_UNLOCK_BEHAVIOR = 12604;
  CHANNEL_ATTRIBUTE_AI_SENSOR_POWER_CFG = 12650;
  CHANNEL_ATTRIBUTE_AI_SENSOR_POWER_TYPE = 12651;
  CHANNEL_ATTRIBUTE_AI_FILTER_RESPONSE = 12661;
  CHANNEL_ATTRIBUTE_CI_FILTER_RESPONSE = 12729;
  CHANNEL_ATTRIBUTE_CI_FILTER_DELAY_UNITS = 12732;
  CHANNEL_ATTRIBUTE_PWR_OUTPUT_STATE = 12759;
  CHANNEL_ATTRIBUTE_PWR_IDLE_OUTPUT_BEHAVIOR = 12760;
  CHANNEL_ATTRIBUTE_PWR_REMOTE_SENSE = 12763;
}

enum ChannelDoubleAttribute {
  CHANNEL_DOUBLE_ATTRIBUTE_UNSPECIFIED = 0;
  CHANNEL_ATTRIBUTE_AI_IMPEDANCE = 98;
  CHANNEL_ATTRIBUTE_AI_AC_EXCIT_FREQ = 257;
  CHANNEL_ATTRIBUTE_AO_GAIN = 280;
  CHANNEL_ATTRIBUTE_AO_LOAD_IMPEDANCE = 289;
  CHANNEL_ATTRIBUTE_CI_FREQ_MEAS_TIME = 325;
  CHANNEL_ATTRIBUTE_CO_PULSE_FREQ_INITIAL_DELAY = 665;
  CHANNEL_ATTRIBUTE_AI_ACCEL_SENSITIVITY = 1682;
  CHANNEL_ATTRIBUTE_AI_FREQ_HYST = 2068;
  CHANNEL_ATTRIBUTE_AI_FREQ_THRESH_VOLTAGE = 2069;
  CHANNEL_ATTRIBUTE_CI_ANG_ENCODER_INITIAL_ANGLE = 2177;
  CHANNEL_ATTRIBUTE_CI_ENCODER_Z_INDEX_VAL = 2184;
  CHANNEL_ATTRIBUTE_AI_RVDT_SENSITIVITY = 2307;
  CHANNEL_ATTRIBUTE_CI_LIN_ENCODER_DIST_PER_PULSE = 2321;
  CHANNEL_ATTRIBUTE_CI_LIN_ENCODER_INITIAL_POS = 2325;
  CHANNEL_ATTRIBUTE_AI_LVDT_SENSITIVITY = 2361;
  CHANNEL_ATTRIBUTE_AI_STRAIN_GAGE_GAGE_FACTOR = 2452;
  CHANNEL_ATTRIBUTE_AI_STRAIN_GAGE_POISSON_RATIO = 2456;
  CHANNEL_ATTRIBUTE_AI_RTD_A = 4112;
  CHANNEL_ATTRIBUTE_AI_RTD_B = 4113;
  CHANNEL_ATTRIBUTE_AI_RTD_C = 4115;
  CHANNEL_ATTRIBUTE_AI_RTD_R0 = 4144;
  CHANNEL_ATTRIBUTE_AI_THRMCPL_CJC_VAL = 4150;
  CHANNEL_ATTRIBUTE_AI_THRMSTR_R1 = 4193;
  CHANNEL_ATTRIBUTE_CO_PULSE_DUTY_CYC = 4470;
  CHANNEL_ATTRIBUTE_CO_PULSE_FREQ = 4472;
  CHANNEL_ATTRIBUTE_AO_MAX = 4486;
  CHANNEL_ATTRIBUTE_AO_MIN = 4487;
  CHANNEL_ATTRIBUTE_AO_OUTPUT_IMPEDANCE = 5264;
  CHANNEL_ATTRIBUTE_AI_MICROPHONE_SENSITIVITY = 5430;
  CHANNEL_ATTRIBUTE_AI_RESOLUTION = 5989;
  CHANNEL_ATTRIBUTE_AI_MAX = 6109;
  CHANNEL_ATTRIBUTE_AI_MIN = 6110;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_NOM_RESISTANCE = 6124;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_INITIAL_VOLTAGE = 6125;
  CHANNEL_ATTRIBUTE_AI_LEAD_WIRE_RESISTANCE = 6126;
  CHANNEL_ATTRIBUTE_AI_CURRENT_SHUNT_RESISTANCE = 6131;
  CHANNEL_ATTRIBUTE_AI_EXCIT_VAL = 6133;
  CHANNEL_ATTRIBUTE_AI_ATTEN = 6145;
  CHANNEL_ATTRIBUTE_AI_LOWPASS_CUTOFF_FREQ = 6147;
  CHANNEL_ATTRIBUTE_AI_HIGHPASS_CUTOFF_FREQ = 6151;
  CHANNEL_ATTRIBUTE_AI_BANDPASS_CENTER_FREQ = 6156;
  CHANNEL_ATTRIBUTE_AI_BANDPASS_WIDTH = 6158;
  CHANNEL_ATTRIBUTE_AI_NOTCH_CENTER_FREQ = 6160;
  CHANNEL_ATTRIBUTE_AI_NOTCH_WIDTH = 6162;
  CHANNEL_ATTRIBUTE_AI_RNG_HIGH = 6165;
  CHANNEL_ATTRIBUTE_AI_RNG_LOW = 6166;
  CHANNEL_ATTRIBUTE_AI_GAIN = 6168;
  CHANNEL_ATTRIBUTE_AO_RESOLUTION = 6188;
  CHANNEL_ATTRIBUTE_AO_DAC_RNG_LOW = 6189;
  CHANNEL_ATTRIBUTE_AO_DAC_RNG_HIGH = 6190;
  CHANNEL_ATTRIBUTE_AO_DAC_REF_VAL = 6194;
  CHANNEL_ATTRIBUTE_AI_EXCIT_ACTUAL_VAL = 6275;
  CHANNEL_ATTRIBUTE_AI_LOWPASS_SWITCH_CAP_EXT_CLK_FREQ = 6277;
  CHANNEL_ATTRIBUTE_CI_MAX = 6300;
  CHANNEL_ATTRIBUTE_CI_MIN = 6301;
  CHANNEL_ATTRIBUTE_CI_CTR_TIMEBASE_RATE = 6322;
  CHANNEL_ATTRIBUTE_CO_PULSE_HIGH_TIME = 6330;
  CHANNEL_ATTRIBUTE_CO_PULSE_LOW_TIME = 6331;
  CHANNEL_ATTRIBUTE_CO_PULSE_TIME_INITIAL_DELAY = 6332;
  CHANNEL_ATTRIBUTE_CO_CTR_TIMEBASE_RATE = 6338;
  CHANNEL_ATTRIBUTE_AI_THRMSTR_A = 6345;
  CHANNEL_ATTRIBUTE_AI_THRMSTR_C = 6346;
  CHANNEL_ATTRIBUTE_AI_THRMSTR_B = 6347;
  CHANNEL_ATTRIBUTE_CI_PERIOD_MEAS_TIME = 6445;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_GAIN_ADJUST = 6463;
  CHANNEL_ATTRIBUTE_DI_DIG_FLTR_MIN_PULSE_WIDTH = 8663;
  CHANNEL_ATTRIBUTE_CI_FREQ_DIG_FLTR_MIN_PULSE_WIDTH = 8680;
  CHANNEL_ATTRIBUTE_CI_FREQ_DIG_FLTR_TIMEBASE_RATE = 8682;
  CHANNEL_ATTRIBUTE_CI_PERIOD_DIG_FLTR_MIN_PULSE_WIDTH = 8685;
  CHANNEL_ATTRIBUTE_CI_PERIOD_DIG_FLTR_TIMEBASE_RATE = 8687;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_DIG_FLTR_MIN_PULSE_WIDTH = 8690;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_DIG_FLTR_TIMEBASE_RATE = 8692;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_DIG_FLTR_MIN_PULSE_WIDTH = 8695;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_DIG_FLTR_TIMEBASE_RATE = 8697;
  CHANNEL_ATTRIBUTE_CI_ENCODER_A_INPUT_DIG_FLTR_MIN_PULSE_WIDTH = 8700;
  CHANNEL_ATTRIBUTE_CI_ENCODER_A_INPUT_DIG_FLTR_TIMEBASE_RATE = 8702;
  CHANNEL_ATTRIBUTE_CI_ENCODER_B_INPUT_DIG_FLTR_MIN_PULSE_WIDTH = 8705;
  CHANNEL_ATTRIBUTE_CI_ENCODER_B_INPUT_DIG_FLTR_TIMEBASE_RATE = 8707;
  CHANNEL_ATTRIBUTE_CI_ENCODER_Z_INPUT_DIG_FLTR_MIN_PULSE_WIDTH = 8710;
  CHANNEL_ATTRIBUTE_CI_ENCODER_Z_INPUT_DIG_FLTR_TIMEBASE_RATE = 8712;
  CHANNEL_ATTRIBUTE_CI_PULSE_WIDTH_DIG_FLTR_MIN_PULSE_WIDTH = 8715;
  CHANNEL_ATTRIBUTE_CI_PULSE_WIDTH_DIG_FLTR_TIMEBASE_RATE = 8717;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_DIG_FLTR_MIN_PULSE_WIDTH = 8720;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_DIG_FLTR_TIMEBASE_RATE = 8722;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_DIG_FLTR_MIN_PULSE_WIDTH = 8725;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_DIG_FLTR_TIMEBASE_RATE = 8727;
  CHANNEL_ATTRIBUTE_CI_SEMI_PERIOD_DIG_FLTR_MIN_PULSE_WIDTH = 8730;
  CHANNEL_ATTRIBUTE_CI_SEMI_PERIOD_DIG_FLTR_TIMEBASE_RATE = 8732;
  CHANNEL_ATTRIBUTE_AI_SOUND_PRESSURE_MAX_SOUND_PRESSURE_LVL = 8762;
  CHANNEL_ATTRIBUTE_AO_DAC_OFFSET_VAL = 8789;
  CHANNEL_ATTRIBUTE_CI_CTR_TIMEBASE_DIG_FLTR_MIN_PULSE_WIDTH = 8818;
  CHANNEL_ATTRIBUTE_CI_CTR_TIMEBASE_DIG_FLTR_TIMEBASE_RATE = 8820;
  CHANNEL_ATTRIBUTE_CO_CTR_TIMEBASE_DIG_FLTR_MIN_PULSE_WIDTH = 8823;
  CHANNEL_ATTRIBUTE_CO_CTR_TIMEBASE_DIG_FLTR_TIMEBASE_RATE = 8825;
  CHANNEL_ATTRIBUTE_AI_VOLTAGE_DB_REF = 10672;
  CHANNEL_ATTRIBUTE_AI_SOUND_PRESSURE_DB_REF = 10673;
  CHANNEL_ATTRIBUTE_AI_ACCEL_DB_REF = 10674;
  CHANNEL_ATTRIBUTE_AO_FUNC_GEN_FREQ = 10777;
  CHANNEL_ATTRIBUTE_AO_FUNC_GEN_AMPLITUDE = 10778;
  CHANNEL_ATTRIBUTE_AO_FUNC_GEN_OFFSET = 10779;
  CHANNEL_ATTRIBUTE_AO_FUNC_GEN_SQUARE_DUTY_CYCLE = 10780;
  CHANNEL_ATTRIBUTE_AO_VOLTAGE_CURRENT_LIMIT = 10781;
  CHANNEL_ATTRIBUTE_AO_FUNC_GEN_FM_DEVIATION = 10787;
  CHANNEL_ATTRIBUTE_DO_OVERCURRENT_LIMIT = 10885;
  CHANNEL_ATTRIBUTE_DO_OVERCURRENT_REENABLE_PERIOD = 10887;
  CHANNEL_ATTRIBUTE_AI_PROBE_ATTEN = 10888;
  CHANNEL_ATTRIBUTE_AI_DC_OFFSET = 10889;
  CHANNEL_ATTRIBUTE_AI_EDDY_CURRENT_PROX_PROBE_SENSITIVITY = 10942;
  CHANNEL_ATTRIBUTE_DI_DIG_FLTR_TIMEBASE_RATE = 11989;
  CHANNEL_ATTRIBUTE_CI_PULSE_FREQ_DIG_FLTR_MIN_PULSE_WIDTH = 12039;
  CHANNEL_ATTRIBUTE_CI_PULSE_FREQ_DIG_FLTR_TIMEBASE_RATE = 12041;
  CHANNEL_ATTRIBUTE_CI_PULSE_TIME_DIG_FLTR_MIN_PULSE_WIDTH = 12047;
  CHANNEL_ATTRIBUTE_CI_PULSE_TIME_DIG_FLTR_TIMEBASE_RATE = 12049;
  CHANNEL_ATTRIBUTE_CI_PULSE_TICKS_DIG_FLTR_MIN_PULSE_WIDTH = 12055;
  CHANNEL_ATTRIBUTE_CI_PULSE_TICKS_DIG_FLTR_TIMEBASE_RATE = 12057;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SHUNT_CAL_A_RESISTANCE = 12152;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SHUNT_CAL_A_ACTUAL_RESISTANCE = 12153;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SHUNT_CAL_B_RESISTANCE = 12154;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SHUNT_CAL_B_ACTUAL_RESISTANCE = 12155;
  CHANNEL_ATTRIBUTE_AI_FORCE_IEPE_SENSOR_SENSITIVITY = 12161;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_INITIAL_RATIO = 12166;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_TWO_POINT_LIN_FIRST_ELECTRICAL_VAL = 12170;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_TWO_POINT_LIN_FIRST_PHYSICAL_VAL = 12171;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_TWO_POINT_LIN_SECOND_ELECTRICAL_VAL = 12172;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_TWO_POINT_LIN_SECOND_PHYSICAL_VAL = 12173;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_DIG_FLTR_MIN_PULSE_WIDTH = 12212;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_DIG_FLTR_TIMEBASE_RATE = 12214;
  CHANNEL_ATTRIBUTE_AI_THRMCPL_LEAD_OFFSET_VOLTAGE = 12216;
  CHANNEL_ATTRIBUTE_AI_FILTER_DELAY = 12269;
  CHANNEL_ATTRIBUTE_AI_VELOCITY_IEPE_SENSOR_DB_REF = 12277;
  CHANNEL_ATTRIBUTE_AI_VELOCITY_IEPE_SENSOR_SENSITIVITY = 12278;
  CHANNEL_ATTRIBUTE_AI_ROSETTE_STRAIN_GAGE_ORIENTATION = 12284;
  CHANNEL_ATTRIBUTE_AO_FILTER_DELAY_ADJUSTMENT = 12402;
  CHANNEL_ATTRIBUTE_AI_FILTER_DELAY_ADJUSTMENT = 12404;
  CHANNEL_ATTRIBUTE_AO_FILTER_DELAY = 12405;
  CHANNEL_ATTRIBUTE_CI_DUTY_CYCLE_DIG_FLTR_MIN_PULSE_WIDTH = 12431;
  CHANNEL_ATTRIBUTE_CI_DUTY_CYCLE_DIG_FLTR_TIMEBASE_RATE = 12433;
  CHANNEL_ATTRIBUTE_CI_MAX_MEAS_PERIOD = 12437;
  CHANNEL_ATTRIBUTE_CI_THRESH_VOLTAGE = 12471;
  CHANNEL_ATTRIBUTE_AI_DIG_FLTR_LOWPASS_CUTOFF_FREQ = 12481;
  CHANNEL_ATTRIBUTE_AI_DIG_FLTR_HIGHPASS_CUTOFF_FREQ = 12482;
  CHANNEL_ATTRIBUTE_AI_DIG_FLTR_BANDPASS_CENTER_FREQ = 12483;
  CHANNEL_ATTRIBUTE_AI_DIG_FLTR_BANDPASS_WIDTH = 12484;
  CHANNEL_ATTRIBUTE_AI_DIG_FLTR_NOTCH_CENTER_FREQ = 12485;
  CHANNEL_ATTRIBUTE_AI_DIG_FLTR_NOTCH_WIDTH = 12486;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_LIN_ENCODER_DIST_PER_PULSE = 12507;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_DIG_FLTR_MIN_PULSE_WIDTH = 12513;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_DIG_FLTR_TIMEBASE_RATE = 12515;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_DIG_FLTR_MIN_PULSE_WIDTH = 12520;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_DIG_FLTR_TIMEBASE_RATE = 12522;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_MEAS_TIME = 12523;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_GATE_DIG_FLTR_MIN_PULSE_WIDTH = 12530;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_GATE_DIG_FLTR_TIMEBASE_RATE = 12532;
  CHANNEL_ATTRIBUTE_AI_ACCEL_CHARGE_SENSITIVITY = 12563;
  CHANNEL_ATTRIBUTE_AI_ACCEL_4_WIRE_DC_VOLTAGE_SENSITIVITY = 12565;
  CHANNEL_ATTRIBUTE_AI_DATA_XFER_MAX_RATE = 12567;
  CHANNEL_ATTRIBUTE_AI_SENSOR_POWER_VOLTAGE = 12649;
  CHANNEL_ATTRIBUTE_AI_FILTER_FREQ = 12660;
  CHANNEL_ATTRIBUTE_AI_INPUT_LIMITS_FAULT_DETECT_UPPER_LIMIT = 12684;
  CHANNEL_ATTRIBUTE_AI_INPUT_LIMITS_FAULT_DETECT_LOWER_LIMIT = 12685;
  CHANNEL_ATTRIBUTE_CI_FREQ_THRESH_VOLTAGE = 12715;
  CHANNEL_ATTRIBUTE_CI_FREQ_HYST = 12716;
  CHANNEL_ATTRIBUTE_CI_PERIOD_THRESH_VOLTAGE = 12717;
  CHANNEL_ATTRIBUTE_CI_PERIOD_HYST = 12718;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_THRESH_VOLTAGE = 12719;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_HYST = 12720;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_THRESH_VOLTAGE = 12721;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_HYST = 12722;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_THRESH_VOLTAGE = 12723;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_HYST = 12724;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_GATE_THRESH_VOLTAGE = 12725;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_GATE_HYST = 12726;
  CHANNEL_ATTRIBUTE_CI_FILTER_FREQ = 12728;
  CHANNEL_ATTRIBUTE_CI_FILTER_DELAY = 12731;
  CHANNEL_ATTRIBUTE_AO_FUNC_GEN_START_PHASE = 12740;
  CHANNEL_ATTRIBUTE_AO_COMMON_MODE_OFFSET = 12748;
  CHANNEL_ATTRIBUTE_PWR_VOLTAGE_SETPOINT = 12756;
  CHANNEL_ATTRIBUTE_PWR_CURRENT_SETPOINT = 12757;
}

enum ChannelResetAttribute {
  CHANNEL_RESET_ATTRIBUTE_UNSPECIFIED = 0;
  CHANNEL_RESET_ATTRIBUTE_AI_IMPEDANCE = 98;
  CHANNEL_RESET_ATTRIBUTE_AI_COUPLING = 100;
  CHANNEL_RESET_ATTRIBUTE_AI_DITHER_ENABLE = 104;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_CFG = 135;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_ENABLE = 148;
  CHANNEL_RESET_ATTRIBUTE_AI_AC_EXCIT_FREQ = 257;
  CHANNEL_RESET_ATTRIBUTE_AI_AC_EXCIT_SYNC_ENABLE = 258;
  CHANNEL_RESET_ATTRIBUTE_AO_GAIN = 280;
  CHANNEL_RESET_ATTRIBUTE_AO_LOAD_IMPEDANCE = 289;
  CHANNEL_RESET_ATTRIBUTE_AO_DAC_REF_CONN_TO_GND = 304;
  CHANNEL_RESET_ATTRIBUTE_AO_DAC_REF_SRC = 306;
  CHANNEL_RESET_ATTRIBUTE_AO_REGLITCH_ENABLE = 307;
  CHANNEL_RESET_ATTRIBUTE_AO_DATA_XFER_MECH = 308;
  CHANNEL_RESET_ATTRIBUTE_CI_CTR_TIMEBASE_ACTIVE_EDGE = 322;
  CHANNEL_RESET_ATTRIBUTE_CI_CTR_TIMEBASE_SRC = 323;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_MEAS_METH = 324;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_MEAS_TIME = 325;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_DIV = 327;
  CHANNEL_RESET_ATTRIBUTE_CI_DATA_XFER_MECH = 512;
  CHANNEL_RESET_ATTRIBUTE_CO_AUTO_INCR_CNT = 661;
  CHANNEL_RESET_ATTRIBUTE_CO_PULSE_TICKS_INITIAL_DELAY = 664;
  CHANNEL_RESET_ATTRIBUTE_CO_PULSE_FREQ_INITIAL_DELAY = 665;
  CHANNEL_RESET_ATTRIBUTE_CO_CTR_TIMEBASE_SRC = 825;
  CHANNEL_RESET_ATTRIBUTE_CO_CTR_TIMEBASE_ACTIVE_EDGE = 833;
  CHANNEL_RESET_ATTRIBUTE_AI_ACCEL_UNITS = 1651;
  CHANNEL_RESET_ATTRIBUTE_AI_ACCEL_SENSITIVITY = 1682;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_DIR = 1686;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_ACTIVE_EDGE = 1687;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_INITIAL_CNT = 1688;
  CHANNEL_RESET_ATTRIBUTE_AI_CURRENT_UNITS = 1793;
  CHANNEL_RESET_ATTRIBUTE_DI_INVERT_LINES = 1939;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_STARTING_EDGE = 1945;
  CHANNEL_RESET_ATTRIBUTE_AI_FREQ_UNITS = 2054;
  CHANNEL_RESET_ATTRIBUTE_AI_FREQ_HYST = 2068;
  CHANNEL_RESET_ATTRIBUTE_AI_FREQ_THRESH_VOLTAGE = 2069;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_WIDTH_UNITS = 2083;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_WIDTH_STARTING_EDGE = 2085;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_EDGE = 2099;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_EDGE = 2100;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_STARTING_EDGE = 2130;
  CHANNEL_RESET_ATTRIBUTE_CI_ANG_ENCODER_PULSES_PER_REV = 2165;
  CHANNEL_RESET_ATTRIBUTE_AI_RVDT_UNITS = 2167;
  CHANNEL_RESET_ATTRIBUTE_CI_ANG_ENCODER_INITIAL_ANGLE = 2177;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_Z_INDEX_VAL = 2184;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_Z_INDEX_PHASE = 2185;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_Z_INDEX_ENABLE = 2192;
  CHANNEL_RESET_ATTRIBUTE_AI_RVDT_SENSITIVITY = 2307;
  CHANNEL_RESET_ATTRIBUTE_AI_LVDT_UNITS = 2320;
  CHANNEL_RESET_ATTRIBUTE_CI_LIN_ENCODER_DIST_PER_PULSE = 2321;
  CHANNEL_RESET_ATTRIBUTE_CI_LIN_ENCODER_INITIAL_POS = 2325;
  CHANNEL_RESET_ATTRIBUTE_AI_LVDT_SENSITIVITY = 2361;
  CHANNEL_RESET_ATTRIBUTE_AI_RESISTANCE_UNITS = 2389;
  CHANNEL_RESET_ATTRIBUTE_AI_STRAIN_UNITS = 2433;
  CHANNEL_RESET_ATTRIBUTE_AI_STRAIN_GAGE_CFG = 2434;
  CHANNEL_RESET_ATTRIBUTE_AI_STRAIN_GAGE_GAGE_FACTOR = 2452;
  CHANNEL_RESET_ATTRIBUTE_AI_STRAIN_GAGE_POISSON_RATIO = 2456;
  CHANNEL_RESET_ATTRIBUTE_AI_RTD_A = 4112;
  CHANNEL_RESET_ATTRIBUTE_AI_RTD_B = 4113;
  CHANNEL_RESET_ATTRIBUTE_AI_RTD_C = 4115;
  CHANNEL_RESET_ATTRIBUTE_AI_RTD_R0 = 4144;
  CHANNEL_RESET_ATTRIBUTE_AI_RTD_TYPE = 4146;
  CHANNEL_RESET_ATTRIBUTE_AI_TEMP_UNITS = 4147;
  CHANNEL_RESET_ATTRIBUTE_AI_THRMCPL_CJC_VAL = 4150;
  CHANNEL_RESET_ATTRIBUTE_AI_THRMCPL_TYPE = 4176;
  CHANNEL_RESET_ATTRIBUTE_AI_THRMSTR_R1 = 4193;
  CHANNEL_RESET_ATTRIBUTE_CI_GPS_SYNC_METHOD = 4242;
  CHANNEL_RESET_ATTRIBUTE_CI_GPS_SYNC_SRC = 4243;
  CHANNEL_RESET_ATTRIBUTE_AI_VOLTAGE_UNITS = 4244;
  CHANNEL_RESET_ATTRIBUTE_AI_TERM_CFG = 4247;
  CHANNEL_RESET_ATTRIBUTE_AO_CURRENT_UNITS = 4361;
  CHANNEL_RESET_ATTRIBUTE_DO_INVERT_LINES = 4403;
  CHANNEL_RESET_ATTRIBUTE_DO_OUTPUT_DRIVE_TYPE = 4407;
  CHANNEL_RESET_ATTRIBUTE_CO_PULSE_HIGH_TICKS = 4457;
  CHANNEL_RESET_ATTRIBUTE_CO_PULSE_IDLE_STATE = 4464;
  CHANNEL_RESET_ATTRIBUTE_CO_PULSE_LOW_TICKS = 4465;
  CHANNEL_RESET_ATTRIBUTE_CO_PULSE_DUTY_CYC = 4470;
  CHANNEL_RESET_ATTRIBUTE_CO_PULSE_FREQ = 4472;
  CHANNEL_RESET_ATTRIBUTE_AO_VOLTAGE_UNITS = 4484;
  CHANNEL_RESET_ATTRIBUTE_AO_MAX = 4486;
  CHANNEL_RESET_ATTRIBUTE_AO_MIN = 4487;
  CHANNEL_RESET_ATTRIBUTE_AO_CUSTOM_SCALE_NAME = 4488;
  CHANNEL_RESET_ATTRIBUTE_AO_OUTPUT_IMPEDANCE = 5264;
  CHANNEL_RESET_ATTRIBUTE_AI_SOUND_PRESSURE_UNITS = 5416;
  CHANNEL_RESET_ATTRIBUTE_AI_MICROPHONE_SENSITIVITY = 5430;
  CHANNEL_RESET_ATTRIBUTE_AI_AUTO_ZERO_MODE = 5984;
  CHANNEL_RESET_ATTRIBUTE_AI_MAX = 6109;
  CHANNEL_RESET_ATTRIBUTE_AI_MIN = 6110;
  CHANNEL_RESET_ATTRIBUTE_AI_CUSTOM_SCALE_NAME = 6112;
  CHANNEL_RESET_ATTRIBUTE_AI_VOLTAGE_ACRMS_UNITS = 6114;
  CHANNEL_RESET_ATTRIBUTE_AI_CURRENT_ACRMS_UNITS = 6115;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_NOM_RESISTANCE = 6124;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_INITIAL_VOLTAGE = 6125;
  CHANNEL_RESET_ATTRIBUTE_AI_LEAD_WIRE_RESISTANCE = 6126;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_BALANCE_COARSE_POT = 6129;
  CHANNEL_RESET_ATTRIBUTE_AI_CURRENT_SHUNT_LOC = 6130;
  CHANNEL_RESET_ATTRIBUTE_AI_CURRENT_SHUNT_RESISTANCE = 6131;
  CHANNEL_RESET_ATTRIBUTE_AI_EXCIT_SRC = 6132;
  CHANNEL_RESET_ATTRIBUTE_AI_EXCIT_VAL = 6133;
  CHANNEL_RESET_ATTRIBUTE_AI_EXCIT_VOLTAGE_OR_CURRENT = 6134;
  CHANNEL_RESET_ATTRIBUTE_AI_EXCIT_D_COR_AC = 6139;
  CHANNEL_RESET_ATTRIBUTE_AI_EXCIT_USE_FOR_SCALING = 6140;
  CHANNEL_RESET_ATTRIBUTE_AI_ATTEN = 6145;
  CHANNEL_RESET_ATTRIBUTE_AI_LOWPASS_ENABLE = 6146;
  CHANNEL_RESET_ATTRIBUTE_AI_LOWPASS_CUTOFF_FREQ = 6147;
  CHANNEL_RESET_ATTRIBUTE_AI_HIGHPASS_ENABLE = 6150;
  CHANNEL_RESET_ATTRIBUTE_AI_HIGHPASS_CUTOFF_FREQ = 6151;
  CHANNEL_RESET_ATTRIBUTE_AI_HIGHPASS_TYPE = 6152;
  CHANNEL_RESET_ATTRIBUTE_AI_HIGHPASS_ORDER = 6153;
  CHANNEL_RESET_ATTRIBUTE_AI_BANDPASS_ENABLE = 6155;
  CHANNEL_RESET_ATTRIBUTE_AI_BANDPASS_CENTER_FREQ = 6156;
  CHANNEL_RESET_ATTRIBUTE_AI_BANDPASS_TYPE = 6157;
  CHANNEL_RESET_ATTRIBUTE_AI_BANDPASS_WIDTH = 6158;
  CHANNEL_RESET_ATTRIBUTE_AI_NOTCH_CENTER_FREQ = 6160;
  CHANNEL_RESET_ATTRIBUTE_AI_NOTCH_TYPE = 6161;
  CHANNEL_RESET_ATTRIBUTE_AI_NOTCH_WIDTH = 6162;
  CHANNEL_RESET_ATTRIBUTE_AI_RNG_HIGH = 6165;
  CHANNEL_RESET_ATTRIBUTE_AI_RNG_LOW = 6166;
  CHANNEL_RESET_ATTRIBUTE_AI_GAIN = 6168;
  CHANNEL_RESET_ATTRIBUTE_AI_SAMP_AND_HOLD_ENABLE = 6170;
  CHANNEL_RESET_ATTRIBUTE_AI_DATA_XFER_MECH = 6177;
  CHANNEL_RESET_ATTRIBUTE_AO_RESOLUTION_UNITS = 6187;
  CHANNEL_RESET_ATTRIBUTE_AO_DAC_RNG_LOW = 6189;
  CHANNEL_RESET_ATTRIBUTE_AO_DAC_RNG_HIGH = 6190;
  CHANNEL_RESET_ATTRIBUTE_AO_DAC_REF_ALLOW_CONN_TO_GND = 6192;
  CHANNEL_RESET_ATTRIBUTE_AO_DAC_REF_VAL = 6194;
  CHANNEL_RESET_ATTRIBUTE_AO_USE_ONLY_ON_BRD_MEM = 6202;
  CHANNEL_RESET_ATTRIBUTE_AO_DATA_XFER_REQ_COND = 6204;
  CHANNEL_RESET_ATTRIBUTE_AI_RESISTANCE_CFG = 6273;
  CHANNEL_RESET_ATTRIBUTE_AI_EXCIT_ACTUAL_VAL = 6275;
  CHANNEL_RESET_ATTRIBUTE_AI_LOWPASS_SWITCH_CAP_CLK_SRC = 6276;
  CHANNEL_RESET_ATTRIBUTE_AI_LOWPASS_SWITCH_CAP_EXT_CLK_FREQ = 6277;
  CHANNEL_RESET_ATTRIBUTE_AI_LOWPASS_SWITCH_CAP_EXT_CLK_DIV = 6278;
  CHANNEL_RESET_ATTRIBUTE_AI_LOWPASS_SWITCH_CAP_OUT_CLK_DIV = 6279;
  CHANNEL_RESET_ATTRIBUTE_AI_DATA_XFER_REQ_COND = 6283;
  CHANNEL_RESET_ATTRIBUTE_AI_MEM_MAP_ENABLE = 6284;
  CHANNEL_RESET_ATTRIBUTE_AO_TERM_CFG = 6286;
  CHANNEL_RESET_ATTRIBUTE_AO_MEM_MAP_ENABLE = 6287;
  CHANNEL_RESET_ATTRIBUTE_DI_TRISTATE = 6288;
  CHANNEL_RESET_ATTRIBUTE_CI_MAX = 6300;
  CHANNEL_RESET_ATTRIBUTE_CI_MIN = 6301;
  CHANNEL_RESET_ATTRIBUTE_CI_CUSTOM_SCALE_NAME = 6302;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_UNITS = 6305;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_TERM = 6306;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_UNITS = 6307;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_TERM = 6308;
  CHANNEL_RESET_ATTRIBUTE_CI_ANG_ENCODER_UNITS = 6310;
  CHANNEL_RESET_ATTRIBUTE_CI_LIN_ENCODER_UNITS = 6313;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_WIDTH_TERM = 6314;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_UNITS = 6316;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_TERM = 6317;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_TERM = 6318;
  CHANNEL_RESET_ATTRIBUTE_CI_SEMI_PERIOD_UNITS = 6319;
  CHANNEL_RESET_ATTRIBUTE_CI_SEMI_PERIOD_TERM = 6320;
  CHANNEL_RESET_ATTRIBUTE_CI_CTR_TIMEBASE_RATE = 6322;
  CHANNEL_RESET_ATTRIBUTE_CI_CTR_TIMEBASE_MASTER_TIMEBASE_DIV = 6323;
  CHANNEL_RESET_ATTRIBUTE_CO_PULSE_HIGH_TIME = 6330;
  CHANNEL_RESET_ATTRIBUTE_CO_PULSE_LOW_TIME = 6331;
  CHANNEL_RESET_ATTRIBUTE_CO_PULSE_TIME_INITIAL_DELAY = 6332;
  CHANNEL_RESET_ATTRIBUTE_CO_CTR_TIMEBASE_RATE = 6338;
  CHANNEL_RESET_ATTRIBUTE_CO_CTR_TIMEBASE_MASTER_TIMEBASE_DIV = 6339;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_TERM = 6343;
  CHANNEL_RESET_ATTRIBUTE_AI_THRMSTR_A = 6345;
  CHANNEL_RESET_ATTRIBUTE_AI_THRMSTR_C = 6346;
  CHANNEL_RESET_ATTRIBUTE_AI_THRMSTR_B = 6347;
  CHANNEL_RESET_ATTRIBUTE_AI_AC_EXCIT_WIRE_MODE = 6349;
  CHANNEL_RESET_ATTRIBUTE_CO_PULSE_FREQ_UNITS = 6357;
  CHANNEL_RESET_ATTRIBUTE_CO_PULSE_TIME_UNITS = 6358;
  CHANNEL_RESET_ATTRIBUTE_CO_PULSE_TERM = 6369;
  CHANNEL_RESET_ATTRIBUTE_DO_TRISTATE = 6387;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_BALANCE_FINE_POT = 6388;
  CHANNEL_RESET_ATTRIBUTE_AI_FORCE_READ_FROM_CHAN = 6392;
  CHANNEL_RESET_ATTRIBUTE_CHAN_DESCR = 6438;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_MEAS_METH = 6444;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_MEAS_TIME = 6445;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_DIV = 6446;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_GAIN_ADJUST = 6463;
  CHANNEL_RESET_ATTRIBUTE_AI_EXCIT_USE_MULTIPLEXED = 8576;
  CHANNEL_RESET_ATTRIBUTE_AI_INPUT_SRC = 8600;
  CHANNEL_RESET_ATTRIBUTE_AI_LVDT_SENSITIVITY_UNITS = 8602;
  CHANNEL_RESET_ATTRIBUTE_AI_RVDT_SENSITIVITY_UNITS = 8603;
  CHANNEL_RESET_ATTRIBUTE_AI_ACCEL_SENSITIVITY_UNITS = 8604;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_A_INPUT_TERM = 8605;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_B_INPUT_TERM = 8606;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_Z_INPUT_TERM = 8607;
  CHANNEL_RESET_ATTRIBUTE_CI_DUP_COUNT_PREVENT = 8620;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SELECT = 8661;
  CHANNEL_RESET_ATTRIBUTE_DI_DIG_FLTR_ENABLE = 8662;
  CHANNEL_RESET_ATTRIBUTE_DI_DIG_FLTR_MIN_PULSE_WIDTH = 8663;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_DIR_TERM = 8673;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_DECODING_TYPE = 8678;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_DIG_FLTR_ENABLE = 8679;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_DIG_FLTR_MIN_PULSE_WIDTH = 8680;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_DIG_FLTR_TIMEBASE_SRC = 8681;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_DIG_FLTR_TIMEBASE_RATE = 8682;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_DIG_SYNC_ENABLE = 8683;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_DIG_FLTR_ENABLE = 8684;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_DIG_FLTR_MIN_PULSE_WIDTH = 8685;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_DIG_FLTR_TIMEBASE_SRC = 8686;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_DIG_FLTR_TIMEBASE_RATE = 8687;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_DIG_SYNC_ENABLE = 8688;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_DIG_FLTR_ENABLE = 8689;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_DIG_FLTR_MIN_PULSE_WIDTH = 8690;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_DIG_FLTR_TIMEBASE_SRC = 8691;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_DIG_FLTR_TIMEBASE_RATE = 8692;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_DIG_SYNC_ENABLE = 8693;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_DIG_FLTR_ENABLE = 8694;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_DIG_FLTR_MIN_PULSE_WIDTH = 8695;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_DIG_FLTR_TIMEBASE_SRC = 8696;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_DIG_FLTR_TIMEBASE_RATE = 8697;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_DIG_SYNC_ENABLE = 8698;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_A_INPUT_DIG_FLTR_ENABLE = 8699;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_A_INPUT_DIG_FLTR_MIN_PULSE_WIDTH = 8700;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_A_INPUT_DIG_FLTR_TIMEBASE_SRC = 8701;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_A_INPUT_DIG_FLTR_TIMEBASE_RATE = 8702;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_A_INPUT_DIG_SYNC_ENABLE = 8703;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_B_INPUT_DIG_FLTR_ENABLE = 8704;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_B_INPUT_DIG_FLTR_MIN_PULSE_WIDTH = 8705;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_B_INPUT_DIG_FLTR_TIMEBASE_SRC = 8706;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_B_INPUT_DIG_FLTR_TIMEBASE_RATE = 8707;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_B_INPUT_DIG_SYNC_ENABLE = 8708;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_Z_INPUT_DIG_FLTR_ENABLE = 8709;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_Z_INPUT_DIG_FLTR_MIN_PULSE_WIDTH = 8710;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_Z_INPUT_DIG_FLTR_TIMEBASE_SRC = 8711;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_Z_INPUT_DIG_FLTR_TIMEBASE_RATE = 8712;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_Z_INPUT_DIG_SYNC_ENABLE = 8713;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_WIDTH_DIG_FLTR_ENABLE = 8714;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_WIDTH_DIG_FLTR_MIN_PULSE_WIDTH = 8715;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_WIDTH_DIG_FLTR_TIMEBASE_SRC = 8716;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_WIDTH_DIG_FLTR_TIMEBASE_RATE = 8717;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_WIDTH_DIG_SYNC_ENABLE = 8718;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_DIG_FLTR_ENABLE = 8719;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_DIG_FLTR_MIN_PULSE_WIDTH = 8720;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_DIG_FLTR_TIMEBASE_SRC = 8721;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_DIG_FLTR_TIMEBASE_RATE = 8722;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_DIG_SYNC_ENABLE = 8723;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_DIG_FLTR_ENABLE = 8724;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_DIG_FLTR_MIN_PULSE_WIDTH = 8725;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_DIG_FLTR_TIMEBASE_SRC = 8726;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_DIG_FLTR_TIMEBASE_RATE = 8727;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_DIG_SYNC_ENABLE = 8728;
  CHANNEL_RESET_ATTRIBUTE_CI_SEMI_PERIOD_DIG_FLTR_ENABLE = 8729;
  CHANNEL_RESET_ATTRIBUTE_CI_SEMI_PERIOD_DIG_FLTR_MIN_PULSE_WIDTH = 8730;
  CHANNEL_RESET_ATTRIBUTE_CI_SEMI_PERIOD_DIG_FLTR_TIMEBASE_SRC = 8731;
  CHANNEL_RESET_ATTRIBUTE_CI_SEMI_PERIOD_DIG_FLTR_TIMEBASE_RATE = 8732;
  CHANNEL_RESET_ATTRIBUTE_CI_SEMI_PERIOD_DIG_SYNC_ENABLE = 8733;
  CHANNEL_RESET_ATTRIBUTE_CI_PRESCALER = 8761;
  CHANNEL_RESET_ATTRIBUTE_AI_SOUND_PRESSURE_MAX_SOUND_PRESSURE_LVL = 8762;
  CHANNEL_RESET_ATTRIBUTE_AO_IDLE_OUTPUT_BEHAVIOR = 8768;
  CHANNEL_RESET_ATTRIBUTE_AO_ENHANCED_IMAGE_REJECTION_ENABLE = 8769;
  CHANNEL_RESET_ATTRIBUTE_AO_DAC_REF_EXT_SRC = 8786;
  CHANNEL_RESET_ATTRIBUTE_AO_DAC_OFFSET_SRC = 8787;
  CHANNEL_RESET_ATTRIBUTE_AO_DAC_OFFSET_EXT_SRC = 8788;
  CHANNEL_RESET_ATTRIBUTE_AO_DAC_OFFSET_VAL = 8789;
  CHANNEL_RESET_ATTRIBUTE_DI_DATA_XFER_MECH = 8803;
  CHANNEL_RESET_ATTRIBUTE_DI_DATA_XFER_REQ_COND = 8804;
  CHANNEL_RESET_ATTRIBUTE_DO_USE_ONLY_ON_BRD_MEM = 8805;
  CHANNEL_RESET_ATTRIBUTE_DO_DATA_XFER_MECH = 8806;
  CHANNEL_RESET_ATTRIBUTE_DO_DATA_XFER_REQ_COND = 8807;
  CHANNEL_RESET_ATTRIBUTE_CO_PRESCALER = 8813;
  CHANNEL_RESET_ATTRIBUTE_CI_CTR_TIMEBASE_DIG_FLTR_ENABLE = 8817;
  CHANNEL_RESET_ATTRIBUTE_CI_CTR_TIMEBASE_DIG_FLTR_MIN_PULSE_WIDTH = 8818;
  CHANNEL_RESET_ATTRIBUTE_CI_CTR_TIMEBASE_DIG_FLTR_TIMEBASE_SRC = 8819;
  CHANNEL_RESET_ATTRIBUTE_CI_CTR_TIMEBASE_DIG_FLTR_TIMEBASE_RATE = 8820;
  CHANNEL_RESET_ATTRIBUTE_CI_CTR_TIMEBASE_DIG_SYNC_ENABLE = 8821;
  CHANNEL_RESET_ATTRIBUTE_CO_CTR_TIMEBASE_DIG_FLTR_ENABLE = 8822;
  CHANNEL_RESET_ATTRIBUTE_CO_CTR_TIMEBASE_DIG_FLTR_MIN_PULSE_WIDTH = 8823;
  CHANNEL_RESET_ATTRIBUTE_CO_CTR_TIMEBASE_DIG_FLTR_TIMEBASE_SRC = 8824;
  CHANNEL_RESET_ATTRIBUTE_CO_CTR_TIMEBASE_DIG_FLTR_TIMEBASE_RATE = 8825;
  CHANNEL_RESET_ATTRIBUTE_CO_CTR_TIMEBASE_DIG_SYNC_ENABLE = 8826;
  CHANNEL_RESET_ATTRIBUTE_AI_ENHANCED_ALIAS_REJECTION_ENABLE = 8852;
  CHANNEL_RESET_ATTRIBUTE_AI_CHAN_CAL_ENABLE_CAL = 8856;
  CHANNEL_RESET_ATTRIBUTE_AI_CHAN_CAL_APPLY_CAL_IF_EXP = 8857;
  CHANNEL_RESET_ATTRIBUTE_AI_CHAN_CAL_SCALE_TYPE = 8860;
  CHANNEL_RESET_ATTRIBUTE_AI_CHAN_CAL_TABLE_PRE_SCALED_VALS = 8861;
  CHANNEL_RESET_ATTRIBUTE_AI_CHAN_CAL_TABLE_SCALED_VALS = 8862;
  CHANNEL_RESET_ATTRIBUTE_AI_CHAN_CAL_POLY_FORWARD_COEFF = 8863;
  CHANNEL_RESET_ATTRIBUTE_AI_CHAN_CAL_POLY_REVERSE_COEFF = 8864;
  CHANNEL_RESET_ATTRIBUTE_AI_CHAN_CAL_VERIF_REF_VALS = 8865;
  CHANNEL_RESET_ATTRIBUTE_AI_CHAN_CAL_VERIF_ACQ_VALS = 8866;
  CHANNEL_RESET_ATTRIBUTE_AI_CHAN_CAL_OPERATOR_NAME = 8867;
  CHANNEL_RESET_ATTRIBUTE_AI_CHAN_CAL_DESC = 8868;
  CHANNEL_RESET_ATTRIBUTE_CI_TIMESTAMP_UNITS = 8883;
  CHANNEL_RESET_ATTRIBUTE_CI_TIMESTAMP_INITIAL_SECONDS = 8884;
  CHANNEL_RESET_ATTRIBUTE_AI_RAW_DATA_COMPRESSION_TYPE = 8920;
  CHANNEL_RESET_ATTRIBUTE_AI_LOSSY_LSB_REMOVAL_COMPRESSED_SAMP_SIZE = 8921;
  CHANNEL_RESET_ATTRIBUTE_CI_SEMI_PERIOD_STARTING_EDGE = 8958;
  CHANNEL_RESET_ATTRIBUTE_AI_DATA_XFER_CUSTOM_THRESHOLD = 8972;
  CHANNEL_RESET_ATTRIBUTE_DI_ACQUIRE_ON = 10598;
  CHANNEL_RESET_ATTRIBUTE_DO_LINE_STATES_PAUSED_STATE = 10599;
  CHANNEL_RESET_ATTRIBUTE_DO_LINE_STATES_DONE_STATE = 10600;
  CHANNEL_RESET_ATTRIBUTE_DO_GENERATE_ON = 10601;
  CHANNEL_RESET_ATTRIBUTE_DI_MEM_MAP_ENABLE = 10602;
  CHANNEL_RESET_ATTRIBUTE_DO_MEM_MAP_ENABLE = 10603;
  CHANNEL_RESET_ATTRIBUTE_DI_LOGIC_FAMILY = 10605;
  CHANNEL_RESET_ATTRIBUTE_DO_LOGIC_FAMILY = 10606;
  CHANNEL_RESET_ATTRIBUTE_DO_LINE_STATES_START_STATE = 10610;
  CHANNEL_RESET_ATTRIBUTE_AI_VOLTAGE_DB_REF = 10672;
  CHANNEL_RESET_ATTRIBUTE_AI_SOUND_PRESSURE_DB_REF = 10673;
  CHANNEL_RESET_ATTRIBUTE_AI_ACCEL_DB_REF = 10674;
  CHANNEL_RESET_ATTRIBUTE_AI_THRMCPL_SCALE_TYPE = 10704;
  CHANNEL_RESET_ATTRIBUTE_CO_CONSTRAINED_GEN_MODE = 10738;
  CHANNEL_RESET_ATTRIBUTE_AI_ADC_TIMING_MODE = 10745;
  CHANNEL_RESET_ATTRIBUTE_AO_FUNC_GEN_TYPE = 10776;
  CHANNEL_RESET_ATTRIBUTE_AO_FUNC_GEN_FREQ = 10777;
  CHANNEL_RESET_ATTRIBUTE_AO_FUNC_GEN_AMPLITUDE = 10778;
  CHANNEL_RESET_ATTRIBUTE_AO_FUNC_GEN_OFFSET = 10779;
  CHANNEL_RESET_ATTRIBUTE_AO_FUNC_GEN_SQUARE_DUTY_CYCLE = 10780;
  CHANNEL_RESET_ATTRIBUTE_AO_VOLTAGE_CURRENT_LIMIT = 10781;
  CHANNEL_RESET_ATTRIBUTE_AO_FUNC_GEN_MODULATION_TYPE = 10786;
  CHANNEL_RESET_ATTRIBUTE_AO_FUNC_GEN_FM_DEVIATION = 10787;
  CHANNEL_RESET_ATTRIBUTE_DO_OVERCURRENT_LIMIT = 10885;
  CHANNEL_RESET_ATTRIBUTE_DO_OVERCURRENT_AUTO_REENABLE = 10886;
  CHANNEL_RESET_ATTRIBUTE_DO_OVERCURRENT_REENABLE_PERIOD = 10887;
  CHANNEL_RESET_ATTRIBUTE_AI_PROBE_ATTEN = 10888;
  CHANNEL_RESET_ATTRIBUTE_AI_DC_OFFSET = 10889;
  CHANNEL_RESET_ATTRIBUTE_AI_USB_XFER_REQ_SIZE = 10894;
  CHANNEL_RESET_ATTRIBUTE_AO_USB_XFER_REQ_SIZE = 10895;
  CHANNEL_RESET_ATTRIBUTE_DI_USB_XFER_REQ_SIZE = 10896;
  CHANNEL_RESET_ATTRIBUTE_DO_USB_XFER_REQ_SIZE = 10897;
  CHANNEL_RESET_ATTRIBUTE_CI_USB_XFER_REQ_SIZE = 10898;
  CHANNEL_RESET_ATTRIBUTE_CO_USB_XFER_REQ_SIZE = 10899;
  CHANNEL_RESET_ATTRIBUTE_AI_EDDY_CURRENT_PROX_PROBE_SENSITIVITY = 10942;
  CHANNEL_RESET_ATTRIBUTE_AI_EDDY_CURRENT_PROX_PROBE_SENSITIVITY_UNITS = 10943;
  CHANNEL_RESET_ATTRIBUTE_AI_EDDY_CURRENT_PROX_PROBE_UNITS = 10944;
  CHANNEL_RESET_ATTRIBUTE_CO_ENABLE_INITIAL_DELAY_ON_RETRIGGER = 11977;
  CHANNEL_RESET_ATTRIBUTE_CO_USE_ONLY_ON_BRD_MEM = 11979;
  CHANNEL_RESET_ATTRIBUTE_CO_DATA_XFER_MECH = 11980;
  CHANNEL_RESET_ATTRIBUTE_CO_DATA_XFER_REQ_COND = 11981;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_ENABLE_AVERAGING = 11984;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_ENABLE_AVERAGING = 11985;
  CHANNEL_RESET_ATTRIBUTE_CI_MEM_MAP_ENABLE = 11986;
  CHANNEL_RESET_ATTRIBUTE_CO_MEM_MAP_ENABLE = 11987;
  CHANNEL_RESET_ATTRIBUTE_DI_DIG_FLTR_TIMEBASE_SRC = 11988;
  CHANNEL_RESET_ATTRIBUTE_DI_DIG_FLTR_TIMEBASE_RATE = 11989;
  CHANNEL_RESET_ATTRIBUTE_DI_DIG_SYNC_ENABLE = 11990;
  CHANNEL_RESET_ATTRIBUTE_CI_DATA_XFER_REQ_COND = 12027;
  CHANNEL_RESET_ATTRIBUTE_DI_DIG_FLTR_ENABLE_BUS_MODE = 12030;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_FREQ_TERM = 12036;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_FREQ_START_EDGE = 12037;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_FREQ_DIG_FLTR_ENABLE = 12038;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_FREQ_DIG_FLTR_MIN_PULSE_WIDTH = 12039;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_FREQ_DIG_FLTR_TIMEBASE_SRC = 12040;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_FREQ_DIG_FLTR_TIMEBASE_RATE = 12041;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_FREQ_DIG_SYNC_ENABLE = 12042;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_FREQ_UNITS = 12043;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TIME_TERM = 12044;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TIME_START_EDGE = 12045;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TIME_DIG_FLTR_ENABLE = 12046;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TIME_DIG_FLTR_MIN_PULSE_WIDTH = 12047;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TIME_DIG_FLTR_TIMEBASE_SRC = 12048;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TIME_DIG_FLTR_TIMEBASE_RATE = 12049;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TIME_DIG_SYNC_ENABLE = 12050;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TIME_UNITS = 12051;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TICKS_TERM = 12052;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TICKS_START_EDGE = 12053;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TICKS_DIG_FLTR_ENABLE = 12054;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TICKS_DIG_FLTR_MIN_PULSE_WIDTH = 12055;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TICKS_DIG_FLTR_TIMEBASE_SRC = 12056;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TICKS_DIG_FLTR_TIMEBASE_RATE = 12057;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TICKS_DIG_SYNC_ENABLE = 12058;
  CHANNEL_RESET_ATTRIBUTE_AI_ADC_CUSTOM_TIMING_MODE = 12139;
  CHANNEL_RESET_ATTRIBUTE_AI_OPEN_THRMCPL_DETECT_ENABLE = 12146;
  CHANNEL_RESET_ATTRIBUTE_AI_FORCE_UNITS = 12149;
  CHANNEL_RESET_ATTRIBUTE_AI_PRESSURE_UNITS = 12150;
  CHANNEL_RESET_ATTRIBUTE_AI_TORQUE_UNITS = 12151;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SHUNT_CAL_A_RESISTANCE = 12152;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SHUNT_CAL_A_ACTUAL_RESISTANCE = 12153;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SHUNT_CAL_B_RESISTANCE = 12154;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SHUNT_CAL_B_ACTUAL_RESISTANCE = 12155;
  CHANNEL_RESET_ATTRIBUTE_AI_FORCE_IEPE_SENSOR_SENSITIVITY = 12161;
  CHANNEL_RESET_ATTRIBUTE_AI_FORCE_IEPE_SENSOR_SENSITIVITY_UNITS = 12162;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_INITIAL_RATIO = 12166;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_ELECTRICAL_UNITS = 12167;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_PHYSICAL_UNITS = 12168;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_SCALE_TYPE = 12169;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_TWO_POINT_LIN_FIRST_ELECTRICAL_VAL = 12170;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_TWO_POINT_LIN_FIRST_PHYSICAL_VAL = 12171;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_TWO_POINT_LIN_SECOND_ELECTRICAL_VAL = 12172;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_TWO_POINT_LIN_SECOND_PHYSICAL_VAL = 12173;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_TABLE_ELECTRICAL_VALS = 12174;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_TABLE_PHYSICAL_VALS = 12175;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_POLY_FORWARD_COEFF = 12176;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_POLY_REVERSE_COEFF = 12177;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_UNITS = 12178;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_ENABLE = 12207;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_RESET_COUNT = 12208;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_TERM = 12209;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_ACTIVE_EDGE = 12210;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_DIG_FLTR_ENABLE = 12211;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_DIG_FLTR_MIN_PULSE_WIDTH = 12212;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_DIG_FLTR_TIMEBASE_SRC = 12213;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_DIG_FLTR_TIMEBASE_RATE = 12214;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_DIG_SYNC_ENABLE = 12215;
  CHANNEL_RESET_ATTRIBUTE_AI_THRMCPL_LEAD_OFFSET_VOLTAGE = 12216;
  CHANNEL_RESET_ATTRIBUTE_AI_REMOVE_FILTER_DELAY = 12221;
  CHANNEL_RESET_ATTRIBUTE_AI_AVERAGING_WIN_SIZE = 12270;
  CHANNEL_RESET_ATTRIBUTE_AI_VELOCITY_UNITS = 12276;
  CHANNEL_RESET_ATTRIBUTE_AI_VELOCITY_IEPE_SENSOR_DB_REF = 12277;
  CHANNEL_RESET_ATTRIBUTE_AI_VELOCITY_IEPE_SENSOR_SENSITIVITY = 12278;
  CHANNEL_RESET_ATTRIBUTE_AI_VELOCITY_IEPE_SENSOR_SENSITIVITY_UNITS = 12279;
  CHANNEL_RESET_ATTRIBUTE_AI_STRAIN_GAGE_FORCE_READ_FROM_CHAN = 12282;
  CHANNEL_RESET_ATTRIBUTE_AI_ROSETTE_STRAIN_GAGE_ORIENTATION = 12284;
  CHANNEL_RESET_ATTRIBUTE_AI_ROSETTE_STRAIN_GAGE_ROSETTE_MEAS_TYPE = 12285;
  CHANNEL_RESET_ATTRIBUTE_AI_USB_XFER_REQ_COUNT = 12288;
  CHANNEL_RESET_ATTRIBUTE_AO_USB_XFER_REQ_COUNT = 12289;
  CHANNEL_RESET_ATTRIBUTE_DI_USB_XFER_REQ_COUNT = 12290;
  CHANNEL_RESET_ATTRIBUTE_DO_USB_XFER_REQ_COUNT = 12291;
  CHANNEL_RESET_ATTRIBUTE_CI_USB_XFER_REQ_COUNT = 12292;
  CHANNEL_RESET_ATTRIBUTE_CO_USB_XFER_REQ_COUNT = 12293;
  CHANNEL_RESET_ATTRIBUTE_CI_TIMESTAMP_TERM = 12345;
  CHANNEL_RESET_ATTRIBUTE_CI_TIMESTAMP_EDGE = 12346;
  CHANNEL_RESET_ATTRIBUTE_CI_TIMESTAMP_TIMESCALE = 12347;
  CHANNEL_RESET_ATTRIBUTE_NAV_CUSTOM_SCALE_NAME = 12348;
  CHANNEL_RESET_ATTRIBUTE_NAV_ALT_UNITS = 12350;
  CHANNEL_RESET_ATTRIBUTE_NAV_LAT_UNITS = 12351;
  CHANNEL_RESET_ATTRIBUTE_NAV_LONG_UNITS = 12352;
  CHANNEL_RESET_ATTRIBUTE_NAV_SPEED_OVER_GROUND_UNITS = 12353;
  CHANNEL_RESET_ATTRIBUTE_NAV_TRACK_UNITS = 12354;
  CHANNEL_RESET_ATTRIBUTE_NAV_VERT_VELOCITY_UNITS = 12355;
  CHANNEL_RESET_ATTRIBUTE_NAV_TIMESTAMP_UNITS = 12356;
  CHANNEL_RESET_ATTRIBUTE_NAV_TIMESTAMP_TIMESCALE = 12357;
  CHANNEL_RESET_ATTRIBUTE_AI_FILTER_DELAY_UNITS = 12401;
  CHANNEL_RESET_ATTRIBUTE_AO_FILTER_DELAY_ADJUSTMENT = 12402;
  CHANNEL_RESET_ATTRIBUTE_AI_FILTER_DELAY_ADJUSTMENT = 12404;
  CHANNEL_RESET_ATTRIBUTE_AO_FILTER_DELAY = 12405;
  CHANNEL_RESET_ATTRIBUTE_AO_FILTER_DELAY_UNITS = 12406;
  CHANNEL_RESET_ATTRIBUTE_CI_DUTY_CYCLE_TERM = 12429;
  CHANNEL_RESET_ATTRIBUTE_CI_DUTY_CYCLE_DIG_FLTR_ENABLE = 12430;
  CHANNEL_RESET_ATTRIBUTE_CI_DUTY_CYCLE_DIG_FLTR_MIN_PULSE_WIDTH = 12431;
  CHANNEL_RESET_ATTRIBUTE_CI_DUTY_CYCLE_DIG_FLTR_TIMEBASE_SRC = 12432;
  CHANNEL_RESET_ATTRIBUTE_CI_DUTY_CYCLE_DIG_FLTR_TIMEBASE_RATE = 12433;
  CHANNEL_RESET_ATTRIBUTE_CI_DUTY_CYCLE_STARTING_EDGE = 12434;
  CHANNEL_RESET_ATTRIBUTE_CI_SAMP_CLK_OVERRUN_BEHAVIOR = 12435;
  CHANNEL_RESET_ATTRIBUTE_CI_SAMP_CLK_OVERRUN_SENTINEL_VAL = 12436;
  CHANNEL_RESET_ATTRIBUTE_CI_MAX_MEAS_PERIOD = 12437;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_TERM_CFG = 12439;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_LOGIC_LVL_BEHAVIOR = 12440;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_TERM_CFG = 12441;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_LOGIC_LVL_BEHAVIOR = 12442;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_TERM_CFG = 12443;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_LOGIC_LVL_BEHAVIOR = 12444;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_TERM_CFG = 12445;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_LOGIC_LVL_BEHAVIOR = 12446;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_TERM_CFG = 12447;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_LOGIC_LVL_BEHAVIOR = 12448;
  CHANNEL_RESET_ATTRIBUTE_CI_DUTY_CYCLE_TERM_CFG = 12449;
  CHANNEL_RESET_ATTRIBUTE_CI_DUTY_CYCLE_LOGIC_LVL_BEHAVIOR = 12450;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_A_INPUT_TERM_CFG = 12451;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_A_INPUT_LOGIC_LVL_BEHAVIOR = 12452;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_B_INPUT_TERM_CFG = 12453;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_B_INPUT_LOGIC_LVL_BEHAVIOR = 12454;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_Z_INPUT_TERM_CFG = 12455;
  CHANNEL_RESET_ATTRIBUTE_CI_ENCODER_Z_INPUT_LOGIC_LVL_BEHAVIOR = 12456;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_WIDTH_TERM_CFG = 12457;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_WIDTH_LOGIC_LVL_BEHAVIOR = 12458;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_TERM_CFG = 12459;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_LOGIC_LVL_BEHAVIOR = 12460;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_TERM_CFG = 12461;
  CHANNEL_RESET_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_LOGIC_LVL_BEHAVIOR = 12462;
  CHANNEL_RESET_ATTRIBUTE_CI_SEMI_PERIOD_TERM_CFG = 12463;
  CHANNEL_RESET_ATTRIBUTE_CI_SEMI_PERIOD_LOGIC_LVL_BEHAVIOR = 12464;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_FREQ_TERM_CFG = 12465;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_FREQ_LOGIC_LVL_BEHAVIOR = 12466;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TIME_TERM_CFG = 12467;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TIME_LOGIC_LVL_BEHAVIOR = 12468;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TICKS_TERM_CFG = 12469;
  CHANNEL_RESET_ATTRIBUTE_CI_PULSE_TICKS_LOGIC_LVL_BEHAVIOR = 12470;
  CHANNEL_RESET_ATTRIBUTE_CI_THRESH_VOLTAGE = 12471;
  CHANNEL_RESET_ATTRIBUTE_AI_EXCIT_IDLE_OUTPUT_BEHAVIOR = 12472;
  CHANNEL_RESET_ATTRIBUTE_AI_DIG_FLTR_ENABLE = 12477;
  CHANNEL_RESET_ATTRIBUTE_AI_DIG_FLTR_TYPE = 12478;
  CHANNEL_RESET_ATTRIBUTE_AI_DIG_FLTR_RESPONSE = 12479;
  CHANNEL_RESET_ATTRIBUTE_AI_DIG_FLTR_ORDER = 12480;
  CHANNEL_RESET_ATTRIBUTE_AI_DIG_FLTR_LOWPASS_CUTOFF_FREQ = 12481;
  CHANNEL_RESET_ATTRIBUTE_AI_DIG_FLTR_HIGHPASS_CUTOFF_FREQ = 12482;
  CHANNEL_RESET_ATTRIBUTE_AI_DIG_FLTR_BANDPASS_CENTER_FREQ = 12483;
  CHANNEL_RESET_ATTRIBUTE_AI_DIG_FLTR_BANDPASS_WIDTH = 12484;
  CHANNEL_RESET_ATTRIBUTE_AI_DIG_FLTR_NOTCH_CENTER_FREQ = 12485;
  CHANNEL_RESET_ATTRIBUTE_AI_DIG_FLTR_NOTCH_WIDTH = 12486;
  CHANNEL_RESET_ATTRIBUTE_AI_DIG_FLTR_COEFF = 12487;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SHUNT_CAL_A_SRC = 12490;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ANG_ENCODER_UNITS = 12504;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ANG_ENCODER_PULSES_PER_REV = 12505;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_LIN_ENCODER_UNITS = 12506;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_LIN_ENCODER_DIST_PER_PULSE = 12507;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_DECODING_TYPE = 12508;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_TERM = 12509;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_TERM_CFG = 12510;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_LOGIC_LVL_BEHAVIOR = 12511;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_DIG_FLTR_ENABLE = 12512;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_DIG_FLTR_MIN_PULSE_WIDTH = 12513;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_DIG_FLTR_TIMEBASE_SRC = 12514;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_DIG_FLTR_TIMEBASE_RATE = 12515;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_TERM = 12516;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_TERM_CFG = 12517;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_LOGIC_LVL_BEHAVIOR = 12518;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_DIG_FLTR_ENABLE = 12519;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_DIG_FLTR_MIN_PULSE_WIDTH = 12520;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_DIG_FLTR_TIMEBASE_SRC = 12521;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_DIG_FLTR_TIMEBASE_RATE = 12522;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_MEAS_TIME = 12523;
  CHANNEL_RESET_ATTRIBUTE_CI_VELOCITY_DIV = 12524;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_GATE_ENABLE = 12525;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_GATE_TERM = 12526;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_GATE_TERM_CFG = 12527;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_GATE_LOGIC_LVL_BEHAVIOR = 12528;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_GATE_DIG_FLTR_ENABLE = 12529;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_GATE_DIG_FLTR_MIN_PULSE_WIDTH = 12530;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_GATE_DIG_FLTR_TIMEBASE_SRC = 12531;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_GATE_DIG_FLTR_TIMEBASE_RATE = 12532;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_GATE_WHEN = 12533;
  CHANNEL_RESET_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_SHUNT_CAL_B_SRC = 12535;
  CHANNEL_RESET_ATTRIBUTE_AI_EXCIT_SENSE = 12541;
  CHANNEL_RESET_ATTRIBUTE_AI_OPEN_CHAN_DETECT_ENABLE = 12543;
  CHANNEL_RESET_ATTRIBUTE_AI_CHARGE_UNITS = 12562;
  CHANNEL_RESET_ATTRIBUTE_AI_ACCEL_CHARGE_SENSITIVITY = 12563;
  CHANNEL_RESET_ATTRIBUTE_AI_ACCEL_CHARGE_SENSITIVITY_UNITS = 12564;
  CHANNEL_RESET_ATTRIBUTE_AI_ACCEL_4_WIRE_DC_VOLTAGE_SENSITIVITY = 12565;
  CHANNEL_RESET_ATTRIBUTE_AI_ACCEL_4_WIRE_DC_VOLTAGE_SENSITIVITY_UNITS = 12566;
  CHANNEL_RESET_ATTRIBUTE_AI_DATA_XFER_MAX_RATE = 12567;
  CHANNEL_RESET_ATTRIBUTE_CHAN_SYNC_UNLOCK_BEHAVIOR = 12604;
  CHANNEL_RESET_ATTRIBUTE_AI_CHOP_ENABLE = 12611;
  CHANNEL_RESET_ATTRIBUTE_AI_SENSOR_POWER_VOLTAGE = 12649;
  CHANNEL_RESET_ATTRIBUTE_AI_SENSOR_POWER_CFG = 12650;
  CHANNEL_RESET_ATTRIBUTE_AI_SENSOR_POWER_TYPE = 12651;
  CHANNEL_RESET_ATTRIBUTE_AI_FILTER_ENABLE = 12659;
  CHANNEL_RESET_ATTRIBUTE_AI_FILTER_FREQ = 12660;
  CHANNEL_RESET_ATTRIBUTE_AI_FILTER_RESPONSE = 12661;
  CHANNEL_RESET_ATTRIBUTE_AI_FILTER_ORDER = 12662;
  CHANNEL_RESET_ATTRIBUTE_AI_INPUT_LIMITS_FAULT_DETECT_UPPER_LIMIT = 12684;
  CHANNEL_RESET_ATTRIBUTE_AI_INPUT_LIMITS_FAULT_DETECT_LOWER_LIMIT = 12685;
  CHANNEL_RESET_ATTRIBUTE_AI_INPUT_LIMITS_FAULT_DETECT_ENABLE = 12686;
  CHANNEL_RESET_ATTRIBUTE_AI_POWER_SUPPLY_FAULT_DETECT_ENABLE = 12689;
  CHANNEL_RESET_ATTRIBUTE_AI_OVERCURRENT_DETECT_ENABLE = 12692;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_THRESH_VOLTAGE = 12715;
  CHANNEL_RESET_ATTRIBUTE_CI_FREQ_HYST = 12716;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_THRESH_VOLTAGE = 12717;
  CHANNEL_RESET_ATTRIBUTE_CI_PERIOD_HYST = 12718;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_THRESH_VOLTAGE = 12719;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_HYST = 12720;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_THRESH_VOLTAGE = 12721;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_HYST = 12722;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_THRESH_VOLTAGE = 12723;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_HYST = 12724;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_GATE_THRESH_VOLTAGE = 12725;
  CHANNEL_RESET_ATTRIBUTE_CI_COUNT_EDGES_GATE_HYST = 12726;
  CHANNEL_RESET_ATTRIBUTE_CI_FILTER_ENABLE = 12727;
  CHANNEL_RESET_ATTRIBUTE_CI_FILTER_FREQ = 12728;
  CHANNEL_RESET_ATTRIBUTE_CI_FILTER_RESPONSE = 12729;
  CHANNEL_RESET_ATTRIBUTE_CI_FILTER_ORDER = 12730;
  CHANNEL_RESET_ATTRIBUTE_CI_FILTER_DELAY_UNITS = 12732;
  CHANNEL_RESET_ATTRIBUTE_AO_FUNC_GEN_START_PHASE = 12740;
  CHANNEL_RESET_ATTRIBUTE_AO_COMMON_MODE_OFFSET = 12748;
  CHANNEL_RESET_ATTRIBUTE_PWR_VOLTAGE_SETPOINT = 12756;
  CHANNEL_RESET_ATTRIBUTE_PWR_CURRENT_SETPOINT = 12757;
  CHANNEL_RESET_ATTRIBUTE_PWR_OUTPUT_ENABLE = 12758;
  CHANNEL_RESET_ATTRIBUTE_PWR_IDLE_OUTPUT_BEHAVIOR = 12760;
  CHANNEL_RESET_ATTRIBUTE_PWR_REMOTE_SENSE = 12763;
}

enum ChannelBoolAttribute {
  CHANNEL_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  CHANNEL_ATTRIBUTE_AI_DITHER_ENABLE = 104;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_SHUNT_CAL_ENABLE = 148;
  CHANNEL_ATTRIBUTE_AI_AC_EXCIT_SYNC_ENABLE = 258;
  CHANNEL_ATTRIBUTE_AO_DAC_REF_CONN_TO_GND = 304;
  CHANNEL_ATTRIBUTE_AO_REGLITCH_ENABLE = 307;
  CHANNEL_ATTRIBUTE_CI_TC_REACHED = 336;
  CHANNEL_ATTRIBUTE_DI_INVERT_LINES = 1939;
  CHANNEL_ATTRIBUTE_CI_ENCODER_Z_INDEX_ENABLE = 2192;
  CHANNEL_ATTRIBUTE_DO_INVERT_LINES = 4403;
  CHANNEL_ATTRIBUTE_AI_EXCIT_USE_FOR_SCALING = 6140;
  CHANNEL_ATTRIBUTE_AI_LOWPASS_ENABLE = 6146;
  CHANNEL_ATTRIBUTE_AI_HIGHPASS_ENABLE = 6150;
  CHANNEL_ATTRIBUTE_AI_BANDPASS_ENABLE = 6155;
  CHANNEL_ATTRIBUTE_AI_SAMP_AND_HOLD_ENABLE = 6170;
  CHANNEL_ATTRIBUTE_AO_DAC_REF_ALLOW_CONN_TO_GND = 6192;
  CHANNEL_ATTRIBUTE_AO_USE_ONLY_ON_BRD_MEM = 6202;
  CHANNEL_ATTRIBUTE_AI_MEM_MAP_ENABLE = 6284;
  CHANNEL_ATTRIBUTE_AO_MEM_MAP_ENABLE = 6287;
  CHANNEL_ATTRIBUTE_DI_TRISTATE = 6288;
  CHANNEL_ATTRIBUTE_DO_TRISTATE = 6387;
  CHANNEL_ATTRIBUTE_AI_FORCE_READ_FROM_CHAN = 6392;
  CHANNEL_ATTRIBUTE_CO_PULSE_DONE = 6414;
  CHANNEL_ATTRIBUTE_AI_EXCIT_USE_MULTIPLEXED = 8576;
  CHANNEL_ATTRIBUTE_CI_DUP_COUNT_PREVENT = 8620;
  CHANNEL_ATTRIBUTE_DI_DIG_FLTR_ENABLE = 8662;
  CHANNEL_ATTRIBUTE_CI_FREQ_DIG_FLTR_ENABLE = 8679;
  CHANNEL_ATTRIBUTE_CI_FREQ_DIG_SYNC_ENABLE = 8683;
  CHANNEL_ATTRIBUTE_CI_PERIOD_DIG_FLTR_ENABLE = 8684;
  CHANNEL_ATTRIBUTE_CI_PERIOD_DIG_SYNC_ENABLE = 8688;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_DIG_FLTR_ENABLE = 8689;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_DIG_SYNC_ENABLE = 8693;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_DIG_FLTR_ENABLE = 8694;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_DIG_SYNC_ENABLE = 8698;
  CHANNEL_ATTRIBUTE_CI_ENCODER_A_INPUT_DIG_FLTR_ENABLE = 8699;
  CHANNEL_ATTRIBUTE_CI_ENCODER_A_INPUT_DIG_SYNC_ENABLE = 8703;
  CHANNEL_ATTRIBUTE_CI_ENCODER_B_INPUT_DIG_FLTR_ENABLE = 8704;
  CHANNEL_ATTRIBUTE_CI_ENCODER_B_INPUT_DIG_SYNC_ENABLE = 8708;
  CHANNEL_ATTRIBUTE_CI_ENCODER_Z_INPUT_DIG_FLTR_ENABLE = 8709;
  CHANNEL_ATTRIBUTE_CI_ENCODER_Z_INPUT_DIG_SYNC_ENABLE = 8713;
  CHANNEL_ATTRIBUTE_CI_PULSE_WIDTH_DIG_FLTR_ENABLE = 8714;
  CHANNEL_ATTRIBUTE_CI_PULSE_WIDTH_DIG_SYNC_ENABLE = 8718;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_DIG_FLTR_ENABLE = 8719;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_DIG_SYNC_ENABLE = 8723;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_DIG_FLTR_ENABLE = 8724;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_DIG_SYNC_ENABLE = 8728;
  CHANNEL_ATTRIBUTE_CI_SEMI_PERIOD_DIG_FLTR_ENABLE = 8729;
  CHANNEL_ATTRIBUTE_CI_SEMI_PERIOD_DIG_SYNC_ENABLE = 8733;
  CHANNEL_ATTRIBUTE_AO_ENHANCED_IMAGE_REJECTION_ENABLE = 8769;
  CHANNEL_ATTRIBUTE_DO_USE_ONLY_ON_BRD_MEM = 8805;
  CHANNEL_ATTRIBUTE_CI_CTR_TIMEBASE_DIG_FLTR_ENABLE = 8817;
  CHANNEL_ATTRIBUTE_CI_CTR_TIMEBASE_DIG_SYNC_ENABLE = 8821;
  CHANNEL_ATTRIBUTE_CO_CTR_TIMEBASE_DIG_FLTR_ENABLE = 8822;
  CHANNEL_ATTRIBUTE_CO_CTR_TIMEBASE_DIG_SYNC_ENABLE = 8826;
  CHANNEL_ATTRIBUTE_AI_ENHANCED_ALIAS_REJECTION_ENABLE = 8852;
  CHANNEL_ATTRIBUTE_AI_CHAN_CAL_HAS_VALID_CAL_INFO = 8855;
  CHANNEL_ATTRIBUTE_AI_CHAN_CAL_ENABLE_CAL = 8856;
  CHANNEL_ATTRIBUTE_AI_CHAN_CAL_APPLY_CAL_IF_EXP = 8857;
  CHANNEL_ATTRIBUTE_CO_RDY_FOR_NEW_VAL = 8959;
  CHANNEL_ATTRIBUTE_CHAN_IS_GLOBAL = 8964;
  CHANNEL_ATTRIBUTE_DI_MEM_MAP_ENABLE = 10602;
  CHANNEL_ATTRIBUTE_DO_MEM_MAP_ENABLE = 10603;
  CHANNEL_ATTRIBUTE_AI_IS_TEDS = 10627;
  CHANNEL_ATTRIBUTE_DO_OVERCURRENT_AUTO_REENABLE = 10886;
  CHANNEL_ATTRIBUTE_CO_ENABLE_INITIAL_DELAY_ON_RETRIGGER = 11977;
  CHANNEL_ATTRIBUTE_CO_USE_ONLY_ON_BRD_MEM = 11979;
  CHANNEL_ATTRIBUTE_CI_FREQ_ENABLE_AVERAGING = 11984;
  CHANNEL_ATTRIBUTE_CI_PERIOD_ENABLE_AVERAGING = 11985;
  CHANNEL_ATTRIBUTE_CI_MEM_MAP_ENABLE = 11986;
  CHANNEL_ATTRIBUTE_CO_MEM_MAP_ENABLE = 11987;
  CHANNEL_ATTRIBUTE_DI_DIG_SYNC_ENABLE = 11990;
  CHANNEL_ATTRIBUTE_DI_DIG_FLTR_ENABLE_BUS_MODE = 12030;
  CHANNEL_ATTRIBUTE_CI_PULSE_FREQ_DIG_FLTR_ENABLE = 12038;
  CHANNEL_ATTRIBUTE_CI_PULSE_FREQ_DIG_SYNC_ENABLE = 12042;
  CHANNEL_ATTRIBUTE_CI_PULSE_TIME_DIG_FLTR_ENABLE = 12046;
  CHANNEL_ATTRIBUTE_CI_PULSE_TIME_DIG_SYNC_ENABLE = 12050;
  CHANNEL_ATTRIBUTE_CI_PULSE_TICKS_DIG_FLTR_ENABLE = 12054;
  CHANNEL_ATTRIBUTE_CI_PULSE_TICKS_DIG_SYNC_ENABLE = 12058;
  CHANNEL_ATTRIBUTE_AI_OPEN_THRMCPL_DETECT_ENABLE = 12146;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_ENABLE = 12207;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_DIG_FLTR_ENABLE = 12211;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_DIG_SYNC_ENABLE = 12215;
  CHANNEL_ATTRIBUTE_AI_REMOVE_FILTER_DELAY = 12221;
  CHANNEL_ATTRIBUTE_AI_STRAIN_GAGE_FORCE_READ_FROM_CHAN = 12282;
  CHANNEL_ATTRIBUTE_CI_DUTY_CYCLE_DIG_FLTR_ENABLE = 12430;
  CHANNEL_ATTRIBUTE_AI_DIG_FLTR_ENABLE = 12477;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_DIG_FLTR_ENABLE = 12512;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_DIG_FLTR_ENABLE = 12519;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_GATE_ENABLE = 12525;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_GATE_DIG_FLTR_ENABLE = 12529;
  CHANNEL_ATTRIBUTE_AI_OPEN_CHAN_DETECT_ENABLE = 12543;
  CHANNEL_ATTRIBUTE_AI_CHOP_ENABLE = 12611;
  CHANNEL_ATTRIBUTE_AI_FILTER_ENABLE = 12659;
  CHANNEL_ATTRIBUTE_AI_INPUT_LIMITS_FAULT_DETECT_ENABLE = 12686;
  CHANNEL_ATTRIBUTE_AI_POWER_SUPPLY_FAULT_DETECT_ENABLE = 12689;
  CHANNEL_ATTRIBUTE_AI_OVERCURRENT_DETECT_ENABLE = 12692;
  CHANNEL_ATTRIBUTE_CI_FILTER_ENABLE = 12727;
  CHANNEL_ATTRIBUTE_PWR_OUTPUT_ENABLE = 12758;
}

enum ChannelStringAttribute {
  CHANNEL_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  CHANNEL_ATTRIBUTE_CI_CTR_TIMEBASE_SRC = 323;
  CHANNEL_ATTRIBUTE_CO_CTR_TIMEBASE_SRC = 825;
  CHANNEL_ATTRIBUTE_AI_THRMCPL_CJC_CHAN = 4148;
  CHANNEL_ATTRIBUTE_CI_GPS_SYNC_SRC = 4243;
  CHANNEL_ATTRIBUTE_AO_CUSTOM_SCALE_NAME = 4488;
  CHANNEL_ATTRIBUTE_AI_CUSTOM_SCALE_NAME = 6112;
  CHANNEL_ATTRIBUTE_CI_CUSTOM_SCALE_NAME = 6302;
  CHANNEL_ATTRIBUTE_CI_FREQ_TERM = 6306;
  CHANNEL_ATTRIBUTE_CI_PERIOD_TERM = 6308;
  CHANNEL_ATTRIBUTE_CI_PULSE_WIDTH_TERM = 6314;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_TERM = 6317;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_TERM = 6318;
  CHANNEL_ATTRIBUTE_CI_SEMI_PERIOD_TERM = 6320;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_TERM = 6343;
  CHANNEL_ATTRIBUTE_CO_PULSE_TERM = 6369;
  CHANNEL_ATTRIBUTE_PHYSICAL_CHAN_NAME = 6389;
  CHANNEL_ATTRIBUTE_CHAN_DESCR = 6438;
  CHANNEL_ATTRIBUTE_AI_INPUT_SRC = 8600;
  CHANNEL_ATTRIBUTE_CI_ENCODER_A_INPUT_TERM = 8605;
  CHANNEL_ATTRIBUTE_CI_ENCODER_B_INPUT_TERM = 8606;
  CHANNEL_ATTRIBUTE_CI_ENCODER_Z_INPUT_TERM = 8607;
  CHANNEL_ATTRIBUTE_AI_TEDS_UNITS = 8672;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_DIR_TERM = 8673;
  CHANNEL_ATTRIBUTE_CI_FREQ_DIG_FLTR_TIMEBASE_SRC = 8681;
  CHANNEL_ATTRIBUTE_CI_PERIOD_DIG_FLTR_TIMEBASE_SRC = 8686;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_DIR_DIG_FLTR_TIMEBASE_SRC = 8691;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_DIG_FLTR_TIMEBASE_SRC = 8696;
  CHANNEL_ATTRIBUTE_CI_ENCODER_A_INPUT_DIG_FLTR_TIMEBASE_SRC = 8701;
  CHANNEL_ATTRIBUTE_CI_ENCODER_B_INPUT_DIG_FLTR_TIMEBASE_SRC = 8706;
  CHANNEL_ATTRIBUTE_CI_ENCODER_Z_INPUT_DIG_FLTR_TIMEBASE_SRC = 8711;
  CHANNEL_ATTRIBUTE_CI_PULSE_WIDTH_DIG_FLTR_TIMEBASE_SRC = 8716;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_FIRST_DIG_FLTR_TIMEBASE_SRC = 8721;
  CHANNEL_ATTRIBUTE_CI_TWO_EDGE_SEP_SECOND_DIG_FLTR_TIMEBASE_SRC = 8726;
  CHANNEL_ATTRIBUTE_CI_SEMI_PERIOD_DIG_FLTR_TIMEBASE_SRC = 8731;
  CHANNEL_ATTRIBUTE_AO_DAC_REF_EXT_SRC = 8786;
  CHANNEL_ATTRIBUTE_AO_DAC_OFFSET_EXT_SRC = 8788;
  CHANNEL_ATTRIBUTE_CI_CTR_TIMEBASE_DIG_FLTR_TIMEBASE_SRC = 8819;
  CHANNEL_ATTRIBUTE_CO_CTR_TIMEBASE_DIG_FLTR_TIMEBASE_SRC = 8824;
  CHANNEL_ATTRIBUTE_AI_CHAN_CAL_OPERATOR_NAME = 8867;
  CHANNEL_ATTRIBUTE_AI_CHAN_CAL_DESC = 8868;
  CHANNEL_ATTRIBUTE_DI_DIG_FLTR_TIMEBASE_SRC = 11988;
  CHANNEL_ATTRIBUTE_CI_PULSE_FREQ_TERM = 12036;
  CHANNEL_ATTRIBUTE_CI_PULSE_FREQ_DIG_FLTR_TIMEBASE_SRC = 12040;
  CHANNEL_ATTRIBUTE_CI_PULSE_TIME_TERM = 12044;
  CHANNEL_ATTRIBUTE_CI_PULSE_TIME_DIG_FLTR_TIMEBASE_SRC = 12048;
  CHANNEL_ATTRIBUTE_CI_PULSE_TICKS_TERM = 12052;
  CHANNEL_ATTRIBUTE_CI_PULSE_TICKS_DIG_FLTR_TIMEBASE_SRC = 12056;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_TERM = 12209;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_DIG_FLTR_TIMEBASE_SRC = 12213;
  CHANNEL_ATTRIBUTE_AI_ROSETTE_STRAIN_GAGE_STRAIN_CHANS = 12283;
  CHANNEL_ATTRIBUTE_CI_TIMESTAMP_TERM = 12345;
  CHANNEL_ATTRIBUTE_NAV_CUSTOM_SCALE_NAME = 12348;
  CHANNEL_ATTRIBUTE_CI_DUTY_CYCLE_TERM = 12429;
  CHANNEL_ATTRIBUTE_CI_DUTY_CYCLE_DIG_FLTR_TIMEBASE_SRC = 12432;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_TERM = 12509;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_A_INPUT_DIG_FLTR_TIMEBASE_SRC = 12514;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_TERM = 12516;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ENCODER_B_INPUT_DIG_FLTR_TIMEBASE_SRC = 12521;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_GATE_TERM = 12526;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_GATE_DIG_FLTR_TIMEBASE_SRC = 12531;
}

enum ChannelUInt32Attribute {
  CHANNEL_UINT32_ATTRIBUTE_UNSPECIFIED = 0;
  CHANNEL_ATTRIBUTE_CI_FREQ_DIV = 327;
  CHANNEL_ATTRIBUTE_CI_COUNT = 328;
  CHANNEL_ATTRIBUTE_CO_COUNT = 659;
  CHANNEL_ATTRIBUTE_CO_AUTO_INCR_CNT = 661;
  CHANNEL_ATTRIBUTE_CO_PULSE_TICKS_INITIAL_DELAY = 664;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_INITIAL_CNT = 1688;
  CHANNEL_ATTRIBUTE_CI_ANG_ENCODER_PULSES_PER_REV = 2165;
  CHANNEL_ATTRIBUTE_CO_PULSE_HIGH_TICKS = 4457;
  CHANNEL_ATTRIBUTE_CO_PULSE_LOW_TICKS = 4465;
  CHANNEL_ATTRIBUTE_AI_HIGHPASS_ORDER = 6153;
  CHANNEL_ATTRIBUTE_AI_LOWPASS_SWITCH_CAP_EXT_CLK_DIV = 6278;
  CHANNEL_ATTRIBUTE_AI_LOWPASS_SWITCH_CAP_OUT_CLK_DIV = 6279;
  CHANNEL_ATTRIBUTE_CI_CTR_TIMEBASE_MASTER_TIMEBASE_DIV = 6323;
  CHANNEL_ATTRIBUTE_CO_CTR_TIMEBASE_MASTER_TIMEBASE_DIV = 6339;
  CHANNEL_ATTRIBUTE_CI_PERIOD_DIV = 6446;
  CHANNEL_ATTRIBUTE_CI_NUM_POSSIBLY_INVALID_SAMPS = 6460;
  CHANNEL_ATTRIBUTE_DI_NUM_LINES = 8568;
  CHANNEL_ATTRIBUTE_DO_NUM_LINES = 8569;
  CHANNEL_ATTRIBUTE_CI_PRESCALER = 8761;
  CHANNEL_ATTRIBUTE_CO_PRESCALER = 8813;
  CHANNEL_ATTRIBUTE_CI_TIMESTAMP_INITIAL_SECONDS = 8884;
  CHANNEL_ATTRIBUTE_AI_LOSSY_LSB_REMOVAL_COMPRESSED_SAMP_SIZE = 8921;
  CHANNEL_ATTRIBUTE_AI_RAW_SAMP_SIZE = 8922;
  CHANNEL_ATTRIBUTE_AI_DATA_XFER_CUSTOM_THRESHOLD = 8972;
  CHANNEL_ATTRIBUTE_AI_USB_XFER_REQ_SIZE = 10894;
  CHANNEL_ATTRIBUTE_AO_USB_XFER_REQ_SIZE = 10895;
  CHANNEL_ATTRIBUTE_DI_USB_XFER_REQ_SIZE = 10896;
  CHANNEL_ATTRIBUTE_DO_USB_XFER_REQ_SIZE = 10897;
  CHANNEL_ATTRIBUTE_CI_USB_XFER_REQ_SIZE = 10898;
  CHANNEL_ATTRIBUTE_CO_USB_XFER_REQ_SIZE = 10899;
  CHANNEL_ATTRIBUTE_AI_ADC_CUSTOM_TIMING_MODE = 12139;
  CHANNEL_ATTRIBUTE_CI_COUNT_EDGES_COUNT_RESET_RESET_COUNT = 12208;
  CHANNEL_ATTRIBUTE_AI_AVERAGING_WIN_SIZE = 12270;
  CHANNEL_ATTRIBUTE_AI_USB_XFER_REQ_COUNT = 12288;
  CHANNEL_ATTRIBUTE_AO_USB_XFER_REQ_COUNT = 12289;
  CHANNEL_ATTRIBUTE_DI_USB_XFER_REQ_COUNT = 12290;
  CHANNEL_ATTRIBUTE_DO_USB_XFER_REQ_COUNT = 12291;
  CHANNEL_ATTRIBUTE_CI_USB_XFER_REQ_COUNT = 12292;
  CHANNEL_ATTRIBUTE_CO_USB_XFER_REQ_COUNT = 12293;
  CHANNEL_ATTRIBUTE_AI_DIG_FLTR_ORDER = 12480;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_ANG_ENCODER_PULSES_PER_REV = 12505;
  CHANNEL_ATTRIBUTE_CI_VELOCITY_DIV = 12524;
  CHANNEL_ATTRIBUTE_AI_FILTER_ORDER = 12662;
  CHANNEL_ATTRIBUTE_CI_FILTER_ORDER = 12730;
}

enum ChannelDoubleArrayAttribute {
  CHANNEL_DOUBLE_ARRAY_ATTRIBUTE_UNSPECIFIED = 0;
  CHANNEL_ATTRIBUTE_AI_DEV_SCALING_COEFF = 6448;
  CHANNEL_ATTRIBUTE_AO_DEV_SCALING_COEFF = 6449;
  CHANNEL_ATTRIBUTE_AI_CHAN_CAL_TABLE_PRE_SCALED_VALS = 8861;
  CHANNEL_ATTRIBUTE_AI_CHAN_CAL_TABLE_SCALED_VALS = 8862;
  CHANNEL_ATTRIBUTE_AI_CHAN_CAL_POLY_FORWARD_COEFF = 8863;
  CHANNEL_ATTRIBUTE_AI_CHAN_CAL_POLY_REVERSE_COEFF = 8864;
  CHANNEL_ATTRIBUTE_AI_CHAN_CAL_VERIF_REF_VALS = 8865;
  CHANNEL_ATTRIBUTE_AI_CHAN_CAL_VERIF_ACQ_VALS = 8866;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_TABLE_ELECTRICAL_VALS = 12174;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_TABLE_PHYSICAL_VALS = 12175;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_POLY_FORWARD_COEFF = 12176;
  CHANNEL_ATTRIBUTE_AI_BRIDGE_POLY_REVERSE_COEFF = 12177;
  CHANNEL_ATTRIBUTE_AI_DIG_FLTR_COEFF = 12487;
  CHANNEL_ATTRIBUTE_PWR_VOLTAGE_DEV_SCALING_COEFF = 12761;
  CHANNEL_ATTRIBUTE_PWR_CURRENT_DEV_SCALING_COEFF = 12762;
}

enum DeviceStringAttribute {
  DEVICE_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  DEVICE_ATTRIBUTE_PRODUCT_TYPE = 1585;
  DEVICE_ATTRIBUTE_AI_PHYSICAL_CHANS = 8990;
  DEVICE_ATTRIBUTE_AO_PHYSICAL_CHANS = 8991;
  DEVICE_ATTRIBUTE_DI_LINES = 8992;
  DEVICE_ATTRIBUTE_DI_PORTS = 8993;
  DEVICE_ATTRIBUTE_DO_LINES = 8994;
  DEVICE_ATTRIBUTE_DO_PORTS = 8995;
  DEVICE_ATTRIBUTE_CI_PHYSICAL_CHANS = 8996;
  DEVICE_ATTRIBUTE_CO_PHYSICAL_CHANS = 8997;
  DEVICE_ATTRIBUTE_CHASSIS_MODULE_DEV_NAMES = 10678;
  DEVICE_ATTRIBUTE_COMPACT_DAQ_CHASSIS_DEV_NAME = 10679;
  DEVICE_ATTRIBUTE_TERMINALS = 10816;
  DEVICE_ATTRIBUTE_TCPIP_HOSTNAME = 10891;
  DEVICE_ATTRIBUTE_TCPIP_ETHERNET_IP = 10892;
  DEVICE_ATTRIBUTE_TCPIP_WIRELESS_IP = 10893;
  DEVICE_ATTRIBUTE_ACCESSORY_PRODUCT_TYPES = 12141;
  DEVICE_ATTRIBUTE_NAV_PHYSICAL_CHANS = 12322;
  DEVICE_ATTRIBUTE_COMPACT_RIO_CHASSIS_DEV_NAME = 12641;
  DEVICE_ATTRIBUTE_FIELD_DAQ_DEV_NAME = 12657;
  DEVICE_ATTRIBUTE_FIELD_DAQ_BANK_DEV_NAMES = 12664;
  DEVICE_ATTRIBUTE_ID_PIN_PIN_NAMES = 12785;
  DEVICE_ATTRIBUTE_ID_PIN_MEM_SERIAL_NUMS = 12788;
}

enum DeviceUInt32Attribute {
  DEVICE_UINT32_ATTRIBUTE_UNSPECIFIED = 0;
  DEVICE_ATTRIBUTE_SERIAL_NUM = 1586;
  DEVICE_ATTRIBUTE_PRODUCT_NUM = 8989;
  DEVICE_ATTRIBUTE_PCI_BUS_NUM = 8999;
  DEVICE_ATTRIBUTE_PCI_DEV_NUM = 9000;
  DEVICE_ATTRIBUTE_PXI_CHASSIS_NUM = 9001;
  DEVICE_ATTRIBUTE_PXI_SLOT_NUM = 9002;
  DEVICE_ATTRIBUTE_NUM_DMA_CHANS = 9020;
  DEVICE_ATTRIBUTE_CI_MAX_SIZE = 10655;
  DEVICE_ATTRIBUTE_CO_MAX_SIZE = 10657;
  DEVICE_ATTRIBUTE_COMPACT_DAQ_SLOT_NUM = 10680;
  DEVICE_ATTRIBUTE_CARRIER_SERIAL_NUM = 10890;
  DEVICE_ATTRIBUTE_NAV_NUM_SURVEY_FIXES = 12331;
  DEVICE_ATTRIBUTE_NAV_REMAINING_SURVEY_FIXES = 12332;
  DEVICE_ATTRIBUTE_NAV_NUM_SATS = 12337;
  DEVICE_ATTRIBUTE_NUM_TIME_TRIGS = 12609;
  DEVICE_ATTRIBUTE_NUM_TIMESTAMP_ENGINES = 12610;
  DEVICE_ATTRIBUTE_COMPACT_RIO_SLOT_NUM = 12642;
  DEVICE_ATTRIBUTE_AI_NUM_SAMP_TIMING_ENGINES = 12643;
  DEVICE_ATTRIBUTE_AI_NUM_SYNC_PULSE_SRCS = 12644;
  DEVICE_ATTRIBUTE_AO_NUM_SAMP_TIMING_ENGINES = 12645;
  DEVICE_ATTRIBUTE_AO_NUM_SYNC_PULSE_SRCS = 12646;
  DEVICE_ATTRIBUTE_DI_NUM_SAMP_TIMING_ENGINES = 12647;
  DEVICE_ATTRIBUTE_DO_NUM_SAMP_TIMING_ENGINES = 12648;
}

enum DeviceBoolAttribute {
  DEVICE_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  DEVICE_ATTRIBUTE_IS_SIMULATED = 8906;
  DEVICE_ATTRIBUTE_ANLG_TRIG_SUPPORTED = 10628;
  DEVICE_ATTRIBUTE_DIG_TRIG_SUPPORTED = 10629;
  DEVICE_ATTRIBUTE_AI_SIMULTANEOUS_SAMPLING_SUPPORTED = 10639;
  DEVICE_ATTRIBUTE_AO_SAMP_CLK_SUPPORTED = 10646;
  DEVICE_ATTRIBUTE_CI_SAMP_CLK_SUPPORTED = 10654;
  DEVICE_ATTRIBUTE_CO_SAMP_CLK_SUPPORTED = 12123;
  DEVICE_ATTRIBUTE_TEDS_HWTEDS_SUPPORTED = 12246;
  DEVICE_ATTRIBUTE_TIME_TRIG_SUPPORTED = 12319;
  DEVICE_ATTRIBUTE_CI_UTC_OFFSET_READY = 12321;
  DEVICE_ATTRIBUTE_NAV_HAS_FIX = 12333;
  DEVICE_ATTRIBUTE_NAV_UTC_OFFSET_READY = 12335;
}

enum DeviceInt32Attribute {
  DEVICE_INT32_ATTRIBUTE_UNSPECIFIED = 0;
  DEVICE_ATTRIBUTE_BUS_TYPE = 8998;
  DEVICE_ATTRIBUTE_AI_TRIG_USAGE = 10630;
  DEVICE_ATTRIBUTE_AO_TRIG_USAGE = 10631;
  DEVICE_ATTRIBUTE_DI_TRIG_USAGE = 10632;
  DEVICE_ATTRIBUTE_DO_TRIG_USAGE = 10633;
  DEVICE_ATTRIBUTE_CI_TRIG_USAGE = 10634;
  DEVICE_ATTRIBUTE_CO_TRIG_USAGE = 10635;
  DEVICE_ATTRIBUTE_AI_COUPLINGS = 10644;
  DEVICE_ATTRIBUTE_PRODUCT_CATEGORY = 10665;
  DEVICE_ATTRIBUTE_CI_CURRENT_UTC_OFFSET = 12320;
  DEVICE_ATTRIBUTE_NAV_MODE = 12325;
  DEVICE_ATTRIBUTE_NAV_ALT_REF = 12329;
  DEVICE_ATTRIBUTE_NAV_ANT_STATUS = 12334;
  DEVICE_ATTRIBUTE_NAV_CURRENT_UTC_OFFSET = 12336;
}

enum DeviceDoubleAttribute {
  DEVICE_DOUBLE_ATTRIBUTE_UNSPECIFIED = 0;
  DEVICE_ATTRIBUTE_AI_MAX_SINGLE_CHAN_RATE = 10636;
  DEVICE_ATTRIBUTE_AI_MAX_MULTI_CHAN_RATE = 10637;
  DEVICE_ATTRIBUTE_AI_MIN_RATE = 10638;
  DEVICE_ATTRIBUTE_AO_MAX_RATE = 10647;
  DEVICE_ATTRIBUTE_AO_MIN_RATE = 10648;
  DEVICE_ATTRIBUTE_DI_MAX_RATE = 10649;
  DEVICE_ATTRIBUTE_DO_MAX_RATE = 10650;
  DEVICE_ATTRIBUTE_CI_MAX_TIMEBASE = 10656;
  DEVICE_ATTRIBUTE_CO_MAX_TIMEBASE = 10658;
  DEVICE_ATTRIBUTE_NAV_PRESET_LAT = 12326;
  DEVICE_ATTRIBUTE_NAV_PRESET_LONG = 12327;
  DEVICE_ATTRIBUTE_NAV_PRESET_ALT = 12328;
  DEVICE_ATTRIBUTE_NAV_PPS_COMPEN = 12330;
  DEVICE_ATTRIBUTE_NAV_PDOP = 12338;
  DEVICE_ATTRIBUTE_NAV_HDOP = 12339;
  DEVICE_ATTRIBUTE_NAV_VDOP = 12340;
}

enum DeviceDoubleArrayAttribute {
  DEVICE_DOUBLE_ARRAY_ATTRIBUTE_UNSPECIFIED = 0;
  DEVICE_ATTRIBUTE_AI_VOLTAGE_RNGS = 10640;
  DEVICE_ATTRIBUTE_AI_CURRENT_RNGS = 10641;
  DEVICE_ATTRIBUTE_AI_FREQ_RNGS = 10642;
  DEVICE_ATTRIBUTE_AI_GAINS = 10643;
  DEVICE_ATTRIBUTE_AI_LOWPASS_CUTOFF_FREQ_DISCRETE_VALS = 10645;
  DEVICE_ATTRIBUTE_AO_VOLTAGE_RNGS = 10651;
  DEVICE_ATTRIBUTE_AO_CURRENT_RNGS = 10652;
  DEVICE_ATTRIBUTE_AO_GAINS = 10653;
  DEVICE_ATTRIBUTE_AI_VOLTAGE_INT_EXCIT_DISCRETE_VALS = 10697;
  DEVICE_ATTRIBUTE_AI_VOLTAGE_INT_EXCIT_RANGE_VALS = 10698;
  DEVICE_ATTRIBUTE_AI_CURRENT_INT_EXCIT_DISCRETE_VALS = 10699;
  DEVICE_ATTRIBUTE_AI_LOWPASS_CUTOFF_FREQ_RANGE_VALS = 10703;
  DEVICE_ATTRIBUTE_AI_RESISTANCE_RNGS = 10773;
  DEVICE_ATTRIBUTE_AI_BRIDGE_RNGS = 12240;
  DEVICE_ATTRIBUTE_AI_DIG_FLTR_LOWPASS_CUTOFF_FREQ_DISCRETE_VALS = 12488;
  DEVICE_ATTRIBUTE_AI_DIG_FLTR_LOWPASS_CUTOFF_FREQ_RANGE_VALS = 12489;
  DEVICE_ATTRIBUTE_AI_CHARGE_RNGS = 12561;
}

enum DeviceUInt32ArrayAttribute {
  DEVICE_UINT32_ARRAY_ATTRIBUTE_UNSPECIFIED = 0;
  DEVICE_ATTRIBUTE_ACCESSORY_PRODUCT_NUMS = 12142;
  DEVICE_ATTRIBUTE_ACCESSORY_SERIAL_NUMS = 12143;
  DEVICE_ATTRIBUTE_ID_PIN_MEM_FAMILY_CODES = 12787;
  DEVICE_ATTRIBUTE_ID_PIN_MEM_SIZES = 12789;
}

enum DeviceInt32ArrayAttribute {
  DEVICE_INT32_ARRAY_ATTRIBUTE_UNSPECIFIED = 0;
  DEVICE_ATTRIBUTE_AI_SUPPORTED_MEAS_TYPES = 12242;
  DEVICE_ATTRIBUTE_AO_SUPPORTED_OUTPUT_TYPES = 12243;
  DEVICE_ATTRIBUTE_CI_SUPPORTED_MEAS_TYPES = 12244;
  DEVICE_ATTRIBUTE_CO_SUPPORTED_OUTPUT_TYPES = 12245;
  DEVICE_ATTRIBUTE_AI_SAMP_MODES = 12252;
  DEVICE_ATTRIBUTE_AO_SAMP_MODES = 12253;
  DEVICE_ATTRIBUTE_CI_SAMP_MODES = 12254;
  DEVICE_ATTRIBUTE_CO_SAMP_MODES = 12255;
  DEVICE_ATTRIBUTE_NAV_SUPPORTED_MEAS_TYPES = 12323;
  DEVICE_ATTRIBUTE_NAV_TRIG_USAGE = 12324;
  DEVICE_ATTRIBUTE_AI_DIG_FLTR_TYPES = 12551;
  DEVICE_ATTRIBUTE_ID_PIN_PIN_STATUSES = 12786;
}

enum ExportSignalDoubleAttribute {
  EXPORTSIGNAL_DOUBLE_ATTRIBUTE_UNSPECIFIED = 0;
  EXPORTSIGNAL_ATTRIBUTE_START_TRIG_DELAY = 1409;
  EXPORTSIGNAL_ATTRIBUTE_START_TRIG_PULSE_WIDTH = 1414;
  EXPORTSIGNAL_ATTRIBUTE_RDY_FOR_REF_EVENT_PULSE_WIDTH = 5668;
  EXPORTSIGNAL_ATTRIBUTE_ADV_TRIG_PULSE_WIDTH = 5704;
  EXPORTSIGNAL_ATTRIBUTE_ADV_CMPLT_EVENT_PULSE_WIDTH = 5716;
  EXPORTSIGNAL_ATTRIBUTE_20_MHZ_TIMEBASE_PULSE_WIDTH = 5728;
  EXPORTSIGNAL_ATTRIBUTE_SAMP_CLK_PULSE_WIDTH = 5734;
  EXPORTSIGNAL_ATTRIBUTE_AI_CONV_CLK_PULSE_WIDTH = 5776;
  EXPORTSIGNAL_ATTRIBUTE_FREQ_OUT_CLK_PULSE_WIDTH = 5908;
  EXPORTSIGNAL_ATTRIBUTE_CTR_OUT_EVENT_PULSE_WIDTH = 5920;
  EXPORTSIGNAL_ATTRIBUTE_REF_CLK_PULSE_WIDTH = 5944;
  EXPORTSIGNAL_ATTRIBUTE_ADV_CMPLT_EVENT_DELAY = 5975;
  EXPORTSIGNAL_ATTRIBUTE_SAMP_CLK_DELAY_OFFSET = 8644;
  EXPORTSIGNAL_ATTRIBUTE_HSHK_EVENT_DELAY = 8892;
  EXPORTSIGNAL_ATTRIBUTE_HSHK_EVENT_INTERLOCKED_DEASSERT_DELAY = 8895;
  EXPORTSIGNAL_ATTRIBUTE_HSHK_EVENT_PULSE_WIDTH = 8897;
}

enum ExportSignalStringAttribute {
  EXPORTSIGNAL_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  EXPORTSIGNAL_ATTRIBUTE_START_TRIG_OUTPUT_TERM = 1412;
  EXPORTSIGNAL_ATTRIBUTE_REF_TRIG_OUTPUT_TERM = 1424;
  EXPORTSIGNAL_ATTRIBUTE_RDY_FOR_START_EVENT_OUTPUT_TERM = 5641;
  EXPORTSIGNAL_ATTRIBUTE_PAUSE_TRIG_OUTPUT_TERM = 5653;
  EXPORTSIGNAL_ATTRIBUTE_DATA_ACTIVE_EVENT_OUTPUT_TERM = 5683;
  EXPORTSIGNAL_ATTRIBUTE_ADV_TRIG_OUTPUT_TERM = 5701;
  EXPORTSIGNAL_ATTRIBUTE_ADV_CMPLT_EVENT_OUTPUT_TERM = 5713;
  EXPORTSIGNAL_ATTRIBUTE_20_MHZ_TIMEBASE_OUTPUT_TERM = 5719;
  EXPORTSIGNAL_ATTRIBUTE_SAMP_CLK_OUTPUT_TERM = 5731;
  EXPORTSIGNAL_ATTRIBUTE_AI_CONV_CLK_OUTPUT_TERM = 5767;
  EXPORTSIGNAL_ATTRIBUTE_CTR_OUT_EVENT_OUTPUT_TERM = 5911;
  EXPORTSIGNAL_ATTRIBUTE_AI_HOLD_CMPLT_EVENT_OUTPUT_TERM = 6381;
  EXPORTSIGNAL_ATTRIBUTE_SAMP_CLK_TIMEBASE_OUTPUT_TERM = 6393;
  EXPORTSIGNAL_ATTRIBUTE_CHANGE_DETECT_EVENT_OUTPUT_TERM = 8599;
  EXPORTSIGNAL_ATTRIBUTE_DIVIDED_SAMP_CLK_TIMEBASE_OUTPUT_TERM = 8609;
  EXPORTSIGNAL_ATTRIBUTE_WATCHDOG_EXPIRED_EVENT_OUTPUT_TERM = 8618;
  EXPORTSIGNAL_ATTRIBUTE_SYNC_PULSE_EVENT_OUTPUT_TERM = 8764;
  EXPORTSIGNAL_ATTRIBUTE_10_MHZ_REF_CLK_OUTPUT_TERM = 8814;
  EXPORTSIGNAL_ATTRIBUTE_RDY_FOR_XFER_EVENT_OUTPUT_TERM = 8885;
  EXPORTSIGNAL_ATTRIBUTE_HSHK_EVENT_OUTPUT_TERM = 8890;
}

enum ExportSignalResetAttribute {
  EXPORTSIGNAL_RESET_ATTRIBUTE_UNSPECIFIED = 0;
  EXPORTSIGNAL_RESET_ATTRIBUTE_START_TRIG_OUTPUT_TERM = 1412;
  EXPORTSIGNAL_RESET_ATTRIBUTE_START_TRIG_PULSE_POLARITY = 1413;
  EXPORTSIGNAL_RESET_ATTRIBUTE_REF_TRIG_OUTPUT_TERM = 1424;
  EXPORTSIGNAL_RESET_ATTRIBUTE_REF_TRIG_PULSE_POLARITY = 1425;
  EXPORTSIGNAL_RESET_ATTRIBUTE_START_TRIG_PULSE_WIDTH_UNITS = 5634;
  EXPORTSIGNAL_RESET_ATTRIBUTE_RDY_FOR_START_EVENT_OUTPUT_TERM = 5641;
  EXPORTSIGNAL_RESET_ATTRIBUTE_PAUSE_TRIG_OUTPUT_TERM = 5653;
  EXPORTSIGNAL_RESET_ATTRIBUTE_PAUSE_TRIG_LVL_ACTIVE_LVL = 5654;
  EXPORTSIGNAL_RESET_ATTRIBUTE_RDY_FOR_REF_EVENT_PULSE_WIDTH_UNITS = 5667;
  EXPORTSIGNAL_RESET_ATTRIBUTE_DATA_ACTIVE_EVENT_OUTPUT_TERM = 5683;
  EXPORTSIGNAL_RESET_ATTRIBUTE_DATA_ACTIVE_EVENT_LVL_ACTIVE_LVL = 5684;
  EXPORTSIGNAL_RESET_ATTRIBUTE_ADV_TRIG_OUTPUT_TERM = 5701;
  EXPORTSIGNAL_RESET_ATTRIBUTE_ADV_TRIG_PULSE_WIDTH_UNITS = 5703;
  EXPORTSIGNAL_RESET_ATTRIBUTE_ADV_TRIG_PULSE_WIDTH = 5704;
  EXPORTSIGNAL_RESET_ATTRIBUTE_ADV_CMPLT_EVENT_ENABLE = 5706;
  EXPORTSIGNAL_RESET_ATTRIBUTE_ADV_CMPLT_EVENT_OUTPUT_TERM = 5713;
  EXPORTSIGNAL_RESET_ATTRIBUTE_ADV_CMPLT_EVENT_PULSE_POLARITY = 5714;
  EXPORTSIGNAL_RESET_ATTRIBUTE_ADV_CMPLT_EVENT_PULSE_WIDTH_UNITS = 5715;
  EXPORTSIGNAL_RESET_ATTRIBUTE_ADV_CMPLT_EVENT_PULSE_WIDTH = 5716;
  EXPORTSIGNAL_RESET_ATTRIBUTE_20_MHZ_TIMEBASE_DIVIDE_DOWN_BY_N = 5718;
  EXPORTSIGNAL_RESET_ATTRIBUTE_20_MHZ_TIMEBASE_OUTPUT_TERM = 5719;
  EXPORTSIGNAL_RESET_ATTRIBUTE_20_MHZ_TIMEBASE_PULSE_WIDTH_UNITS = 5721;
  EXPORTSIGNAL_RESET_ATTRIBUTE_SAMP_CLK_OUTPUT_TERM = 5731;
  EXPORTSIGNAL_RESET_ATTRIBUTE_SAMP_CLK_PULSE_POLARITY = 5732;
  EXPORTSIGNAL_RESET_ATTRIBUTE_SAMP_CLK_PULSE_WIDTH_UNITS = 5733;
  EXPORTSIGNAL_RESET_ATTRIBUTE_AI_CONV_CLK_OUTPUT_TERM = 5767;
  EXPORTSIGNAL_RESET_ATTRIBUTE_AI_CONV_CLK_PULSE_WIDTH_UNITS = 5769;
  EXPORTSIGNAL_RESET_ATTRIBUTE_FREQ_OUT_CLK_PULSE_WIDTH_UNITS = 5907;
  EXPORTSIGNAL_RESET_ATTRIBUTE_CTR_OUT_EVENT_OUTPUT_TERM = 5911;
  EXPORTSIGNAL_RESET_ATTRIBUTE_CTR_OUT_EVENT_PULSE_POLARITY = 5912;
  EXPORTSIGNAL_RESET_ATTRIBUTE_CTR_OUT_EVENT_PULSE_WIDTH_UNITS = 5913;
  EXPORTSIGNAL_RESET_ATTRIBUTE_REF_CLK_PULSE_WIDTH_UNITS = 5943;
  EXPORTSIGNAL_RESET_ATTRIBUTE_START_TRIG_OUTPUT_BEHAVIOR = 5955;
  EXPORTSIGNAL_RESET_ATTRIBUTE_START_TRIG_TOGGLE_INITIAL_STATE = 5956;
  EXPORTSIGNAL_RESET_ATTRIBUTE_START_TRIG_DELAY_UNITS = 5965;
  EXPORTSIGNAL_RESET_ATTRIBUTE_CTR_OUT_EVENT_OUTPUT_BEHAVIOR = 5967;
  EXPORTSIGNAL_RESET_ATTRIBUTE_RDY_FOR_START_EVENT_LVL_ACTIVE_LVL = 5969;
  EXPORTSIGNAL_RESET_ATTRIBUTE_ADV_CMPLT_EVENT_DELAY = 5975;
  EXPORTSIGNAL_RESET_ATTRIBUTE_CTR_OUT_EVENT_TOGGLE_IDLE_STATE = 6250;
  EXPORTSIGNAL_RESET_ATTRIBUTE_SAMP_CLK_OUTPUT_BEHAVIOR = 6251;
  EXPORTSIGNAL_RESET_ATTRIBUTE_AI_HOLD_CMPLT_EVENT_OUTPUT_TERM = 6381;
  EXPORTSIGNAL_RESET_ATTRIBUTE_AI_HOLD_CMPLT_EVENT_PULSE_POLARITY = 6382;
  EXPORTSIGNAL_RESET_ATTRIBUTE_SAMP_CLK_TIMEBASE_OUTPUT_TERM = 6393;
  EXPORTSIGNAL_RESET_ATTRIBUTE_CHANGE_DETECT_EVENT_OUTPUT_TERM = 8599;
  EXPORTSIGNAL_RESET_ATTRIBUTE_DIVIDED_SAMP_CLK_TIMEBASE_OUTPUT_TERM = 8609;
  EXPORTSIGNAL_RESET_ATTRIBUTE_WATCHDOG_EXPIRED_EVENT_OUTPUT_TERM = 8618;
  EXPORTSIGNAL_RESET_ATTRIBUTE_SAMP_CLK_DELAY_OFFSET = 8644;
  EXPORTSIGNAL_RESET_ATTRIBUTE_SYNC_PULSE_EVENT_OUTPUT_TERM = 8764;
  EXPORTSIGNAL_RESET_ATTRIBUTE_10_MHZ_REF_CLK_OUTPUT_TERM = 8814;
  EXPORTSIGNAL_RESET_ATTRIBUTE_RDY_FOR_XFER_EVENT_OUTPUT_TERM = 8885;
  EXPORTSIGNAL_RESET_ATTRIBUTE_RDY_FOR_XFER_EVENT_LVL_ACTIVE_LVL = 8886;
  EXPORTSIGNAL_RESET_ATTRIBUTE_HSHK_EVENT_OUTPUT_TERM = 8890;
  EXPORTSIGNAL_RESET_ATTRIBUTE_HSHK_EVENT_OUTPUT_BEHAVIOR = 8891;
  EXPORTSIGNAL_RESET_ATTRIBUTE_HSHK_EVENT_DELAY = 8892;
  EXPORTSIGNAL_RESET_ATTRIBUTE_HSHK_EVENT_INTERLOCKED_ASSERTED_LVL = 8893;
  EXPORTSIGNAL_RESET_ATTRIBUTE_HSHK_EVENT_INTERLOCKED_ASSERT_ON_START = 8894;
  EXPORTSIGNAL_RESET_ATTRIBUTE_HSHK_EVENT_INTERLOCKED_DEASSERT_DELAY = 8895;
  EXPORTSIGNAL_RESET_ATTRIBUTE_HSHK_EVENT_PULSE_POLARITY = 8896;
  EXPORTSIGNAL_RESET_ATTRIBUTE_HSHK_EVENT_PULSE_WIDTH = 8897;
  EXPORTSIGNAL_RESET_ATTRIBUTE_CHANGE_DETECT_EVENT_PULSE_POLARITY = 8963;
  EXPORTSIGNAL_RESET_ATTRIBUTE_RDY_FOR_XFER_EVENT_DEASSERT_COND = 10595;
  EXPORTSIGNAL_RESET_ATTRIBUTE_RDY_FOR_XFER_EVENT_DEASSERT_COND_CUSTOM_THRESHOLD = 10596;
}

enum ExportSignalInt32Attribute {
  EXPORTSIGNAL_INT32_ATTRIBUTE_UNSPECIFIED = 0;
  EXPORTSIGNAL_ATTRIBUTE_START_TRIG_PULSE_POLARITY = 1413;
  EXPORTSIGNAL_ATTRIBUTE_REF_TRIG_PULSE_POLARITY = 1425;
  EXPORTSIGNAL_ATTRIBUTE_START_TRIG_PULSE_WIDTH_UNITS = 5634;
  EXPORTSIGNAL_ATTRIBUTE_PAUSE_TRIG_LVL_ACTIVE_LVL = 5654;
  EXPORTSIGNAL_ATTRIBUTE_RDY_FOR_REF_EVENT_PULSE_POLARITY = 5666;
  EXPORTSIGNAL_ATTRIBUTE_RDY_FOR_REF_EVENT_PULSE_WIDTH_UNITS = 5667;
  EXPORTSIGNAL_ATTRIBUTE_DATA_ACTIVE_EVENT_LVL_ACTIVE_LVL = 5684;
  EXPORTSIGNAL_ATTRIBUTE_ADV_TRIG_PULSE_POLARITY = 5702;
  EXPORTSIGNAL_ATTRIBUTE_ADV_TRIG_PULSE_WIDTH_UNITS = 5703;
  EXPORTSIGNAL_ATTRIBUTE_ADV_CMPLT_EVENT_PULSE_POLARITY = 5714;
  EXPORTSIGNAL_ATTRIBUTE_ADV_CMPLT_EVENT_PULSE_WIDTH_UNITS = 5715;
  EXPORTSIGNAL_ATTRIBUTE_20_MHZ_TIMEBASE_PULSE_POLARITY = 5720;
  EXPORTSIGNAL_ATTRIBUTE_20_MHZ_TIMEBASE_PULSE_WIDTH_UNITS = 5721;
  EXPORTSIGNAL_ATTRIBUTE_SAMP_CLK_PULSE_POLARITY = 5732;
  EXPORTSIGNAL_ATTRIBUTE_SAMP_CLK_PULSE_WIDTH_UNITS = 5733;
  EXPORTSIGNAL_ATTRIBUTE_AI_CONV_CLK_PULSE_POLARITY = 5768;
  EXPORTSIGNAL_ATTRIBUTE_AI_CONV_CLK_PULSE_WIDTH_UNITS = 5769;
  EXPORTSIGNAL_ATTRIBUTE_FREQ_OUT_CLK_PULSE_POLARITY = 5906;
  EXPORTSIGNAL_ATTRIBUTE_FREQ_OUT_CLK_PULSE_WIDTH_UNITS = 5907;
  EXPORTSIGNAL_ATTRIBUTE_CTR_OUT_EVENT_PULSE_POLARITY = 5912;
  EXPORTSIGNAL_ATTRIBUTE_CTR_OUT_EVENT_PULSE_WIDTH_UNITS = 5913;
  EXPORTSIGNAL_ATTRIBUTE_REF_CLK_PULSE_POLARITY = 5942;
  EXPORTSIGNAL_ATTRIBUTE_REF_CLK_PULSE_WIDTH_UNITS = 5943;
  EXPORTSIGNAL_ATTRIBUTE_START_TRIG_OUTPUT_BEHAVIOR = 5955;
  EXPORTSIGNAL_ATTRIBUTE_START_TRIG_TOGGLE_INITIAL_STATE = 5956;
  EXPORTSIGNAL_ATTRIBUTE_START_TRIG_DELAY_UNITS = 5965;
  EXPORTSIGNAL_ATTRIBUTE_CTR_OUT_EVENT_OUTPUT_BEHAVIOR = 5967;
  EXPORTSIGNAL_ATTRIBUTE_CTR_OUT_EVENT_LVL_POLARITY = 5968;
  EXPORTSIGNAL_ATTRIBUTE_RDY_FOR_START_EVENT_LVL_ACTIVE_LVL = 5969;
  EXPORTSIGNAL_ATTRIBUTE_CTR_OUT_EVENT_TOGGLE_IDLE_STATE = 6250;
  EXPORTSIGNAL_ATTRIBUTE_SAMP_CLK_OUTPUT_BEHAVIOR = 6251;
  EXPORTSIGNAL_ATTRIBUTE_AI_HOLD_CMPLT_EVENT_PULSE_POLARITY = 6382;
  EXPORTSIGNAL_ATTRIBUTE_RDY_FOR_XFER_EVENT_LVL_ACTIVE_LVL = 8886;
  EXPORTSIGNAL_ATTRIBUTE_HSHK_EVENT_OUTPUT_BEHAVIOR = 8891;
  EXPORTSIGNAL_ATTRIBUTE_HSHK_EVENT_INTERLOCKED_ASSERTED_LVL = 8893;
  EXPORTSIGNAL_ATTRIBUTE_HSHK_EVENT_PULSE_POLARITY = 8896;
  EXPORTSIGNAL_ATTRIBUTE_CHANGE_DETECT_EVENT_PULSE_POLARITY = 8963;
  EXPORTSIGNAL_ATTRIBUTE_RDY_FOR_XFER_EVENT_DEASSERT_COND = 10595;
}

enum ExportSignalBoolAttribute {
  EXPORTSIGNAL_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  EXPORTSIGNAL_ATTRIBUTE_ADV_CMPLT_EVENT_ENABLE = 5706;
  EXPORTSIGNAL_ATTRIBUTE_HSHK_EVENT_INTERLOCKED_ASSERT_ON_START = 8894;
}

enum ExportSignalUInt32Attribute {
  EXPORTSIGNAL_UINT32_ATTRIBUTE_UNSPECIFIED = 0;
  EXPORTSIGNAL_ATTRIBUTE_20_MHZ_TIMEBASE_DIVIDE_DOWN_BY_N = 5718;
  EXPORTSIGNAL_ATTRIBUTE_RDY_FOR_XFER_EVENT_DEASSERT_COND_CUSTOM_THRESHOLD = 10596;
}

enum PersistedChannelStringAttribute {
  PERSISTEDCHANNEL_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  PERSISTEDCHANNEL_ATTRIBUTE_ACTIVE_CHAN = 8911;
  PERSISTEDCHANNEL_ATTRIBUTE_AUTHOR = 8912;
}

enum PersistedChannelBoolAttribute {
  PERSISTEDCHANNEL_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  PERSISTEDCHANNEL_ATTRIBUTE_ALLOW_INTERACTIVE_EDITING = 8913;
  PERSISTEDCHANNEL_ATTRIBUTE_ALLOW_INTERACTIVE_DELETION = 8914;
}

enum PersistedScaleStringAttribute {
  PERSISTEDSCALE_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  PERSISTEDSCALE_ATTRIBUTE_ACTIVE_SCALE = 8915;
  PERSISTEDSCALE_ATTRIBUTE_AUTHOR = 8916;
}

enum PersistedScaleBoolAttribute {
  PERSISTEDSCALE_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  PERSISTEDSCALE_ATTRIBUTE_ALLOW_INTERACTIVE_EDITING = 8917;
  PERSISTEDSCALE_ATTRIBUTE_ALLOW_INTERACTIVE_DELETION = 8918;
}

enum PersistedTaskStringAttribute {
  PERSISTEDTASK_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  PERSISTEDTASK_ATTRIBUTE_ACTIVE_TASK = 8907;
  PERSISTEDTASK_ATTRIBUTE_AUTHOR = 8908;
}

enum PersistedTaskBoolAttribute {
  PERSISTEDTASK_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  PERSISTEDTASK_ATTRIBUTE_ALLOW_INTERACTIVE_EDITING = 8909;
  PERSISTEDTASK_ATTRIBUTE_ALLOW_INTERACTIVE_DELETION = 8910;
}

enum PhysicalChannelUInt32Attribute {
  PHYSICALCHANNEL_UINT32_ATTRIBUTE_UNSPECIFIED = 0;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_TEDS_MFG_ID = 8666;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_TEDS_MODEL_NUM = 8667;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_TEDS_SERIAL_NUM = 8668;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_TEDS_VERSION_NUM = 8669;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_DI_PORT_WIDTH = 10660;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_DO_PORT_WIDTH = 10663;
}

enum PhysicalChannelStringAttribute {
  PHYSICALCHANNEL_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_TEDS_VERSION_LETTER = 8670;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AI_INPUT_SRCS = 12248;
}

enum PhysicalChannelBytesAttribute {
  PHYSICALCHANNEL_BYTES_ATTRIBUTE_UNSPECIFIED = 0;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_TEDS_BIT_STREAM = 8671;
}

enum PhysicalChannelUInt32ArrayAttribute {
  PHYSICALCHANNEL_UINT32_ARRAY_ATTRIBUTE_UNSPECIFIED = 0;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_TEDS_TEMPLATE_I_DS = 8847;
}

enum PhysicalChannelInt32Attribute {
  PHYSICALCHANNEL_INT32_ATTRIBUTE_UNSPECIFIED = 0;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AI_TERM_CFGS = 9026;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AO_TERM_CFGS = 10659;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AI_POWER_CONTROL_TYPE = 12654;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_DIG_PORT_LOGIC_FAMILY = 12779;
}

enum PhysicalChannelBoolAttribute {
  PHYSICALCHANNEL_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_DI_SAMP_CLK_SUPPORTED = 10661;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_DI_CHANGE_DETECT_SUPPORTED = 10662;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_DO_SAMP_CLK_SUPPORTED = 10664;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AO_MANUAL_CONTROL_ENABLE = 10782;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AO_MANUAL_CONTROL_SHORT_DETECTED = 11971;
  PHYSICALCHANNEL_ATTRIBUTE_AO_POWER_AMP_CHANNEL_ENABLE = 12386;
  PHYSICALCHANNEL_ATTRIBUTE_AO_POWER_AMP_OVERCURRENT = 12388;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AI_POWER_CONTROL_ENABLE = 12653;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AI_SENSOR_POWER_OPEN_CHAN = 12668;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AI_SENSOR_POWER_OVERCURRENT = 12669;
}

enum PhysicalChannelResetAttribute {
  PHYSICALCHANNEL_RESET_ATTRIBUTE_UNSPECIFIED = 0;
  PHYSICALCHANNEL_RESET_ATTRIBUTE_PHYSICAL_CHAN_AO_MANUAL_CONTROL_ENABLE = 10782;
  PHYSICALCHANNEL_RESET_ATTRIBUTE_AO_POWER_AMP_CHANNEL_ENABLE = 12386;
  PHYSICALCHANNEL_RESET_ATTRIBUTE_PHYSICAL_CHAN_AI_POWER_CONTROL_VOLTAGE = 12652;
  PHYSICALCHANNEL_RESET_ATTRIBUTE_PHYSICAL_CHAN_AI_POWER_CONTROL_ENABLE = 12653;
  PHYSICALCHANNEL_RESET_ATTRIBUTE_PHYSICAL_CHAN_AI_POWER_CONTROL_TYPE = 12654;
  PHYSICALCHANNEL_RESET_ATTRIBUTE_PHYSICAL_CHAN_DIG_PORT_LOGIC_FAMILY = 12779;
}

enum PhysicalChannelDoubleAttribute {
  PHYSICALCHANNEL_DOUBLE_ATTRIBUTE_UNSPECIFIED = 0;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AO_MANUAL_CONTROL_AMPLITUDE = 10783;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AO_MANUAL_CONTROL_FREQ = 10784;
  PHYSICALCHANNEL_ATTRIBUTE_AO_POWER_AMP_GAIN = 12389;
  PHYSICALCHANNEL_ATTRIBUTE_AO_POWER_AMP_OFFSET = 12390;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AI_POWER_CONTROL_VOLTAGE = 12652;
}

enum PhysicalChannelInt32ArrayAttribute {
  PHYSICALCHANNEL_INT32_ARRAY_ATTRIBUTE_UNSPECIFIED = 0;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AI_SUPPORTED_MEAS_TYPES = 12247;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AO_SUPPORTED_OUTPUT_TYPES = 12249;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_CI_SUPPORTED_MEAS_TYPES = 12250;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_CO_SUPPORTED_OUTPUT_TYPES = 12251;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_DI_SAMP_MODES = 12256;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_DO_SAMP_MODES = 12257;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_NAV_SUPPORTED_MEAS_TYPES = 12343;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AO_SUPPORTED_POWER_UP_OUTPUT_TYPES = 12366;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AI_SENSOR_POWER_TYPES = 12665;
}

enum PhysicalChannelDoubleArrayAttribute {
  PHYSICALCHANNEL_DOUBLE_ARRAY_ATTRIBUTE_UNSPECIFIED = 0;
  PHYSICALCHANNEL_ATTRIBUTE_AO_POWER_AMP_SCALING_COEFF = 12387;
  PHYSICALCHANNEL_ATTRIBUTE_PHYSICAL_CHAN_AI_SENSOR_POWER_VOLTAGE_RANGE_VALS = 12666;
}

enum ReadInt32Attribute {
  READ_INT32_ATTRIBUTE_UNSPECIFIED = 0;
  READ_ATTRIBUTE_OVERWRITE = 4625;
  READ_ATTRIBUTE_RELATIVE_TO = 6410;
  READ_ATTRIBUTE_OFFSET = 6411;
  READ_ATTRIBUTE_WAIT_MODE = 8754;
  READ_ATTRIBUTE_LOGGING_MODE = 11973;
  READ_ATTRIBUTE_LOGGING_TDMS_OPERATION = 11975;
}

enum ReadResetAttribute {
  READ_RESET_ATTRIBUTE_UNSPECIFIED = 0;
  READ_RESET_ATTRIBUTE_OVERWRITE = 4625;
  READ_RESET_ATTRIBUTE_READ_ALL_AVAIL_SAMP = 4629;
  READ_RESET_ATTRIBUTE_CHANNELS_TO_READ = 6179;
  READ_RESET_ATTRIBUTE_AUTO_START = 6182;
  READ_RESET_ATTRIBUTE_RELATIVE_TO = 6410;
  READ_RESET_ATTRIBUTE_OFFSET = 6411;
  READ_RESET_ATTRIBUTE_WAIT_MODE = 8754;
  READ_RESET_ATTRIBUTE_SLEEP_TIME = 8880;
  READ_RESET_ATTRIBUTE_LOGGING_FILE_PATH = 11972;
  READ_RESET_ATTRIBUTE_LOGGING_MODE = 11973;
  READ_RESET_ATTRIBUTE_LOGGING_TDMS_GROUP_NAME = 11974;
  READ_RESET_ATTRIBUTE_LOGGING_TDMS_OPERATION = 11975;
  READ_RESET_ATTRIBUTE_LOGGING_FILE_WRITE_SIZE = 12227;
  READ_RESET_ATTRIBUTE_LOGGING_FILE_PREALLOCATION_SIZE = 12230;
  READ_RESET_ATTRIBUTE_LOGGING_PAUSE = 12259;
  READ_RESET_ATTRIBUTE_LOGGING_SAMPS_PER_FILE = 12260;
}

enum ReadBoolAttribute {
  READ_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  READ_ATTRIBUTE_READ_ALL_AVAIL_SAMP = 4629;
  READ_ATTRIBUTE_AUTO_START = 6182;
  READ_ATTRIBUTE_OVERLOADED_CHANS_EXIST = 8564;
  READ_ATTRIBUTE_CHANGE_DETECT_HAS_OVERFLOWED = 8596;
  READ_ATTRIBUTE_OVERCURRENT_CHANS_EXIST = 10726;
  READ_ATTRIBUTE_OPEN_CURRENT_LOOP_CHANS_EXIST = 10761;
  READ_ATTRIBUTE_OPEN_THRMCPL_CHANS_EXIST = 10902;
  READ_ATTRIBUTE_COMMON_MODE_RANGE_ERROR_CHANS_EXIST = 10904;
  READ_ATTRIBUTE_ACCESSORY_INSERTION_OR_REMOVAL_DETECTED = 12144;
  READ_ATTRIBUTE_LOGGING_PAUSE = 12259;
  READ_ATTRIBUTE_NAV_FIX_LOST = 12341;
  READ_ATTRIBUTE_OVERTEMPERATURE_CHANS_EXIST = 12417;
  READ_ATTRIBUTE_EXCIT_FAULT_CHANS_EXIST = 12424;
  READ_ATTRIBUTE_OPEN_CHANS_EXIST = 12544;
  READ_ATTRIBUTE_PLL_UNLOCKED_CHANS_EXIST = 12568;
  READ_ATTRIBUTE_SYNC_UNLOCKED_CHANS_EXIST = 12605;
  READ_ATTRIBUTE_INPUT_LIMITS_FAULT_CHANS_EXIST = 12687;
  READ_ATTRIBUTE_POWER_SUPPLY_FAULT_CHANS_EXIST = 12690;
  READ_ATTRIBUTE_REMOTE_SENSE_ERROR_CHANS_EXIST = 12765;
  READ_ATTRIBUTE_AUX_POWER_ERROR_CHANS_EXIST = 12767;
  READ_ATTRIBUTE_REVERSE_VOLTAGE_ERROR_CHANS_EXIST = 12774;
}

enum ReadUInt64Attribute {
  READ_UINT64_ATTRIBUTE_UNSPECIFIED = 0;
  READ_ATTRIBUTE_CURR_READ_POS = 4641;
  READ_ATTRIBUTE_TOTAL_SAMP_PER_CHAN_ACQUIRED = 6442;
  READ_ATTRIBUTE_LOGGING_FILE_PREALLOCATION_SIZE = 12230;
  READ_ATTRIBUTE_LOGGING_SAMPS_PER_FILE = 12260;
}

enum ReadUInt32Attribute {
  READ_UINT32_ATTRIBUTE_UNSPECIFIED = 0;
  READ_ATTRIBUTE_AVAIL_SAMP_PER_CHAN = 4643;
  READ_ATTRIBUTE_RAW_DATA_WIDTH = 8570;
  READ_ATTRIBUTE_NUM_CHANS = 8571;
  READ_ATTRIBUTE_DIGITAL_LINES_BYTES_PER_CHAN = 8572;
  READ_ATTRIBUTE_LOGGING_FILE_WRITE_SIZE = 12227;
}

enum ReadStringAttribute {
  READ_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  READ_ATTRIBUTE_CHANNELS_TO_READ = 6179;
  READ_ATTRIBUTE_OVERLOADED_CHANS = 8565;
  READ_ATTRIBUTE_OVERCURRENT_CHANS = 10727;
  READ_ATTRIBUTE_OPEN_CURRENT_LOOP_CHANS = 10762;
  READ_ATTRIBUTE_OPEN_THRMCPL_CHANS = 10903;
  READ_ATTRIBUTE_COMMON_MODE_RANGE_ERROR_CHANS = 10905;
  READ_ATTRIBUTE_LOGGING_FILE_PATH = 11972;
  READ_ATTRIBUTE_LOGGING_TDMS_GROUP_NAME = 11974;
  READ_ATTRIBUTE_DEVS_WITH_INSERTED_OR_REMOVED_ACCESSORIES = 12145;
  READ_ATTRIBUTE_OVERTEMPERATURE_CHANS = 12418;
  READ_ATTRIBUTE_EXCIT_FAULT_CHANS = 12425;
  READ_ATTRIBUTE_OPEN_CHANS = 12545;
  READ_ATTRIBUTE_OPEN_CHANS_DETAILS = 12546;
  READ_ATTRIBUTE_PLL_UNLOCKED_CHANS = 12569;
  READ_ATTRIBUTE_SYNC_UNLOCKED_CHANS = 12606;
  READ_ATTRIBUTE_INPUT_LIMITS_FAULT_CHANS = 12688;
  READ_ATTRIBUTE_POWER_SUPPLY_FAULT_CHANS = 12691;
  READ_ATTRIBUTE_REMOTE_SENSE_ERROR_CHANS = 12766;
  READ_ATTRIBUTE_AUX_POWER_ERROR_CHANS = 12768;
  READ_ATTRIBUTE_REVERSE_VOLTAGE_ERROR_CHANS = 12775;
}

enum ReadDoubleAttribute {
  READ_DOUBLE_ATTRIBUTE_UNSPECIFIED = 0;
  READ_ATTRIBUTE_SLEEP_TIME = 8880;
}

enum RealTimeUInt32Attribute {
  REALTIME_UINT32_ATTRIBUTE_UNSPECIFIED = 0;
  REALTIME_ATTRIBUTE_NUM_OF_WARMUP_ITERS = 8941;
}

enum RealTimeResetAttribute {
  REALTIME_RESET_ATTRIBUTE_UNSPECIFIED = 0;
  REALTIME_RESET_ATTRIBUTE_NUM_OF_WARMUP_ITERS = 8941;
  REALTIME_RESET_ATTRIBUTE_CONV_LATE_ERRORS_TO_WARNINGS = 8942;
  REALTIME_RESET_ATTRIBUTE_WAIT_FOR_NEXT_SAMP_CLK_WAIT_MODE = 8943;
  REALTIME_RESET_ATTRIBUTE_REPORT_MISSED_SAMP = 8985;
  REALTIME_RESET_ATTRIBUTE_WRITE_RECOVERY_MODE = 8986;
}

enum RealTimeBoolAttribute {
  REALTIME_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  REALTIME_ATTRIBUTE_CONV_LATE_ERRORS_TO_WARNINGS = 8942;
  REALTIME_ATTRIBUTE_REPORT_MISSED_SAMP = 8985;
}

enum RealTimeInt32Attribute {
  REALTIME_INT32_ATTRIBUTE_UNSPECIFIED = 0;
  REALTIME_ATTRIBUTE_WAIT_FOR_NEXT_SAMP_CLK_WAIT_MODE = 8943;
  REALTIME_ATTRIBUTE_WRITE_RECOVERY_MODE = 8986;
}

enum ScaleStringAttribute {
  SCALE_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  SCALE_ATTRIBUTE_DESCR = 4646;
  SCALE_ATTRIBUTE_SCALED_UNITS = 6427;
}

enum ScaleDoubleAttribute {
  SCALE_DOUBLE_ATTRIBUTE_UNSPECIFIED = 0;
  SCALE_ATTRIBUTE_LIN_SLOPE = 4647;
  SCALE_ATTRIBUTE_LIN_Y_INTERCEPT = 4648;
  SCALE_ATTRIBUTE_MAP_SCALED_MAX = 4649;
  SCALE_ATTRIBUTE_MAP_SCALED_MIN = 4656;
  SCALE_ATTRIBUTE_MAP_PRE_SCALED_MAX = 4657;
  SCALE_ATTRIBUTE_MAP_PRE_SCALED_MIN = 4658;
}

enum ScaleDoubleArrayAttribute {
  SCALE_DOUBLE_ARRAY_ATTRIBUTE_UNSPECIFIED = 0;
  SCALE_ATTRIBUTE_POLY_FORWARD_COEFF = 4660;
  SCALE_ATTRIBUTE_POLY_REVERSE_COEFF = 4661;
  SCALE_ATTRIBUTE_TABLE_SCALED_VALS = 4662;
  SCALE_ATTRIBUTE_TABLE_PRE_SCALED_VALS = 4663;
}

enum ScaleInt32Attribute {
  SCALE_INT32_ATTRIBUTE_UNSPECIFIED = 0;
  SCALE_ATTRIBUTE_PRE_SCALED_UNITS = 6391;
  SCALE_ATTRIBUTE_TYPE = 6441;
}

enum SystemStringAttribute {
  SYSTEM_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  SYSTEM_ATTRIBUTE_GLOBAL_CHANS = 4709;
  SYSTEM_ATTRIBUTE_SCALES = 4710;
  SYSTEM_ATTRIBUTE_TASKS = 4711;
  SYSTEM_ATTRIBUTE_DEV_NAMES = 6459;
}

enum SystemUInt32Attribute {
  SYSTEM_UINT32_ATTRIBUTE_UNSPECIFIED = 0;
  SYSTEM_ATTRIBUTE_NIDAQ_MAJOR_VERSION = 4722;
  SYSTEM_ATTRIBUTE_NIDAQ_MINOR_VERSION = 6435;
  SYSTEM_ATTRIBUTE_NIDAQ_UPDATE_VERSION = 12066;
}

enum TaskStringAttribute {
  TASK_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  TASK_ATTRIBUTE_CHANNELS = 4723;
  TASK_ATTRIBUTE_NAME = 4726;
  TASK_ATTRIBUTE_DEVICES = 8974;
}

enum TaskBoolAttribute {
  TASK_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  TASK_ATTRIBUTE_COMPLETE = 4724;
}

enum TaskUInt32Attribute {
  TASK_UINT32_ATTRIBUTE_UNSPECIFIED = 0;
  TASK_ATTRIBUTE_NUM_CHANS = 8577;
  TASK_ATTRIBUTE_NUM_DEVICES = 10682;
}

enum TimingInt32Attribute {
  TIMING_INT32_ATTRIBUTE_UNSPECIFIED = 0;
  TIMING_ATTRIBUTE_SAMP_QUANT_SAMP_MODE = 4864;
  TIMING_ATTRIBUTE_SAMP_CLK_ACTIVE_EDGE = 4865;
  TIMING_ATTRIBUTE_DELAY_FROM_SAMP_CLK_DELAY_UNITS = 4868;
  TIMING_ATTRIBUTE_AI_CONV_TIMEBASE_SRC = 4921;
  TIMING_ATTRIBUTE_SAMP_TIMING_TYPE = 4935;
  TIMING_ATTRIBUTE_AI_CONV_ACTIVE_EDGE = 6227;
  TIMING_ATTRIBUTE_SAMP_CLK_TIMEBASE_ACTIVE_EDGE = 6380;
  TIMING_ATTRIBUTE_HSHK_START_COND = 8899;
  TIMING_ATTRIBUTE_HSHK_SAMPLE_INPUT_DATA_WHEN = 8900;
  TIMING_ATTRIBUTE_SAMP_CLK_UNDERFLOW_BEHAVIOR = 10593;
  TIMING_ATTRIBUTE_SAMP_CLK_OVERRUN_BEHAVIOR = 12028;
  TIMING_ATTRIBUTE_IMPLICIT_UNDERFLOW_BEHAVIOR = 12029;
  TIMING_ATTRIBUTE_SYNC_PULSE_TYPE = 12598;
  TIMING_ATTRIBUTE_SYNC_PULSE_TIME_TIMESCALE = 12600;
  TIMING_ATTRIBUTE_FIRST_SAMP_TIMESTAMP_TIMESCALE = 12603;
  TIMING_ATTRIBUTE_FIRST_SAMP_CLK_TIMESCALE = 12675;
}

enum TimingResetAttribute {
  TIMING_RESET_ATTRIBUTE_UNSPECIFIED = 0;
  TIMING_RESET_ATTRIBUTE_SAMP_QUANT_SAMP_MODE = 4864;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_ACTIVE_EDGE = 4865;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_TIMEBASE_RATE = 4867;
  TIMING_RESET_ATTRIBUTE_DELAY_FROM_SAMP_CLK_DELAY_UNITS = 4868;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_TIMEBASE_MASTER_TIMEBASE_DIV = 4869;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_TIMEBASE_SRC = 4872;
  TIMING_RESET_ATTRIBUTE_SAMP_QUANT_SAMP_PER_CHAN = 4880;
  TIMING_RESET_ATTRIBUTE_REF_CLK_RATE = 4885;
  TIMING_RESET_ATTRIBUTE_REF_CLK_SRC = 4886;
  TIMING_RESET_ATTRIBUTE_DELAY_FROM_SAMP_CLK_DELAY = 4887;
  TIMING_RESET_ATTRIBUTE_AI_CONV_TIMEBASE_DIV = 4917;
  TIMING_RESET_ATTRIBUTE_AI_CONV_TIMEBASE_SRC = 4921;
  TIMING_RESET_ATTRIBUTE_MASTER_TIMEBASE_SRC = 4931;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_RATE = 4932;
  TIMING_RESET_ATTRIBUTE_SAMP_TIMING_TYPE = 4935;
  TIMING_RESET_ATTRIBUTE_MASTER_TIMEBASE_RATE = 5269;
  TIMING_RESET_ATTRIBUTE_AI_CONV_SRC = 5378;
  TIMING_RESET_ATTRIBUTE_AI_CONV_RATE = 6216;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_SRC = 6226;
  TIMING_RESET_ATTRIBUTE_AI_CONV_ACTIVE_EDGE = 6227;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_TIMEBASE_DIV = 6379;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_TIMEBASE_ACTIVE_EDGE = 6380;
  TIMING_RESET_ATTRIBUTE_CHANGE_DETECT_DI_RISING_EDGE_PHYSICAL_CHANS = 8597;
  TIMING_RESET_ATTRIBUTE_CHANGE_DETECT_DI_FALLING_EDGE_PHYSICAL_CHANS = 8598;
  TIMING_RESET_ATTRIBUTE_ON_DEMAND_SIMULTANEOUS_AO_ENABLE = 8608;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_DIG_FLTR_ENABLE = 8734;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_DIG_FLTR_MIN_PULSE_WIDTH = 8735;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_DIG_FLTR_TIMEBASE_SRC = 8736;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_DIG_FLTR_TIMEBASE_RATE = 8737;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_DIG_SYNC_ENABLE = 8738;
  TIMING_RESET_ATTRIBUTE_SYNC_PULSE_SRC = 8765;
  TIMING_RESET_ATTRIBUTE_SYNC_PULSE_MIN_DELAY_TO_START = 8767;
  TIMING_RESET_ATTRIBUTE_HSHK_DELAY_AFTER_XFER = 8898;
  TIMING_RESET_ATTRIBUTE_HSHK_START_COND = 8899;
  TIMING_RESET_ATTRIBUTE_HSHK_SAMPLE_INPUT_DATA_WHEN = 8900;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_UNDERFLOW_BEHAVIOR = 10593;
  TIMING_RESET_ATTRIBUTE_SAMP_TIMING_ENGINE = 10790;
  TIMING_RESET_ATTRIBUTE_AI_CONV_DIG_FLTR_ENABLE = 11996;
  TIMING_RESET_ATTRIBUTE_AI_CONV_DIG_FLTR_MIN_PULSE_WIDTH = 11997;
  TIMING_RESET_ATTRIBUTE_AI_CONV_DIG_FLTR_TIMEBASE_SRC = 11998;
  TIMING_RESET_ATTRIBUTE_AI_CONV_DIG_FLTR_TIMEBASE_RATE = 11999;
  TIMING_RESET_ATTRIBUTE_AI_CONV_DIG_SYNC_ENABLE = 12000;
  TIMING_RESET_ATTRIBUTE_CHANGE_DETECT_DI_TRISTATE = 12026;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_OVERRUN_BEHAVIOR = 12028;
  TIMING_RESET_ATTRIBUTE_IMPLICIT_UNDERFLOW_BEHAVIOR = 12029;
  TIMING_RESET_ATTRIBUTE_SYNC_PULSE_RESET_DELAY = 12157;
  TIMING_RESET_ATTRIBUTE_SYNC_CLK_INTERVAL = 12158;
  TIMING_RESET_ATTRIBUTE_TIMING_SYNC_PULSE_FORCE = 12474;
  TIMING_RESET_ATTRIBUTE_SAMP_CLK_WRITE_WFM_USE_INITIAL_WFM_DT = 12540;
  TIMING_RESET_ATTRIBUTE_SYNC_PULSE_TYPE = 12598;
  TIMING_RESET_ATTRIBUTE_SYNC_PULSE_TIME_WHEN = 12599;
  TIMING_RESET_ATTRIBUTE_SYNC_PULSE_TIME_TIMESCALE = 12600;
  TIMING_RESET_ATTRIBUTE_FIRST_SAMP_TIMESTAMP_ENABLE = 12601;
  TIMING_RESET_ATTRIBUTE_FIRST_SAMP_TIMESTAMP_TIMESCALE = 12603;
  TIMING_RESET_ATTRIBUTE_FIRST_SAMP_CLK_WHEN = 12674;
  TIMING_RESET_ATTRIBUTE_FIRST_SAMP_CLK_TIMESCALE = 12675;
  TIMING_RESET_ATTRIBUTE_FIRST_SAMP_CLK_OFFSET = 12714;
}

enum TimingDoubleAttribute {
  TIMING_DOUBLE_ATTRIBUTE_UNSPECIFIED = 0;
  TIMING_ATTRIBUTE_SAMP_CLK_TIMEBASE_RATE = 4867;
  TIMING_ATTRIBUTE_REF_CLK_RATE = 4885;
  TIMING_ATTRIBUTE_DELAY_FROM_SAMP_CLK_DELAY = 4887;
  TIMING_ATTRIBUTE_SAMP_CLK_RATE = 4932;
  TIMING_ATTRIBUTE_MASTER_TIMEBASE_RATE = 5269;
  TIMING_ATTRIBUTE_AI_CONV_RATE = 6216;
  TIMING_ATTRIBUTE_SAMP_CLK_DIG_FLTR_MIN_PULSE_WIDTH = 8735;
  TIMING_ATTRIBUTE_SAMP_CLK_DIG_FLTR_TIMEBASE_RATE = 8737;
  TIMING_ATTRIBUTE_SYNC_PULSE_SYNC_TIME = 8766;
  TIMING_ATTRIBUTE_SYNC_PULSE_MIN_DELAY_TO_START = 8767;
  TIMING_ATTRIBUTE_HSHK_DELAY_AFTER_XFER = 8898;
  TIMING_ATTRIBUTE_SAMP_CLK_MAX_RATE = 8904;
  TIMING_ATTRIBUTE_AI_CONV_MAX_RATE = 8905;
  TIMING_ATTRIBUTE_AI_CONV_DIG_FLTR_MIN_PULSE_WIDTH = 11997;
  TIMING_ATTRIBUTE_AI_CONV_DIG_FLTR_TIMEBASE_RATE = 11999;
  TIMING_ATTRIBUTE_SYNC_PULSE_RESET_TIME = 12156;
  TIMING_ATTRIBUTE_SYNC_PULSE_RESET_DELAY = 12157;
  TIMING_ATTRIBUTE_FIRST_SAMP_CLK_OFFSET = 12714;
}

enum TimingUInt32Attribute {
  TIMING_UINT32_ATTRIBUTE_UNSPECIFIED = 0;
  TIMING_ATTRIBUTE_SAMP_CLK_TIMEBASE_MASTER_TIMEBASE_DIV = 4869;
  TIMING_ATTRIBUTE_AI_CONV_TIMEBASE_DIV = 4917;
  TIMING_ATTRIBUTE_SAMP_CLK_TIMEBASE_DIV = 6379;
  TIMING_ATTRIBUTE_SAMP_TIMING_ENGINE = 10790;
  TIMING_ATTRIBUTE_SYNC_CLK_INTERVAL = 12158;
}

enum TimingStringAttribute {
  TIMING_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  TIMING_ATTRIBUTE_SAMP_CLK_TIMEBASE_SRC = 4872;
  TIMING_ATTRIBUTE_REF_CLK_SRC = 4886;
  TIMING_ATTRIBUTE_MASTER_TIMEBASE_SRC = 4931;
  TIMING_ATTRIBUTE_AI_CONV_SRC = 5378;
  TIMING_ATTRIBUTE_SAMP_CLK_SRC = 6226;
  TIMING_ATTRIBUTE_CHANGE_DETECT_DI_RISING_EDGE_PHYSICAL_CHANS = 8597;
  TIMING_ATTRIBUTE_CHANGE_DETECT_DI_FALLING_EDGE_PHYSICAL_CHANS = 8598;
  TIMING_ATTRIBUTE_SAMP_CLK_DIG_FLTR_TIMEBASE_SRC = 8736;
  TIMING_ATTRIBUTE_SYNC_PULSE_SRC = 8765;
  TIMING_ATTRIBUTE_AI_CONV_DIG_FLTR_TIMEBASE_SRC = 11998;
  TIMING_ATTRIBUTE_SAMP_CLK_TERM = 12059;
  TIMING_ATTRIBUTE_SAMP_CLK_TIMEBASE_TERM = 12060;
  TIMING_ATTRIBUTE_SYNC_PULSE_TERM = 12165;
}

enum TimingUInt64Attribute {
  TIMING_UINT64_ATTRIBUTE_UNSPECIFIED = 0;
  TIMING_ATTRIBUTE_SAMP_QUANT_SAMP_PER_CHAN = 4880;
}

enum TimingBoolAttribute {
  TIMING_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  TIMING_ATTRIBUTE_ON_DEMAND_SIMULTANEOUS_AO_ENABLE = 8608;
  TIMING_ATTRIBUTE_SAMP_CLK_DIG_FLTR_ENABLE = 8734;
  TIMING_ATTRIBUTE_SAMP_CLK_DIG_SYNC_ENABLE = 8738;
  TIMING_ATTRIBUTE_AI_CONV_DIG_FLTR_ENABLE = 11996;
  TIMING_ATTRIBUTE_AI_CONV_DIG_SYNC_ENABLE = 12000;
  TIMING_ATTRIBUTE_CHANGE_DETECT_DI_TRISTATE = 12026;
  TIMING_ATTRIBUTE_TIMING_SYNC_PULSE_FORCE = 12474;
  TIMING_ATTRIBUTE_SAMP_CLK_WRITE_WFM_USE_INITIAL_WFM_DT = 12540;
  TIMING_ATTRIBUTE_FIRST_SAMP_TIMESTAMP_ENABLE = 12601;
}

enum TimingTimestampAttribute {
  TIMING_TIMESTAMP_ATTRIBUTE_UNSPECIFIED = 0;
  TIMING_ATTRIBUTE_SYNC_PULSE_TIME_WHEN = 12599;
  TIMING_ATTRIBUTE_FIRST_SAMP_TIMESTAMP_VAL = 12602;
  TIMING_ATTRIBUTE_FIRST_SAMP_CLK_WHEN = 12674;
}

enum TriggerInt32Attribute {
  TRIGGER_INT32_ATTRIBUTE_UNSPECIFIED = 0;
  TRIGGER_ATTRIBUTE_DIG_EDGE_ADV_TRIG_EDGE = 4960;
  TRIGGER_ATTRIBUTE_ADV_TRIG_TYPE = 4965;
  TRIGGER_ATTRIBUTE_PAUSE_TRIG_TYPE = 4966;
  TRIGGER_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_WHEN = 4977;
  TRIGGER_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_WHEN = 4980;
  TRIGGER_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_WHEN = 4992;
  TRIGGER_ATTRIBUTE_START_TRIG_TYPE = 5011;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_START_TRIG_SLOPE = 5015;
  TRIGGER_ATTRIBUTE_ANLG_WIN_START_TRIG_WHEN = 5121;
  TRIGGER_ATTRIBUTE_DIG_EDGE_START_TRIG_EDGE = 5124;
  TRIGGER_ATTRIBUTE_DIG_PATTERN_START_TRIG_WHEN = 5137;
  TRIGGER_ATTRIBUTE_ARM_START_TRIG_TYPE = 5140;
  TRIGGER_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_EDGE = 5141;
  TRIGGER_ATTRIBUTE_REF_TRIG_TYPE = 5145;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_REF_TRIG_SLOPE = 5155;
  TRIGGER_ATTRIBUTE_ANLG_WIN_REF_TRIG_WHEN = 5159;
  TRIGGER_ATTRIBUTE_DIG_EDGE_REF_TRIG_EDGE = 5168;
  TRIGGER_ATTRIBUTE_DIG_PATTERN_REF_TRIG_WHEN = 5176;
  TRIGGER_ATTRIBUTE_ANLG_WIN_REF_TRIG_COUPLING = 6231;
  TRIGGER_ATTRIBUTE_START_TRIG_DELAY_UNITS = 6344;
  TRIGGER_ATTRIBUTE_DIG_PATTERN_PAUSE_TRIG_WHEN = 8560;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_START_TRIG_COUPLING = 8755;
  TRIGGER_ATTRIBUTE_ANLG_WIN_START_TRIG_COUPLING = 8756;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_REF_TRIG_COUPLING = 8757;
  TRIGGER_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_COUPLING = 8758;
  TRIGGER_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_COUPLING = 8759;
  TRIGGER_ATTRIBUTE_HSHK_TRIG_TYPE = 8887;
  TRIGGER_ATTRIBUTE_INTERLOCKED_HSHK_TRIG_ASSERTED_LVL = 8889;
  TRIGGER_ATTRIBUTE_TRIGGER_SYNC_TYPE = 12160;
  TRIGGER_ATTRIBUTE_START_TRIG_TIMESCALE = 12342;
  TRIGGER_ATTRIBUTE_START_TRIG_TIMESTAMP_TIMESCALE = 12589;
  TRIGGER_ATTRIBUTE_REF_TRIG_TIMESTAMP_TIMESCALE = 12592;
  TRIGGER_ATTRIBUTE_ARM_START_TRIG_TIMESCALE = 12594;
  TRIGGER_ATTRIBUTE_ARM_START_TRIG_TIMESTAMP_TIMESCALE = 12597;
}

enum TriggerResetAttribute {
  TRIGGER_RESET_ATTRIBUTE_UNSPECIFIED = 0;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_ADV_TRIG_EDGE = 4960;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_ADV_TRIG_SRC = 4962;
  TRIGGER_RESET_ATTRIBUTE_ADV_TRIG_TYPE = 4965;
  TRIGGER_RESET_ATTRIBUTE_PAUSE_TRIG_TYPE = 4966;
  TRIGGER_RESET_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_HYST = 4968;
  TRIGGER_RESET_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_LVL = 4969;
  TRIGGER_RESET_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_SRC = 4976;
  TRIGGER_RESET_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_WHEN = 4977;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_SRC = 4979;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_WHEN = 4980;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_BTM = 4981;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_TOP = 4982;
  TRIGGER_RESET_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_SRC = 4985;
  TRIGGER_RESET_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_WHEN = 4992;
  TRIGGER_RESET_ATTRIBUTE_START_TRIG_TYPE = 5011;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_START_TRIG_HYST = 5013;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_START_TRIG_LVL = 5014;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_START_TRIG_SLOPE = 5015;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_START_TRIG_SRC = 5016;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_START_TRIG_SRC = 5120;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_START_TRIG_WHEN = 5121;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_START_TRIG_BTM = 5122;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_START_TRIG_TOP = 5123;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_START_TRIG_EDGE = 5124;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_START_TRIG_SRC = 5127;
  TRIGGER_RESET_ATTRIBUTE_DIG_PATTERN_START_TRIG_SRC = 5136;
  TRIGGER_RESET_ATTRIBUTE_DIG_PATTERN_START_TRIG_WHEN = 5137;
  TRIGGER_RESET_ATTRIBUTE_ARM_START_TRIG_TYPE = 5140;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_EDGE = 5141;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_SRC = 5143;
  TRIGGER_RESET_ATTRIBUTE_REF_TRIG_TYPE = 5145;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_REF_TRIG_HYST = 5153;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_REF_TRIG_LVL = 5154;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_REF_TRIG_SLOPE = 5155;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_REF_TRIG_SRC = 5156;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_REF_TRIG_SRC = 5158;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_REF_TRIG_WHEN = 5159;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_REF_TRIG_BTM = 5160;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_REF_TRIG_TOP = 5161;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_REF_TRIG_EDGE = 5168;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_REF_TRIG_SRC = 5172;
  TRIGGER_RESET_ATTRIBUTE_DIG_PATTERN_REF_TRIG_SRC = 5175;
  TRIGGER_RESET_ATTRIBUTE_DIG_PATTERN_REF_TRIG_WHEN = 5176;
  TRIGGER_RESET_ATTRIBUTE_REF_TRIG_PRETRIG_SAMPLES = 5189;
  TRIGGER_RESET_ATTRIBUTE_REF_TRIG_DELAY = 5251;
  TRIGGER_RESET_ATTRIBUTE_START_TRIG_DELAY = 6230;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_REF_TRIG_COUPLING = 6231;
  TRIGGER_RESET_ATTRIBUTE_START_TRIG_DELAY_UNITS = 6344;
  TRIGGER_RESET_ATTRIBUTE_START_TRIG_RETRIGGERABLE = 6415;
  TRIGGER_RESET_ATTRIBUTE_DIG_PATTERN_PAUSE_TRIG_SRC = 8559;
  TRIGGER_RESET_ATTRIBUTE_DIG_PATTERN_PAUSE_TRIG_WHEN = 8560;
  TRIGGER_RESET_ATTRIBUTE_DIG_PATTERN_START_TRIG_PATTERN = 8582;
  TRIGGER_RESET_ATTRIBUTE_DIG_PATTERN_REF_TRIG_PATTERN = 8583;
  TRIGGER_RESET_ATTRIBUTE_DIG_PATTERN_PAUSE_TRIG_PATTERN = 8584;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_START_TRIG_DIG_FLTR_ENABLE = 8739;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_START_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 8740;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_START_TRIG_DIG_FLTR_TIMEBASE_SRC = 8741;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_START_TRIG_DIG_FLTR_TIMEBASE_RATE = 8742;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_START_TRIG_DIG_SYNC_ENABLE = 8743;
  TRIGGER_RESET_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_DIG_FLTR_ENABLE = 8744;
  TRIGGER_RESET_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 8745;
  TRIGGER_RESET_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_DIG_FLTR_TIMEBASE_SRC = 8746;
  TRIGGER_RESET_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_DIG_FLTR_TIMEBASE_RATE = 8747;
  TRIGGER_RESET_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_DIG_SYNC_ENABLE = 8748;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_DIG_FLTR_ENABLE = 8749;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 8750;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_DIG_FLTR_TIMEBASE_SRC = 8751;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_DIG_FLTR_TIMEBASE_RATE = 8752;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_DIG_SYNC_ENABLE = 8753;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_START_TRIG_COUPLING = 8755;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_START_TRIG_COUPLING = 8756;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_REF_TRIG_COUPLING = 8757;
  TRIGGER_RESET_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_COUPLING = 8758;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_COUPLING = 8759;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_ADV_TRIG_DIG_FLTR_ENABLE = 8760;
  TRIGGER_RESET_ATTRIBUTE_HSHK_TRIG_TYPE = 8887;
  TRIGGER_RESET_ATTRIBUTE_INTERLOCKED_HSHK_TRIG_SRC = 8888;
  TRIGGER_RESET_ATTRIBUTE_INTERLOCKED_HSHK_TRIG_ASSERTED_LVL = 8889;
  TRIGGER_RESET_ATTRIBUTE_REF_TRIG_AUTO_TRIG_ENABLE = 11969;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_REF_TRIG_DIG_FLTR_ENABLE = 11991;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_REF_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 11992;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_REF_TRIG_DIG_FLTR_TIMEBASE_SRC = 11993;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_REF_TRIG_DIG_FLTR_TIMEBASE_RATE = 11994;
  TRIGGER_RESET_ATTRIBUTE_DIG_EDGE_REF_TRIG_DIG_SYNC_ENABLE = 11995;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_START_TRIG_DIG_FLTR_ENABLE = 12001;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_START_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 12002;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_START_TRIG_DIG_FLTR_TIMEBASE_SRC = 12003;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_START_TRIG_DIG_FLTR_TIMEBASE_RATE = 12004;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_START_TRIG_DIG_SYNC_ENABLE = 12005;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_REF_TRIG_DIG_FLTR_ENABLE = 12006;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_REF_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 12007;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_REF_TRIG_DIG_FLTR_TIMEBASE_SRC = 12008;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_REF_TRIG_DIG_FLTR_TIMEBASE_RATE = 12009;
  TRIGGER_RESET_ATTRIBUTE_ANLG_EDGE_REF_TRIG_DIG_SYNC_ENABLE = 12010;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_REF_TRIG_DIG_FLTR_ENABLE = 12011;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_REF_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 12012;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_REF_TRIG_DIG_FLTR_TIMEBASE_SRC = 12013;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_REF_TRIG_DIG_FLTR_TIMEBASE_RATE = 12014;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_REF_TRIG_DIG_SYNC_ENABLE = 12015;
  TRIGGER_RESET_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_DIG_FLTR_ENABLE = 12016;
  TRIGGER_RESET_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 12017;
  TRIGGER_RESET_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_DIG_FLTR_TIMEBASE_SRC = 12018;
  TRIGGER_RESET_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_DIG_FLTR_TIMEBASE_RATE = 12019;
  TRIGGER_RESET_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_DIG_SYNC_ENABLE = 12020;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_DIG_FLTR_ENABLE = 12021;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 12022;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_DIG_FLTR_TIMEBASE_SRC = 12023;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_DIG_FLTR_TIMEBASE_RATE = 12024;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_DIG_SYNC_ENABLE = 12025;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_START_TRIG_DIG_FLTR_ENABLE = 12031;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_START_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 12032;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_START_TRIG_DIG_FLTR_TIMEBASE_SRC = 12033;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_START_TRIG_DIG_FLTR_TIMEBASE_RATE = 12034;
  TRIGGER_RESET_ATTRIBUTE_ANLG_WIN_START_TRIG_DIG_SYNC_ENABLE = 12035;
  TRIGGER_RESET_ATTRIBUTE_TRIGGER_SYNC_TYPE = 12160;
  TRIGGER_RESET_ATTRIBUTE_TIME_START_TRIG_SRC = 12317;
  TRIGGER_RESET_ATTRIBUTE_START_TRIG_TIMESCALE = 12342;
  TRIGGER_RESET_ATTRIBUTE_START_TRIG_TRIG_WHEN = 12365;
  TRIGGER_RESET_ATTRIBUTE_START_TRIG_TRIG_WIN = 12570;
  TRIGGER_RESET_ATTRIBUTE_START_TRIG_RETRIGGER_WIN = 12571;
  TRIGGER_RESET_ATTRIBUTE_START_TRIG_MAX_NUM_TRIGS_TO_DETECT = 12572;
  TRIGGER_RESET_ATTRIBUTE_REF_TRIG_RETRIGGERABLE = 12573;
  TRIGGER_RESET_ATTRIBUTE_REF_TRIG_TRIG_WIN = 12574;
  TRIGGER_RESET_ATTRIBUTE_REF_TRIG_RETRIGGER_WIN = 12575;
  TRIGGER_RESET_ATTRIBUTE_REF_TRIG_MAX_NUM_TRIGS_TO_DETECT = 12576;
  TRIGGER_RESET_ATTRIBUTE_ANLG_MULTI_EDGE_START_TRIG_SRCS = 12577;
  TRIGGER_RESET_ATTRIBUTE_ANLG_MULTI_EDGE_START_TRIG_SLOPES = 12578;
  TRIGGER_RESET_ATTRIBUTE_ANLG_MULTI_EDGE_START_TRIG_LVLS = 12579;
  TRIGGER_RESET_ATTRIBUTE_ANLG_MULTI_EDGE_START_TRIG_HYSTS = 12580;
  TRIGGER_RESET_ATTRIBUTE_ANLG_MULTI_EDGE_START_TRIG_COUPLINGS = 12581;
  TRIGGER_RESET_ATTRIBUTE_ANLG_MULTI_EDGE_REF_TRIG_SRCS = 12582;
  TRIGGER_RESET_ATTRIBUTE_ANLG_MULTI_EDGE_REF_TRIG_SLOPES = 12583;
  TRIGGER_RESET_ATTRIBUTE_ANLG_MULTI_EDGE_REF_TRIG_LVLS = 12584;
  TRIGGER_RESET_ATTRIBUTE_ANLG_MULTI_EDGE_REF_TRIG_HYSTS = 12585;
  TRIGGER_RESET_ATTRIBUTE_ANLG_MULTI_EDGE_REF_TRIG_COUPLINGS = 12586;
  TRIGGER_RESET_ATTRIBUTE_START_TRIG_TIMESTAMP_TIMESCALE = 12589;
  TRIGGER_RESET_ATTRIBUTE_REF_TRIG_TIMESTAMP_ENABLE = 12590;
  TRIGGER_RESET_ATTRIBUTE_REF_TRIG_TIMESTAMP_TIMESCALE = 12592;
  TRIGGER_RESET_ATTRIBUTE_ARM_START_TRIG_TRIG_WHEN = 12593;
  TRIGGER_RESET_ATTRIBUTE_ARM_START_TRIG_TIMESCALE = 12594;
  TRIGGER_RESET_ATTRIBUTE_ARM_START_TRIG_TIMESTAMP_ENABLE = 12595;
  TRIGGER_RESET_ATTRIBUTE_ARM_START_TRIG_TIMESTAMP_TIMESCALE = 12597;
  TRIGGER_RESET_ATTRIBUTE_START_TRIG_TIMESTAMP_ENABLE = 12618;
}

enum TriggerStringAttribute {
  TRIGGER_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  TRIGGER_ATTRIBUTE_DIG_EDGE_ADV_TRIG_SRC = 4962;
  TRIGGER_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_SRC = 4976;
  TRIGGER_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_SRC = 4979;
  TRIGGER_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_SRC = 4985;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_START_TRIG_SRC = 5016;
  TRIGGER_ATTRIBUTE_ANLG_WIN_START_TRIG_SRC = 5120;
  TRIGGER_ATTRIBUTE_DIG_EDGE_START_TRIG_SRC = 5127;
  TRIGGER_ATTRIBUTE_DIG_PATTERN_START_TRIG_SRC = 5136;
  TRIGGER_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_SRC = 5143;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_REF_TRIG_SRC = 5156;
  TRIGGER_ATTRIBUTE_ANLG_WIN_REF_TRIG_SRC = 5158;
  TRIGGER_ATTRIBUTE_DIG_EDGE_REF_TRIG_SRC = 5172;
  TRIGGER_ATTRIBUTE_DIG_PATTERN_REF_TRIG_SRC = 5175;
  TRIGGER_ATTRIBUTE_DIG_PATTERN_PAUSE_TRIG_SRC = 8559;
  TRIGGER_ATTRIBUTE_DIG_PATTERN_START_TRIG_PATTERN = 8582;
  TRIGGER_ATTRIBUTE_DIG_PATTERN_REF_TRIG_PATTERN = 8583;
  TRIGGER_ATTRIBUTE_DIG_PATTERN_PAUSE_TRIG_PATTERN = 8584;
  TRIGGER_ATTRIBUTE_DIG_EDGE_START_TRIG_DIG_FLTR_TIMEBASE_SRC = 8741;
  TRIGGER_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_DIG_FLTR_TIMEBASE_SRC = 8746;
  TRIGGER_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_DIG_FLTR_TIMEBASE_SRC = 8751;
  TRIGGER_ATTRIBUTE_INTERLOCKED_HSHK_TRIG_SRC = 8888;
  TRIGGER_ATTRIBUTE_DIG_EDGE_REF_TRIG_DIG_FLTR_TIMEBASE_SRC = 11993;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_START_TRIG_DIG_FLTR_TIMEBASE_SRC = 12003;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_REF_TRIG_DIG_FLTR_TIMEBASE_SRC = 12008;
  TRIGGER_ATTRIBUTE_ANLG_WIN_REF_TRIG_DIG_FLTR_TIMEBASE_SRC = 12013;
  TRIGGER_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_DIG_FLTR_TIMEBASE_SRC = 12018;
  TRIGGER_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_DIG_FLTR_TIMEBASE_SRC = 12023;
  TRIGGER_ATTRIBUTE_ANLG_WIN_START_TRIG_DIG_FLTR_TIMEBASE_SRC = 12033;
  TRIGGER_ATTRIBUTE_START_TRIG_TERM = 12062;
  TRIGGER_ATTRIBUTE_REF_TRIG_TERM = 12063;
  TRIGGER_ATTRIBUTE_PAUSE_TRIG_TERM = 12064;
  TRIGGER_ATTRIBUTE_ARM_START_TERM = 12159;
  TRIGGER_ATTRIBUTE_TIME_START_TRIG_SRC = 12317;
  TRIGGER_ATTRIBUTE_ANLG_MULTI_EDGE_START_TRIG_SRCS = 12577;
  TRIGGER_ATTRIBUTE_ANLG_MULTI_EDGE_REF_TRIG_SRCS = 12582;
}

enum TriggerDoubleAttribute {
  TRIGGER_DOUBLE_ATTRIBUTE_UNSPECIFIED = 0;
  TRIGGER_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_HYST = 4968;
  TRIGGER_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_LVL = 4969;
  TRIGGER_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_BTM = 4981;
  TRIGGER_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_TOP = 4982;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_START_TRIG_HYST = 5013;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_START_TRIG_LVL = 5014;
  TRIGGER_ATTRIBUTE_ANLG_WIN_START_TRIG_BTM = 5122;
  TRIGGER_ATTRIBUTE_ANLG_WIN_START_TRIG_TOP = 5123;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_REF_TRIG_HYST = 5153;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_REF_TRIG_LVL = 5154;
  TRIGGER_ATTRIBUTE_ANLG_WIN_REF_TRIG_BTM = 5160;
  TRIGGER_ATTRIBUTE_ANLG_WIN_REF_TRIG_TOP = 5161;
  TRIGGER_ATTRIBUTE_REF_TRIG_DELAY = 5251;
  TRIGGER_ATTRIBUTE_START_TRIG_DELAY = 6230;
  TRIGGER_ATTRIBUTE_DIG_EDGE_START_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 8740;
  TRIGGER_ATTRIBUTE_DIG_EDGE_START_TRIG_DIG_FLTR_TIMEBASE_RATE = 8742;
  TRIGGER_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 8745;
  TRIGGER_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_DIG_FLTR_TIMEBASE_RATE = 8747;
  TRIGGER_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 8750;
  TRIGGER_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_DIG_FLTR_TIMEBASE_RATE = 8752;
  TRIGGER_ATTRIBUTE_DIG_EDGE_REF_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 11992;
  TRIGGER_ATTRIBUTE_DIG_EDGE_REF_TRIG_DIG_FLTR_TIMEBASE_RATE = 11994;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_START_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 12002;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_START_TRIG_DIG_FLTR_TIMEBASE_RATE = 12004;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_REF_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 12007;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_REF_TRIG_DIG_FLTR_TIMEBASE_RATE = 12009;
  TRIGGER_ATTRIBUTE_ANLG_WIN_REF_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 12012;
  TRIGGER_ATTRIBUTE_ANLG_WIN_REF_TRIG_DIG_FLTR_TIMEBASE_RATE = 12014;
  TRIGGER_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 12017;
  TRIGGER_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_DIG_FLTR_TIMEBASE_RATE = 12019;
  TRIGGER_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 12022;
  TRIGGER_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_DIG_FLTR_TIMEBASE_RATE = 12024;
  TRIGGER_ATTRIBUTE_ANLG_WIN_START_TRIG_DIG_FLTR_MIN_PULSE_WIDTH = 12032;
  TRIGGER_ATTRIBUTE_ANLG_WIN_START_TRIG_DIG_FLTR_TIMEBASE_RATE = 12034;
  TRIGGER_ATTRIBUTE_START_TRIG_TRIG_WIN = 12570;
  TRIGGER_ATTRIBUTE_START_TRIG_RETRIGGER_WIN = 12571;
  TRIGGER_ATTRIBUTE_REF_TRIG_TRIG_WIN = 12574;
  TRIGGER_ATTRIBUTE_REF_TRIG_RETRIGGER_WIN = 12575;
}

enum TriggerUInt32Attribute {
  TRIGGER_UINT32_ATTRIBUTE_UNSPECIFIED = 0;
  TRIGGER_ATTRIBUTE_REF_TRIG_PRETRIG_SAMPLES = 5189;
  TRIGGER_ATTRIBUTE_START_TRIG_MAX_NUM_TRIGS_TO_DETECT = 12572;
  TRIGGER_ATTRIBUTE_REF_TRIG_MAX_NUM_TRIGS_TO_DETECT = 12576;
}

enum TriggerBoolAttribute {
  TRIGGER_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  TRIGGER_ATTRIBUTE_START_TRIG_RETRIGGERABLE = 6415;
  TRIGGER_ATTRIBUTE_DIG_EDGE_START_TRIG_DIG_FLTR_ENABLE = 8739;
  TRIGGER_ATTRIBUTE_DIG_EDGE_START_TRIG_DIG_SYNC_ENABLE = 8743;
  TRIGGER_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_DIG_FLTR_ENABLE = 8744;
  TRIGGER_ATTRIBUTE_DIG_LVL_PAUSE_TRIG_DIG_SYNC_ENABLE = 8748;
  TRIGGER_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_DIG_FLTR_ENABLE = 8749;
  TRIGGER_ATTRIBUTE_DIG_EDGE_ARM_START_TRIG_DIG_SYNC_ENABLE = 8753;
  TRIGGER_ATTRIBUTE_DIG_EDGE_ADV_TRIG_DIG_FLTR_ENABLE = 8760;
  TRIGGER_ATTRIBUTE_REF_TRIG_AUTO_TRIG_ENABLE = 11969;
  TRIGGER_ATTRIBUTE_REF_TRIG_AUTO_TRIGGERED = 11970;
  TRIGGER_ATTRIBUTE_DIG_EDGE_REF_TRIG_DIG_FLTR_ENABLE = 11991;
  TRIGGER_ATTRIBUTE_DIG_EDGE_REF_TRIG_DIG_SYNC_ENABLE = 11995;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_START_TRIG_DIG_FLTR_ENABLE = 12001;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_START_TRIG_DIG_SYNC_ENABLE = 12005;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_REF_TRIG_DIG_FLTR_ENABLE = 12006;
  TRIGGER_ATTRIBUTE_ANLG_EDGE_REF_TRIG_DIG_SYNC_ENABLE = 12010;
  TRIGGER_ATTRIBUTE_ANLG_WIN_REF_TRIG_DIG_FLTR_ENABLE = 12011;
  TRIGGER_ATTRIBUTE_ANLG_WIN_REF_TRIG_DIG_SYNC_ENABLE = 12015;
  TRIGGER_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_DIG_FLTR_ENABLE = 12016;
  TRIGGER_ATTRIBUTE_ANLG_LVL_PAUSE_TRIG_DIG_SYNC_ENABLE = 12020;
  TRIGGER_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_DIG_FLTR_ENABLE = 12021;
  TRIGGER_ATTRIBUTE_ANLG_WIN_PAUSE_TRIG_DIG_SYNC_ENABLE = 12025;
  TRIGGER_ATTRIBUTE_ANLG_WIN_START_TRIG_DIG_FLTR_ENABLE = 12031;
  TRIGGER_ATTRIBUTE_ANLG_WIN_START_TRIG_DIG_SYNC_ENABLE = 12035;
  TRIGGER_ATTRIBUTE_REF_TRIG_RETRIGGERABLE = 12573;
  TRIGGER_ATTRIBUTE_REF_TRIG_TIMESTAMP_ENABLE = 12590;
  TRIGGER_ATTRIBUTE_ARM_START_TRIG_TIMESTAMP_ENABLE = 12595;
  TRIGGER_ATTRIBUTE_START_TRIG_TIMESTAMP_ENABLE = 12618;
}

enum TriggerTimestampAttribute {
  TRIGGER_TIMESTAMP_ATTRIBUTE_UNSPECIFIED = 0;
  TRIGGER_ATTRIBUTE_START_TRIG_TRIG_WHEN = 12365;
  TRIGGER_ATTRIBUTE_REF_TRIG_TIMESTAMP_VAL = 12591;
  TRIGGER_ATTRIBUTE_ARM_START_TRIG_TRIG_WHEN = 12593;
  TRIGGER_ATTRIBUTE_ARM_START_TRIG_TIMESTAMP_VAL = 12596;
  TRIGGER_ATTRIBUTE_START_TRIG_TIMESTAMP_VAL = 12619;
}

enum TriggerInt32ArrayAttribute {
  TRIGGER_INT32_ARRAY_ATTRIBUTE_UNSPECIFIED = 0;
  TRIGGER_ATTRIBUTE_ANLG_MULTI_EDGE_START_TRIG_SLOPES = 12578;
  TRIGGER_ATTRIBUTE_ANLG_MULTI_EDGE_START_TRIG_COUPLINGS = 12581;
  TRIGGER_ATTRIBUTE_ANLG_MULTI_EDGE_REF_TRIG_SLOPES = 12583;
  TRIGGER_ATTRIBUTE_ANLG_MULTI_EDGE_REF_TRIG_COUPLINGS = 12586;
}

enum TriggerDoubleArrayAttribute {
  TRIGGER_DOUBLE_ARRAY_ATTRIBUTE_UNSPECIFIED = 0;
  TRIGGER_ATTRIBUTE_ANLG_MULTI_EDGE_START_TRIG_LVLS = 12579;
  TRIGGER_ATTRIBUTE_ANLG_MULTI_EDGE_START_TRIG_HYSTS = 12580;
  TRIGGER_ATTRIBUTE_ANLG_MULTI_EDGE_REF_TRIG_LVLS = 12584;
  TRIGGER_ATTRIBUTE_ANLG_MULTI_EDGE_REF_TRIG_HYSTS = 12585;
}

enum WatchdogInt32Attribute {
  WATCHDOG_INT32_ATTRIBUTE_UNSPECIFIED = 0;
  WATCHDOG_ATTRIBUTE_EXPIR_TRIG_TYPE = 8611;
  WATCHDOG_ATTRIBUTE_DIG_EDGE_WATCHDOG_EXPIR_TRIG_EDGE = 8613;
  WATCHDOG_ATTRIBUTE_DO_EXPIR_STATE = 8615;
  WATCHDOG_ATTRIBUTE_AO_OUTPUT_TYPE = 12382;
  WATCHDOG_ATTRIBUTE_CO_EXPIR_STATE = 12384;
}

enum WatchdogResetAttribute {
  WATCHDOG_RESET_ATTRIBUTE_UNSPECIFIED = 0;
  WATCHDOG_RESET_ATTRIBUTE_EXPIR_TRIG_TYPE = 8611;
  WATCHDOG_RESET_ATTRIBUTE_DIG_EDGE_WATCHDOG_EXPIR_TRIG_SRC = 8612;
  WATCHDOG_RESET_ATTRIBUTE_DIG_EDGE_WATCHDOG_EXPIR_TRIG_EDGE = 8613;
  WATCHDOG_RESET_ATTRIBUTE_DO_EXPIR_STATE = 8615;
  WATCHDOG_RESET_ATTRIBUTE_TIMEOUT = 8617;
  WATCHDOG_RESET_ATTRIBUTE_EXPIR_TRIG_TRIG_ON_NETWORK_CONN_LOSS = 12381;
  WATCHDOG_RESET_ATTRIBUTE_AO_OUTPUT_TYPE = 12382;
  WATCHDOG_RESET_ATTRIBUTE_AO_EXPIR_STATE = 12383;
  WATCHDOG_RESET_ATTRIBUTE_CO_EXPIR_STATE = 12384;
}

enum WatchdogStringAttribute {
  WATCHDOG_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  WATCHDOG_ATTRIBUTE_DIG_EDGE_WATCHDOG_EXPIR_TRIG_SRC = 8612;
}

enum WatchdogBoolAttribute {
  WATCHDOG_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  WATCHDOG_ATTRIBUTE_HAS_EXPIRED = 8616;
  WATCHDOG_ATTRIBUTE_EXPIR_TRIG_TRIG_ON_NETWORK_CONN_LOSS = 12381;
}

enum WatchdogDoubleAttribute {
  WATCHDOG_DOUBLE_ATTRIBUTE_UNSPECIFIED = 0;
  WATCHDOG_ATTRIBUTE_TIMEOUT = 8617;
  WATCHDOG_ATTRIBUTE_AO_EXPIR_STATE = 12383;
}

enum WriteInt32Attribute {
  WRITE_INT32_ATTRIBUTE_UNSPECIFIED = 0;
  WRITE_ATTRIBUTE_REGEN_MODE = 5203;
  WRITE_ATTRIBUTE_RELATIVE_TO = 6412;
  WRITE_ATTRIBUTE_OFFSET = 6413;
  WRITE_ATTRIBUTE_WAIT_MODE = 8881;
}

enum WriteResetAttribute {
  WRITE_RESET_ATTRIBUTE_UNSPECIFIED = 0;
  WRITE_RESET_ATTRIBUTE_REGEN_MODE = 5203;
  WRITE_RESET_ATTRIBUTE_RELATIVE_TO = 6412;
  WRITE_RESET_ATTRIBUTE_OFFSET = 6413;
  WRITE_RESET_ATTRIBUTE_WAIT_MODE = 8881;
  WRITE_RESET_ATTRIBUTE_SLEEP_TIME = 8882;
  WRITE_RESET_ATTRIBUTE_NEXT_WRITE_IS_LAST = 10604;
}

enum WriteUInt64Attribute {
  WRITE_UINT64_ATTRIBUTE_UNSPECIFIED = 0;
  WRITE_ATTRIBUTE_CURR_WRITE_POS = 5208;
  WRITE_ATTRIBUTE_TOTAL_SAMP_PER_CHAN_GENERATED = 6443;
}

enum WriteUInt32Attribute {
  WRITE_UINT32_ATTRIBUTE_UNSPECIFIED = 0;
  WRITE_ATTRIBUTE_SPACE_AVAIL = 5216;
  WRITE_ATTRIBUTE_RAW_DATA_WIDTH = 8573;
  WRITE_ATTRIBUTE_NUM_CHANS = 8574;
  WRITE_ATTRIBUTE_DIGITAL_LINES_BYTES_PER_CHAN = 8575;
}

enum WriteDoubleAttribute {
  WRITE_DOUBLE_ATTRIBUTE_UNSPECIFIED = 0;
  WRITE_ATTRIBUTE_SLEEP_TIME = 8882;
}

enum WriteBoolAttribute {
  WRITE_BOOL_ATTRIBUTE_UNSPECIFIED = 0;
  WRITE_ATTRIBUTE_NEXT_WRITE_IS_LAST = 10604;
  WRITE_ATTRIBUTE_OVERCURRENT_CHANS_EXIST = 10728;
  WRITE_ATTRIBUTE_OPEN_CURRENT_LOOP_CHANS_EXIST = 10730;
  WRITE_ATTRIBUTE_POWER_SUPPLY_FAULT_CHANS_EXIST = 10732;
  WRITE_ATTRIBUTE_OVERTEMPERATURE_CHANS_EXIST = 10884;
  WRITE_ATTRIBUTE_ACCESSORY_INSERTION_OR_REMOVAL_DETECTED = 12371;
  WRITE_ATTRIBUTE_OVERLOADED_CHANS_EXIST = 12420;
  WRITE_ATTRIBUTE_EXTERNAL_OVERVOLTAGE_CHANS_EXIST = 12475;
  WRITE_ATTRIBUTE_SYNC_UNLOCKED_CHANS_EXIST = 12607;
}

enum WriteStringAttribute {
  WRITE_STRING_ATTRIBUTE_UNSPECIFIED = 0;
  WRITE_ATTRIBUTE_OVERCURRENT_CHANS = 10729;
  WRITE_ATTRIBUTE_OPEN_CURRENT_LOOP_CHANS = 10731;
  WRITE_ATTRIBUTE_POWER_SUPPLY_FAULT_CHANS = 10733;
  WRITE_ATTRIBUTE_DEVS_WITH_INSERTED_OR_REMOVED_ACCESSORIES = 12372;
  WRITE_ATTRIBUTE_OVERTEMPERATURE_CHANS = 12419;
  WRITE_ATTRIBUTE_OVERLOADED_CHANS = 12421;
  WRITE_ATTRIBUTE_EXTERNAL_OVERVOLTAGE_CHANS = 12476;
  WRITE_ATTRIBUTE_SYNC_UNLOCKED_CHANS = 12608;
}

enum ACExcitWireMode {
  AC_EXCIT_WIRE_MODE_UNSPECIFIED = 0;
  AC_EXCIT_WIRE_MODE_4_WIRE = 4;
  AC_EXCIT_WIRE_MODE_5_WIRE = 5;
  AC_EXCIT_WIRE_MODE_6_WIRE = 6;
}

enum AccelChargeSensitivityUnits {
  ACCEL_CHARGE_SENSITIVITY_UNITS_UNSPECIFIED = 0;
  ACCEL_CHARGE_SENSITIVITY_UNITS_PICO_COULOMBS_PER_G = 16099;
  ACCEL_CHARGE_SENSITIVITY_UNITS_PICO_COULOMBS_PER_METERS_PER_SECOND_SQUARED = 16100;
  ACCEL_CHARGE_SENSITIVITY_UNITS_PICO_COULOMBS_PER_INCHES_PER_SECOND_SQUARED = 16101;
}

enum AccelSensitivityUnits1 {
  ACCEL_SENSITIVITY_UNITS1_UNSPECIFIED = 0;
  ACCEL_SENSITIVITY_UNITS1_M_VOLTS_PER_G = 12509;
  ACCEL_SENSITIVITY_UNITS1_VOLTS_PER_G = 12510;
}

enum AccelUnits2 {
  ACCEL_UNITS2_UNSPECIFIED = 0;
  ACCEL_UNITS2_ACCEL_UNIT_G = 10186;
  ACCEL_UNITS2_METERS_PER_SECOND_SQUARED = 12470;
  ACCEL_UNITS2_INCHES_PER_SECOND_SQUARED = 12471;
  ACCEL_UNITS2_FROM_CUSTOM_SCALE = 10065;
}

enum AcquisitionType {
  ACQUISITION_TYPE_UNSPECIFIED = 0;
  ACQUISITION_TYPE_FINITE_SAMPS = 10178;
  ACQUISITION_TYPE_CONT_SAMPS = 10123;
  ACQUISITION_TYPE_HW_TIMED_SINGLE_POINT = 12522;
}

enum AngleUnits1 {
  ANGLE_UNITS1_UNSPECIFIED = 0;
  ANGLE_UNITS1_DEGREES = 10146;
  ANGLE_UNITS1_RADIANS = 10273;
  ANGLE_UNITS1_FROM_CUSTOM_SCALE = 10065;
}

enum AngleUnits2 {
  ANGLE_UNITS2_UNSPECIFIED = 0;
  ANGLE_UNITS2_DEGREES = 10146;
  ANGLE_UNITS2_RADIANS = 10273;
  ANGLE_UNITS2_TICKS = 10304;
  ANGLE_UNITS2_FROM_CUSTOM_SCALE = 10065;
}

enum AngularVelocityUnits {
  ANGULAR_VELOCITY_UNITS_UNSPECIFIED = 0;
  ANGULAR_VELOCITY_UNITS_RPM = 16080;
  ANGULAR_VELOCITY_UNITS_RADIANS_PER_SECOND = 16081;
  ANGULAR_VELOCITY_UNITS_DEGREES_PER_SECOND = 16082;
  ANGULAR_VELOCITY_UNITS_FROM_CUSTOM_SCALE = 10065;
}

enum BridgeConfiguration1 {
  BRIDGE_CONFIGURATION1_UNSPECIFIED = 0;
  BRIDGE_CONFIGURATION1_FULL_BRIDGE = 10182;
  BRIDGE_CONFIGURATION1_HALF_BRIDGE = 10187;
  BRIDGE_CONFIGURATION1_QUARTER_BRIDGE = 10270;
  BRIDGE_CONFIGURATION1_NO_BRIDGE = 10228;
}

enum BridgeElectricalUnits {
  BRIDGE_ELECTRICAL_UNITS_UNSPECIFIED = 0;
  BRIDGE_ELECTRICAL_UNITS_VOLTS_PER_VOLT = 15896;
  BRIDGE_ELECTRICAL_UNITS_M_VOLTS_PER_VOLT = 15897;
}

enum BridgePhysicalUnits {
  BRIDGE_PHYSICAL_UNITS_UNSPECIFIED = 0;
  BRIDGE_PHYSICAL_UNITS_NEWTONS = 15875;
  BRIDGE_PHYSICAL_UNITS_POUNDS = 15876;
  BRIDGE_PHYSICAL_UNITS_KILOGRAM_FORCE = 15877;
  BRIDGE_PHYSICAL_UNITS_PASCALS = 10081;
  BRIDGE_PHYSICAL_UNITS_POUNDS_PER_SQUARE_INCH = 15879;
  BRIDGE_PHYSICAL_UNITS_BAR = 15880;
  BRIDGE_PHYSICAL_UNITS_NEWTON_METERS = 15881;
  BRIDGE_PHYSICAL_UNITS_INCH_OUNCES = 15882;
  BRIDGE_PHYSICAL_UNITS_INCH_POUNDS = 15883;
  BRIDGE_PHYSICAL_UNITS_FOOT_POUNDS = 15884;
}

enum BridgeUnits {
  BRIDGE_UNITS_UNSPECIFIED = 0;
  BRIDGE_UNITS_VOLTS_PER_VOLT = 15896;
  BRIDGE_UNITS_M_VOLTS_PER_VOLT = 15897;
  BRIDGE_UNITS_FROM_CUSTOM_SCALE = 10065;
  BRIDGE_UNITS_FROM_TEDS = 12516;
}

enum CJCSource1 {
  CJC_SOURCE1_UNSPECIFIED = 0;
  CJC_SOURCE1_BUILT_IN = 10200;
  CJC_SOURCE1_CONST_VAL = 10116;
  CJC_SOURCE1_CHAN = 10113;
}

enum ChargeUnits {
  CHARGE_UNITS_UNSPECIFIED = 0;
  CHARGE_UNITS_COULOMBS = 16102;
  CHARGE_UNITS_PICO_COULOMBS = 16103;
  CHARGE_UNITS_FROM_CUSTOM_SCALE = 10065;
}

enum CountDirection1 {
  COUNT_DIRECTION1_UNSPECIFIED = 0;
  COUNT_DIRECTION1_COUNT_UP = 10128;
  COUNT_DIRECTION1_COUNT_DOWN = 10124;
  COUNT_DIRECTION1_EXT_CONTROLLED = 10326;
}

enum CounterFrequencyMethod {
  COUNTER_FREQUENCY_METHOD_UNSPECIFIED = 0;
  COUNTER_FREQUENCY_METHOD_LOW_FREQ_1_CTR = 10105;
  COUNTER_FREQUENCY_METHOD_HIGH_FREQ_2_CTR = 10157;
  COUNTER_FREQUENCY_METHOD_LARGE_RNG_2_CTR = 10205;
  COUNTER_FREQUENCY_METHOD_DYN_AVG = 16065;
}

enum CurrentShuntResistorLocationWithDefault {
  CURRENT_SHUNT_RESISTOR_LOCATION_WITH_DEFAULT_UNSPECIFIED = 0;
  CURRENT_SHUNT_RESISTOR_LOCATION_WITH_DEFAULT_DEFAULT = -1;
  CURRENT_SHUNT_RESISTOR_LOCATION_WITH_DEFAULT_INTERNAL = 10200;
  CURRENT_SHUNT_RESISTOR_LOCATION_WITH_DEFAULT_EXTERNAL = 10167;
}

enum CurrentUnits2 {
  CURRENT_UNITS2_UNSPECIFIED = 0;
  CURRENT_UNITS2_AMPS = 10342;
  CURRENT_UNITS2_FROM_CUSTOM_SCALE = 10065;
}

enum DigitalLineState {
  DIGITAL_LINE_STATE_UNSPECIFIED = 0;
  DIGITAL_LINE_STATE_HIGH = 10192;
  DIGITAL_LINE_STATE_LOW = 10214;
  DIGITAL_LINE_STATE_TRISTATE = 10310;
  DIGITAL_LINE_STATE_NO_CHANGE = 10160;
}

enum DigitalPatternCondition1 {
  DIGITAL_PATTERN_CONDITION1_UNSPECIFIED = 0;
  DIGITAL_PATTERN_CONDITION1_PATTERN_MATCHES = 10254;
  DIGITAL_PATTERN_CONDITION1_PATTERN_DOES_NOT_MATCH = 10253;
}

enum DigitalWidthUnits3 {
  DIGITAL_WIDTH_UNITS3_UNSPECIFIED = 0;
  DIGITAL_WIDTH_UNITS3_SECONDS = 10364;
}

enum EddyCurrentProxProbeSensitivityUnits {
  EDDY_CURRENT_PROX_PROBE_SENSITIVITY_UNITS_UNSPECIFIED = 0;
  EDDY_CURRENT_PROX_PROBE_SENSITIVITY_UNITS_M_VOLTS_PER_MIL = 14836;
  EDDY_CURRENT_PROX_PROBE_SENSITIVITY_UNITS_VOLTS_PER_MIL = 14837;
  EDDY_CURRENT_PROX_PROBE_SENSITIVITY_UNITS_M_VOLTS_PER_MILLIMETER = 14838;
  EDDY_CURRENT_PROX_PROBE_SENSITIVITY_UNITS_VOLTS_PER_MILLIMETER = 14839;
  EDDY_CURRENT_PROX_PROBE_SENSITIVITY_UNITS_M_VOLTS_PER_MICRON = 14840;
}

enum Edge1 {
  EDGE1_UNSPECIFIED = 0;
  EDGE1_RISING = 10280;
  EDGE1_FALLING = 10171;
}

enum EncoderType2 {
  ENCODER_TYPE2_UNSPECIFIED = 0;
  ENCODER_TYPE2_X1 = 10090;
  ENCODER_TYPE2_X2 = 10091;
  ENCODER_TYPE2_X4 = 10092;
  ENCODER_TYPE2_TWO_PULSE_COUNTING = 10313;
}

enum EncoderZIndexPhase1 {
  ENCODER_Z_INDEX_PHASE1_UNSPECIFIED = 0;
  ENCODER_Z_INDEX_PHASE1_A_HIGH_B_HIGH = 10040;
  ENCODER_Z_INDEX_PHASE1_A_HIGH_B_LOW = 10041;
  ENCODER_Z_INDEX_PHASE1_A_LOW_B_HIGH = 10042;
  ENCODER_Z_INDEX_PHASE1_A_LOW_B_LOW = 10043;
}

enum EveryNSamplesEventType {
  EVERY_N_SAMPLES_EVENT_TYPE_UNSPECIFIED = 0;
  EVERY_N_SAMPLES_EVENT_TYPE_ACQUIRED_INTO_BUFFER = 1;
  EVERY_N_SAMPLES_EVENT_TYPE_TRANSFERRED_FROM_BUFFER = 2;
}

enum ExcitationSource {
  EXCITATION_SOURCE_UNSPECIFIED = 0;
  EXCITATION_SOURCE_INTERNAL = 10200;
  EXCITATION_SOURCE_EXTERNAL = 10167;
  EXCITATION_SOURCE_NONE = 10230;
}

enum ForceIEPESensorSensitivityUnits {
  FORCE_IEPE_SENSOR_SENSITIVITY_UNITS_UNSPECIFIED = 0;
  FORCE_IEPE_SENSOR_SENSITIVITY_UNITS_M_VOLTS_PER_NEWTON = 15891;
  FORCE_IEPE_SENSOR_SENSITIVITY_UNITS_M_VOLTS_PER_POUND = 15892;
}

enum ForceIEPEUnits {
  FORCE_IEPE_UNITS_UNSPECIFIED = 0;
  FORCE_IEPE_UNITS_NEWTONS = 15875;
  FORCE_IEPE_UNITS_POUNDS = 15876;
  FORCE_IEPE_UNITS_FROM_CUSTOM_SCALE = 10065;
}

enum ForceUnits {
  FORCE_UNITS_UNSPECIFIED = 0;
  FORCE_UNITS_NEWTONS = 15875;
  FORCE_UNITS_POUNDS = 15876;
  FORCE_UNITS_KILOGRAM_FORCE = 15877;
  FORCE_UNITS_FROM_CUSTOM_SCALE = 10065;
}

enum FrequencyUnits {
  FREQUENCY_UNITS_UNSPECIFIED = 0;
  FREQUENCY_UNITS_HZ = 10373;
  FREQUENCY_UNITS_FROM_CUSTOM_SCALE = 10065;
}

enum FrequencyUnits2 {
  FREQUENCY_UNITS2_UNSPECIFIED = 0;
  FREQUENCY_UNITS2_HZ = 10373;
}

enum FrequencyUnits3 {
  FREQUENCY_UNITS3_UNSPECIFIED = 0;
  FREQUENCY_UNITS3_HZ = 10373;
  FREQUENCY_UNITS3_TICKS = 10304;
  FREQUENCY_UNITS3_FROM_CUSTOM_SCALE = 10065;
}

enum FuncGenType {
  FUNC_GEN_TYPE_UNSPECIFIED = 0;
  FUNC_GEN_TYPE_SINE = 14751;
  FUNC_GEN_TYPE_TRIANGLE = 14752;
  FUNC_GEN_TYPE_SQUARE = 14753;
  FUNC_GEN_TYPE_SAWTOOTH = 14754;
}

enum GpsSignalType1 {
  GPS_SIGNAL_TYPE1_UNSPECIFIED = 0;
  GPS_SIGNAL_TYPE1_IRIGB = 10070;
  GPS_SIGNAL_TYPE1_PPS = 10080;
  GPS_SIGNAL_TYPE1_NONE = 10230;
}

enum GroupBy {
  GROUP_BY_GROUP_BY_CHANNEL = 0;
  GROUP_BY_GROUP_BY_SCAN_NUMBER = 1;
}

enum InputTermCfgWithDefault {
  INPUT_TERM_CFG_WITH_DEFAULT_UNSPECIFIED = 0;
  INPUT_TERM_CFG_WITH_DEFAULT_CFG_DEFAULT = -1;
  INPUT_TERM_CFG_WITH_DEFAULT_RSE = 10083;
  INPUT_TERM_CFG_WITH_DEFAULT_NRSE = 10078;
  INPUT_TERM_CFG_WITH_DEFAULT_DIFF = 10106;
  INPUT_TERM_CFG_WITH_DEFAULT_PSEUDO_DIFF = 12529;
}

enum InvertPolarity {
  INVERT_POLARITY_DO_NOT_INVERT_POLARITY = 0;
  INVERT_POLARITY_INVERT_POLARITY = 1;
}

enum LVDTSensitivityUnits1 {
  LVDT_SENSITIVITY_UNITS1_UNSPECIFIED = 0;
  LVDT_SENSITIVITY_UNITS1_M_VOLTS_PER_VOLT_PER_MILLIMETER = 12506;
  LVDT_SENSITIVITY_UNITS1_M_VOLTS_PER_VOLT_PER_MILLI_INCH = 12505;
}

enum LengthUnits2 {
  LENGTH_UNITS2_UNSPECIFIED = 0;
  LENGTH_UNITS2_METERS = 10219;
  LENGTH_UNITS2_INCHES = 10379;
  LENGTH_UNITS2_FROM_CUSTOM_SCALE = 10065;
}

enum LengthUnits3 {
  LENGTH_UNITS3_UNSPECIFIED = 0;
  LENGTH_UNITS3_METERS = 10219;
  LENGTH_UNITS3_INCHES = 10379;
  LENGTH_UNITS3_TICKS = 10304;
  LENGTH_UNITS3_FROM_CUSTOM_SCALE = 10065;
}

enum Level1 {
  LEVEL1_UNSPECIFIED = 0;
  LEVEL1_HIGH = 10192;
  LEVEL1_LOW = 10214;
}

enum LineGrouping {
  LINE_GROUPING_CHAN_PER_LINE = 0;
  LINE_GROUPING_CHAN_FOR_ALL_LINES = 1;
}

enum LoggingMode {
  LOGGING_MODE_UNSPECIFIED = 0;
  LOGGING_MODE_OFF = 10231;
  LOGGING_MODE_LOG = 15844;
  LOGGING_MODE_LOG_AND_READ = 15842;
}

enum LoggingOperation {
  LOGGING_OPERATION_UNSPECIFIED = 0;
  LOGGING_OPERATION_OPEN = 10437;
  LOGGING_OPERATION_OPEN_OR_CREATE = 15846;
  LOGGING_OPERATION_CREATE_OR_REPLACE = 15847;
  LOGGING_OPERATION_CREATE = 15848;
}

enum LogicFamily {
  LOGIC_FAMILY_UNSPECIFIED = 0;
  LOGIC_FAMILY_1POINT_8_V = 16184;
  LOGIC_FAMILY_2POINT_5_V = 14620;
  LOGIC_FAMILY_3POINT_3_V = 14621;
  LOGIC_FAMILY_5_V = 14619;
}

enum Polarity2 {
  POLARITY2_UNSPECIFIED = 0;
  POLARITY2_ACTIVE_HIGH = 10095;
  POLARITY2_ACTIVE_LOW = 10096;
}

enum PowerUpChannelType {
  POWER_UP_CHANNEL_TYPE_CHANNEL_VOLTAGE = 0;
  POWER_UP_CHANNEL_TYPE_CHANNEL_CURRENT = 1;
  POWER_UP_CHANNEL_TYPE_CHANNEL_HIGH_IMPEDANCE = 2;
}

enum PowerUpStates {
  POWER_UP_STATES_UNSPECIFIED = 0;
  POWER_UP_STATES_HIGH = 10192;
  POWER_UP_STATES_LOW = 10214;
  POWER_UP_STATES_TRISTATE = 10310;
}

enum PressureUnits {
  PRESSURE_UNITS_UNSPECIFIED = 0;
  PRESSURE_UNITS_PASCALS = 10081;
  PRESSURE_UNITS_POUNDS_PER_SQUARE_INCH = 15879;
  PRESSURE_UNITS_BAR = 15880;
  PRESSURE_UNITS_FROM_CUSTOM_SCALE = 10065;
}

enum RTDType1 {
  RTD_TYPE1_UNSPECIFIED = 0;
  RTD_TYPE1_PT_3750 = 12481;
  RTD_TYPE1_PT_3851 = 10071;
  RTD_TYPE1_PT_3911 = 12482;
  RTD_TYPE1_PT_3916 = 10069;
  RTD_TYPE1_PT_3920 = 10053;
  RTD_TYPE1_PT_3928 = 12483;
  RTD_TYPE1_CUSTOM = 10137;
}

enum RVDTSensitivityUnits1 {
  RVDT_SENSITIVITY_UNITS1_UNSPECIFIED = 0;
  RVDT_SENSITIVITY_UNITS1_M_VOLTS_PER_VOLT_PER_DEGREE = 12507;
  RVDT_SENSITIVITY_UNITS1_M_VOLTS_PER_VOLT_PER_RADIAN = 12508;
}

enum ResistanceConfiguration {
  RESISTANCE_CONFIGURATION_UNSPECIFIED = 0;
  RESISTANCE_CONFIGURATION_2_WIRE = 2;
  RESISTANCE_CONFIGURATION_3_WIRE = 3;
  RESISTANCE_CONFIGURATION_4_WIRE = 4;
}

enum ResistanceUnits2 {
  RESISTANCE_UNITS2_UNSPECIFIED = 0;
  RESISTANCE_UNITS2_OHMS = 10384;
  RESISTANCE_UNITS2_FROM_CUSTOM_SCALE = 10065;
}

enum ResistorState {
  RESISTOR_STATE_UNSPECIFIED = 0;
  RESISTOR_STATE_PULL_UP = 15950;
  RESISTOR_STATE_PULL_DOWN = 15951;
}

enum SaveOptions {
  SAVE_OPTIONS_UNSPECIFIED = 0;
  SAVE_OPTIONS_OVERWRITE = 1;
  SAVE_OPTIONS_ALLOW_INTERACTIVE_EDITING = 2;
  SAVE_OPTIONS_ALLOW_INTERACTIVE_DELETION = 4;
}

enum ShuntCalSelect {
  SHUNT_CAL_SELECT_UNSPECIFIED = 0;
  SHUNT_CAL_SELECT_A = 12513;
  SHUNT_CAL_SELECT_B = 12514;
  SHUNT_CAL_SELECT_A_AND_B = 12515;
}

enum ShuntCalSource {
  SHUNT_CAL_SOURCE_UNSPECIFIED = 0;
  SHUNT_CAL_SOURCE_DEFAULT = -1;
  SHUNT_CAL_SOURCE_BUILT_IN = 10200;
  SHUNT_CAL_SOURCE_USER_PROVIDED = 10167;
}

enum ShuntElementLocation {
  SHUNT_ELEMENT_LOCATION_UNSPECIFIED = 0;
  SHUNT_ELEMENT_LOCATION_R1 = 12465;
  SHUNT_ELEMENT_LOCATION_R2 = 12466;
  SHUNT_ELEMENT_LOCATION_R3 = 12467;
  SHUNT_ELEMENT_LOCATION_R4 = 14813;
  SHUNT_ELEMENT_LOCATION_NONE = 10230;
}

enum Signal {
  SIGNAL_UNSPECIFIED = 0;
  SIGNAL_AI_CONVERT_CLOCK = 12484;
  SIGNAL_10_MHZ_REF_CLOCK = 12536;
  SIGNAL_20_MHZ_TIMEBASE_CLOCK = 12486;
  SIGNAL_SAMPLE_CLOCK = 12487;
  SIGNAL_ADVANCE_TRIGGER = 12488;
  SIGNAL_REFERENCE_TRIGGER = 12490;
  SIGNAL_START_TRIGGER = 12491;
  SIGNAL_ADV_CMPLT_EVENT = 12492;
  SIGNAL_AI_HOLD_CMPLT_EVENT = 12493;
  SIGNAL_COUNTER_OUTPUT_EVENT = 12494;
  SIGNAL_CHANGE_DETECTION_EVENT = 12511;
  SIGNAL_WDT_EXPIRED_EVENT = 12512;
}

enum Signal2 {
  SIGNAL2_UNSPECIFIED = 0;
  SIGNAL2_SAMPLE_COMPLETE_EVENT = 12530;
  SIGNAL2_COUNTER_OUTPUT_EVENT = 12494;
  SIGNAL2_CHANGE_DETECTION_EVENT = 12511;
  SIGNAL2_SAMPLE_CLOCK = 12487;
}

enum Slope1 {
  SLOPE1_UNSPECIFIED = 0;
  SLOPE1_RISING_SLOPE = 10280;
  SLOPE1_FALLING_SLOPE = 10171;
}

enum SoundPressureUnits1 {
  SOUND_PRESSURE_UNITS1_UNSPECIFIED = 0;
  SOUND_PRESSURE_UNITS1_PASCALS = 10081;
  SOUND_PRESSURE_UNITS1_FROM_CUSTOM_SCALE = 10065;
}

enum StrainGageBridgeType1 {
  STRAIN_GAGE_BRIDGE_TYPE1_UNSPECIFIED = 0;
  STRAIN_GAGE_BRIDGE_TYPE1_FULL_BRIDGE_I = 10183;
  STRAIN_GAGE_BRIDGE_TYPE1_FULL_BRIDGE_II = 10184;
  STRAIN_GAGE_BRIDGE_TYPE1_FULL_BRIDGE_III = 10185;
  STRAIN_GAGE_BRIDGE_TYPE1_HALF_BRIDGE_I = 10188;
  STRAIN_GAGE_BRIDGE_TYPE1_HALF_BRIDGE_II = 10189;
  STRAIN_GAGE_BRIDGE_TYPE1_QUARTER_BRIDGE_I = 10271;
  STRAIN_GAGE_BRIDGE_TYPE1_QUARTER_BRIDGE_II = 10272;
}

enum StrainGageRosetteMeasurementType {
  STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_UNSPECIFIED = 0;
  STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_PRINCIPAL_STRAIN_1 = 15971;
  STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_PRINCIPAL_STRAIN_2 = 15972;
  STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_PRINCIPAL_STRAIN_ANGLE = 15973;
  STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_CARTESIAN_STRAIN_X = 15974;
  STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_CARTESIAN_STRAIN_Y = 15975;
  STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_CARTESIAN_SHEAR_STRAIN_XY = 15976;
  STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_MAX_SHEAR_STRAIN = 15977;
  STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_MAX_SHEAR_STRAIN_ANGLE = 15978;
}

enum StrainGageRosetteType {
  STRAIN_GAGE_ROSETTE_TYPE_UNSPECIFIED = 0;
  STRAIN_GAGE_ROSETTE_TYPE_RECTANGULAR_ROSETTE = 15968;
  STRAIN_GAGE_ROSETTE_TYPE_DELTA_ROSETTE = 15969;
  STRAIN_GAGE_ROSETTE_TYPE_TEE_ROSETTE = 15970;
}

enum StrainUnits1 {
  STRAIN_UNITS1_UNSPECIFIED = 0;
  STRAIN_UNITS1_STRAIN = 10299;
  STRAIN_UNITS1_FROM_CUSTOM_SCALE = 10065;
}

enum TEDSUnits {
  TEDS_UNITS_UNSPECIFIED = 0;
  TEDS_UNITS_FROM_CUSTOM_SCALE = 10065;
  TEDS_UNITS_FROM_TEDS = 12516;
}

enum TaskControlAction {
  TASK_CONTROL_ACTION_TASK_START = 0;
  TASK_CONTROL_ACTION_TASK_STOP = 1;
  TASK_CONTROL_ACTION_TASK_VERIFY = 2;
  TASK_CONTROL_ACTION_TASK_COMMIT = 3;
  TASK_CONTROL_ACTION_TASK_RESERVE = 4;
  TASK_CONTROL_ACTION_TASK_UNRESERVE = 5;
  TASK_CONTROL_ACTION_TASK_ABORT = 6;
}

enum TemperatureUnits {
  TEMPERATURE_UNITS_UNSPECIFIED = 0;
  TEMPERATURE_UNITS_DEG_C = 10143;
  TEMPERATURE_UNITS_DEG_F = 10144;
  TEMPERATURE_UNITS_KELVINS = 10325;
  TEMPERATURE_UNITS_DEG_R = 10145;
}

enum ThermocoupleType1 {
  THERMOCOUPLE_TYPE1_UNSPECIFIED = 0;
  THERMOCOUPLE_TYPE1_J_TYPE_TC = 10072;
  THERMOCOUPLE_TYPE1_K_TYPE_TC = 10073;
  THERMOCOUPLE_TYPE1_N_TYPE_TC = 10077;
  THERMOCOUPLE_TYPE1_R_TYPE_TC = 10082;
  THERMOCOUPLE_TYPE1_S_TYPE_TC = 10085;
  THERMOCOUPLE_TYPE1_T_TYPE_TC = 10086;
  THERMOCOUPLE_TYPE1_B_TYPE_TC = 10047;
  THERMOCOUPLE_TYPE1_E_TYPE_TC = 10055;
}

enum TimeUnits {
  TIME_UNITS_UNSPECIFIED = 0;
  TIME_UNITS_SECONDS = 10364;
  TIME_UNITS_FROM_CUSTOM_SCALE = 10065;
}

enum TimeUnits3 {
  TIME_UNITS3_UNSPECIFIED = 0;
  TIME_UNITS3_SECONDS = 10364;
  TIME_UNITS3_TICKS = 10304;
  TIME_UNITS3_FROM_CUSTOM_SCALE = 10065;
}

enum Timescale2 {
  TIMESCALE2_UNSPECIFIED = 0;
  TIMESCALE2_HOST_TIME = 16126;
  TIMESCALE2_IO_DEVICE_TIME = 16127;
}

enum TimestampEvent {
  TIMESTAMP_EVENT_UNSPECIFIED = 0;
  TIMESTAMP_EVENT_START_TRIGGER = 12491;
  TIMESTAMP_EVENT_REFERENCE_TRIGGER = 12490;
  TIMESTAMP_EVENT_ARM_START_TRIGGER = 14641;
  TIMESTAMP_EVENT_FIRST_SAMPLE_TIMESTAMP = 16130;
}

enum TorqueUnits {
  TORQUE_UNITS_UNSPECIFIED = 0;
  TORQUE_UNITS_NEWTON_METERS = 15881;
  TORQUE_UNITS_INCH_OUNCES = 15882;
  TORQUE_UNITS_INCH_POUNDS = 15883;
  TORQUE_UNITS_FOOT_POUNDS = 15884;
  TORQUE_UNITS_FROM_CUSTOM_SCALE = 10065;
}

enum UnitsPreScaled {
  UNITS_PRE_SCALED_UNSPECIFIED = 0;
  UNITS_PRE_SCALED_VOLTS = 10348;
  UNITS_PRE_SCALED_AMPS = 10342;
  UNITS_PRE_SCALED_DEG_F = 10144;
  UNITS_PRE_SCALED_DEG_C = 10143;
  UNITS_PRE_SCALED_DEG_R = 10145;
  UNITS_PRE_SCALED_KELVINS = 10325;
  UNITS_PRE_SCALED_STRAIN = 10299;
  UNITS_PRE_SCALED_OHMS = 10384;
  UNITS_PRE_SCALED_HZ = 10373;
  UNITS_PRE_SCALED_SECONDS = 10364;
  UNITS_PRE_SCALED_METERS = 10219;
  UNITS_PRE_SCALED_INCHES = 10379;
  UNITS_PRE_SCALED_DEGREES = 10146;
  UNITS_PRE_SCALED_RADIANS = 10273;
  UNITS_PRE_SCALED_TICKS = 10304;
  UNITS_PRE_SCALED_RPM = 16080;
  UNITS_PRE_SCALED_RADIANS_PER_SECOND = 16081;
  UNITS_PRE_SCALED_DEGREES_PER_SECOND = 16082;
  UNITS_PRE_SCALED_G = 10186;
  UNITS_PRE_SCALED_METERS_PER_SECOND_SQUARED = 12470;
  UNITS_PRE_SCALED_INCHES_PER_SECOND_SQUARED = 12471;
  UNITS_PRE_SCALED_METERS_PER_SECOND = 15959;
  UNITS_PRE_SCALED_INCHES_PER_SECOND = 15960;
  UNITS_PRE_SCALED_PASCALS = 10081;
  UNITS_PRE_SCALED_NEWTONS = 15875;
  UNITS_PRE_SCALED_POUNDS = 15876;
  UNITS_PRE_SCALED_KILOGRAM_FORCE = 15877;
  UNITS_PRE_SCALED_POUNDS_PER_SQUARE_INCH = 15879;
  UNITS_PRE_SCALED_BAR = 15880;
  UNITS_PRE_SCALED_NEWTON_METERS = 15881;
  UNITS_PRE_SCALED_INCH_OUNCES = 15882;
  UNITS_PRE_SCALED_INCH_POUNDS = 15883;
  UNITS_PRE_SCALED_FOOT_POUNDS = 15884;
  UNITS_PRE_SCALED_VOLTS_PER_VOLT = 15896;
  UNITS_PRE_SCALED_M_VOLTS_PER_VOLT = 15897;
  UNITS_PRE_SCALED_COULOMBS = 16102;
  UNITS_PRE_SCALED_PICO_COULOMBS = 16103;
  UNITS_PRE_SCALED_FROM_TEDS = 12516;
}

enum VelocityIEPESensorSensitivityUnits {
  VELOCITY_IEPE_SENSOR_SENSITIVITY_UNITS_UNSPECIFIED = 0;
  VELOCITY_IEPE_SENSOR_SENSITIVITY_UNITS_MILLIVOLTS_PER_MILLIMETER_PER_SECOND = 15963;
  VELOCITY_IEPE_SENSOR_SENSITIVITY_UNITS_MILLI_VOLTS_PER_INCH_PER_SECOND = 15964;
}

enum VelocityUnits {
  VELOCITY_UNITS_UNSPECIFIED = 0;
  VELOCITY_UNITS_METERS_PER_SECOND = 15959;
  VELOCITY_UNITS_INCHES_PER_SECOND = 15960;
  VELOCITY_UNITS_FROM_CUSTOM_SCALE = 10065;
}

enum VoltageUnits2 {
  VOLTAGE_UNITS2_UNSPECIFIED = 0;
  VOLTAGE_UNITS2_VOLTS = 10348;
  VOLTAGE_UNITS2_FROM_CUSTOM_SCALE = 10065;
}

enum WatchdogAOOutputType {
  WATCHDOG_AO_OUTPUT_TYPE_UNSPECIFIED = 0;
  WATCHDOG_AO_OUTPUT_TYPE_VOLTAGE = 10322;
  WATCHDOG_AO_OUTPUT_TYPE_CURRENT = 10134;
  WATCHDOG_AO_OUTPUT_TYPE_NO_CHANGE = 10160;
}

enum WatchdogCOExpirState {
  WATCHDOG_CO_EXPIR_STATE_UNSPECIFIED = 0;
  WATCHDOG_CO_EXPIR_STATE_LOW = 10214;
  WATCHDOG_CO_EXPIR_STATE_HIGH = 10192;
  WATCHDOG_CO_EXPIR_STATE_NO_CHANGE = 10160;
}

enum WatchdogControlAction {
  WATCHDOG_CONTROL_ACTION_RESET_TIMER = 0;
  WATCHDOG_CONTROL_ACTION_CLEAR_EXPIRATION = 1;
}

enum WindowTriggerCondition1 {
  WINDOW_TRIGGER_CONDITION1_UNSPECIFIED = 0;
  WINDOW_TRIGGER_CONDITION1_ENTERING_WIN = 10163;
  WINDOW_TRIGGER_CONDITION1_LEAVING_WIN = 10208;
}

enum WriteBasicTEDSOptions {
  WRITE_BASIC_TEDS_OPTIONS_UNSPECIFIED = 0;
  WRITE_BASIC_TEDS_OPTIONS_WRITE_TO_EEPROM = 12538;
  WRITE_BASIC_TEDS_OPTIONS_WRITE_TO_PROM = 12539;
  WRITE_BASIC_TEDS_OPTIONS_DO_NOT_WRITE = 12540;
}

enum ChannelInt32AttributeValues {
  option allow_alias = true;
  CHANNEL_INT32_UNSPECIFIED = 0;
  CHANNEL_INT32_AC_EXCIT_WIRE_MODE_4_WIRE = 4;
  CHANNEL_INT32_AC_EXCIT_WIRE_MODE_5_WIRE = 5;
  CHANNEL_INT32_AC_EXCIT_WIRE_MODE_6_WIRE = 6;
  CHANNEL_INT32_ADC_TIMING_MODE_AUTOMATIC = 16097;
  CHANNEL_INT32_ADC_TIMING_MODE_HIGH_RESOLUTION = 10195;
  CHANNEL_INT32_ADC_TIMING_MODE_HIGH_SPEED = 14712;
  CHANNEL_INT32_ADC_TIMING_MODE_BEST_50_HZ_REJECTION = 14713;
  CHANNEL_INT32_ADC_TIMING_MODE_BEST_60_HZ_REJECTION = 14714;
  CHANNEL_INT32_ADC_TIMING_MODE_CUSTOM = 10137;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_VOLTAGE = 10322;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_VOLTAGE_RMS = 10350;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_CURRENT = 10134;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_CURRENT_RMS = 10351;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_VOLTAGE_CUSTOM_WITH_EXCITATION = 10323;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_BRIDGE = 15908;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_FREQ_VOLTAGE = 10181;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_RESISTANCE = 10278;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_TEMP_TC = 10303;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_TEMP_THRMSTR = 10302;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_TEMP_RTD = 10301;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_TEMP_BUILT_IN_SENSOR = 10311;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_STRAIN_GAGE = 10300;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_ROSETTE_STRAIN_GAGE = 15980;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_POSITION_LVDT = 10352;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_POSITION_RVDT = 10353;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_POSITION_EDDY_CURRENT_PROXIMITY_PROBE = 14835;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_ACCELEROMETER = 10356;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_ACCELERATION_CHARGE = 16104;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_ACCELERATION_4_WIRE_DC_VOLTAGE = 16106;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_VELOCITY_IEPE_SENSOR = 15966;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_FORCE_BRIDGE = 15899;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_FORCE_IEPE_SENSOR = 15895;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_PRESSURE_BRIDGE = 15902;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_SOUND_PRESSURE_MICROPHONE = 10354;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_TORQUE_BRIDGE = 15905;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_TEDS_SENSOR = 12531;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_CHARGE = 16105;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_POWER = 16201;
  CHANNEL_INT32_AI_MEASUREMENT_TYPE_CALCULATED_POWER = 16204;
  CHANNEL_INT32_AO_IDLE_OUTPUT_BEHAVIOR_ZERO_VOLTS = 12526;
  CHANNEL_INT32_AO_IDLE_OUTPUT_BEHAVIOR_HIGH_IMPEDANCE = 12527;
  CHANNEL_INT32_AO_IDLE_OUTPUT_BEHAVIOR_MAINTAIN_EXISTING_VALUE = 12528;
  CHANNEL_INT32_AO_OUTPUT_CHANNEL_TYPE_VOLTAGE = 10322;
  CHANNEL_INT32_AO_OUTPUT_CHANNEL_TYPE_CURRENT = 10134;
  CHANNEL_INT32_AO_OUTPUT_CHANNEL_TYPE_FUNC_GEN = 14750;
  CHANNEL_INT32_ACCEL_CHARGE_SENSITIVITY_UNITS_PICO_COULOMBS_PER_G = 16099;
  CHANNEL_INT32_ACCEL_CHARGE_SENSITIVITY_UNITS_PICO_COULOMBS_PER_METERS_PER_SECOND_SQUARED = 16100;
  CHANNEL_INT32_ACCEL_CHARGE_SENSITIVITY_UNITS_PICO_COULOMBS_PER_INCHES_PER_SECOND_SQUARED = 16101;
  CHANNEL_INT32_ACCEL_SENSITIVITY_UNITS1_M_VOLTS_PER_G = 12509;
  CHANNEL_INT32_ACCEL_SENSITIVITY_UNITS1_VOLTS_PER_G = 12510;
  CHANNEL_INT32_ACCEL_UNITS2_ACCEL_UNIT_G = 10186;
  CHANNEL_INT32_ACCEL_UNITS2_METERS_PER_SECOND_SQUARED = 12470;
  CHANNEL_INT32_ACCEL_UNITS2_INCHES_PER_SECOND_SQUARED = 12471;
  CHANNEL_INT32_ACCEL_UNITS2_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_ANGLE_UNITS1_DEGREES = 10146;
  CHANNEL_INT32_ANGLE_UNITS1_RADIANS = 10273;
  CHANNEL_INT32_ANGLE_UNITS1_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_ANGLE_UNITS2_DEGREES = 10146;
  CHANNEL_INT32_ANGLE_UNITS2_RADIANS = 10273;
  CHANNEL_INT32_ANGLE_UNITS2_TICKS = 10304;
  CHANNEL_INT32_ANGLE_UNITS2_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_ANGLE_UNITS3_DEGREES = 10146;
  CHANNEL_INT32_ANGLE_UNITS3_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_ANGULAR_VELOCITY_UNITS_RPM = 16080;
  CHANNEL_INT32_ANGULAR_VELOCITY_UNITS_RADIANS_PER_SECOND = 16081;
  CHANNEL_INT32_ANGULAR_VELOCITY_UNITS_DEGREES_PER_SECOND = 16082;
  CHANNEL_INT32_ANGULAR_VELOCITY_UNITS_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_AUTO_ZERO_TYPE1_NONE = 10230;
  CHANNEL_INT32_AUTO_ZERO_TYPE1_ONCE = 10244;
  CHANNEL_INT32_AUTO_ZERO_TYPE1_EVERY_SAMPLE = 10164;
  CHANNEL_INT32_BRIDGE_CONFIGURATION1_FULL_BRIDGE = 10182;
  CHANNEL_INT32_BRIDGE_CONFIGURATION1_HALF_BRIDGE = 10187;
  CHANNEL_INT32_BRIDGE_CONFIGURATION1_QUARTER_BRIDGE = 10270;
  CHANNEL_INT32_BRIDGE_CONFIGURATION1_NO_BRIDGE = 10228;
  CHANNEL_INT32_BRIDGE_ELECTRICAL_UNITS_VOLTS_PER_VOLT = 15896;
  CHANNEL_INT32_BRIDGE_ELECTRICAL_UNITS_M_VOLTS_PER_VOLT = 15897;
  CHANNEL_INT32_BRIDGE_PHYSICAL_UNITS_NEWTONS = 15875;
  CHANNEL_INT32_BRIDGE_PHYSICAL_UNITS_POUNDS = 15876;
  CHANNEL_INT32_BRIDGE_PHYSICAL_UNITS_KILOGRAM_FORCE = 15877;
  CHANNEL_INT32_BRIDGE_PHYSICAL_UNITS_PASCALS = 10081;
  CHANNEL_INT32_BRIDGE_PHYSICAL_UNITS_POUNDS_PER_SQUARE_INCH = 15879;
  CHANNEL_INT32_BRIDGE_PHYSICAL_UNITS_BAR = 15880;
  CHANNEL_INT32_BRIDGE_PHYSICAL_UNITS_NEWTON_METERS = 15881;
  CHANNEL_INT32_BRIDGE_PHYSICAL_UNITS_INCH_OUNCES = 15882;
  CHANNEL_INT32_BRIDGE_PHYSICAL_UNITS_INCH_POUNDS = 15883;
  CHANNEL_INT32_BRIDGE_PHYSICAL_UNITS_FOOT_POUNDS = 15884;
  CHANNEL_INT32_BRIDGE_SHUNT_CAL_SOURCE_BUILT_IN = 10200;
  CHANNEL_INT32_BRIDGE_SHUNT_CAL_SOURCE_USER_PROVIDED = 10167;
  CHANNEL_INT32_BRIDGE_UNITS_VOLTS_PER_VOLT = 15896;
  CHANNEL_INT32_BRIDGE_UNITS_M_VOLTS_PER_VOLT = 15897;
  CHANNEL_INT32_BRIDGE_UNITS_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_BRIDGE_UNITS_FROM_TEDS = 12516;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_COUNT_EDGES = 10125;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_FREQ = 10179;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_PERIOD = 10256;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_PULSE_WIDTH = 10359;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_SEMI_PERIOD = 10289;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_PULSE_FREQUENCY = 15864;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_PULSE_TIME = 15865;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_PULSE_TICKS = 15866;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_DUTY_CYCLE = 16070;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_POSITION_ANG_ENCODER = 10360;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_POSITION_LIN_ENCODER = 10361;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_VELOCITY_ANG_ENCODER = 16078;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_VELOCITY_LIN_ENCODER = 16079;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_TWO_EDGE_SEP = 10267;
  CHANNEL_INT32_CI_MEASUREMENT_TYPE_GPS_TIMESTAMP = 10362;
  CHANNEL_INT32_CJC_SOURCE1_BUILT_IN = 10200;
  CHANNEL_INT32_CJC_SOURCE1_CONST_VAL = 10116;
  CHANNEL_INT32_CJC_SOURCE1_CHAN = 10113;
  CHANNEL_INT32_CO_OUTPUT_TYPE_PULSE_TIME = 10269;
  CHANNEL_INT32_CO_OUTPUT_TYPE_PULSE_FREQ = 10119;
  CHANNEL_INT32_CO_OUTPUT_TYPE_PULSE_TICKS = 10268;
  CHANNEL_INT32_CHANNEL_TYPE_AI = 10100;
  CHANNEL_INT32_CHANNEL_TYPE_AO = 10102;
  CHANNEL_INT32_CHANNEL_TYPE_DI = 10151;
  CHANNEL_INT32_CHANNEL_TYPE_DO = 10153;
  CHANNEL_INT32_CHANNEL_TYPE_CI = 10131;
  CHANNEL_INT32_CHANNEL_TYPE_CO = 10132;
  CHANNEL_INT32_CHARGE_UNITS_COULOMBS = 16102;
  CHANNEL_INT32_CHARGE_UNITS_PICO_COULOMBS = 16103;
  CHANNEL_INT32_CHARGE_UNITS_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_CONSTRAINED_GEN_MODE_UNCONSTRAINED = 14708;
  CHANNEL_INT32_CONSTRAINED_GEN_MODE_FIXED_HIGH_FREQ = 14709;
  CHANNEL_INT32_CONSTRAINED_GEN_MODE_FIXED_LOW_FREQ = 14710;
  CHANNEL_INT32_CONSTRAINED_GEN_MODE_FIXED_50_PERCENT_DUTY_CYCLE = 14711;
  CHANNEL_INT32_COUNT_DIRECTION1_COUNT_UP = 10128;
  CHANNEL_INT32_COUNT_DIRECTION1_COUNT_DOWN = 10124;
  CHANNEL_INT32_COUNT_DIRECTION1_EXT_CONTROLLED = 10326;
  CHANNEL_INT32_COUNTER_FREQUENCY_METHOD_LOW_FREQ_1_CTR = 10105;
  CHANNEL_INT32_COUNTER_FREQUENCY_METHOD_HIGH_FREQ_2_CTR = 10157;
  CHANNEL_INT32_COUNTER_FREQUENCY_METHOD_LARGE_RNG_2_CTR = 10205;
  CHANNEL_INT32_COUNTER_FREQUENCY_METHOD_DYN_AVG = 16065;
  CHANNEL_INT32_COUPLING1_AC = 10045;
  CHANNEL_INT32_COUPLING1_DC = 10050;
  CHANNEL_INT32_COUPLING1_GND = 10066;
  CHANNEL_INT32_CURRENT_SHUNT_RESISTOR_LOCATION1_INTERNAL = 10200;
  CHANNEL_INT32_CURRENT_SHUNT_RESISTOR_LOCATION1_EXTERNAL = 10167;
  CHANNEL_INT32_CURRENT_UNITS1_AMPS = 10342;
  CHANNEL_INT32_CURRENT_UNITS1_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_CURRENT_UNITS1_FROM_TEDS = 12516;
  CHANNEL_INT32_DATA_JUSTIFICATION1_RIGHT_JUSTIFIED = 10279;
  CHANNEL_INT32_DATA_JUSTIFICATION1_LEFT_JUSTIFIED = 10209;
  CHANNEL_INT32_DATA_TRANSFER_MECHANISM_DMA = 10054;
  CHANNEL_INT32_DATA_TRANSFER_MECHANISM_INTERRUPTS = 10204;
  CHANNEL_INT32_DATA_TRANSFER_MECHANISM_PROGRAMMED_IO = 10264;
  CHANNEL_INT32_DATA_TRANSFER_MECHANISM_US_BBULK = 12590;
  CHANNEL_INT32_DIGITAL_DRIVE_TYPE_ACTIVE_DRIVE = 12573;
  CHANNEL_INT32_DIGITAL_DRIVE_TYPE_OPEN_COLLECTOR = 12574;
  CHANNEL_INT32_DIGITAL_LINE_STATE_HIGH = 10192;
  CHANNEL_INT32_DIGITAL_LINE_STATE_LOW = 10214;
  CHANNEL_INT32_DIGITAL_LINE_STATE_TRISTATE = 10310;
  CHANNEL_INT32_DIGITAL_LINE_STATE_NO_CHANGE = 10160;
  CHANNEL_INT32_DIGITAL_WIDTH_UNITS4_SECONDS = 10364;
  CHANNEL_INT32_DIGITAL_WIDTH_UNITS4_SAMPLE_CLK_PERIODS = 10286;
  CHANNEL_INT32_EDDY_CURRENT_PROX_PROBE_SENSITIVITY_UNITS_M_VOLTS_PER_MIL = 14836;
  CHANNEL_INT32_EDDY_CURRENT_PROX_PROBE_SENSITIVITY_UNITS_VOLTS_PER_MIL = 14837;
  CHANNEL_INT32_EDDY_CURRENT_PROX_PROBE_SENSITIVITY_UNITS_M_VOLTS_PER_MILLIMETER = 14838;
  CHANNEL_INT32_EDDY_CURRENT_PROX_PROBE_SENSITIVITY_UNITS_VOLTS_PER_MILLIMETER = 14839;
  CHANNEL_INT32_EDDY_CURRENT_PROX_PROBE_SENSITIVITY_UNITS_M_VOLTS_PER_MICRON = 14840;
  CHANNEL_INT32_EDGE1_RISING = 10280;
  CHANNEL_INT32_EDGE1_FALLING = 10171;
  CHANNEL_INT32_ENCODER_TYPE2_X1 = 10090;
  CHANNEL_INT32_ENCODER_TYPE2_X2 = 10091;
  CHANNEL_INT32_ENCODER_TYPE2_X4 = 10092;
  CHANNEL_INT32_ENCODER_TYPE2_TWO_PULSE_COUNTING = 10313;
  CHANNEL_INT32_ENCODER_Z_INDEX_PHASE1_A_HIGH_B_HIGH = 10040;
  CHANNEL_INT32_ENCODER_Z_INDEX_PHASE1_A_HIGH_B_LOW = 10041;
  CHANNEL_INT32_ENCODER_Z_INDEX_PHASE1_A_LOW_B_HIGH = 10042;
  CHANNEL_INT32_ENCODER_Z_INDEX_PHASE1_A_LOW_B_LOW = 10043;
  CHANNEL_INT32_EXCITATION_D_COR_AC_DC = 10050;
  CHANNEL_INT32_EXCITATION_D_COR_AC_AC = 10045;
  CHANNEL_INT32_EXCITATION_IDLE_OUTPUT_BEHAVIOR_ZERO_VOLTS_OR_AMPS = 12526;
  CHANNEL_INT32_EXCITATION_IDLE_OUTPUT_BEHAVIOR_MAINTAIN_EXISTING_VALUE = 12528;
  CHANNEL_INT32_EXCITATION_SOURCE_INTERNAL = 10200;
  CHANNEL_INT32_EXCITATION_SOURCE_EXTERNAL = 10167;
  CHANNEL_INT32_EXCITATION_SOURCE_NONE = 10230;
  CHANNEL_INT32_EXCITATION_VOLTAGE_OR_CURRENT_VOLTAGE = 10322;
  CHANNEL_INT32_EXCITATION_VOLTAGE_OR_CURRENT_CURRENT = 10134;
  CHANNEL_INT32_FILTER_RESPONSE_CONSTANT_GROUP_DELAY = 16075;
  CHANNEL_INT32_FILTER_RESPONSE_BUTTERWORTH = 16076;
  CHANNEL_INT32_FILTER_RESPONSE_ELLIPTICAL = 16077;
  CHANNEL_INT32_FILTER_RESPONSE_HARDWARE_DEFINED = 10191;
  CHANNEL_INT32_FILTER_RESPONSE1_COMB = 16152;
  CHANNEL_INT32_FILTER_RESPONSE1_BESSEL = 16153;
  CHANNEL_INT32_FILTER_RESPONSE1_BRICKWALL = 16155;
  CHANNEL_INT32_FILTER_RESPONSE1_BUTTERWORTH = 16076;
  CHANNEL_INT32_FILTER_TYPE1_HARDWARE_DEFINED = 10191;
  CHANNEL_INT32_FILTER_TYPE2_LOWPASS = 16071;
  CHANNEL_INT32_FILTER_TYPE2_HIGHPASS = 16072;
  CHANNEL_INT32_FILTER_TYPE2_BANDPASS = 16073;
  CHANNEL_INT32_FILTER_TYPE2_NOTCH = 16074;
  CHANNEL_INT32_FILTER_TYPE2_CUSTOM = 10137;
  CHANNEL_INT32_FORCE_IEPE_SENSOR_SENSITIVITY_UNITS_M_VOLTS_PER_NEWTON = 15891;
  CHANNEL_INT32_FORCE_IEPE_SENSOR_SENSITIVITY_UNITS_M_VOLTS_PER_POUND = 15892;
  CHANNEL_INT32_FORCE_UNITS_NEWTONS = 15875;
  CHANNEL_INT32_FORCE_UNITS_POUNDS = 15876;
  CHANNEL_INT32_FORCE_UNITS_KILOGRAM_FORCE = 15877;
  CHANNEL_INT32_FORCE_UNITS_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_FREQUENCY_UNITS_HZ = 10373;
  CHANNEL_INT32_FREQUENCY_UNITS_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_FREQUENCY_UNITS2_HZ = 10373;
  CHANNEL_INT32_FREQUENCY_UNITS3_HZ = 10373;
  CHANNEL_INT32_FREQUENCY_UNITS3_TICKS = 10304;
  CHANNEL_INT32_FREQUENCY_UNITS3_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_FUNC_GEN_TYPE_SINE = 14751;
  CHANNEL_INT32_FUNC_GEN_TYPE_TRIANGLE = 14752;
  CHANNEL_INT32_FUNC_GEN_TYPE_SQUARE = 14753;
  CHANNEL_INT32_FUNC_GEN_TYPE_SAWTOOTH = 14754;
  CHANNEL_INT32_GPS_SIGNAL_TYPE1_IRIGB = 10070;
  CHANNEL_INT32_GPS_SIGNAL_TYPE1_PPS = 10080;
  CHANNEL_INT32_GPS_SIGNAL_TYPE1_NONE = 10230;
  CHANNEL_INT32_INPUT_DATA_TRANSFER_CONDITION_ON_BRD_MEM_MORE_THAN_HALF_FULL = 10237;
  CHANNEL_INT32_INPUT_DATA_TRANSFER_CONDITION_ON_BRD_MEM_NOT_EMPTY = 10241;
  CHANNEL_INT32_INPUT_DATA_TRANSFER_CONDITION_ONBRD_MEM_CUSTOM_THRESHOLD = 12577;
  CHANNEL_INT32_INPUT_DATA_TRANSFER_CONDITION_WHEN_ACQ_COMPLETE = 12546;
  CHANNEL_INT32_INPUT_TERM_CFG_RSE = 10083;
  CHANNEL_INT32_INPUT_TERM_CFG_NRSE = 10078;
  CHANNEL_INT32_INPUT_TERM_CFG_DIFF = 10106;
  CHANNEL_INT32_INPUT_TERM_CFG_PSEUDO_DIFF = 12529;
  CHANNEL_INT32_INPUT_TERM_CFG2_DIFF = 10106;
  CHANNEL_INT32_INPUT_TERM_CFG2_RSE = 10083;
  CHANNEL_INT32_LVDT_SENSITIVITY_UNITS1_M_VOLTS_PER_VOLT_PER_MILLIMETER = 12506;
  CHANNEL_INT32_LVDT_SENSITIVITY_UNITS1_M_VOLTS_PER_VOLT_PER_MILLI_INCH = 12505;
  CHANNEL_INT32_LENGTH_UNITS2_METERS = 10219;
  CHANNEL_INT32_LENGTH_UNITS2_INCHES = 10379;
  CHANNEL_INT32_LENGTH_UNITS2_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_LENGTH_UNITS3_METERS = 10219;
  CHANNEL_INT32_LENGTH_UNITS3_INCHES = 10379;
  CHANNEL_INT32_LENGTH_UNITS3_TICKS = 10304;
  CHANNEL_INT32_LENGTH_UNITS3_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_LENGTH_UNITS4_METERS = 10219;
  CHANNEL_INT32_LENGTH_UNITS4_FEET = 10380;
  CHANNEL_INT32_LENGTH_UNITS4_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_LEVEL1_HIGH = 10192;
  CHANNEL_INT32_LEVEL1_LOW = 10214;
  CHANNEL_INT32_LOGIC_FAMILY_1POINT_8_V = 16184;
  CHANNEL_INT32_LOGIC_FAMILY_2POINT_5_V = 14620;
  CHANNEL_INT32_LOGIC_FAMILY_3POINT_3_V = 14621;
  CHANNEL_INT32_LOGIC_FAMILY_5_V = 14619;
  CHANNEL_INT32_LOGIC_LVL_BEHAVIOR_LOGIC_LEVEL_PULL_UP = 16064;
  CHANNEL_INT32_LOGIC_LVL_BEHAVIOR_NONE = 10230;
  CHANNEL_INT32_MODULATION_TYPE_AM = 14756;
  CHANNEL_INT32_MODULATION_TYPE_FM = 14757;
  CHANNEL_INT32_MODULATION_TYPE_NONE = 10230;
  CHANNEL_INT32_NAV_MEASUREMENT_TYPE_ALTITUDE = 15997;
  CHANNEL_INT32_NAV_MEASUREMENT_TYPE_LONGITUDE = 15998;
  CHANNEL_INT32_NAV_MEASUREMENT_TYPE_LATITUDE = 15999;
  CHANNEL_INT32_NAV_MEASUREMENT_TYPE_SPEED_OVER_GROUND = 16000;
  CHANNEL_INT32_NAV_MEASUREMENT_TYPE_TRACK = 16001;
  CHANNEL_INT32_NAV_MEASUREMENT_TYPE_TIMESTAMP = 15986;
  CHANNEL_INT32_NAV_MEASUREMENT_TYPE_VERT_VELOCITY = 16003;
  CHANNEL_INT32_OUTPUT_DATA_TRANSFER_CONDITION_ON_BRD_MEM_EMPTY = 10235;
  CHANNEL_INT32_OUTPUT_DATA_TRANSFER_CONDITION_ON_BRD_MEM_HALF_FULL_OR_LESS = 10239;
  CHANNEL_INT32_OUTPUT_DATA_TRANSFER_CONDITION_ON_BRD_MEM_NOT_FULL = 10242;
  CHANNEL_INT32_OUTPUT_TERM_CFG_RSE = 10083;
  CHANNEL_INT32_OUTPUT_TERM_CFG_DIFF = 10106;
  CHANNEL_INT32_OUTPUT_TERM_CFG_PSEUDO_DIFF = 12529;
  CHANNEL_INT32_POWER_IDLE_OUTPUT_BEHAVIOR_OUTPUT_DISABLED = 15503;
  CHANNEL_INT32_POWER_IDLE_OUTPUT_BEHAVIOR_MAINTAIN_EXISTING_VALUE = 12528;
  CHANNEL_INT32_POWER_OUTPUT_STATE_CONSTANT_VOLTAGE = 15500;
  CHANNEL_INT32_POWER_OUTPUT_STATE_CONSTANT_CURRENT = 15501;
  CHANNEL_INT32_POWER_OUTPUT_STATE_OVERVOLTAGE = 15502;
  CHANNEL_INT32_POWER_OUTPUT_STATE_OUTPUT_DISABLED = 15503;
  CHANNEL_INT32_PRESSURE_UNITS_PASCALS = 10081;
  CHANNEL_INT32_PRESSURE_UNITS_POUNDS_PER_SQUARE_INCH = 15879;
  CHANNEL_INT32_PRESSURE_UNITS_BAR = 15880;
  CHANNEL_INT32_PRESSURE_UNITS_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_RTD_TYPE1_PT_3750 = 12481;
  CHANNEL_INT32_RTD_TYPE1_PT_3851 = 10071;
  CHANNEL_INT32_RTD_TYPE1_PT_3911 = 12482;
  CHANNEL_INT32_RTD_TYPE1_PT_3916 = 10069;
  CHANNEL_INT32_RTD_TYPE1_PT_3920 = 10053;
  CHANNEL_INT32_RTD_TYPE1_PT_3928 = 12483;
  CHANNEL_INT32_RTD_TYPE1_CUSTOM = 10137;
  CHANNEL_INT32_RVDT_SENSITIVITY_UNITS1_M_VOLTS_PER_VOLT_PER_DEGREE = 12507;
  CHANNEL_INT32_RVDT_SENSITIVITY_UNITS1_M_VOLTS_PER_VOLT_PER_RADIAN = 12508;
  CHANNEL_INT32_RAW_DATA_COMPRESSION_TYPE_NONE = 10230;
  CHANNEL_INT32_RAW_DATA_COMPRESSION_TYPE_LOSSLESS_PACKING = 12555;
  CHANNEL_INT32_RAW_DATA_COMPRESSION_TYPE_LOSSY_LSB_REMOVAL = 12556;
  CHANNEL_INT32_RESISTANCE_CONFIGURATION_2_WIRE = 2;
  CHANNEL_INT32_RESISTANCE_CONFIGURATION_3_WIRE = 3;
  CHANNEL_INT32_RESISTANCE_CONFIGURATION_4_WIRE = 4;
  CHANNEL_INT32_RESISTANCE_UNITS1_OHMS = 10384;
  CHANNEL_INT32_RESISTANCE_UNITS1_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_RESISTANCE_UNITS1_FROM_TEDS = 12516;
  CHANNEL_INT32_RESOLUTION_TYPE1_BITS = 10109;
  CHANNEL_INT32_SAMP_CLK_OVERRUN_BEHAVIOR_REPEATED_DATA = 16062;
  CHANNEL_INT32_SAMP_CLK_OVERRUN_BEHAVIOR_SENTINEL_VALUE = 16063;
  CHANNEL_INT32_SAMPLE_CLOCK_ACTIVE_OR_INACTIVE_EDGE_SELECTION_SAMP_CLK_ACTIVE_EDGE = 14617;
  CHANNEL_INT32_SAMPLE_CLOCK_ACTIVE_OR_INACTIVE_EDGE_SELECTION_SAMP_CLK_INACTIVE_EDGE = 14618;
  CHANNEL_INT32_SCALE_TYPE2_POLYNOMIAL = 10449;
  CHANNEL_INT32_SCALE_TYPE2_TABLE = 10450;
  CHANNEL_INT32_SCALE_TYPE3_POLYNOMIAL = 10449;
  CHANNEL_INT32_SCALE_TYPE3_TABLE = 10450;
  CHANNEL_INT32_SCALE_TYPE3_NONE = 10230;
  CHANNEL_INT32_SCALE_TYPE4_NONE = 10230;
  CHANNEL_INT32_SCALE_TYPE4_TWO_POINT_LINEAR = 15898;
  CHANNEL_INT32_SCALE_TYPE4_TABLE = 10450;
  CHANNEL_INT32_SCALE_TYPE4_POLYNOMIAL = 10449;
  CHANNEL_INT32_SENSE_LOCAL = 16095;
  CHANNEL_INT32_SENSE_REMOTE = 16096;
  CHANNEL_INT32_SENSOR_POWER_CFG_NO_CHANGE = 10160;
  CHANNEL_INT32_SENSOR_POWER_CFG_ENABLED = 16145;
  CHANNEL_INT32_SENSOR_POWER_CFG_DISABLED = 16146;
  CHANNEL_INT32_SENSOR_POWER_TYPE_DC = 10050;
  CHANNEL_INT32_SENSOR_POWER_TYPE_AC = 10045;
  CHANNEL_INT32_SENSOR_POWER_TYPE_BIPOLAR_DC = 16147;
  CHANNEL_INT32_SHUNT_CAL_SELECT_A = 12513;
  CHANNEL_INT32_SHUNT_CAL_SELECT_B = 12514;
  CHANNEL_INT32_SHUNT_CAL_SELECT_A_AND_B = 12515;
  CHANNEL_INT32_SOUND_PRESSURE_UNITS1_PASCALS = 10081;
  CHANNEL_INT32_SOUND_PRESSURE_UNITS1_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_SOURCE_SELECTION_INTERNAL = 10200;
  CHANNEL_INT32_SOURCE_SELECTION_EXTERNAL = 10167;
  CHANNEL_INT32_STRAIN_GAGE_BRIDGE_TYPE1_FULL_BRIDGE_I = 10183;
  CHANNEL_INT32_STRAIN_GAGE_BRIDGE_TYPE1_FULL_BRIDGE_II = 10184;
  CHANNEL_INT32_STRAIN_GAGE_BRIDGE_TYPE1_FULL_BRIDGE_III = 10185;
  CHANNEL_INT32_STRAIN_GAGE_BRIDGE_TYPE1_HALF_BRIDGE_I = 10188;
  CHANNEL_INT32_STRAIN_GAGE_BRIDGE_TYPE1_HALF_BRIDGE_II = 10189;
  CHANNEL_INT32_STRAIN_GAGE_BRIDGE_TYPE1_QUARTER_BRIDGE_I = 10271;
  CHANNEL_INT32_STRAIN_GAGE_BRIDGE_TYPE1_QUARTER_BRIDGE_II = 10272;
  CHANNEL_INT32_STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_PRINCIPAL_STRAIN_1 = 15971;
  CHANNEL_INT32_STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_PRINCIPAL_STRAIN_2 = 15972;
  CHANNEL_INT32_STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_PRINCIPAL_STRAIN_ANGLE = 15973;
  CHANNEL_INT32_STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_CARTESIAN_STRAIN_X = 15974;
  CHANNEL_INT32_STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_CARTESIAN_STRAIN_Y = 15975;
  CHANNEL_INT32_STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_CARTESIAN_SHEAR_STRAIN_XY = 15976;
  CHANNEL_INT32_STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_MAX_SHEAR_STRAIN = 15977;
  CHANNEL_INT32_STRAIN_GAGE_ROSETTE_MEASUREMENT_TYPE_MAX_SHEAR_STRAIN_ANGLE = 15978;
  CHANNEL_INT32_STRAIN_GAGE_ROSETTE_TYPE_RECTANGULAR_ROSETTE = 15968;
  CHANNEL_INT32_STRAIN_GAGE_ROSETTE_TYPE_DELTA_ROSETTE = 15969;
  CHANNEL_INT32_STRAIN_GAGE_ROSETTE_TYPE_TEE_ROSETTE = 15970;
  CHANNEL_INT32_STRAIN_UNITS1_STRAIN = 10299;
  CHANNEL_INT32_STRAIN_UNITS1_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_SYNC_UNLOCK_BEHAVIOR_STOP_TASK_AND_ERROR = 15862;
  CHANNEL_INT32_SYNC_UNLOCK_BEHAVIOR_IGNORE_LOST_SYNC_LOCK = 16129;
  CHANNEL_INT32_TEMPERATURE_UNITS1_DEG_C = 10143;
  CHANNEL_INT32_TEMPERATURE_UNITS1_DEG_F = 10144;
  CHANNEL_INT32_TEMPERATURE_UNITS1_KELVINS = 10325;
  CHANNEL_INT32_TEMPERATURE_UNITS1_DEG_R = 10145;
  CHANNEL_INT32_TEMPERATURE_UNITS1_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_THERMOCOUPLE_TYPE1_J_TYPE_TC = 10072;
  CHANNEL_INT32_THERMOCOUPLE_TYPE1_K_TYPE_TC = 10073;
  CHANNEL_INT32_THERMOCOUPLE_TYPE1_N_TYPE_TC = 10077;
  CHANNEL_INT32_THERMOCOUPLE_TYPE1_R_TYPE_TC = 10082;
  CHANNEL_INT32_THERMOCOUPLE_TYPE1_S_TYPE_TC = 10085;
  CHANNEL_INT32_THERMOCOUPLE_TYPE1_T_TYPE_TC = 10086;
  CHANNEL_INT32_THERMOCOUPLE_TYPE1_B_TYPE_TC = 10047;
  CHANNEL_INT32_THERMOCOUPLE_TYPE1_E_TYPE_TC = 10055;
  CHANNEL_INT32_TIME_UNITS_SECONDS = 10364;
  CHANNEL_INT32_TIME_UNITS_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_TIME_UNITS2_SECONDS = 10364;
  CHANNEL_INT32_TIME_UNITS3_SECONDS = 10364;
  CHANNEL_INT32_TIME_UNITS3_TICKS = 10304;
  CHANNEL_INT32_TIME_UNITS3_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_TIMESCALE_TAI = 15988;
  CHANNEL_INT32_TIMESCALE_UTC = 15987;
  CHANNEL_INT32_TORQUE_UNITS_NEWTON_METERS = 15881;
  CHANNEL_INT32_TORQUE_UNITS_INCH_OUNCES = 15882;
  CHANNEL_INT32_TORQUE_UNITS_INCH_POUNDS = 15883;
  CHANNEL_INT32_TORQUE_UNITS_FOOT_POUNDS = 15884;
  CHANNEL_INT32_TORQUE_UNITS_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_VELOCITY_IEPE_SENSOR_SENSITIVITY_UNITS_MILLIVOLTS_PER_MILLIMETER_PER_SECOND = 15963;
  CHANNEL_INT32_VELOCITY_IEPE_SENSOR_SENSITIVITY_UNITS_MILLI_VOLTS_PER_INCH_PER_SECOND = 15964;
  CHANNEL_INT32_VELOCITY_UNITS_METERS_PER_SECOND = 15959;
  CHANNEL_INT32_VELOCITY_UNITS_INCHES_PER_SECOND = 15960;
  CHANNEL_INT32_VELOCITY_UNITS_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_VELOCITY_UNITS2_METERS_PER_SECOND = 15959;
  CHANNEL_INT32_VELOCITY_UNITS2_KILOMETERS_PER_HOUR = 16007;
  CHANNEL_INT32_VELOCITY_UNITS2_FEET_PER_SECOND = 16008;
  CHANNEL_INT32_VELOCITY_UNITS2_MILES_PER_HOUR = 16009;
  CHANNEL_INT32_VELOCITY_UNITS2_KNOTS = 16010;
  CHANNEL_INT32_VELOCITY_UNITS2_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_VOLTAGE_UNITS1_VOLTS = 10348;
  CHANNEL_INT32_VOLTAGE_UNITS1_FROM_CUSTOM_SCALE = 10065;
  CHANNEL_INT32_VOLTAGE_UNITS1_FROM_TEDS = 12516;
  CHANNEL_INT32_VOLTAGE_UNITS2_VOLTS = 10348;
  CHANNEL_INT32_VOLTAGE_UNITS2_FROM_CUSTOM_SCALE = 10065;
}

enum DeviceInt32AttributeValues {
  option allow_alias = true;
  DEVICE_INT32_UNSPECIFIED = 0;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_VOLTAGE = 10322;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_VOLTAGE_RMS = 10350;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_CURRENT = 10134;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_CURRENT_RMS = 10351;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_VOLTAGE_CUSTOM_WITH_EXCITATION = 10323;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_BRIDGE = 15908;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_FREQ_VOLTAGE = 10181;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_RESISTANCE = 10278;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_TEMP_TC = 10303;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_TEMP_THRMSTR = 10302;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_TEMP_RTD = 10301;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_TEMP_BUILT_IN_SENSOR = 10311;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_STRAIN_GAGE = 10300;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_ROSETTE_STRAIN_GAGE = 15980;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_POSITION_LVDT = 10352;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_POSITION_RVDT = 10353;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_POSITION_EDDY_CURRENT_PROXIMITY_PROBE = 14835;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_ACCELEROMETER = 10356;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_ACCELERATION_CHARGE = 16104;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_ACCELERATION_4_WIRE_DC_VOLTAGE = 16106;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_VELOCITY_IEPE_SENSOR = 15966;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_FORCE_BRIDGE = 15899;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_FORCE_IEPE_SENSOR = 15895;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_PRESSURE_BRIDGE = 15902;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_SOUND_PRESSURE_MICROPHONE = 10354;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_TORQUE_BRIDGE = 15905;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_TEDS_SENSOR = 12531;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_CHARGE = 16105;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_POWER = 16201;
  DEVICE_INT32_AI_MEASUREMENT_TYPE_CALCULATED_POWER = 16204;
  DEVICE_INT32_AO_OUTPUT_CHANNEL_TYPE_VOLTAGE = 10322;
  DEVICE_INT32_AO_OUTPUT_CHANNEL_TYPE_CURRENT = 10134;
  DEVICE_INT32_AO_OUTPUT_CHANNEL_TYPE_FUNC_GEN = 14750;
  DEVICE_INT32_ACQUISITION_TYPE_FINITE_SAMPS = 10178;
  DEVICE_INT32_ACQUISITION_TYPE_CONT_SAMPS = 10123;
  DEVICE_INT32_ACQUISITION_TYPE_HW_TIMED_SINGLE_POINT = 12522;
  DEVICE_INT32_ALT_REF_MSL = 16005;
  DEVICE_INT32_ALT_REF_HAE = 16006;
  DEVICE_INT32_ANT_STATUS_UNKNOWN = 12588;
  DEVICE_INT32_ANT_STATUS_NORMAL = 10459;
  DEVICE_INT32_ANT_STATUS_ABSENT = 15994;
  DEVICE_INT32_ANT_STATUS_OVERCURRENT = 15995;
  DEVICE_INT32_BUS_TYPE_PCI = 12582;
  DEVICE_INT32_BUS_TYPE_PCIE = 13612;
  DEVICE_INT32_BUS_TYPE_PXI = 12583;
  DEVICE_INT32_BUS_TYPE_PXIE = 14706;
  DEVICE_INT32_BUS_TYPE_SCXI = 12584;
  DEVICE_INT32_BUS_TYPE_SCC = 14707;
  DEVICE_INT32_BUS_TYPE_PC_CARD = 12585;
  DEVICE_INT32_BUS_TYPE_USB = 12586;
  DEVICE_INT32_BUS_TYPE_COMPACT_DAQ = 14637;
  DEVICE_INT32_BUS_TYPE_COMPACT_RIO = 16143;
  DEVICE_INT32_BUS_TYPE_TCPIP = 14828;
  DEVICE_INT32_BUS_TYPE_UNKNOWN = 12588;
  DEVICE_INT32_BUS_TYPE_SWITCH_BLOCK = 15870;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_COUNT_EDGES = 10125;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_FREQ = 10179;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_PERIOD = 10256;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_PULSE_WIDTH = 10359;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_SEMI_PERIOD = 10289;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_PULSE_FREQUENCY = 15864;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_PULSE_TIME = 15865;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_PULSE_TICKS = 15866;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_DUTY_CYCLE = 16070;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_POSITION_ANG_ENCODER = 10360;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_POSITION_LIN_ENCODER = 10361;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_VELOCITY_ANG_ENCODER = 16078;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_VELOCITY_LIN_ENCODER = 16079;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_TWO_EDGE_SEP = 10267;
  DEVICE_INT32_CI_MEASUREMENT_TYPE_GPS_TIMESTAMP = 10362;
  DEVICE_INT32_CO_OUTPUT_TYPE_PULSE_TIME = 10269;
  DEVICE_INT32_CO_OUTPUT_TYPE_PULSE_FREQ = 10119;
  DEVICE_INT32_CO_OUTPUT_TYPE_PULSE_TICKS = 10268;
  DEVICE_INT32_COUPLING_TYPES_AC = 1;
  DEVICE_INT32_COUPLING_TYPES_DC = 2;
  DEVICE_INT32_COUPLING_TYPES_GROUND = 4;
  DEVICE_INT32_COUPLING_TYPES_HF_REJECT = 8;
  DEVICE_INT32_COUPLING_TYPES_LF_REJECT = 16;
  DEVICE_INT32_COUPLING_TYPES_NOISE_REJECT = 32;
  DEVICE_INT32_FILTER_TYPE2_LOWPASS = 16071;
  DEVICE_INT32_FILTER_TYPE2_HIGHPASS = 16072;
  DEVICE_INT32_FILTER_TYPE2_BANDPASS = 16073;
  DEVICE_INT32_FILTER_TYPE2_NOTCH = 16074;
  DEVICE_INT32_FILTER_TYPE2_CUSTOM = 10137;
  DEVICE_INT32_ID_PIN_STATUS_MEMORY_NOT_PRESENT = 16205;
  DEVICE_INT32_ID_PIN_STATUS_MEMORY_PRESENT = 16206;
  DEVICE_INT32_NAV_MEASUREMENT_TYPE_ALTITUDE = 15997;
  DEVICE_INT32_NAV_MEASUREMENT_TYPE_LONGITUDE = 15998;
  DEVICE_INT32_NAV_MEASUREMENT_TYPE_LATITUDE = 15999;
  DEVICE_INT32_NAV_MEASUREMENT_TYPE_SPEED_OVER_GROUND = 16000;
  DEVICE_INT32_NAV_MEASUREMENT_TYPE_TRACK = 16001;
  DEVICE_INT32_NAV_MEASUREMENT_TYPE_TIMESTAMP = 15986;
  DEVICE_INT32_NAV_MEASUREMENT_TYPE_VERT_VELOCITY = 16003;
  DEVICE_INT32_NAV_MODE_MOBILE = 15989;
  DEVICE_INT32_NAV_MODE_STATIONARY_WITH_SURVEY = 15990;
  DEVICE_INT32_NAV_MODE_STATIONARY_WITH_PRESET_LOCATION = 15991;
  DEVICE_INT32_PRODUCT_CATEGORY_M_SERIES_DAQ = 14643;
  DEVICE_INT32_PRODUCT_CATEGORY_X_SERIES_DAQ = 15858;
  DEVICE_INT32_PRODUCT_CATEGORY_E_SERIES_DAQ = 14642;
  DEVICE_INT32_PRODUCT_CATEGORY_S_SERIES_DAQ = 14644;
  DEVICE_INT32_PRODUCT_CATEGORY_B_SERIES_DAQ = 14662;
  DEVICE_INT32_PRODUCT_CATEGORY_SC_SERIES_DAQ = 14645;
  DEVICE_INT32_PRODUCT_CATEGORY_USBDAQ = 14646;
  DEVICE_INT32_PRODUCT_CATEGORY_AO_SERIES = 14647;
  DEVICE_INT32_PRODUCT_CATEGORY_DIGITAL_IO = 14648;
  DEVICE_INT32_PRODUCT_CATEGORY_TIO_SERIES = 14661;
  DEVICE_INT32_PRODUCT_CATEGORY_DYNAMIC_SIGNAL_ACQUISITION = 14649;
  DEVICE_INT32_PRODUCT_CATEGORY_SWITCHES = 14650;
  DEVICE_INT32_PRODUCT_CATEGORY_COMPACT_DAQ_CHASSIS = 14658;
  DEVICE_INT32_PRODUCT_CATEGORY_COMPACT_RIO_CHASSIS = 16144;
  DEVICE_INT32_PRODUCT_CATEGORY_C_SERIES_MODULE = 14659;
  DEVICE_INT32_PRODUCT_CATEGORY_SCXI_MODULE = 14660;
  DEVICE_INT32_PRODUCT_CATEGORY_SCC_CONNECTOR_BLOCK = 14704;
  DEVICE_INT32_PRODUCT_CATEGORY_SCC_MODULE = 14705;
  DEVICE_INT32_PRODUCT_CATEGORY_NIELVIS = 14755;
  DEVICE_INT32_PRODUCT_CATEGORY_NETWORK_DAQ = 14829;
  DEVICE_INT32_PRODUCT_CATEGORY_SC_EXPRESS = 15886;
  DEVICE_INT32_PRODUCT_CATEGORY_FIELD_DAQ = 16151;
  DEVICE_INT32_PRODUCT_CATEGORY_TEST_SCALE_CHASSIS = 16180;
  DEVICE_INT32_PRODUCT_CATEGORY_TEST_SCALE_MODULE = 16181;
  DEVICE_INT32_PRODUCT_CATEGORY_MIO_DAQ = 16182;
  DEVICE_INT32_PRODUCT_CATEGORY_UNKNOWN = 12588;
  DEVICE_INT32_TRIGGER_USAGE_ADVANCE = 12488;
  DEVICE_INT32_TRIGGER_USAGE_PAUSE = 12489;
  DEVICE_INT32_TRIGGER_USAGE_REFERENCE = 12490;
  DEVICE_INT32_TRIGGER_USAGE_START = 12491;
  DEVICE_INT32_TRIGGER_USAGE_HANDSHAKE = 10389;
  DEVICE_INT32_TRIGGER_USAGE_ARM_START = 14641;
  DEVICE_INT32_TRIGGER_USAGE_TYPES_ADVANCE = 1;
  DEVICE_INT32_TRIGGER_USAGE_TYPES_PAUSE = 2;
  DEVICE_INT32_TRIGGER_USAGE_TYPES_REFERENCE = 4;
  DEVICE_INT32_TRIGGER_USAGE_TYPES_START = 8;
  DEVICE_INT32_TRIGGER_USAGE_TYPES_HANDSHAKE = 16;
  DEVICE_INT32_TRIGGER_USAGE_TYPES_ARM_START = 32;
}

enum ExportSignalInt32AttributeValues {
  option allow_alias = true;
  EXPORTSIGNAL_INT32_UNSPECIFIED = 0;
  EXPORTSIGNAL_INT32_DEASSERT_CONDITION_ONBRD_MEM_MORE_THAN_HALF_FULL = 10237;
  EXPORTSIGNAL_INT32_DEASSERT_CONDITION_ONBRD_MEM_FULL = 10236;
  EXPORTSIGNAL_INT32_DEASSERT_CONDITION_ONBRD_MEM_CUSTOM_THRESHOLD = 12577;
  EXPORTSIGNAL_INT32_DIGITAL_WIDTH_UNITS1_SAMP_CLK_PERIODS = 10286;
  EXPORTSIGNAL_INT32_DIGITAL_WIDTH_UNITS1_SECONDS = 10364;
  EXPORTSIGNAL_INT32_DIGITAL_WIDTH_UNITS1_TICKS = 10304;
  EXPORTSIGNAL_INT32_DIGITAL_WIDTH_UNITS3_SECONDS = 10364;
  EXPORTSIGNAL_INT32_EXPORT_ACTIONS_PULSE = 10265;
  EXPORTSIGNAL_INT32_EXPORT_ACTIONS_TOGGLE = 10307;
  EXPORTSIGNAL_INT32_EXPORT_ACTIONS_LVL = 10210;
  EXPORTSIGNAL_INT32_EXPORT_ACTIONS2_PULSE = 10265;
  EXPORTSIGNAL_INT32_EXPORT_ACTIONS2_TOGGLE = 10307;
  EXPORTSIGNAL_INT32_EXPORT_ACTIONS3_PULSE = 10265;
  EXPORTSIGNAL_INT32_EXPORT_ACTIONS3_LVL = 10210;
  EXPORTSIGNAL_INT32_EXPORT_ACTIONS5_INTERLOCKED = 12549;
  EXPORTSIGNAL_INT32_EXPORT_ACTIONS5_PULSE = 10265;
  EXPORTSIGNAL_INT32_LEVEL1_HIGH = 10192;
  EXPORTSIGNAL_INT32_LEVEL1_LOW = 10214;
  EXPORTSIGNAL_INT32_POLARITY2_ACTIVE_HIGH = 10095;
  EXPORTSIGNAL_INT32_POLARITY2_ACTIVE_LOW = 10096;
}

enum PhysicalChannelInt32AttributeValues {
  option allow_alias = true;
  PHYSICALCHANNEL_INT32_UNSPECIFIED = 0;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_VOLTAGE = 10322;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_VOLTAGE_RMS = 10350;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_CURRENT = 10134;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_CURRENT_RMS = 10351;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_VOLTAGE_CUSTOM_WITH_EXCITATION = 10323;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_BRIDGE = 15908;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_FREQ_VOLTAGE = 10181;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_RESISTANCE = 10278;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_TEMP_TC = 10303;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_TEMP_THRMSTR = 10302;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_TEMP_RTD = 10301;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_TEMP_BUILT_IN_SENSOR = 10311;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_STRAIN_GAGE = 10300;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_ROSETTE_STRAIN_GAGE = 15980;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_POSITION_LVDT = 10352;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_POSITION_RVDT = 10353;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_POSITION_EDDY_CURRENT_PROXIMITY_PROBE = 14835;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_ACCELEROMETER = 10356;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_ACCELERATION_CHARGE = 16104;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_ACCELERATION_4_WIRE_DC_VOLTAGE = 16106;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_VELOCITY_IEPE_SENSOR = 15966;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_FORCE_BRIDGE = 15899;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_FORCE_IEPE_SENSOR = 15895;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_PRESSURE_BRIDGE = 15902;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_SOUND_PRESSURE_MICROPHONE = 10354;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_TORQUE_BRIDGE = 15905;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_TEDS_SENSOR = 12531;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_CHARGE = 16105;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_POWER = 16201;
  PHYSICALCHANNEL_INT32_AI_MEASUREMENT_TYPE_CALCULATED_POWER = 16204;
  PHYSICALCHANNEL_INT32_AO_OUTPUT_CHANNEL_TYPE_VOLTAGE = 10322;
  PHYSICALCHANNEL_INT32_AO_OUTPUT_CHANNEL_TYPE_CURRENT = 10134;
  PHYSICALCHANNEL_INT32_AO_OUTPUT_CHANNEL_TYPE_FUNC_GEN = 14750;
  PHYSICALCHANNEL_INT32_AO_POWER_UP_OUTPUT_BEHAVIOR_VOLTAGE = 10322;
  PHYSICALCHANNEL_INT32_AO_POWER_UP_OUTPUT_BEHAVIOR_CURRENT = 10134;
  PHYSICALCHANNEL_INT32_AO_POWER_UP_OUTPUT_BEHAVIOR_HIGH_IMPEDANCE = 12527;
  PHYSICALCHANNEL_INT32_ACQUISITION_TYPE_FINITE_SAMPS = 10178;
  PHYSICALCHANNEL_INT32_ACQUISITION_TYPE_CONT_SAMPS = 10123;
  PHYSICALCHANNEL_INT32_ACQUISITION_TYPE_HW_TIMED_SINGLE_POINT = 12522;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_COUNT_EDGES = 10125;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_FREQ = 10179;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_PERIOD = 10256;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_PULSE_WIDTH = 10359;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_SEMI_PERIOD = 10289;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_PULSE_FREQUENCY = 15864;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_PULSE_TIME = 15865;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_PULSE_TICKS = 15866;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_DUTY_CYCLE = 16070;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_POSITION_ANG_ENCODER = 10360;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_POSITION_LIN_ENCODER = 10361;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_VELOCITY_ANG_ENCODER = 16078;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_VELOCITY_LIN_ENCODER = 16079;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_TWO_EDGE_SEP = 10267;
  PHYSICALCHANNEL_INT32_CI_MEASUREMENT_TYPE_GPS_TIMESTAMP = 10362;
  PHYSICALCHANNEL_INT32_CO_OUTPUT_TYPE_PULSE_TIME = 10269;
  PHYSICALCHANNEL_INT32_CO_OUTPUT_TYPE_PULSE_FREQ = 10119;
  PHYSICALCHANNEL_INT32_CO_OUTPUT_TYPE_PULSE_TICKS = 10268;
  PHYSICALCHANNEL_INT32_LOGIC_FAMILY_1POINT_8_V = 16184;
  PHYSICALCHANNEL_INT32_LOGIC_FAMILY_2POINT_5_V = 14620;
  PHYSICALCHANNEL_INT32_LOGIC_FAMILY_3POINT_3_V = 14621;
  PHYSICALCHANNEL_INT32_LOGIC_FAMILY_5_V = 14619;
  PHYSICALCHANNEL_INT32_NAV_MEASUREMENT_TYPE_ALTITUDE = 15997;
  PHYSICALCHANNEL_INT32_NAV_MEASUREMENT_TYPE_LONGITUDE = 15998;
  PHYSICALCHANNEL_INT32_NAV_MEASUREMENT_TYPE_LATITUDE = 15999;
  PHYSICALCHANNEL_INT32_NAV_MEASUREMENT_TYPE_SPEED_OVER_GROUND = 16000;
  PHYSICALCHANNEL_INT32_NAV_MEASUREMENT_TYPE_TRACK = 16001;
  PHYSICALCHANNEL_INT32_NAV_MEASUREMENT_TYPE_TIMESTAMP = 15986;
  PHYSICALCHANNEL_INT32_NAV_MEASUREMENT_TYPE_VERT_VELOCITY = 16003;
  PHYSICALCHANNEL_INT32_SENSOR_POWER_TYPE_DC = 10050;
  PHYSICALCHANNEL_INT32_SENSOR_POWER_TYPE_AC = 10045;
  PHYSICALCHANNEL_INT32_SENSOR_POWER_TYPE_BIPOLAR_DC = 16147;
  PHYSICALCHANNEL_INT32_TERM_CFG_RSE = 1;
  PHYSICALCHANNEL_INT32_TERM_CFG_NRSE = 2;
  PHYSICALCHANNEL_INT32_TERM_CFG_DIFF = 4;
  PHYSICALCHANNEL_INT32_TERM_CFG_PSEUDO_DIFF = 8;
}

enum ReadInt32AttributeValues {
  READ_INT32_UNSPECIFIED = 0;
  READ_INT32_LOGGING_MODE_OFF = 10231;
  READ_INT32_LOGGING_MODE_LOG = 15844;
  READ_INT32_LOGGING_MODE_LOG_AND_READ = 15842;
  READ_INT32_LOGGING_OPERATION_OPEN = 10437;
  READ_INT32_LOGGING_OPERATION_OPEN_OR_CREATE = 15846;
  READ_INT32_LOGGING_OPERATION_CREATE_OR_REPLACE = 15847;
  READ_INT32_LOGGING_OPERATION_CREATE = 15848;
  READ_INT32_OVERWRITE_MODE1_OVERWRITE_UNREAD_SAMPS = 10252;
  READ_INT32_OVERWRITE_MODE1_DO_NOT_OVERWRITE_UNREAD_SAMPS = 10159;
  READ_INT32_READ_RELATIVE_TO_FIRST_SAMPLE = 10424;
  READ_INT32_READ_RELATIVE_TO_CURR_READ_POS = 10425;
  READ_INT32_READ_RELATIVE_TO_REF_TRIG = 10426;
  READ_INT32_READ_RELATIVE_TO_FIRST_PRETRIG_SAMP = 10427;
  READ_INT32_READ_RELATIVE_TO_MOST_RECENT_SAMP = 10428;
  READ_INT32_WAIT_MODE_WAIT_FOR_INTERRUPT = 12523;
  READ_INT32_WAIT_MODE_POLL = 12524;
  READ_INT32_WAIT_MODE_YIELD = 12525;
  READ_INT32_WAIT_MODE_SLEEP = 12547;
}

enum RealTimeInt32AttributeValues {
  option allow_alias = true;
  REALTIME_INT32_UNSPECIFIED = 0;
  REALTIME_INT32_WAIT_MODE3_WAIT_FOR_INTERRUPT = 12523;
  REALTIME_INT32_WAIT_MODE3_POLL = 12524;
  REALTIME_INT32_WAIT_MODE4_WAIT_FOR_INTERRUPT = 12523;
  REALTIME_INT32_WAIT_MODE4_POLL = 12524;
}

enum ScaleInt32AttributeValues {
  SCALE_INT32_UNSPECIFIED = 0;
  SCALE_INT32_SCALE_TYPE_LINEAR = 10447;
  SCALE_INT32_SCALE_TYPE_MAP_RANGES = 10448;
  SCALE_INT32_SCALE_TYPE_POLYNOMIAL = 10449;
  SCALE_INT32_SCALE_TYPE_TABLE = 10450;
  SCALE_INT32_UNITS_PRE_SCALED_VOLTS = 10348;
  SCALE_INT32_UNITS_PRE_SCALED_AMPS = 10342;
  SCALE_INT32_UNITS_PRE_SCALED_DEG_F = 10144;
  SCALE_INT32_UNITS_PRE_SCALED_DEG_C = 10143;
  SCALE_INT32_UNITS_PRE_SCALED_DEG_R = 10145;
  SCALE_INT32_UNITS_PRE_SCALED_KELVINS = 10325;
  SCALE_INT32_UNITS_PRE_SCALED_STRAIN = 10299;
  SCALE_INT32_UNITS_PRE_SCALED_OHMS = 10384;
  SCALE_INT32_UNITS_PRE_SCALED_HZ = 10373;
  SCALE_INT32_UNITS_PRE_SCALED_SECONDS = 10364;
  SCALE_INT32_UNITS_PRE_SCALED_METERS = 10219;
  SCALE_INT32_UNITS_PRE_SCALED_INCHES = 10379;
  SCALE_INT32_UNITS_PRE_SCALED_DEGREES = 10146;
  SCALE_INT32_UNITS_PRE_SCALED_RADIANS = 10273;
  SCALE_INT32_UNITS_PRE_SCALED_TICKS = 10304;
  SCALE_INT32_UNITS_PRE_SCALED_RPM = 16080;
  SCALE_INT32_UNITS_PRE_SCALED_RADIANS_PER_SECOND = 16081;
  SCALE_INT32_UNITS_PRE_SCALED_DEGREES_PER_SECOND = 16082;
  SCALE_INT32_UNITS_PRE_SCALED_G = 10186;
  SCALE_INT32_UNITS_PRE_SCALED_METERS_PER_SECOND_SQUARED = 12470;
  SCALE_INT32_UNITS_PRE_SCALED_INCHES_PER_SECOND_SQUARED = 12471;
  SCALE_INT32_UNITS_PRE_SCALED_METERS_PER_SECOND = 15959;
  SCALE_INT32_UNITS_PRE_SCALED_INCHES_PER_SECOND = 15960;
  SCALE_INT32_UNITS_PRE_SCALED_PASCALS = 10081;
  SCALE_INT32_UNITS_PRE_SCALED_NEWTONS = 15875;
  SCALE_INT32_UNITS_PRE_SCALED_POUNDS = 15876;
  SCALE_INT32_UNITS_PRE_SCALED_KILOGRAM_FORCE = 15877;
  SCALE_INT32_UNITS_PRE_SCALED_POUNDS_PER_SQUARE_INCH = 15879;
  SCALE_INT32_UNITS_PRE_SCALED_BAR = 15880;
  SCALE_INT32_UNITS_PRE_SCALED_NEWTON_METERS = 15881;
  SCALE_INT32_UNITS_PRE_SCALED_INCH_OUNCES = 15882;
  SCALE_INT32_UNITS_PRE_SCALED_INCH_POUNDS = 15883;
  SCALE_INT32_UNITS_PRE_SCALED_FOOT_POUNDS = 15884;
  SCALE_INT32_UNITS_PRE_SCALED_VOLTS_PER_VOLT = 15896;
  SCALE_INT32_UNITS_PRE_SCALED_M_VOLTS_PER_VOLT = 15897;
  SCALE_INT32_UNITS_PRE_SCALED_COULOMBS = 16102;
  SCALE_INT32_UNITS_PRE_SCALED_PICO_COULOMBS = 16103;
  SCALE_INT32_UNITS_PRE_SCALED_FROM_TEDS = 12516;
}

enum TimingInt32AttributeValues {
  TIMING_INT32_UNSPECIFIED = 0;
  TIMING_INT32_ACQUISITION_TYPE_FINITE_SAMPS = 10178;
  TIMING_INT32_ACQUISITION_TYPE_CONT_SAMPS = 10123;
  TIMING_INT32_ACQUISITION_TYPE_HW_TIMED_SINGLE_POINT = 12522;
  TIMING_INT32_DIGITAL_WIDTH_UNITS2_SECONDS = 10364;
  TIMING_INT32_DIGITAL_WIDTH_UNITS2_TICKS = 10304;
  TIMING_INT32_EDGE1_RISING = 10280;
  TIMING_INT32_EDGE1_FALLING = 10171;
  TIMING_INT32_HANDSHAKE_START_CONDITION_IMMEDIATE = 10198;
  TIMING_INT32_HANDSHAKE_START_CONDITION_WAIT_FOR_HANDSHAKE_TRIGGER_ASSERT = 12550;
  TIMING_INT32_HANDSHAKE_START_CONDITION_WAIT_FOR_HANDSHAKE_TRIGGER_DEASSERT = 12551;
  TIMING_INT32_MIOAI_CONVERT_TB_SRC_SAME_AS_SAMP_TIMEBASE = 10284;
  TIMING_INT32_MIOAI_CONVERT_TB_SRC_SAME_AS_MASTER_TIMEBASE = 10282;
  TIMING_INT32_MIOAI_CONVERT_TB_SRC_100_MHZ_TIMEBASE = 15857;
  TIMING_INT32_MIOAI_CONVERT_TB_SRC_80_MHZ_TIMEBASE = 14636;
  TIMING_INT32_MIOAI_CONVERT_TB_SRC_20_MHZ_TIMEBASE = 12537;
  TIMING_INT32_MIOAI_CONVERT_TB_SRC_8_MHZ_TIMEBASE = 16023;
  TIMING_INT32_OVERFLOW_BEHAVIOR_STOP_TASK_AND_ERROR = 15862;
  TIMING_INT32_OVERFLOW_BEHAVIOR_IGNORE_OVERRUNS = 15863;
  TIMING_INT32_SAMPLE_INPUT_DATA_WHEN_HANDSHAKE_TRIGGER_ASSERTS = 12552;
  TIMING_INT32_SAMPLE_INPUT_DATA_WHEN_HANDSHAKE_TRIGGER_DEASSERTS = 12553;
  TIMING_INT32_SAMPLE_TIMING_TYPE_SAMP_CLK = 10388;
  TIMING_INT32_SAMPLE_TIMING_TYPE_BURST_HANDSHAKE = 12548;
  TIMING_INT32_SAMPLE_TIMING_TYPE_HANDSHAKE = 10389;
  TIMING_INT32_SAMPLE_TIMING_TYPE_IMPLICIT = 10451;
  TIMING_INT32_SAMPLE_TIMING_TYPE_ON_DEMAND = 10390;
  TIMING_INT32_SAMPLE_TIMING_TYPE_CHANGE_DETECTION = 12504;
  TIMING_INT32_SAMPLE_TIMING_TYPE_PIPELINED_SAMP_CLK = 14668;
  TIMING_INT32_SYNC_PULSE_TYPE_ONBOARD = 16128;
  TIMING_INT32_SYNC_PULSE_TYPE_DIG_EDGE = 10150;
  TIMING_INT32_SYNC_PULSE_TYPE_TIME = 15996;
  TIMING_INT32_TIMESCALE2_HOST_TIME = 16126;
  TIMING_INT32_TIMESCALE2_IO_DEVICE_TIME = 16127;
  TIMING_INT32_UNDERFLOW_BEHAVIOR_HALT_OUTPUT_AND_ERROR = 14615;
  TIMING_INT32_UNDERFLOW_BEHAVIOR_PAUSE_UNTIL_DATA_AVAILABLE = 14616;
}

enum TriggerInt32AttributeValues {
  option allow_alias = true;
  TRIGGER_INT32_UNSPECIFIED = 0;
  TRIGGER_INT32_ACTIVE_LEVEL_ABOVE_LVL = 10093;
  TRIGGER_INT32_ACTIVE_LEVEL_BELOW_LVL = 10107;
  TRIGGER_INT32_COUPLING2_AC = 10045;
  TRIGGER_INT32_COUPLING2_DC = 10050;
  TRIGGER_INT32_DIGITAL_PATTERN_CONDITION1_PATTERN_MATCHES = 10254;
  TRIGGER_INT32_DIGITAL_PATTERN_CONDITION1_PATTERN_DOES_NOT_MATCH = 10253;
  TRIGGER_INT32_DIGITAL_WIDTH_UNITS1_SAMP_CLK_PERIODS = 10286;
  TRIGGER_INT32_DIGITAL_WIDTH_UNITS1_SECONDS = 10364;
  TRIGGER_INT32_DIGITAL_WIDTH_UNITS1_TICKS = 10304;
  TRIGGER_INT32_EDGE1_RISING = 10280;
  TRIGGER_INT32_EDGE1_FALLING = 10171;
  TRIGGER_INT32_LEVEL1_HIGH = 10192;
  TRIGGER_INT32_LEVEL1_LOW = 10214;
  TRIGGER_INT32_SLOPE1_RISING_SLOPE = 10280;
  TRIGGER_INT32_SLOPE1_FALLING_SLOPE = 10171;
  TRIGGER_INT32_SYNC_TYPE_NONE = 10230;
  TRIGGER_INT32_SYNC_TYPE_MASTER = 15888;
  TRIGGER_INT32_SYNC_TYPE_SLAVE = 15889;
  TRIGGER_INT32_TIMESCALE2_HOST_TIME = 16126;
  TRIGGER_INT32_TIMESCALE2_IO_DEVICE_TIME = 16127;
  TRIGGER_INT32_TRIGGER_TYPE10_ANLG_EDGE = 10099;
  TRIGGER_INT32_TRIGGER_TYPE10_ANLG_MULTI_EDGE = 16108;
  TRIGGER_INT32_TRIGGER_TYPE10_DIG_EDGE = 10150;
  TRIGGER_INT32_TRIGGER_TYPE10_DIG_PATTERN = 10398;
  TRIGGER_INT32_TRIGGER_TYPE10_ANLG_WIN = 10103;
  TRIGGER_INT32_TRIGGER_TYPE10_TIME = 15996;
  TRIGGER_INT32_TRIGGER_TYPE10_NONE = 10230;
  TRIGGER_INT32_TRIGGER_TYPE4_DIG_EDGE = 10150;
  TRIGGER_INT32_TRIGGER_TYPE4_TIME = 15996;
  TRIGGER_INT32_TRIGGER_TYPE4_NONE = 10230;
  TRIGGER_INT32_TRIGGER_TYPE5_DIG_EDGE = 10150;
  TRIGGER_INT32_TRIGGER_TYPE5_SOFTWARE = 10292;
  TRIGGER_INT32_TRIGGER_TYPE5_NONE = 10230;
  TRIGGER_INT32_TRIGGER_TYPE6_ANLG_LVL = 10101;
  TRIGGER_INT32_TRIGGER_TYPE6_ANLG_WIN = 10103;
  TRIGGER_INT32_TRIGGER_TYPE6_DIG_LVL = 10152;
  TRIGGER_INT32_TRIGGER_TYPE6_DIG_PATTERN = 10398;
  TRIGGER_INT32_TRIGGER_TYPE6_NONE = 10230;
  TRIGGER_INT32_TRIGGER_TYPE8_ANLG_EDGE = 10099;
  TRIGGER_INT32_TRIGGER_TYPE8_ANLG_MULTI_EDGE = 16108;
  TRIGGER_INT32_TRIGGER_TYPE8_DIG_EDGE = 10150;
  TRIGGER_INT32_TRIGGER_TYPE8_DIG_PATTERN = 10398;
  TRIGGER_INT32_TRIGGER_TYPE8_ANLG_WIN = 10103;
  TRIGGER_INT32_TRIGGER_TYPE8_TIME = 15996;
  TRIGGER_INT32_TRIGGER_TYPE8_NONE = 10230;
  TRIGGER_INT32_TRIGGER_TYPE9_INTERLOCKED = 12549;
  TRIGGER_INT32_TRIGGER_TYPE9_NONE = 10230;
  TRIGGER_INT32_WINDOW_TRIGGER_CONDITION1_ENTERING_WIN = 10163;
  TRIGGER_INT32_WINDOW_TRIGGER_CONDITION1_LEAVING_WIN = 10208;
  TRIGGER_INT32_WINDOW_TRIGGER_CONDITION2_INSIDE_WIN = 10199;
  TRIGGER_INT32_WINDOW_TRIGGER_CONDITION2_OUTSIDE_WIN = 10251;
}

enum WatchdogInt32AttributeValues {
  option allow_alias = true;
  WATCHDOG_INT32_UNSPECIFIED = 0;
  WATCHDOG_INT32_DIGITAL_LINE_STATE_HIGH = 10192;
  WATCHDOG_INT32_DIGITAL_LINE_STATE_LOW = 10214;
  WATCHDOG_INT32_DIGITAL_LINE_STATE_TRISTATE = 10310;
  WATCHDOG_INT32_DIGITAL_LINE_STATE_NO_CHANGE = 10160;
  WATCHDOG_INT32_EDGE1_RISING = 10280;
  WATCHDOG_INT32_EDGE1_FALLING = 10171;
  WATCHDOG_INT32_TRIGGER_TYPE4_DIG_EDGE = 10150;
  WATCHDOG_INT32_TRIGGER_TYPE4_TIME = 15996;
  WATCHDOG_INT32_TRIGGER_TYPE4_NONE = 10230;
  WATCHDOG_INT32_WATCHDOG_AO_EXPIR_STATE_VOLTAGE = 10322;
  WATCHDOG_INT32_WATCHDOG_AO_EXPIR_STATE_CURRENT = 10134;
  WATCHDOG_INT32_WATCHDOG_AO_EXPIR_STATE_NO_CHANGE = 10160;
  WATCHDOG_INT32_WATCHDOG_CO_EXPIR_STATE_LOW = 10214;
  WATCHDOG_INT32_WATCHDOG_CO_EXPIR_STATE_HIGH = 10192;
  WATCHDOG_INT32_WATCHDOG_CO_EXPIR_STATE_NO_CHANGE = 10160;
}

enum WriteInt32AttributeValues {
  WRITE_INT32_UNSPECIFIED = 0;
  WRITE_INT32_REGENERATION_MODE1_ALLOW_REGEN = 10097;
  WRITE_INT32_REGENERATION_MODE1_DO_NOT_ALLOW_REGEN = 10158;
  WRITE_INT32_WAIT_MODE2_POLL = 12524;
  WRITE_INT32_WAIT_MODE2_YIELD = 12525;
  WRITE_INT32_WAIT_MODE2_SLEEP = 12547;
  WRITE_INT32_WRITE_RELATIVE_TO_FIRST_SAMPLE = 10424;
  WRITE_INT32_WRITE_RELATIVE_TO_CURR_WRITE_POS = 10430;
}

message AnalogPowerUpChannelsAndState {
  string channel_names = 1;
  double state = 2;
  PowerUpChannelType channel_type = 3;
}

message WatchdogExpChannelsAndState {
  string lines = 1;
  DigitalLineState exp_state = 2;
}

message DigitalPowerUpTypeAndChannel {
  string channel_name = 1;
  PowerUpStates state = 2;
}

message DigitalPowerUpChannelsAndState {
  string channel_names = 1;
  PowerUpStates state = 2;
}

message DigitalPullUpPullDownChannelsAndState {
  string channel_names = 1;
  ResistorState state = 2;
}

message AnalogPowerUpChannelAndType {
  string channel_name = 1;
  PowerUpChannelType channel_type = 2;
}

message AddCDAQSyncConnectionRequest {
  string port_list = 1;
}

message AddCDAQSyncConnectionResponse {
  int32 status = 1;
}

message AddGlobalChansToTaskRequest {
  nidevice_grpc.Session task = 1;
  string channel_names = 2;
}

message AddGlobalChansToTaskResponse {
  int32 status = 1;
}

message AddNetworkDeviceRequest {
  string ip_address = 1;
  string device_name = 2;
  bool attempt_reservation = 3;
  double timeout = 4;
}

message AddNetworkDeviceResponse {
  int32 status = 1;
  string device_name_out = 2;
}

message AreConfiguredCDAQSyncPortsDisconnectedRequest {
  string chassis_devices_ports = 1;
  double timeout = 2;
}

message AreConfiguredCDAQSyncPortsDisconnectedResponse {
  int32 status = 1;
  bool disconnected_ports_exist = 2;
}

message AutoConfigureCDAQSyncConnectionsRequest {
  string chassis_devices_ports = 1;
  double timeout = 2;
}

message AutoConfigureCDAQSyncConnectionsResponse {
  int32 status = 1;
}

message CalculateReversePolyCoeffRequest {
  repeated double forward_coeffs = 1;
  double min_val_x = 2;
  double max_val_x = 3;
  int32 num_points_to_compute = 4;
  int32 reverse_poly_order = 5;
}

message CalculateReversePolyCoeffResponse {
  int32 status = 1;
  repeated double reverse_coeffs = 2;
}

message CfgAnlgEdgeRefTrigRequest {
  nidevice_grpc.Session task = 1;
  string trigger_source = 2;
  oneof trigger_slope_enum {
    Slope1 trigger_slope = 3;
    int32 trigger_slope_raw = 4;
  }
  double trigger_level = 5;
  uint32 pretrigger_samples = 6;
}

message CfgAnlgEdgeRefTrigResponse {
  int32 status = 1;
}

message CfgAnlgEdgeStartTrigRequest {
  nidevice_grpc.Session task = 1;
  string trigger_source = 2;
  oneof trigger_slope_enum {
    Slope1 trigger_slope = 3;
    int32 trigger_slope_raw = 4;
  }
  double trigger_level = 5;
}

message CfgAnlgEdgeStartTrigResponse {
  int32 status = 1;
}

message CfgAnlgMultiEdgeRefTrigRequest {
  nidevice_grpc.Session task = 1;
  string trigger_sources = 2;
  repeated Slope1 trigger_slope_array = 3;
  repeated double trigger_level_array = 4;
  uint32 pretrigger_samples = 5;
}

message CfgAnlgMultiEdgeRefTrigResponse {
  int32 status = 1;
}

message CfgAnlgMultiEdgeStartTrigRequest {
  nidevice_grpc.Session task = 1;
  string trigger_sources = 2;
  repeated Slope1 trigger_slope_array = 3;
  repeated double trigger_level_array = 4;
}

message CfgAnlgMultiEdgeStartTrigResponse {
  int32 status = 1;
}

message CfgAnlgWindowRefTrigRequest {
  nidevice_grpc.Session task = 1;
  string trigger_source = 2;
  oneof trigger_when_enum {
    WindowTriggerCondition1 trigger_when = 3;
    int32 trigger_when_raw = 4;
  }
  double window_top = 5;
  double window_bottom = 6;
  uint32 pretrigger_samples = 7;
}

message CfgAnlgWindowRefTrigResponse {
  int32 status = 1;
}

message CfgAnlgWindowStartTrigRequest {
  nidevice_grpc.Session task = 1;
  string trigger_source = 2;
  oneof trigger_when_enum {
    WindowTriggerCondition1 trigger_when = 3;
    int32 trigger_when_raw = 4;
  }
  double window_top = 5;
  double window_bottom = 6;
}

message CfgAnlgWindowStartTrigResponse {
  int32 status = 1;
}

message CfgBurstHandshakingTimingExportClockRequest {
  nidevice_grpc.Session task = 1;
  oneof sample_mode_enum {
    AcquisitionType sample_mode = 2;
    int32 sample_mode_raw = 3;
  }
  uint64 samps_per_chan = 4;
  double sample_clk_rate = 5;
  string sample_clk_outp_term = 6;
  oneof sample_clk_pulse_polarity_enum {
    Polarity2 sample_clk_pulse_polarity = 7;
    int32 sample_clk_pulse_polarity_raw = 8;
  }
  oneof pause_when_enum {
    Level1 pause_when = 9;
    int32 pause_when_raw = 10;
  }
  oneof ready_event_active_level_enum {
    Polarity2 ready_event_active_level = 11;
    int32 ready_event_active_level_raw = 12;
  }
}

message CfgBurstHandshakingTimingExportClockResponse {
  int32 status = 1;
}

message CfgBurstHandshakingTimingImportClockRequest {
  nidevice_grpc.Session task = 1;
  oneof sample_mode_enum {
    AcquisitionType sample_mode = 2;
    int32 sample_mode_raw = 3;
  }
  uint64 samps_per_chan = 4;
  double sample_clk_rate = 5;
  string sample_clk_src = 6;
  oneof sample_clk_active_edge_enum {
    Edge1 sample_clk_active_edge = 7;
    int32 sample_clk_active_edge_raw = 8;
  }
  oneof pause_when_enum {
    Level1 pause_when = 9;
    int32 pause_when_raw = 10;
  }
  oneof ready_event_active_level_enum {
    Polarity2 ready_event_active_level = 11;
    int32 ready_event_active_level_raw = 12;
  }
}

message CfgBurstHandshakingTimingImportClockResponse {
  int32 status = 1;
}

message CfgChangeDetectionTimingRequest {
  nidevice_grpc.Session task = 1;
  string rising_edge_chan = 2;
  string falling_edge_chan = 3;
  oneof sample_mode_enum {
    AcquisitionType sample_mode = 4;
    int32 sample_mode_raw = 5;
  }
  uint64 samps_per_chan = 6;
}

message CfgChangeDetectionTimingResponse {
  int32 status = 1;
}

message CfgDigEdgeRefTrigRequest {
  nidevice_grpc.Session task = 1;
  string trigger_source = 2;
  oneof trigger_edge_enum {
    Edge1 trigger_edge = 3;
    int32 trigger_edge_raw = 4;
  }
  uint32 pretrigger_samples = 5;
}

message CfgDigEdgeRefTrigResponse {
  int32 status = 1;
}

message CfgDigEdgeStartTrigRequest {
  nidevice_grpc.Session task = 1;
  string trigger_source = 2;
  oneof trigger_edge_enum {
    Edge1 trigger_edge = 3;
    int32 trigger_edge_raw = 4;
  }
}

message CfgDigEdgeStartTrigResponse {
  int32 status = 1;
}

message CfgDigPatternRefTrigRequest {
  nidevice_grpc.Session task = 1;
  string trigger_source = 2;
  string trigger_pattern = 3;
  oneof trigger_when_enum {
    DigitalPatternCondition1 trigger_when = 4;
    int32 trigger_when_raw = 5;
  }
  uint32 pretrigger_samples = 6;
}

message CfgDigPatternRefTrigResponse {
  int32 status = 1;
}

message CfgDigPatternStartTrigRequest {
  nidevice_grpc.Session task = 1;
  string trigger_source = 2;
  string trigger_pattern = 3;
  oneof trigger_when_enum {
    DigitalPatternCondition1 trigger_when = 4;
    int32 trigger_when_raw = 5;
  }
}

message CfgDigPatternStartTrigResponse {
  int32 status = 1;
}

message CfgHandshakingTimingRequest {
  nidevice_grpc.Session task = 1;
  oneof sample_mode_enum {
    AcquisitionType sample_mode = 2;
    int32 sample_mode_raw = 3;
  }
  uint64 samps_per_chan = 4;
}

message CfgHandshakingTimingResponse {
  int32 status = 1;
}

message CfgImplicitTimingRequest {
  nidevice_grpc.Session task = 1;
  oneof sample_mode_enum {
    AcquisitionType sample_mode = 2;
    int32 sample_mode_raw = 3;
  }
  uint64 samps_per_chan = 4;
}

message CfgImplicitTimingResponse {
  int32 status = 1;
}

message CfgInputBufferRequest {
  nidevice_grpc.Session task = 1;
  uint32 num_samps_per_chan = 2;
}

message CfgInputBufferResponse {
  int32 status = 1;
}

message CfgOutputBufferRequest {
  nidevice_grpc.Session task = 1;
  uint32 num_samps_per_chan = 2;
}

message CfgOutputBufferResponse {
  int32 status = 1;
}

message CfgPipelinedSampClkTimingRequest {
  nidevice_grpc.Session task = 1;
  string source = 2;
  double rate = 3;
  oneof active_edge_enum {
    Edge1 active_edge = 4;
    int32 active_edge_raw = 5;
  }
  oneof sample_mode_enum {
    AcquisitionType sample_mode = 6;
    int32 sample_mode_raw = 7;
  }
  uint64 samps_per_chan = 8;
}

message CfgPipelinedSampClkTimingResponse {
  int32 status = 1;
}

message CfgSampClkTimingRequest {
  nidevice_grpc.Session task = 1;
  string source = 2;
  double rate = 3;
  oneof active_edge_enum {
    Edge1 active_edge = 4;
    int32 active_edge_raw = 5;
  }
  oneof sample_mode_enum {
    AcquisitionType sample_mode = 6;
    int32 sample_mode_raw = 7;
  }
  uint64 samps_per_chan = 8;
}

message CfgSampClkTimingResponse {
  int32 status = 1;
}

message CfgTimeStartTrigRequest {
  nidevice_grpc.Session task = 1;
  google.protobuf.Timestamp when = 2;
  oneof timescale_enum {
    Timescale2 timescale = 3;
    int32 timescale_raw = 4;
  }
}

message CfgTimeStartTrigResponse {
  int32 status = 1;
}

message CfgWatchdogAOExpirStatesRequest {
  nidevice_grpc.Session task = 1;
  string channel_names = 2;
  repeated double expir_state_array = 3;
  repeated WatchdogAOOutputType output_type_array = 4;
}

message CfgWatchdogAOExpirStatesResponse {
  int32 status = 1;
}

message CfgWatchdogCOExpirStatesRequest {
  nidevice_grpc.Session task = 1;
  string channel_names = 2;
  repeated WatchdogCOExpirState expir_state_array = 3;
}

message CfgWatchdogCOExpirStatesResponse {
  int32 status = 1;
}

message CfgWatchdogDOExpirStatesRequest {
  nidevice_grpc.Session task = 1;
  string channel_names = 2;
  repeated DigitalLineState expir_state_array = 3;
}

message CfgWatchdogDOExpirStatesResponse {
  int32 status = 1;
}

message ClearTEDSRequest {
  string physical_channel = 1;
}

message ClearTEDSResponse {
  int32 status = 1;
}

message ClearTaskRequest {
  nidevice_grpc.Session task = 1;
}

message ClearTaskResponse {
  int32 status = 1;
}

message ConfigureLoggingRequest {
  nidevice_grpc.Session task = 1;
  string file_path = 2;
  oneof logging_mode_enum {
    LoggingMode logging_mode = 3;
    int32 logging_mode_raw = 4;
  }
  string group_name = 5;
  oneof operation_enum {
    LoggingOperation operation = 6;
    int32 operation_raw = 7;
  }
}

message ConfigureLoggingResponse {
  int32 status = 1;
}

message ConfigureTEDSRequest {
  string physical_channel = 1;
  string file_path = 2;
}

message ConfigureTEDSResponse {
  int32 status = 1;
}

message ConnectTermsRequest {
  string source_terminal = 1;
  string destination_terminal = 2;
  oneof signal_modifiers_enum {
    InvertPolarity signal_modifiers = 3;
    int32 signal_modifiers_raw = 4;
  }
}

message ConnectTermsResponse {
  int32 status = 1;
}

message ControlWatchdogTaskRequest {
  nidevice_grpc.Session task = 1;
  oneof action_enum {
    WatchdogControlAction action = 2;
    int32 action_raw = 3;
  }
}

message ControlWatchdogTaskResponse {
  int32 status = 1;
}

message CreateAIAccel4WireDCVoltageChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    AccelUnits2 units = 8;
    int32 units_raw = 9;
  }
  double sensitivity = 10;
  oneof sensitivity_units_enum {
    AccelSensitivityUnits1 sensitivity_units = 11;
    int32 sensitivity_units_raw = 12;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 13;
    int32 voltage_excit_source_raw = 14;
  }
  double voltage_excit_val = 15;
  bool use_excit_for_scaling = 16;
  string custom_scale_name = 17;
}

message CreateAIAccel4WireDCVoltageChanResponse {
  int32 status = 1;
}

message CreateAIAccelChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    AccelUnits2 units = 8;
    int32 units_raw = 9;
  }
  double sensitivity = 10;
  oneof sensitivity_units_enum {
    AccelSensitivityUnits1 sensitivity_units = 11;
    int32 sensitivity_units_raw = 12;
  }
  oneof current_excit_source_enum {
    ExcitationSource current_excit_source = 13;
    int32 current_excit_source_raw = 14;
  }
  double current_excit_val = 15;
  string custom_scale_name = 16;
}

message CreateAIAccelChanResponse {
  int32 status = 1;
}

message CreateAIAccelChargeChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    AccelUnits2 units = 8;
    int32 units_raw = 9;
  }
  double sensitivity = 10;
  oneof sensitivity_units_enum {
    AccelChargeSensitivityUnits sensitivity_units = 11;
    int32 sensitivity_units_raw = 12;
  }
  string custom_scale_name = 13;
}

message CreateAIAccelChargeChanResponse {
  int32 status = 1;
}

message CreateAIBridgeChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    BridgeUnits units = 6;
    int32 units_raw = 7;
  }
  oneof bridge_config_enum {
    BridgeConfiguration1 bridge_config = 8;
    int32 bridge_config_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  double nominal_bridge_resistance = 13;
  string custom_scale_name = 14;
}

message CreateAIBridgeChanResponse {
  int32 status = 1;
}

message CreateAIChargeChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    ChargeUnits units = 8;
    int32 units_raw = 9;
  }
  string custom_scale_name = 10;
}

message CreateAIChargeChanResponse {
  int32 status = 1;
}

message CreateAICurrentChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    CurrentUnits2 units = 8;
    int32 units_raw = 9;
  }
  oneof shunt_resistor_loc_enum {
    CurrentShuntResistorLocationWithDefault shunt_resistor_loc = 10;
    int32 shunt_resistor_loc_raw = 11;
  }
  double ext_shunt_resistor_val = 12;
  string custom_scale_name = 13;
}

message CreateAICurrentChanResponse {
  int32 status = 1;
}

message CreateAICurrentRMSChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    CurrentUnits2 units = 8;
    int32 units_raw = 9;
  }
  oneof shunt_resistor_loc_enum {
    CurrentShuntResistorLocationWithDefault shunt_resistor_loc = 10;
    int32 shunt_resistor_loc_raw = 11;
  }
  double ext_shunt_resistor_val = 12;
  string custom_scale_name = 13;
}

message CreateAICurrentRMSChanResponse {
  int32 status = 1;
}

message CreateAIForceBridgePolynomialChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    ForceUnits units = 6;
    int32 units_raw = 7;
  }
  oneof bridge_config_enum {
    BridgeConfiguration1 bridge_config = 8;
    int32 bridge_config_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  double nominal_bridge_resistance = 13;
  repeated double forward_coeffs = 14;
  repeated double reverse_coeffs = 15;
  oneof electrical_units_enum {
    BridgeElectricalUnits electrical_units = 16;
    int32 electrical_units_raw = 17;
  }
  oneof physical_units_enum {
    BridgePhysicalUnits physical_units = 18;
    int32 physical_units_raw = 19;
  }
  string custom_scale_name = 20;
}

message CreateAIForceBridgePolynomialChanResponse {
  int32 status = 1;
}

message CreateAIForceBridgeTableChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    ForceUnits units = 6;
    int32 units_raw = 7;
  }
  oneof bridge_config_enum {
    BridgeConfiguration1 bridge_config = 8;
    int32 bridge_config_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  double nominal_bridge_resistance = 13;
  repeated double electrical_vals = 14;
  oneof electrical_units_enum {
    BridgeElectricalUnits electrical_units = 15;
    int32 electrical_units_raw = 16;
  }
  repeated double physical_vals = 17;
  oneof physical_units_enum {
    BridgePhysicalUnits physical_units = 18;
    int32 physical_units_raw = 19;
  }
  string custom_scale_name = 20;
}

message CreateAIForceBridgeTableChanResponse {
  int32 status = 1;
}

message CreateAIForceBridgeTwoPointLinChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    ForceUnits units = 6;
    int32 units_raw = 7;
  }
  oneof bridge_config_enum {
    BridgeConfiguration1 bridge_config = 8;
    int32 bridge_config_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  double nominal_bridge_resistance = 13;
  double first_electrical_val = 14;
  double second_electrical_val = 15;
  oneof electrical_units_enum {
    BridgeElectricalUnits electrical_units = 16;
    int32 electrical_units_raw = 17;
  }
  double first_physical_val = 18;
  double second_physical_val = 19;
  oneof physical_units_enum {
    BridgePhysicalUnits physical_units = 20;
    int32 physical_units_raw = 21;
  }
  string custom_scale_name = 22;
}

message CreateAIForceBridgeTwoPointLinChanResponse {
  int32 status = 1;
}

message CreateAIForceIEPEChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    ForceIEPEUnits units = 8;
    int32 units_raw = 9;
  }
  double sensitivity = 10;
  oneof sensitivity_units_enum {
    ForceIEPESensorSensitivityUnits sensitivity_units = 11;
    int32 sensitivity_units_raw = 12;
  }
  oneof current_excit_source_enum {
    ExcitationSource current_excit_source = 13;
    int32 current_excit_source_raw = 14;
  }
  double current_excit_val = 15;
  string custom_scale_name = 16;
}

message CreateAIForceIEPEChanResponse {
  int32 status = 1;
}

message CreateAIFreqVoltageChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    FrequencyUnits units = 6;
    int32 units_raw = 7;
  }
  double threshold_level = 8;
  double hysteresis = 9;
  string custom_scale_name = 10;
}

message CreateAIFreqVoltageChanResponse {
  int32 status = 1;
}

message CreateAIMicrophoneChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  oneof units_enum {
    SoundPressureUnits1 units = 6;
    int32 units_raw = 7;
  }
  double mic_sensitivity = 8;
  double max_snd_press_level = 9;
  oneof current_excit_source_enum {
    ExcitationSource current_excit_source = 10;
    int32 current_excit_source_raw = 11;
  }
  double current_excit_val = 12;
  string custom_scale_name = 13;
}

message CreateAIMicrophoneChanResponse {
  int32 status = 1;
}

message CreateAIPosEddyCurrProxProbeChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    LengthUnits2 units = 6;
    int32 units_raw = 7;
  }
  double sensitivity = 8;
  oneof sensitivity_units_enum {
    EddyCurrentProxProbeSensitivityUnits sensitivity_units = 9;
    int32 sensitivity_units_raw = 10;
  }
  string custom_scale_name = 11;
}

message CreateAIPosEddyCurrProxProbeChanResponse {
  int32 status = 1;
}

message CreateAIPosLVDTChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    LengthUnits2 units = 6;
    int32 units_raw = 7;
  }
  double sensitivity = 8;
  oneof sensitivity_units_enum {
    LVDTSensitivityUnits1 sensitivity_units = 9;
    int32 sensitivity_units_raw = 10;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 11;
    int32 voltage_excit_source_raw = 12;
  }
  double voltage_excit_val = 13;
  double voltage_excit_freq = 14;
  oneof ac_excit_wire_mode_enum {
    ACExcitWireMode ac_excit_wire_mode = 15;
    int32 ac_excit_wire_mode_raw = 16;
  }
  string custom_scale_name = 17;
}

message CreateAIPosLVDTChanResponse {
  int32 status = 1;
}

message CreateAIPosRVDTChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    AngleUnits1 units = 6;
    int32 units_raw = 7;
  }
  double sensitivity = 8;
  oneof sensitivity_units_enum {
    RVDTSensitivityUnits1 sensitivity_units = 9;
    int32 sensitivity_units_raw = 10;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 11;
    int32 voltage_excit_source_raw = 12;
  }
  double voltage_excit_val = 13;
  double voltage_excit_freq = 14;
  oneof ac_excit_wire_mode_enum {
    ACExcitWireMode ac_excit_wire_mode = 15;
    int32 ac_excit_wire_mode_raw = 16;
  }
  string custom_scale_name = 17;
}

message CreateAIPosRVDTChanResponse {
  int32 status = 1;
}

message CreateAIPowerChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double voltage_setpoint = 4;
  double current_setpoint = 5;
  bool output_enable = 6;
}

message CreateAIPowerChanResponse {
  int32 status = 1;
}

message CreateAIPressureBridgePolynomialChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    PressureUnits units = 6;
    int32 units_raw = 7;
  }
  oneof bridge_config_enum {
    BridgeConfiguration1 bridge_config = 8;
    int32 bridge_config_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  double nominal_bridge_resistance = 13;
  repeated double forward_coeffs = 14;
  repeated double reverse_coeffs = 15;
  oneof electrical_units_enum {
    BridgeElectricalUnits electrical_units = 16;
    int32 electrical_units_raw = 17;
  }
  oneof physical_units_enum {
    BridgePhysicalUnits physical_units = 18;
    int32 physical_units_raw = 19;
  }
  string custom_scale_name = 20;
}

message CreateAIPressureBridgePolynomialChanResponse {
  int32 status = 1;
}

message CreateAIPressureBridgeTableChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    PressureUnits units = 6;
    int32 units_raw = 7;
  }
  oneof bridge_config_enum {
    BridgeConfiguration1 bridge_config = 8;
    int32 bridge_config_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  double nominal_bridge_resistance = 13;
  repeated double electrical_vals = 14;
  oneof electrical_units_enum {
    BridgeElectricalUnits electrical_units = 15;
    int32 electrical_units_raw = 16;
  }
  repeated double physical_vals = 17;
  oneof physical_units_enum {
    BridgePhysicalUnits physical_units = 18;
    int32 physical_units_raw = 19;
  }
  string custom_scale_name = 20;
}

message CreateAIPressureBridgeTableChanResponse {
  int32 status = 1;
}

message CreateAIPressureBridgeTwoPointLinChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    PressureUnits units = 6;
    int32 units_raw = 7;
  }
  oneof bridge_config_enum {
    BridgeConfiguration1 bridge_config = 8;
    int32 bridge_config_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  double nominal_bridge_resistance = 13;
  double first_electrical_val = 14;
  double second_electrical_val = 15;
  oneof electrical_units_enum {
    BridgeElectricalUnits electrical_units = 16;
    int32 electrical_units_raw = 17;
  }
  double first_physical_val = 18;
  double second_physical_val = 19;
  oneof physical_units_enum {
    BridgePhysicalUnits physical_units = 20;
    int32 physical_units_raw = 21;
  }
  string custom_scale_name = 22;
}

message CreateAIPressureBridgeTwoPointLinChanResponse {
  int32 status = 1;
}

message CreateAIRTDChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TemperatureUnits units = 6;
    int32 units_raw = 7;
  }
  oneof rtd_type_enum {
    RTDType1 rtd_type = 8;
    int32 rtd_type_raw = 9;
  }
  oneof resistance_config_enum {
    ResistanceConfiguration resistance_config = 10;
    int32 resistance_config_raw = 11;
  }
  oneof current_excit_source_enum {
    ExcitationSource current_excit_source = 12;
    int32 current_excit_source_raw = 13;
  }
  double current_excit_val = 14;
  double r0 = 15;
}

message CreateAIRTDChanResponse {
  int32 status = 1;
}

message CreateAIResistanceChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    ResistanceUnits2 units = 6;
    int32 units_raw = 7;
  }
  oneof resistance_config_enum {
    ResistanceConfiguration resistance_config = 8;
    int32 resistance_config_raw = 9;
  }
  oneof current_excit_source_enum {
    ExcitationSource current_excit_source = 10;
    int32 current_excit_source_raw = 11;
  }
  double current_excit_val = 12;
  string custom_scale_name = 13;
}

message CreateAIResistanceChanResponse {
  int32 status = 1;
}

message CreateAIRosetteStrainGageChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof rosette_type_enum {
    StrainGageRosetteType rosette_type = 6;
    int32 rosette_type_raw = 7;
  }
  double gage_orientation = 8;
  repeated StrainGageRosetteMeasurementType rosette_meas_types = 9;
  oneof strain_config_enum {
    StrainGageBridgeType1 strain_config = 10;
    int32 strain_config_raw = 11;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 12;
    int32 voltage_excit_source_raw = 13;
  }
  double voltage_excit_val = 14;
  double gage_factor = 15;
  double nominal_gage_resistance = 16;
  double poisson_ratio = 17;
  double lead_wire_resistance = 18;
}

message CreateAIRosetteStrainGageChanResponse {
  int32 status = 1;
}

message CreateAIStrainGageChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    StrainUnits1 units = 6;
    int32 units_raw = 7;
  }
  oneof strain_config_enum {
    StrainGageBridgeType1 strain_config = 8;
    int32 strain_config_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  double gage_factor = 13;
  double initial_bridge_voltage = 14;
  double nominal_gage_resistance = 15;
  double poisson_ratio = 16;
  double lead_wire_resistance = 17;
  string custom_scale_name = 18;
}

message CreateAIStrainGageChanResponse {
  int32 status = 1;
}

message CreateAITempBuiltInSensorChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof units_enum {
    TemperatureUnits units = 4;
    int32 units_raw = 5;
  }
}

message CreateAITempBuiltInSensorChanResponse {
  int32 status = 1;
}

message CreateAIThrmcplChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TemperatureUnits units = 6;
    int32 units_raw = 7;
  }
  oneof thermocouple_type_enum {
    ThermocoupleType1 thermocouple_type = 8;
    int32 thermocouple_type_raw = 9;
  }
  oneof cjc_source_enum {
    CJCSource1 cjc_source = 10;
    int32 cjc_source_raw = 11;
  }
  double cjc_val = 12;
  string cjc_channel = 13;
}

message CreateAIThrmcplChanResponse {
  int32 status = 1;
}

message CreateAIThrmstrChanIexRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TemperatureUnits units = 6;
    int32 units_raw = 7;
  }
  oneof resistance_config_enum {
    ResistanceConfiguration resistance_config = 8;
    int32 resistance_config_raw = 9;
  }
  oneof current_excit_source_enum {
    ExcitationSource current_excit_source = 10;
    int32 current_excit_source_raw = 11;
  }
  double current_excit_val = 12;
  double a = 13;
  double b = 14;
  double c = 15;
}

message CreateAIThrmstrChanIexResponse {
  int32 status = 1;
}

message CreateAIThrmstrChanVexRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TemperatureUnits units = 6;
    int32 units_raw = 7;
  }
  oneof resistance_config_enum {
    ResistanceConfiguration resistance_config = 8;
    int32 resistance_config_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  double a = 13;
  double b = 14;
  double c = 15;
  double r1 = 16;
}

message CreateAIThrmstrChanVexResponse {
  int32 status = 1;
}

message CreateAITorqueBridgePolynomialChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TorqueUnits units = 6;
    int32 units_raw = 7;
  }
  oneof bridge_config_enum {
    BridgeConfiguration1 bridge_config = 8;
    int32 bridge_config_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  double nominal_bridge_resistance = 13;
  repeated double forward_coeffs = 14;
  repeated double reverse_coeffs = 15;
  oneof electrical_units_enum {
    BridgeElectricalUnits electrical_units = 16;
    int32 electrical_units_raw = 17;
  }
  oneof physical_units_enum {
    BridgePhysicalUnits physical_units = 18;
    int32 physical_units_raw = 19;
  }
  string custom_scale_name = 20;
}

message CreateAITorqueBridgePolynomialChanResponse {
  int32 status = 1;
}

message CreateAITorqueBridgeTableChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TorqueUnits units = 6;
    int32 units_raw = 7;
  }
  oneof bridge_config_enum {
    BridgeConfiguration1 bridge_config = 8;
    int32 bridge_config_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  double nominal_bridge_resistance = 13;
  repeated double electrical_vals = 14;
  oneof electrical_units_enum {
    BridgeElectricalUnits electrical_units = 15;
    int32 electrical_units_raw = 16;
  }
  repeated double physical_vals = 17;
  oneof physical_units_enum {
    BridgePhysicalUnits physical_units = 18;
    int32 physical_units_raw = 19;
  }
  string custom_scale_name = 20;
}

message CreateAITorqueBridgeTableChanResponse {
  int32 status = 1;
}

message CreateAITorqueBridgeTwoPointLinChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TorqueUnits units = 6;
    int32 units_raw = 7;
  }
  oneof bridge_config_enum {
    BridgeConfiguration1 bridge_config = 8;
    int32 bridge_config_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  double nominal_bridge_resistance = 13;
  double first_electrical_val = 14;
  double second_electrical_val = 15;
  oneof electrical_units_enum {
    BridgeElectricalUnits electrical_units = 16;
    int32 electrical_units_raw = 17;
  }
  double first_physical_val = 18;
  double second_physical_val = 19;
  oneof physical_units_enum {
    BridgePhysicalUnits physical_units = 20;
    int32 physical_units_raw = 21;
  }
  string custom_scale_name = 22;
}

message CreateAITorqueBridgeTwoPointLinChanResponse {
  int32 status = 1;
}

message CreateAIVelocityIEPEChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    VelocityUnits units = 8;
    int32 units_raw = 9;
  }
  double sensitivity = 10;
  oneof sensitivity_units_enum {
    VelocityIEPESensorSensitivityUnits sensitivity_units = 11;
    int32 sensitivity_units_raw = 12;
  }
  oneof current_excit_source_enum {
    ExcitationSource current_excit_source = 13;
    int32 current_excit_source_raw = 14;
  }
  double current_excit_val = 15;
  string custom_scale_name = 16;
}

message CreateAIVelocityIEPEChanResponse {
  int32 status = 1;
}

message CreateAIVoltageChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    VoltageUnits2 units = 8;
    int32 units_raw = 9;
  }
  string custom_scale_name = 10;
}

message CreateAIVoltageChanResponse {
  int32 status = 1;
}

message CreateAIVoltageChanWithExcitRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    VoltageUnits2 units = 8;
    int32 units_raw = 9;
  }
  oneof bridge_config_enum {
    BridgeConfiguration1 bridge_config = 10;
    int32 bridge_config_raw = 11;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 12;
    int32 voltage_excit_source_raw = 13;
  }
  double voltage_excit_val = 14;
  bool use_excit_for_scaling = 15;
  string custom_scale_name = 16;
}

message CreateAIVoltageChanWithExcitResponse {
  int32 status = 1;
}

message CreateAIVoltageRMSChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    VoltageUnits2 units = 8;
    int32 units_raw = 9;
  }
  string custom_scale_name = 10;
}

message CreateAIVoltageRMSChanResponse {
  int32 status = 1;
}

message CreateAOCurrentChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    CurrentUnits2 units = 6;
    int32 units_raw = 7;
  }
  string custom_scale_name = 8;
}

message CreateAOCurrentChanResponse {
  int32 status = 1;
}

message CreateAOFuncGenChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof type_enum {
    FuncGenType type = 4;
    int32 type_raw = 5;
  }
  double freq = 6;
  double amplitude = 7;
  double offset = 8;
}

message CreateAOFuncGenChanResponse {
  int32 status = 1;
}

message CreateAOVoltageChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    VoltageUnits2 units = 6;
    int32 units_raw = 7;
  }
  string custom_scale_name = 8;
}

message CreateAOVoltageChanResponse {
  int32 status = 1;
}

message CreateCIAngEncoderChanRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  oneof decoding_type_enum {
    EncoderType2 decoding_type = 4;
    int32 decoding_type_raw = 5;
  }
  bool zidx_enable = 6;
  double zidx_val = 7;
  oneof zidx_phase_enum {
    EncoderZIndexPhase1 zidx_phase = 8;
    int32 zidx_phase_raw = 9;
  }
  oneof units_enum {
    AngleUnits2 units = 10;
    int32 units_raw = 11;
  }
  uint32 pulses_per_rev = 12;
  double initial_angle = 13;
  string custom_scale_name = 14;
}

message CreateCIAngEncoderChanResponse {
  int32 status = 1;
}

message CreateCIAngVelocityChanRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof decoding_type_enum {
    EncoderType2 decoding_type = 6;
    int32 decoding_type_raw = 7;
  }
  oneof units_enum {
    AngularVelocityUnits units = 8;
    int32 units_raw = 9;
  }
  uint32 pulses_per_rev = 10;
  string custom_scale_name = 11;
}

message CreateCIAngVelocityChanResponse {
  int32 status = 1;
}

message CreateCICountEdgesChanRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  oneof edge_enum {
    Edge1 edge = 4;
    int32 edge_raw = 5;
  }
  uint32 initial_count = 6;
  oneof count_direction_enum {
    CountDirection1 count_direction = 7;
    int32 count_direction_raw = 8;
  }
}

message CreateCICountEdgesChanResponse {
  int32 status = 1;
}

message CreateCIDutyCycleChanRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  double min_freq = 4;
  double max_freq = 5;
  oneof edge_enum {
    Edge1 edge = 6;
    int32 edge_raw = 7;
  }
  string custom_scale_name = 8;
}

message CreateCIDutyCycleChanResponse {
  int32 status = 1;
}

message CreateCIFreqChanRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    FrequencyUnits3 units = 6;
    int32 units_raw = 7;
  }
  oneof edge_enum {
    Edge1 edge = 8;
    int32 edge_raw = 9;
  }
  oneof meas_method_enum {
    CounterFrequencyMethod meas_method = 10;
    int32 meas_method_raw = 11;
  }
  double meas_time = 12;
  uint32 divisor = 13;
  string custom_scale_name = 14;
}

message CreateCIFreqChanResponse {
  int32 status = 1;
}

message CreateCIGPSTimestampChanRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  oneof units_enum {
    TimeUnits units = 4;
    int32 units_raw = 5;
  }
  oneof sync_method_enum {
    GpsSignalType1 sync_method = 6;
    int32 sync_method_raw = 7;
  }
  string custom_scale_name = 8;
}

message CreateCIGPSTimestampChanResponse {
  int32 status = 1;
}

message CreateCILinEncoderChanRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  oneof decoding_type_enum {
    EncoderType2 decoding_type = 4;
    int32 decoding_type_raw = 5;
  }
  bool zidx_enable = 6;
  double zidx_val = 7;
  oneof zidx_phase_enum {
    EncoderZIndexPhase1 zidx_phase = 8;
    int32 zidx_phase_raw = 9;
  }
  oneof units_enum {
    LengthUnits3 units = 10;
    int32 units_raw = 11;
  }
  double dist_per_pulse = 12;
  double initial_pos = 13;
  string custom_scale_name = 14;
}

message CreateCILinEncoderChanResponse {
  int32 status = 1;
}

message CreateCILinVelocityChanRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof decoding_type_enum {
    EncoderType2 decoding_type = 6;
    int32 decoding_type_raw = 7;
  }
  oneof units_enum {
    VelocityUnits units = 8;
    int32 units_raw = 9;
  }
  double dist_per_pulse = 10;
  string custom_scale_name = 11;
}

message CreateCILinVelocityChanResponse {
  int32 status = 1;
}

message CreateCIPeriodChanRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TimeUnits3 units = 6;
    int32 units_raw = 7;
  }
  oneof edge_enum {
    Edge1 edge = 8;
    int32 edge_raw = 9;
  }
  oneof meas_method_enum {
    CounterFrequencyMethod meas_method = 10;
    int32 meas_method_raw = 11;
  }
  double meas_time = 12;
  uint32 divisor = 13;
  string custom_scale_name = 14;
}

message CreateCIPeriodChanResponse {
  int32 status = 1;
}

message CreateCIPulseChanFreqRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    FrequencyUnits2 units = 6;
    int32 units_raw = 7;
  }
}

message CreateCIPulseChanFreqResponse {
  int32 status = 1;
}

message CreateCIPulseChanTicksRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  string source_terminal = 4;
  double min_val = 5;
  double max_val = 6;
}

message CreateCIPulseChanTicksResponse {
  int32 status = 1;
}

message CreateCIPulseChanTimeRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    DigitalWidthUnits3 units = 6;
    int32 units_raw = 7;
  }
}

message CreateCIPulseChanTimeResponse {
  int32 status = 1;
}

message CreateCIPulseWidthChanRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TimeUnits3 units = 6;
    int32 units_raw = 7;
  }
  oneof starting_edge_enum {
    Edge1 starting_edge = 8;
    int32 starting_edge_raw = 9;
  }
  string custom_scale_name = 10;
}

message CreateCIPulseWidthChanResponse {
  int32 status = 1;
}

message CreateCISemiPeriodChanRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TimeUnits3 units = 6;
    int32 units_raw = 7;
  }
  string custom_scale_name = 8;
}

message CreateCISemiPeriodChanResponse {
  int32 status = 1;
}

message CreateCITwoEdgeSepChanRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TimeUnits3 units = 6;
    int32 units_raw = 7;
  }
  oneof first_edge_enum {
    Edge1 first_edge = 8;
    int32 first_edge_raw = 9;
  }
  oneof second_edge_enum {
    Edge1 second_edge = 10;
    int32 second_edge_raw = 11;
  }
  string custom_scale_name = 12;
}

message CreateCITwoEdgeSepChanResponse {
  int32 status = 1;
}

message CreateCOPulseChanFreqRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  oneof units_enum {
    FrequencyUnits2 units = 4;
    int32 units_raw = 5;
  }
  oneof idle_state_enum {
    Level1 idle_state = 6;
    int32 idle_state_raw = 7;
  }
  double initial_delay = 8;
  double freq = 9;
  double duty_cycle = 10;
}

message CreateCOPulseChanFreqResponse {
  int32 status = 1;
}

message CreateCOPulseChanTicksRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  string source_terminal = 4;
  oneof idle_state_enum {
    Level1 idle_state = 5;
    int32 idle_state_raw = 6;
  }
  int32 initial_delay = 7;
  int32 low_ticks = 8;
  int32 high_ticks = 9;
}

message CreateCOPulseChanTicksResponse {
  int32 status = 1;
}

message CreateCOPulseChanTimeRequest {
  nidevice_grpc.Session task = 1;
  string counter = 2;
  string name_to_assign_to_channel = 3;
  oneof units_enum {
    DigitalWidthUnits3 units = 4;
    int32 units_raw = 5;
  }
  oneof idle_state_enum {
    Level1 idle_state = 6;
    int32 idle_state_raw = 7;
  }
  double initial_delay = 8;
  double low_time = 9;
  double high_time = 10;
}

message CreateCOPulseChanTimeResponse {
  int32 status = 1;
}

message CreateDIChanRequest {
  nidevice_grpc.Session task = 1;
  string lines = 2;
  string name_to_assign_to_lines = 3;
  oneof line_grouping_enum {
    LineGrouping line_grouping = 4;
    int32 line_grouping_raw = 5;
  }
}

message CreateDIChanResponse {
  int32 status = 1;
}

message CreateDOChanRequest {
  nidevice_grpc.Session task = 1;
  string lines = 2;
  string name_to_assign_to_lines = 3;
  oneof line_grouping_enum {
    LineGrouping line_grouping = 4;
    int32 line_grouping_raw = 5;
  }
}

message CreateDOChanResponse {
  int32 status = 1;
}

message CreateLinScaleRequest {
  string name = 1;
  double slope = 2;
  double y_intercept = 3;
  oneof pre_scaled_units_enum {
    UnitsPreScaled pre_scaled_units = 4;
    int32 pre_scaled_units_raw = 5;
  }
  string scaled_units = 6;
}

message CreateLinScaleResponse {
  int32 status = 1;
}

message CreateMapScaleRequest {
  string name = 1;
  double prescaled_min = 2;
  double prescaled_max = 3;
  double scaled_min = 4;
  double scaled_max = 5;
  oneof pre_scaled_units_enum {
    UnitsPreScaled pre_scaled_units = 6;
    int32 pre_scaled_units_raw = 7;
  }
  string scaled_units = 8;
}

message CreateMapScaleResponse {
  int32 status = 1;
}

message CreatePolynomialScaleRequest {
  string name = 1;
  repeated double forward_coeffs = 2;
  repeated double reverse_coeffs = 3;
  oneof pre_scaled_units_enum {
    UnitsPreScaled pre_scaled_units = 4;
    int32 pre_scaled_units_raw = 5;
  }
  string scaled_units = 6;
}

message CreatePolynomialScaleResponse {
  int32 status = 1;
}

message CreateTEDSAIAccelChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    AccelUnits2 units = 8;
    int32 units_raw = 9;
  }
  oneof current_excit_source_enum {
    ExcitationSource current_excit_source = 10;
    int32 current_excit_source_raw = 11;
  }
  double current_excit_val = 12;
  string custom_scale_name = 13;
}

message CreateTEDSAIAccelChanResponse {
  int32 status = 1;
}

message CreateTEDSAIBridgeChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TEDSUnits units = 6;
    int32 units_raw = 7;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 8;
    int32 voltage_excit_source_raw = 9;
  }
  double voltage_excit_val = 10;
  string custom_scale_name = 11;
}

message CreateTEDSAIBridgeChanResponse {
  int32 status = 1;
}

message CreateTEDSAICurrentChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    TEDSUnits units = 8;
    int32 units_raw = 9;
  }
  oneof shunt_resistor_loc_enum {
    CurrentShuntResistorLocationWithDefault shunt_resistor_loc = 10;
    int32 shunt_resistor_loc_raw = 11;
  }
  double ext_shunt_resistor_val = 12;
  string custom_scale_name = 13;
}

message CreateTEDSAICurrentChanResponse {
  int32 status = 1;
}

message CreateTEDSAIForceBridgeChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    ForceUnits units = 6;
    int32 units_raw = 7;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 8;
    int32 voltage_excit_source_raw = 9;
  }
  double voltage_excit_val = 10;
  string custom_scale_name = 11;
}

message CreateTEDSAIForceBridgeChanResponse {
  int32 status = 1;
}

message CreateTEDSAIForceIEPEChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    ForceIEPEUnits units = 8;
    int32 units_raw = 9;
  }
  oneof current_excit_source_enum {
    ExcitationSource current_excit_source = 10;
    int32 current_excit_source_raw = 11;
  }
  double current_excit_val = 12;
  string custom_scale_name = 13;
}

message CreateTEDSAIForceIEPEChanResponse {
  int32 status = 1;
}

message CreateTEDSAIMicrophoneChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  oneof units_enum {
    SoundPressureUnits1 units = 6;
    int32 units_raw = 7;
  }
  double max_snd_press_level = 8;
  oneof current_excit_source_enum {
    ExcitationSource current_excit_source = 9;
    int32 current_excit_source_raw = 10;
  }
  double current_excit_val = 11;
  string custom_scale_name = 12;
}

message CreateTEDSAIMicrophoneChanResponse {
  int32 status = 1;
}

message CreateTEDSAIPosLVDTChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    LengthUnits2 units = 6;
    int32 units_raw = 7;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 8;
    int32 voltage_excit_source_raw = 9;
  }
  double voltage_excit_val = 10;
  double voltage_excit_freq = 11;
  oneof ac_excit_wire_mode_enum {
    ACExcitWireMode ac_excit_wire_mode = 12;
    int32 ac_excit_wire_mode_raw = 13;
  }
  string custom_scale_name = 14;
}

message CreateTEDSAIPosLVDTChanResponse {
  int32 status = 1;
}

message CreateTEDSAIPosRVDTChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    AngleUnits1 units = 6;
    int32 units_raw = 7;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 8;
    int32 voltage_excit_source_raw = 9;
  }
  double voltage_excit_val = 10;
  double voltage_excit_freq = 11;
  oneof ac_excit_wire_mode_enum {
    ACExcitWireMode ac_excit_wire_mode = 12;
    int32 ac_excit_wire_mode_raw = 13;
  }
  string custom_scale_name = 14;
}

message CreateTEDSAIPosRVDTChanResponse {
  int32 status = 1;
}

message CreateTEDSAIPressureBridgeChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    PressureUnits units = 6;
    int32 units_raw = 7;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 8;
    int32 voltage_excit_source_raw = 9;
  }
  double voltage_excit_val = 10;
  string custom_scale_name = 11;
}

message CreateTEDSAIPressureBridgeChanResponse {
  int32 status = 1;
}

message CreateTEDSAIRTDChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TemperatureUnits units = 6;
    int32 units_raw = 7;
  }
  oneof resistance_config_enum {
    ResistanceConfiguration resistance_config = 8;
    int32 resistance_config_raw = 9;
  }
  oneof current_excit_source_enum {
    ExcitationSource current_excit_source = 10;
    int32 current_excit_source_raw = 11;
  }
  double current_excit_val = 12;
}

message CreateTEDSAIRTDChanResponse {
  int32 status = 1;
}

message CreateTEDSAIResistanceChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TEDSUnits units = 6;
    int32 units_raw = 7;
  }
  oneof resistance_config_enum {
    ResistanceConfiguration resistance_config = 8;
    int32 resistance_config_raw = 9;
  }
  oneof current_excit_source_enum {
    ExcitationSource current_excit_source = 10;
    int32 current_excit_source_raw = 11;
  }
  double current_excit_val = 12;
  string custom_scale_name = 13;
}

message CreateTEDSAIResistanceChanResponse {
  int32 status = 1;
}

message CreateTEDSAIStrainGageChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    StrainUnits1 units = 6;
    int32 units_raw = 7;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 8;
    int32 voltage_excit_source_raw = 9;
  }
  double voltage_excit_val = 10;
  double initial_bridge_voltage = 11;
  double lead_wire_resistance = 12;
  string custom_scale_name = 13;
}

message CreateTEDSAIStrainGageChanResponse {
  int32 status = 1;
}

message CreateTEDSAIThrmcplChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TemperatureUnits units = 6;
    int32 units_raw = 7;
  }
  oneof cjc_source_enum {
    CJCSource1 cjc_source = 8;
    int32 cjc_source_raw = 9;
  }
  double cjc_val = 10;
  string cjc_channel = 11;
}

message CreateTEDSAIThrmcplChanResponse {
  int32 status = 1;
}

message CreateTEDSAIThrmstrChanIexRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TemperatureUnits units = 6;
    int32 units_raw = 7;
  }
  oneof resistance_config_enum {
    ResistanceConfiguration resistance_config = 8;
    int32 resistance_config_raw = 9;
  }
  oneof current_excit_source_enum {
    ExcitationSource current_excit_source = 10;
    int32 current_excit_source_raw = 11;
  }
  double current_excit_val = 12;
}

message CreateTEDSAIThrmstrChanIexResponse {
  int32 status = 1;
}

message CreateTEDSAIThrmstrChanVexRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TemperatureUnits units = 6;
    int32 units_raw = 7;
  }
  oneof resistance_config_enum {
    ResistanceConfiguration resistance_config = 8;
    int32 resistance_config_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  double r1 = 13;
}

message CreateTEDSAIThrmstrChanVexResponse {
  int32 status = 1;
}

message CreateTEDSAITorqueBridgeChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  double min_val = 4;
  double max_val = 5;
  oneof units_enum {
    TorqueUnits units = 6;
    int32 units_raw = 7;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 8;
    int32 voltage_excit_source_raw = 9;
  }
  double voltage_excit_val = 10;
  string custom_scale_name = 11;
}

message CreateTEDSAITorqueBridgeChanResponse {
  int32 status = 1;
}

message CreateTEDSAIVoltageChanRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    TEDSUnits units = 8;
    int32 units_raw = 9;
  }
  string custom_scale_name = 10;
}

message CreateTEDSAIVoltageChanResponse {
  int32 status = 1;
}

message CreateTEDSAIVoltageChanWithExcitRequest {
  nidevice_grpc.Session task = 1;
  string physical_channel = 2;
  string name_to_assign_to_channel = 3;
  oneof terminal_config_enum {
    InputTermCfgWithDefault terminal_config = 4;
    int32 terminal_config_raw = 5;
  }
  double min_val = 6;
  double max_val = 7;
  oneof units_enum {
    TEDSUnits units = 8;
    int32 units_raw = 9;
  }
  oneof voltage_excit_source_enum {
    ExcitationSource voltage_excit_source = 10;
    int32 voltage_excit_source_raw = 11;
  }
  double voltage_excit_val = 12;
  string custom_scale_name = 13;
}

message CreateTEDSAIVoltageChanWithExcitResponse {
  int32 status = 1;
}

message CreateTableScaleRequest {
  string name = 1;
  repeated double prescaled_vals = 2;
  repeated double scaled_vals = 3;
  oneof pre_scaled_units_enum {
    UnitsPreScaled pre_scaled_units = 4;
    int32 pre_scaled_units_raw = 5;
  }
  string scaled_units = 6;
}

message CreateTableScaleResponse {
  int32 status = 1;
}

message CreateTaskRequest {
  string session_name = 1;
  nidevice_grpc.SessionInitializationBehavior initialization_behavior = 2;
}

message CreateTaskResponse {
  int32 status = 1;
  nidevice_grpc.Session task = 2;
  bool new_session_initialized = 3;
}

message CreateWatchdogTimerTaskRequest {
  string device_name = 1;
  string session_name = 2;
  double timeout = 3;
  repeated WatchdogExpChannelsAndState exp_states = 4;
  nidevice_grpc.SessionInitializationBehavior initialization_behavior = 5;
}

message CreateWatchdogTimerTaskResponse {
  int32 status = 1;
  nidevice_grpc.Session task = 2;
  bool new_session_initialized = 3;
}

message CreateWatchdogTimerTaskExRequest {
  string device_name = 1;
  string session_name = 2;
  double timeout = 3;
  nidevice_grpc.SessionInitializationBehavior initialization_behavior = 4;
}

message CreateWatchdogTimerTaskExResponse {
  int32 status = 1;
  nidevice_grpc.Session task = 2;
  bool new_session_initialized = 3;
}

message DeleteNetworkDeviceRequest {
  string device_name = 1;
}

message DeleteNetworkDeviceResponse {
  int32 status = 1;
}

message DeleteSavedGlobalChanRequest {
  string channel_name = 1;
}

message DeleteSavedGlobalChanResponse {
  int32 status = 1;
}

message DeleteSavedScaleRequest {
  string scale_name = 1;
}

message DeleteSavedScaleResponse {
  int32 status = 1;
}

message DeleteSavedTaskRequest {
  string task_name = 1;
}

message DeleteSavedTaskResponse {
  int32 status = 1;
}

message DeviceSupportsCalRequest {
  string device_name = 1;
}

message DeviceSupportsCalResponse {
  int32 status = 1;
  bool cal_supported = 2;
}

message DisableRefTrigRequest {
  nidevice_grpc.Session task = 1;
}

message DisableRefTrigResponse {
  int32 status = 1;
}

message DisableStartTrigRequest {
  nidevice_grpc.Session task = 1;
}

message DisableStartTrigResponse {
  int32 status = 1;
}

message DisconnectTermsRequest {
  string source_terminal = 1;
  string destination_terminal = 2;
}

message DisconnectTermsResponse {
  int32 status = 1;
}

message ExportSignalRequest {
  nidevice_grpc.Session task = 1;
  oneof signal_id_enum {
    Signal signal_id = 2;
    int32 signal_id_raw = 3;
  }
  string output_terminal = 4;
}

message ExportSignalResponse {
  int32 status = 1;
}

message GetAIChanCalCalDateRequest {
  nidevice_grpc.Session task = 1;
  string channel_name = 2;
}

message GetAIChanCalCalDateResponse {
  int32 status = 1;
  uint32 year = 2;
  uint32 month = 3;
  uint32 day = 4;
  uint32 hour = 5;
  uint32 minute = 6;
}

message GetAIChanCalExpDateRequest {
  nidevice_grpc.Session task = 1;
  string channel_name = 2;
}

message GetAIChanCalExpDateResponse {
  int32 status = 1;
  uint32 year = 2;
  uint32 month = 3;
  uint32 day = 4;
  uint32 hour = 5;
  uint32 minute = 6;
}

message GetAnalogPowerUpStatesRequest {
  string device_name = 1;
  repeated AnalogPowerUpChannelAndType channels = 2;
}

message GetAnalogPowerUpStatesResponse {
  int32 status = 1;
  repeated double power_up_states = 2;
}

message GetAnalogPowerUpStatesWithOutputTypeRequest {
  string channel_names = 1;
  uint32 array_size = 2;
}

message GetAnalogPowerUpStatesWithOutputTypeResponse {
  int32 status = 1;
  repeated double state_array = 2;
  repeated PowerUpChannelType channel_type_array = 3;
  repeated int32 channel_type_array_raw = 4;
}

message GetArmStartTrigTimestampValRequest {
  nidevice_grpc.Session task = 1;
}

message GetArmStartTrigTimestampValResponse {
  int32 status = 1;
  google.protobuf.Timestamp data = 2;
}

message GetArmStartTrigTrigWhenRequest {
  nidevice_grpc.Session task = 1;
}

message GetArmStartTrigTrigWhenResponse {
  int32 status = 1;
  google.protobuf.Timestamp data = 2;
}

message GetAutoConfiguredCDAQSyncConnectionsRequest {
}

message GetAutoConfiguredCDAQSyncConnectionsResponse {
  int32 status = 1;
  string port_list = 2;
}

message GetBufferAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    BufferUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetBufferAttributeUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetCalInfoAttributeBoolRequest {
  string device_name = 1;
  oneof attribute_enum {
    CalibrationInfoBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetCalInfoAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetCalInfoAttributeDoubleRequest {
  string device_name = 1;
  oneof attribute_enum {
    CalibrationInfoDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetCalInfoAttributeDoubleResponse {
  int32 status = 1;
  double value = 2;
}

message GetCalInfoAttributeStringRequest {
  string device_name = 1;
  oneof attribute_enum {
    CalibrationInfoStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetCalInfoAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetCalInfoAttributeUInt32Request {
  string device_name = 1;
  oneof attribute_enum {
    CalibrationInfoUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetCalInfoAttributeUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetChanAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  oneof attribute_enum {
    ChannelBoolAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetChanAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetChanAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  oneof attribute_enum {
    ChannelDoubleAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetChanAttributeDoubleResponse {
  int32 status = 1;
  double value = 2;
}

message GetChanAttributeDoubleArrayRequest {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  oneof attribute_enum {
    ChannelDoubleArrayAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetChanAttributeDoubleArrayResponse {
  int32 status = 1;
  repeated double value = 2;
}

message GetChanAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  oneof attribute_enum {
    ChannelInt32Attribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetChanAttributeInt32Response {
  int32 status = 1;
  ChannelInt32AttributeValues value = 2;
  int32 value_raw = 3;
}

message GetChanAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  oneof attribute_enum {
    ChannelStringAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetChanAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetChanAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  oneof attribute_enum {
    ChannelUInt32Attribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetChanAttributeUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetDeviceAttributeBoolRequest {
  string device_name = 1;
  oneof attribute_enum {
    DeviceBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetDeviceAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetDeviceAttributeDoubleRequest {
  string device_name = 1;
  oneof attribute_enum {
    DeviceDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetDeviceAttributeDoubleResponse {
  int32 status = 1;
  double value = 2;
}

message GetDeviceAttributeDoubleArrayRequest {
  string device_name = 1;
  oneof attribute_enum {
    DeviceDoubleArrayAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetDeviceAttributeDoubleArrayResponse {
  int32 status = 1;
  repeated double value = 2;
}

message GetDeviceAttributeInt32Request {
  string device_name = 1;
  oneof attribute_enum {
    DeviceInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetDeviceAttributeInt32Response {
  int32 status = 1;
  DeviceInt32AttributeValues value = 2;
  int32 value_raw = 3;
}

message GetDeviceAttributeInt32ArrayRequest {
  string device_name = 1;
  oneof attribute_enum {
    DeviceInt32ArrayAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetDeviceAttributeInt32ArrayResponse {
  int32 status = 1;
  repeated DeviceInt32AttributeValues value = 2;
  repeated int32 value_raw = 3;
}

message GetDeviceAttributeStringRequest {
  string device_name = 1;
  oneof attribute_enum {
    DeviceStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetDeviceAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetDeviceAttributeUInt32Request {
  string device_name = 1;
  oneof attribute_enum {
    DeviceUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetDeviceAttributeUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetDeviceAttributeUInt32ArrayRequest {
  string device_name = 1;
  oneof attribute_enum {
    DeviceUInt32ArrayAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetDeviceAttributeUInt32ArrayResponse {
  int32 status = 1;
  repeated uint32 value = 2;
}

message GetDigitalLogicFamilyPowerUpStateRequest {
  string device_name = 1;
}

message GetDigitalLogicFamilyPowerUpStateResponse {
  int32 status = 1;
  int32 logic_family = 2;
}

message GetDigitalPowerUpStatesRequest {
  string device_name = 1;
  repeated string channel_name = 2;
}

message GetDigitalPowerUpStatesResponse {
  int32 status = 1;
  repeated PowerUpStates power_up_states = 2;
}

message GetDigitalPullUpPullDownStatesRequest {
  string device_name = 1;
  repeated string channel_name = 2;
}

message GetDigitalPullUpPullDownStatesResponse {
  int32 status = 1;
  repeated ResistorState pull_up_pull_down_states = 2;
}

message GetDisconnectedCDAQSyncPortsRequest {
}

message GetDisconnectedCDAQSyncPortsResponse {
  int32 status = 1;
  string port_list = 2;
}

message GetErrorStringRequest {
  int32 error_code = 1;
}

message GetErrorStringResponse {
  int32 status = 1;
  string error_string = 2;
}

message GetExportedSignalAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ExportSignalBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetExportedSignalAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetExportedSignalAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ExportSignalDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetExportedSignalAttributeDoubleResponse {
  int32 status = 1;
  double value = 2;
}

message GetExportedSignalAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ExportSignalInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetExportedSignalAttributeInt32Response {
  int32 status = 1;
  ExportSignalInt32AttributeValues value = 2;
  int32 value_raw = 3;
}

message GetExportedSignalAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ExportSignalStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetExportedSignalAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetExportedSignalAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ExportSignalUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetExportedSignalAttributeUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetExtCalLastDateAndTimeRequest {
  string device_name = 1;
}

message GetExtCalLastDateAndTimeResponse {
  int32 status = 1;
  uint32 year = 2;
  uint32 month = 3;
  uint32 day = 4;
  uint32 hour = 5;
  uint32 minute = 6;
}

message GetFirstSampClkWhenRequest {
  nidevice_grpc.Session task = 1;
}

message GetFirstSampClkWhenResponse {
  int32 status = 1;
  google.protobuf.Timestamp data = 2;
}

message GetFirstSampTimestampValRequest {
  nidevice_grpc.Session task = 1;
}

message GetFirstSampTimestampValResponse {
  int32 status = 1;
  google.protobuf.Timestamp data = 2;
}

message GetNthTaskChannelRequest {
  nidevice_grpc.Session task = 1;
  uint32 index = 2;
}

message GetNthTaskChannelResponse {
  int32 status = 1;
  string buffer = 2;
}

message GetNthTaskDeviceRequest {
  nidevice_grpc.Session task = 1;
  uint32 index = 2;
}

message GetNthTaskDeviceResponse {
  int32 status = 1;
  string buffer = 2;
}

message GetNthTaskReadChannelRequest {
  nidevice_grpc.Session task = 1;
  uint32 index = 2;
}

message GetNthTaskReadChannelResponse {
  int32 status = 1;
  string buffer = 2;
}

message GetPersistedChanAttributeBoolRequest {
  string channel = 1;
  oneof attribute_enum {
    PersistedChannelBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPersistedChanAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetPersistedChanAttributeStringRequest {
  string channel = 1;
  oneof attribute_enum {
    PersistedChannelStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPersistedChanAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetPersistedScaleAttributeBoolRequest {
  string scale_name = 1;
  oneof attribute_enum {
    PersistedScaleBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPersistedScaleAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetPersistedScaleAttributeStringRequest {
  string scale_name = 1;
  oneof attribute_enum {
    PersistedScaleStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPersistedScaleAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetPersistedTaskAttributeBoolRequest {
  string task_name = 1;
  oneof attribute_enum {
    PersistedTaskBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPersistedTaskAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetPersistedTaskAttributeStringRequest {
  string task_name = 1;
  oneof attribute_enum {
    PersistedTaskStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPersistedTaskAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetPhysicalChanAttributeBoolRequest {
  string physical_channel = 1;
  oneof attribute_enum {
    PhysicalChannelBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPhysicalChanAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetPhysicalChanAttributeBytesRequest {
  string physical_channel = 1;
  oneof attribute_enum {
    PhysicalChannelBytesAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPhysicalChanAttributeBytesResponse {
  int32 status = 1;
  bytes value = 2;
}

message GetPhysicalChanAttributeDoubleRequest {
  string physical_channel = 1;
  oneof attribute_enum {
    PhysicalChannelDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPhysicalChanAttributeDoubleResponse {
  int32 status = 1;
  double value = 2;
}

message GetPhysicalChanAttributeDoubleArrayRequest {
  string physical_channel = 1;
  oneof attribute_enum {
    PhysicalChannelDoubleArrayAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPhysicalChanAttributeDoubleArrayResponse {
  int32 status = 1;
  repeated double value = 2;
}

message GetPhysicalChanAttributeInt32Request {
  string physical_channel = 1;
  oneof attribute_enum {
    PhysicalChannelInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPhysicalChanAttributeInt32Response {
  int32 status = 1;
  PhysicalChannelInt32AttributeValues value = 2;
  int32 value_raw = 3;
}

message GetPhysicalChanAttributeInt32ArrayRequest {
  string physical_channel = 1;
  oneof attribute_enum {
    PhysicalChannelInt32ArrayAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPhysicalChanAttributeInt32ArrayResponse {
  int32 status = 1;
  repeated PhysicalChannelInt32AttributeValues value = 2;
  repeated int32 value_raw = 3;
}

message GetPhysicalChanAttributeStringRequest {
  string physical_channel = 1;
  oneof attribute_enum {
    PhysicalChannelStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPhysicalChanAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetPhysicalChanAttributeUInt32Request {
  string physical_channel = 1;
  oneof attribute_enum {
    PhysicalChannelUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPhysicalChanAttributeUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetPhysicalChanAttributeUInt32ArrayRequest {
  string physical_channel = 1;
  oneof attribute_enum {
    PhysicalChannelUInt32ArrayAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetPhysicalChanAttributeUInt32ArrayResponse {
  int32 status = 1;
  repeated uint32 value = 2;
}

message GetReadAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ReadBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetReadAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetReadAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ReadDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetReadAttributeDoubleResponse {
  int32 status = 1;
  double value = 2;
}

message GetReadAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ReadInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetReadAttributeInt32Response {
  int32 status = 1;
  ReadInt32AttributeValues value = 2;
  int32 value_raw = 3;
}

message GetReadAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ReadStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetReadAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetReadAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ReadUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetReadAttributeUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetReadAttributeUInt64Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ReadUInt64Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetReadAttributeUInt64Response {
  int32 status = 1;
  uint64 value = 2;
}

message GetRealTimeAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    RealTimeBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetRealTimeAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetRealTimeAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    RealTimeInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetRealTimeAttributeInt32Response {
  int32 status = 1;
  RealTimeInt32AttributeValues value = 2;
  int32 value_raw = 3;
}

message GetRealTimeAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    RealTimeUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetRealTimeAttributeUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetRefTrigTimestampValRequest {
  nidevice_grpc.Session task = 1;
}

message GetRefTrigTimestampValResponse {
  int32 status = 1;
  google.protobuf.Timestamp data = 2;
}

message GetScaleAttributeDoubleRequest {
  string scale_name = 1;
  oneof attribute_enum {
    ScaleDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetScaleAttributeDoubleResponse {
  int32 status = 1;
  double value = 2;
}

message GetScaleAttributeDoubleArrayRequest {
  string scale_name = 1;
  oneof attribute_enum {
    ScaleDoubleArrayAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetScaleAttributeDoubleArrayResponse {
  int32 status = 1;
  repeated double value = 2;
}

message GetScaleAttributeInt32Request {
  string scale_name = 1;
  oneof attribute_enum {
    ScaleInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetScaleAttributeInt32Response {
  int32 status = 1;
  ScaleInt32AttributeValues value = 2;
  int32 value_raw = 3;
}

message GetScaleAttributeStringRequest {
  string scale_name = 1;
  oneof attribute_enum {
    ScaleStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetScaleAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetSelfCalLastDateAndTimeRequest {
  string device_name = 1;
}

message GetSelfCalLastDateAndTimeResponse {
  int32 status = 1;
  uint32 year = 2;
  uint32 month = 3;
  uint32 day = 4;
  uint32 hour = 5;
  uint32 minute = 6;
}

message GetStartTrigTimestampValRequest {
  nidevice_grpc.Session task = 1;
}

message GetStartTrigTimestampValResponse {
  int32 status = 1;
  google.protobuf.Timestamp data = 2;
}

message GetStartTrigTrigWhenRequest {
  nidevice_grpc.Session task = 1;
}

message GetStartTrigTrigWhenResponse {
  int32 status = 1;
  google.protobuf.Timestamp data = 2;
}

message GetSyncPulseTimeWhenRequest {
  nidevice_grpc.Session task = 1;
}

message GetSyncPulseTimeWhenResponse {
  int32 status = 1;
  google.protobuf.Timestamp data = 2;
}

message GetSystemInfoAttributeStringRequest {
  oneof attribute_enum {
    SystemStringAttribute attribute = 1;
    int32 attribute_raw = 2;
  }
}

message GetSystemInfoAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetSystemInfoAttributeUInt32Request {
  oneof attribute_enum {
    SystemUInt32Attribute attribute = 1;
    int32 attribute_raw = 2;
  }
}

message GetSystemInfoAttributeUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetTaskAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TaskBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTaskAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetTaskAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TaskStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTaskAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetTaskAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TaskUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTaskAttributeUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetTimingAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTimingAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetTimingAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTimingAttributeDoubleResponse {
  int32 status = 1;
  double value = 2;
}

message GetTimingAttributeExBoolRequest {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingBoolAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetTimingAttributeExBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetTimingAttributeExDoubleRequest {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingDoubleAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetTimingAttributeExDoubleResponse {
  int32 status = 1;
  double value = 2;
}

message GetTimingAttributeExInt32Request {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingInt32Attribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetTimingAttributeExInt32Response {
  int32 status = 1;
  TimingInt32AttributeValues value = 2;
  int32 value_raw = 3;
}

message GetTimingAttributeExStringRequest {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingStringAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetTimingAttributeExStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetTimingAttributeExTimestampRequest {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingTimestampAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetTimingAttributeExTimestampResponse {
  int32 status = 1;
  google.protobuf.Timestamp value = 2;
}

message GetTimingAttributeExUInt32Request {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingUInt32Attribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetTimingAttributeExUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetTimingAttributeExUInt64Request {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingUInt64Attribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetTimingAttributeExUInt64Response {
  int32 status = 1;
  uint64 value = 2;
}

message GetTimingAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTimingAttributeInt32Response {
  int32 status = 1;
  TimingInt32AttributeValues value = 2;
  int32 value_raw = 3;
}

message GetTimingAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTimingAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetTimingAttributeTimestampRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingTimestampAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTimingAttributeTimestampResponse {
  int32 status = 1;
  google.protobuf.Timestamp value = 2;
}

message GetTimingAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTimingAttributeUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetTimingAttributeUInt64Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingUInt64Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTimingAttributeUInt64Response {
  int32 status = 1;
  uint64 value = 2;
}

message GetTrigAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTrigAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetTrigAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTrigAttributeDoubleResponse {
  int32 status = 1;
  double value = 2;
}

message GetTrigAttributeDoubleArrayRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerDoubleArrayAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTrigAttributeDoubleArrayResponse {
  int32 status = 1;
  repeated double value = 2;
}

message GetTrigAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTrigAttributeInt32Response {
  int32 status = 1;
  TriggerInt32AttributeValues value = 2;
  int32 value_raw = 3;
}

message GetTrigAttributeInt32ArrayRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerInt32ArrayAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTrigAttributeInt32ArrayResponse {
  int32 status = 1;
  repeated TriggerInt32AttributeValues value = 2;
  repeated int32 value_raw = 3;
}

message GetTrigAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTrigAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetTrigAttributeTimestampRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerTimestampAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTrigAttributeTimestampResponse {
  int32 status = 1;
  google.protobuf.Timestamp value = 2;
}

message GetTrigAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetTrigAttributeUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetWatchdogAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  string lines = 2;
  oneof attribute_enum {
    WatchdogBoolAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetWatchdogAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetWatchdogAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  string lines = 2;
  oneof attribute_enum {
    WatchdogDoubleAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetWatchdogAttributeDoubleResponse {
  int32 status = 1;
  double value = 2;
}

message GetWatchdogAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  string lines = 2;
  oneof attribute_enum {
    WatchdogInt32Attribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetWatchdogAttributeInt32Response {
  int32 status = 1;
  WatchdogInt32AttributeValues value = 2;
  int32 value_raw = 3;
}

message GetWatchdogAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  string lines = 2;
  oneof attribute_enum {
    WatchdogStringAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message GetWatchdogAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetWriteAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    WriteBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetWriteAttributeBoolResponse {
  int32 status = 1;
  bool value = 2;
}

message GetWriteAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    WriteDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetWriteAttributeDoubleResponse {
  int32 status = 1;
  double value = 2;
}

message GetWriteAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    WriteInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetWriteAttributeInt32Response {
  int32 status = 1;
  WriteInt32AttributeValues value = 2;
  int32 value_raw = 3;
}

message GetWriteAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    WriteStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetWriteAttributeStringResponse {
  int32 status = 1;
  string value = 2;
}

message GetWriteAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    WriteUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetWriteAttributeUInt32Response {
  int32 status = 1;
  uint32 value = 2;
}

message GetWriteAttributeUInt64Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    WriteUInt64Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message GetWriteAttributeUInt64Response {
  int32 status = 1;
  uint64 value = 2;
}

message IsTaskDoneRequest {
  nidevice_grpc.Session task = 1;
}

message IsTaskDoneResponse {
  int32 status = 1;
  bool is_task_done = 2;
}

message LoadTaskRequest {
  string session_name = 1;
  nidevice_grpc.SessionInitializationBehavior initialization_behavior = 2;
}

message LoadTaskResponse {
  int32 status = 1;
  nidevice_grpc.Session task = 2;
  bool new_session_initialized = 3;
}

message PerformBridgeOffsetNullingCalExRequest {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  bool skip_unsupported_channels = 3;
}

message PerformBridgeOffsetNullingCalExResponse {
  int32 status = 1;
}

message PerformBridgeShuntCalExRequest {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  double shunt_resistor_value = 3;
  oneof shunt_resistor_location_enum {
    ShuntElementLocation shunt_resistor_location = 4;
    int32 shunt_resistor_location_raw = 5;
  }
  oneof shunt_resistor_select_enum {
    ShuntCalSelect shunt_resistor_select = 6;
    int32 shunt_resistor_select_raw = 7;
  }
  oneof shunt_resistor_source_enum {
    ShuntCalSource shunt_resistor_source = 8;
    int32 shunt_resistor_source_raw = 9;
  }
  double bridge_resistance = 10;
  bool skip_unsupported_channels = 11;
}

message PerformBridgeShuntCalExResponse {
  int32 status = 1;
}

message PerformStrainShuntCalExRequest {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  double shunt_resistor_value = 3;
  oneof shunt_resistor_location_enum {
    ShuntElementLocation shunt_resistor_location = 4;
    int32 shunt_resistor_location_raw = 5;
  }
  oneof shunt_resistor_select_enum {
    ShuntCalSelect shunt_resistor_select = 6;
    int32 shunt_resistor_select_raw = 7;
  }
  oneof shunt_resistor_source_enum {
    ShuntCalSource shunt_resistor_source = 8;
    int32 shunt_resistor_source_raw = 9;
  }
  bool skip_unsupported_channels = 10;
}

message PerformStrainShuntCalExResponse {
  int32 status = 1;
}

message PerformThrmcplLeadOffsetNullingCalRequest {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  bool skip_unsupported_channels = 3;
}

message PerformThrmcplLeadOffsetNullingCalResponse {
  int32 status = 1;
}

message ReadAnalogF64Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadAnalogF64Response {
  int32 status = 1;
  repeated double read_array = 2;
  int32 samps_per_chan_read = 3;
}

message BeginReadAnalogF64Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadAnalogF64Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadAnalogF64Response {
  int32 status = 1;
  repeated double read_array = 2;
  int32 samps_per_chan_read = 3;
}

message ReadAnalogScalarF64Request {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message ReadAnalogScalarF64Response {
  int32 status = 1;
  double value = 2;
}

message BeginReadAnalogScalarF64Request {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message BeginReadAnalogScalarF64Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadAnalogScalarF64Response {
  int32 status = 1;
  double value = 2;
}

message ReadBinaryI16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadBinaryI16Response {
  int32 status = 1;
  repeated int32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message BeginReadBinaryI16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadBinaryI16Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadBinaryI16Response {
  int32 status = 1;
  repeated int32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message ReadBinaryI32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadBinaryI32Response {
  int32 status = 1;
  repeated int32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message BeginReadBinaryI32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadBinaryI32Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadBinaryI32Response {
  int32 status = 1;
  repeated int32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message ReadBinaryU16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadBinaryU16Response {
  int32 status = 1;
  repeated uint32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message BeginReadBinaryU16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadBinaryU16Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadBinaryU16Response {
  int32 status = 1;
  repeated uint32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message ReadBinaryU32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadBinaryU32Response {
  int32 status = 1;
  repeated uint32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message BeginReadBinaryU32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadBinaryU32Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadBinaryU32Response {
  int32 status = 1;
  repeated uint32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message ReadCounterF64Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  uint32 array_size_in_samps = 4;
}

message ReadCounterF64Response {
  int32 status = 1;
  repeated double read_array = 2;
  int32 samps_per_chan_read = 3;
}

message BeginReadCounterF64Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  uint32 array_size_in_samps = 4;
}

message BeginReadCounterF64Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadCounterF64Response {
  int32 status = 1;
  repeated double read_array = 2;
  int32 samps_per_chan_read = 3;
}

message ReadCounterF64ExRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadCounterF64ExResponse {
  int32 status = 1;
  repeated double read_array = 2;
  int32 samps_per_chan_read = 3;
}

message BeginReadCounterF64ExRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadCounterF64ExResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadCounterF64ExResponse {
  int32 status = 1;
  repeated double read_array = 2;
  int32 samps_per_chan_read = 3;
}

message ReadCounterScalarF64Request {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message ReadCounterScalarF64Response {
  int32 status = 1;
  double value = 2;
}

message BeginReadCounterScalarF64Request {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message BeginReadCounterScalarF64Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadCounterScalarF64Response {
  int32 status = 1;
  double value = 2;
}

message ReadCounterScalarU32Request {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message ReadCounterScalarU32Response {
  int32 status = 1;
  uint32 value = 2;
}

message BeginReadCounterScalarU32Request {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message BeginReadCounterScalarU32Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadCounterScalarU32Response {
  int32 status = 1;
  uint32 value = 2;
}

message ReadCounterU32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  uint32 array_size_in_samps = 4;
}

message ReadCounterU32Response {
  int32 status = 1;
  repeated uint32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message BeginReadCounterU32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  uint32 array_size_in_samps = 4;
}

message BeginReadCounterU32Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadCounterU32Response {
  int32 status = 1;
  repeated uint32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message ReadCounterU32ExRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadCounterU32ExResponse {
  int32 status = 1;
  repeated uint32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message BeginReadCounterU32ExRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadCounterU32ExResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadCounterU32ExResponse {
  int32 status = 1;
  repeated uint32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message ReadCtrFreqRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof interleaved_enum {
    GroupBy interleaved = 4;
    int32 interleaved_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadCtrFreqResponse {
  int32 status = 1;
  repeated double read_array_frequency = 2;
  repeated double read_array_duty_cycle = 3;
  int32 samps_per_chan_read = 4;
}

message BeginReadCtrFreqRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof interleaved_enum {
    GroupBy interleaved = 4;
    int32 interleaved_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadCtrFreqResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadCtrFreqResponse {
  int32 status = 1;
  repeated double read_array_frequency = 2;
  repeated double read_array_duty_cycle = 3;
  int32 samps_per_chan_read = 4;
}

message ReadCtrFreqScalarRequest {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message ReadCtrFreqScalarResponse {
  int32 status = 1;
  double frequency = 2;
  double duty_cycle = 3;
}

message BeginReadCtrFreqScalarRequest {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message BeginReadCtrFreqScalarResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadCtrFreqScalarResponse {
  int32 status = 1;
  double frequency = 2;
  double duty_cycle = 3;
}

message ReadCtrTicksRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof interleaved_enum {
    GroupBy interleaved = 4;
    int32 interleaved_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadCtrTicksResponse {
  int32 status = 1;
  repeated uint32 read_array_high_ticks = 2;
  repeated uint32 read_array_low_ticks = 3;
  int32 samps_per_chan_read = 4;
}

message BeginReadCtrTicksRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof interleaved_enum {
    GroupBy interleaved = 4;
    int32 interleaved_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadCtrTicksResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadCtrTicksResponse {
  int32 status = 1;
  repeated uint32 read_array_high_ticks = 2;
  repeated uint32 read_array_low_ticks = 3;
  int32 samps_per_chan_read = 4;
}

message ReadCtrTicksScalarRequest {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message ReadCtrTicksScalarResponse {
  int32 status = 1;
  uint32 high_ticks = 2;
  uint32 low_ticks = 3;
}

message BeginReadCtrTicksScalarRequest {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message BeginReadCtrTicksScalarResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadCtrTicksScalarResponse {
  int32 status = 1;
  uint32 high_ticks = 2;
  uint32 low_ticks = 3;
}

message ReadCtrTimeRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof interleaved_enum {
    GroupBy interleaved = 4;
    int32 interleaved_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadCtrTimeResponse {
  int32 status = 1;
  repeated double read_array_high_time = 2;
  repeated double read_array_low_time = 3;
  int32 samps_per_chan_read = 4;
}

message BeginReadCtrTimeRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof interleaved_enum {
    GroupBy interleaved = 4;
    int32 interleaved_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadCtrTimeResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadCtrTimeResponse {
  int32 status = 1;
  repeated double read_array_high_time = 2;
  repeated double read_array_low_time = 3;
  int32 samps_per_chan_read = 4;
}

message ReadCtrTimeScalarRequest {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message ReadCtrTimeScalarResponse {
  int32 status = 1;
  double high_time = 2;
  double low_time = 3;
}

message BeginReadCtrTimeScalarRequest {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message BeginReadCtrTimeScalarResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadCtrTimeScalarResponse {
  int32 status = 1;
  double high_time = 2;
  double low_time = 3;
}

message ReadDigitalLinesRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_bytes = 6;
}

message ReadDigitalLinesResponse {
  int32 status = 1;
  bytes read_array = 2;
  int32 samps_per_chan_read = 3;
  int32 num_bytes_per_samp = 4;
}

message BeginReadDigitalLinesRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_bytes = 6;
}

message BeginReadDigitalLinesResponse {
  int32 status = 1;
  int32 num_bytes_per_samp = 2;
  ni.data_monikers.Moniker moniker = 3;
}

message MonikerReadDigitalLinesResponse {
  int32 status = 1;
  bytes read_array = 2;
  int32 samps_per_chan_read = 3;
  int32 num_bytes_per_samp = 4;
}

message ReadDigitalScalarU32Request {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message ReadDigitalScalarU32Response {
  int32 status = 1;
  uint32 value = 2;
}

message BeginReadDigitalScalarU32Request {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message BeginReadDigitalScalarU32Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadDigitalScalarU32Response {
  int32 status = 1;
  uint32 value = 2;
}

message ReadDigitalU16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadDigitalU16Response {
  int32 status = 1;
  repeated uint32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message BeginReadDigitalU16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadDigitalU16Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadDigitalU16Response {
  int32 status = 1;
  repeated uint32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message ReadDigitalU32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadDigitalU32Response {
  int32 status = 1;
  repeated uint32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message BeginReadDigitalU32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadDigitalU32Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadDigitalU32Response {
  int32 status = 1;
  repeated uint32 read_array = 2;
  int32 samps_per_chan_read = 3;
}

message ReadDigitalU8Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadDigitalU8Response {
  int32 status = 1;
  bytes read_array = 2;
  int32 samps_per_chan_read = 3;
}

message BeginReadDigitalU8Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadDigitalU8Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadDigitalU8Response {
  int32 status = 1;
  bytes read_array = 2;
  int32 samps_per_chan_read = 3;
}

message ReadIDPinMemoryRequest {
  string device_name = 1;
  string id_pin_name = 2;
  uint32 array_size = 3;
}

message ReadIDPinMemoryResponse {
  int32 status = 1;
  bytes data = 2;
  uint32 data_length_read = 3;
  uint32 format_code = 4;
}

message ReadPowerBinaryI16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadPowerBinaryI16Response {
  int32 status = 1;
  repeated int32 read_array_voltage = 2;
  repeated int32 read_array_current = 3;
  int32 samps_per_chan_read = 4;
}

message BeginReadPowerBinaryI16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadPowerBinaryI16Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadPowerBinaryI16Response {
  int32 status = 1;
  repeated int32 read_array_voltage = 2;
  repeated int32 read_array_current = 3;
  int32 samps_per_chan_read = 4;
}

message ReadPowerF64Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message ReadPowerF64Response {
  int32 status = 1;
  repeated double read_array_voltage = 2;
  repeated double read_array_current = 3;
  int32 samps_per_chan_read = 4;
}

message BeginReadPowerF64Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  oneof fill_mode_enum {
    GroupBy fill_mode = 4;
    int32 fill_mode_raw = 5;
  }
  uint32 array_size_in_samps = 6;
}

message BeginReadPowerF64Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadPowerF64Response {
  int32 status = 1;
  repeated double read_array_voltage = 2;
  repeated double read_array_current = 3;
  int32 samps_per_chan_read = 4;
}

message ReadPowerScalarF64Request {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message ReadPowerScalarF64Response {
  int32 status = 1;
  double voltage = 2;
  double current = 3;
}

message BeginReadPowerScalarF64Request {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message BeginReadPowerScalarF64Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadPowerScalarF64Response {
  int32 status = 1;
  double voltage = 2;
  double current = 3;
}

message ReadRawRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  uint32 array_size_in_bytes = 4;
}

message ReadRawResponse {
  int32 status = 1;
  bytes read_array = 2;
  int32 samps_read = 3;
  int32 num_bytes_per_samp = 4;
}

message BeginReadRawRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  double timeout = 3;
  uint32 array_size_in_bytes = 4;
}

message BeginReadRawResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerReadRawResponse {
  int32 status = 1;
  bytes read_array = 2;
  int32 samps_read = 3;
  int32 num_bytes_per_samp = 4;
}

message RegisterDoneEventRequest {
  nidevice_grpc.Session task = 1;
}

message RegisterDoneEventResponse {
  int32 status = 1;
}

message RegisterEveryNSamplesEventRequest {
  nidevice_grpc.Session task = 1;
  oneof every_n_samples_event_type_enum {
    EveryNSamplesEventType every_n_samples_event_type = 2;
    int32 every_n_samples_event_type_raw = 3;
  }
  uint32 n_samples = 4;
}

message RegisterEveryNSamplesEventResponse {
  int32 status = 1;
  EveryNSamplesEventType every_n_samples_event_type = 2;
  int32 every_n_samples_event_type_raw = 3;
  uint32 n_samples = 4;
}

message RegisterSignalEventRequest {
  nidevice_grpc.Session task = 1;
  oneof signal_id_enum {
    Signal2 signal_id = 2;
    int32 signal_id_raw = 3;
  }
}

message RegisterSignalEventResponse {
  int32 status = 1;
  int32 signal_id = 2;
}

message RemoveCDAQSyncConnectionRequest {
  string port_list = 1;
}

message RemoveCDAQSyncConnectionResponse {
  int32 status = 1;
}

message ReserveNetworkDeviceRequest {
  string device_name = 1;
  bool override_reservation = 2;
}

message ReserveNetworkDeviceResponse {
  int32 status = 1;
}

message ResetBufferAttributeRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    BufferResetAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message ResetBufferAttributeResponse {
  int32 status = 1;
}

message ResetChanAttributeRequest {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  oneof attribute_enum {
    ChannelResetAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message ResetChanAttributeResponse {
  int32 status = 1;
}

message ResetDeviceRequest {
  string device_name = 1;
}

message ResetDeviceResponse {
  int32 status = 1;
}

message ResetExportedSignalAttributeRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ExportSignalResetAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message ResetExportedSignalAttributeResponse {
  int32 status = 1;
}

message ResetReadAttributeRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ReadResetAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message ResetReadAttributeResponse {
  int32 status = 1;
}

message ResetRealTimeAttributeRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    RealTimeResetAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message ResetRealTimeAttributeResponse {
  int32 status = 1;
}

message ResetTimingAttributeRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingResetAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message ResetTimingAttributeResponse {
  int32 status = 1;
}

message ResetTimingAttributeExRequest {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingResetAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message ResetTimingAttributeExResponse {
  int32 status = 1;
}

message ResetTrigAttributeRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerResetAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message ResetTrigAttributeResponse {
  int32 status = 1;
}

message ResetWatchdogAttributeRequest {
  nidevice_grpc.Session task = 1;
  string lines = 2;
  oneof attribute_enum {
    WatchdogResetAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
}

message ResetWatchdogAttributeResponse {
  int32 status = 1;
}

message ResetWriteAttributeRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    WriteResetAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
}

message ResetWriteAttributeResponse {
  int32 status = 1;
}

message RestoreLastExtCalConstRequest {
  string device_name = 1;
}

message RestoreLastExtCalConstResponse {
  int32 status = 1;
}

message SaveGlobalChanRequest {
  nidevice_grpc.Session task = 1;
  string channel_name = 2;
  string save_as = 3;
  string author = 4;
  oneof options_enum {
    SaveOptions options = 5;
    uint32 options_raw = 6;
  }
}

message SaveGlobalChanResponse {
  int32 status = 1;
}

message SaveScaleRequest {
  string scale_name = 1;
  string save_as = 2;
  string author = 3;
  oneof options_enum {
    SaveOptions options = 4;
    uint32 options_raw = 5;
  }
}

message SaveScaleResponse {
  int32 status = 1;
}

message SaveTaskRequest {
  nidevice_grpc.Session task = 1;
  string save_as = 2;
  string author = 3;
  oneof options_enum {
    SaveOptions options = 4;
    uint32 options_raw = 5;
  }
}

message SaveTaskResponse {
  int32 status = 1;
}

message SelfCalRequest {
  string device_name = 1;
}

message SelfCalResponse {
  int32 status = 1;
}

message SelfTestDeviceRequest {
  string device_name = 1;
}

message SelfTestDeviceResponse {
  int32 status = 1;
}

message SetAIChanCalCalDateRequest {
  nidevice_grpc.Session task = 1;
  string channel_name = 2;
  uint32 year = 3;
  uint32 month = 4;
  uint32 day = 5;
  uint32 hour = 6;
  uint32 minute = 7;
}

message SetAIChanCalCalDateResponse {
  int32 status = 1;
}

message SetAIChanCalExpDateRequest {
  nidevice_grpc.Session task = 1;
  string channel_name = 2;
  uint32 year = 3;
  uint32 month = 4;
  uint32 day = 5;
  uint32 hour = 6;
  uint32 minute = 7;
}

message SetAIChanCalExpDateResponse {
  int32 status = 1;
}

message SetAnalogPowerUpStatesRequest {
  string device_name = 1;
  repeated AnalogPowerUpChannelsAndState power_up_states = 2;
}

message SetAnalogPowerUpStatesResponse {
  int32 status = 1;
}

message SetAnalogPowerUpStatesWithOutputTypeRequest {
  string channel_names = 1;
  repeated double state_array = 2;
  repeated PowerUpChannelType channel_type_array = 3;
}

message SetAnalogPowerUpStatesWithOutputTypeResponse {
  int32 status = 1;
}

message SetArmStartTrigTrigWhenRequest {
  nidevice_grpc.Session task = 1;
  google.protobuf.Timestamp data = 2;
}

message SetArmStartTrigTrigWhenResponse {
  int32 status = 1;
}

message SetBufferAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    BufferUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  uint32 value = 4;
}

message SetBufferAttributeUInt32Response {
  int32 status = 1;
}

message SetCalInfoAttributeBoolRequest {
  string device_name = 1;
  oneof attribute_enum {
    CalibrationInfoBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  bool value = 4;
}

message SetCalInfoAttributeBoolResponse {
  int32 status = 1;
}

message SetCalInfoAttributeDoubleRequest {
  string device_name = 1;
  oneof attribute_enum {
    CalibrationInfoDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  double value = 4;
}

message SetCalInfoAttributeDoubleResponse {
  int32 status = 1;
}

message SetCalInfoAttributeStringRequest {
  string device_name = 1;
  oneof attribute_enum {
    CalibrationInfoStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  string value = 4;
}

message SetCalInfoAttributeStringResponse {
  int32 status = 1;
}

message SetCalInfoAttributeUInt32Request {
  string device_name = 1;
  oneof attribute_enum {
    CalibrationInfoUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  uint32 value = 4;
}

message SetCalInfoAttributeUInt32Response {
  int32 status = 1;
}

message SetChanAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  oneof attribute_enum {
    ChannelBoolAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
  bool value = 5;
}

message SetChanAttributeBoolResponse {
  int32 status = 1;
}

message SetChanAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  oneof attribute_enum {
    ChannelDoubleAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
  double value = 5;
}

message SetChanAttributeDoubleResponse {
  int32 status = 1;
}

message SetChanAttributeDoubleArrayRequest {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  oneof attribute_enum {
    ChannelDoubleArrayAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
  repeated double value = 5;
}

message SetChanAttributeDoubleArrayResponse {
  int32 status = 1;
}

message SetChanAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  oneof attribute_enum {
    ChannelInt32Attribute attribute = 3;
    int32 attribute_raw = 4;
  }
  oneof value_enum {
    ChannelInt32AttributeValues value = 5;
    int32 value_raw = 6;
  }
}

message SetChanAttributeInt32Response {
  int32 status = 1;
}

message SetChanAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  oneof attribute_enum {
    ChannelStringAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
  string value = 5;
}

message SetChanAttributeStringResponse {
  int32 status = 1;
}

message SetChanAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  string channel = 2;
  oneof attribute_enum {
    ChannelUInt32Attribute attribute = 3;
    int32 attribute_raw = 4;
  }
  uint32 value = 5;
}

message SetChanAttributeUInt32Response {
  int32 status = 1;
}

message SetDigitalLogicFamilyPowerUpStateRequest {
  string device_name = 1;
  oneof logic_family_enum {
    LogicFamily logic_family = 2;
    int32 logic_family_raw = 3;
  }
}

message SetDigitalLogicFamilyPowerUpStateResponse {
  int32 status = 1;
}

message SetDigitalPowerUpStatesRequest {
  string device_name = 1;
  repeated DigitalPowerUpChannelsAndState power_up_states = 2;
}

message SetDigitalPowerUpStatesResponse {
  int32 status = 1;
}

message SetDigitalPullUpPullDownStatesRequest {
  string device_name = 1;
  repeated DigitalPullUpPullDownChannelsAndState pull_up_pull_down_states = 2;
}

message SetDigitalPullUpPullDownStatesResponse {
  int32 status = 1;
}

message SetExportedSignalAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ExportSignalBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  bool value = 4;
}

message SetExportedSignalAttributeBoolResponse {
  int32 status = 1;
}

message SetExportedSignalAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ExportSignalDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  double value = 4;
}

message SetExportedSignalAttributeDoubleResponse {
  int32 status = 1;
}

message SetExportedSignalAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ExportSignalInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  oneof value_enum {
    ExportSignalInt32AttributeValues value = 4;
    int32 value_raw = 5;
  }
}

message SetExportedSignalAttributeInt32Response {
  int32 status = 1;
}

message SetExportedSignalAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ExportSignalStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  string value = 4;
}

message SetExportedSignalAttributeStringResponse {
  int32 status = 1;
}

message SetExportedSignalAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ExportSignalUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  uint32 value = 4;
}

message SetExportedSignalAttributeUInt32Response {
  int32 status = 1;
}

message SetFirstSampClkWhenRequest {
  nidevice_grpc.Session task = 1;
  google.protobuf.Timestamp data = 2;
}

message SetFirstSampClkWhenResponse {
  int32 status = 1;
}

message SetReadAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ReadBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  bool value = 4;
}

message SetReadAttributeBoolResponse {
  int32 status = 1;
}

message SetReadAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ReadDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  double value = 4;
}

message SetReadAttributeDoubleResponse {
  int32 status = 1;
}

message SetReadAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ReadInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  oneof value_enum {
    ReadInt32AttributeValues value = 4;
    int32 value_raw = 5;
  }
}

message SetReadAttributeInt32Response {
  int32 status = 1;
}

message SetReadAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ReadStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  string value = 4;
}

message SetReadAttributeStringResponse {
  int32 status = 1;
}

message SetReadAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ReadUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  uint32 value = 4;
}

message SetReadAttributeUInt32Response {
  int32 status = 1;
}

message SetReadAttributeUInt64Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    ReadUInt64Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  uint64 value = 4;
}

message SetReadAttributeUInt64Response {
  int32 status = 1;
}

message SetRealTimeAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    RealTimeBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  bool value = 4;
}

message SetRealTimeAttributeBoolResponse {
  int32 status = 1;
}

message SetRealTimeAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    RealTimeInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  oneof value_enum {
    RealTimeInt32AttributeValues value = 4;
    int32 value_raw = 5;
  }
}

message SetRealTimeAttributeInt32Response {
  int32 status = 1;
}

message SetRealTimeAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    RealTimeUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  uint32 value = 4;
}

message SetRealTimeAttributeUInt32Response {
  int32 status = 1;
}

message SetScaleAttributeDoubleRequest {
  string scale_name = 1;
  oneof attribute_enum {
    ScaleDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  double value = 4;
}

message SetScaleAttributeDoubleResponse {
  int32 status = 1;
}

message SetScaleAttributeDoubleArrayRequest {
  string scale_name = 1;
  oneof attribute_enum {
    ScaleDoubleArrayAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  repeated double value = 4;
}

message SetScaleAttributeDoubleArrayResponse {
  int32 status = 1;
}

message SetScaleAttributeInt32Request {
  string scale_name = 1;
  oneof attribute_enum {
    ScaleInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  oneof value_enum {
    ScaleInt32AttributeValues value = 4;
    int32 value_raw = 5;
  }
}

message SetScaleAttributeInt32Response {
  int32 status = 1;
}

message SetScaleAttributeStringRequest {
  string scale_name = 1;
  oneof attribute_enum {
    ScaleStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  string value = 4;
}

message SetScaleAttributeStringResponse {
  int32 status = 1;
}

message SetStartTrigTrigWhenRequest {
  nidevice_grpc.Session task = 1;
  google.protobuf.Timestamp data = 2;
}

message SetStartTrigTrigWhenResponse {
  int32 status = 1;
}

message SetSyncPulseTimeWhenRequest {
  nidevice_grpc.Session task = 1;
  google.protobuf.Timestamp data = 2;
}

message SetSyncPulseTimeWhenResponse {
  int32 status = 1;
}

message SetTimingAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  bool value = 4;
}

message SetTimingAttributeBoolResponse {
  int32 status = 1;
}

message SetTimingAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  double value = 4;
}

message SetTimingAttributeDoubleResponse {
  int32 status = 1;
}

message SetTimingAttributeExBoolRequest {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingBoolAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
  bool value = 5;
}

message SetTimingAttributeExBoolResponse {
  int32 status = 1;
}

message SetTimingAttributeExDoubleRequest {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingDoubleAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
  double value = 5;
}

message SetTimingAttributeExDoubleResponse {
  int32 status = 1;
}

message SetTimingAttributeExInt32Request {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingInt32Attribute attribute = 3;
    int32 attribute_raw = 4;
  }
  oneof value_enum {
    TimingInt32AttributeValues value = 5;
    int32 value_raw = 6;
  }
}

message SetTimingAttributeExInt32Response {
  int32 status = 1;
}

message SetTimingAttributeExStringRequest {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingStringAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
  string value = 5;
}

message SetTimingAttributeExStringResponse {
  int32 status = 1;
}

message SetTimingAttributeExTimestampRequest {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingTimestampAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
  google.protobuf.Timestamp value = 5;
}

message SetTimingAttributeExTimestampResponse {
  int32 status = 1;
}

message SetTimingAttributeExUInt32Request {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingUInt32Attribute attribute = 3;
    int32 attribute_raw = 4;
  }
  uint32 value = 5;
}

message SetTimingAttributeExUInt32Response {
  int32 status = 1;
}

message SetTimingAttributeExUInt64Request {
  nidevice_grpc.Session task = 1;
  string device_names = 2;
  oneof attribute_enum {
    TimingUInt64Attribute attribute = 3;
    int32 attribute_raw = 4;
  }
  uint64 value = 5;
}

message SetTimingAttributeExUInt64Response {
  int32 status = 1;
}

message SetTimingAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  oneof value_enum {
    TimingInt32AttributeValues value = 4;
    int32 value_raw = 5;
  }
}

message SetTimingAttributeInt32Response {
  int32 status = 1;
}

message SetTimingAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  string value = 4;
}

message SetTimingAttributeStringResponse {
  int32 status = 1;
}

message SetTimingAttributeTimestampRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingTimestampAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  google.protobuf.Timestamp value = 4;
}

message SetTimingAttributeTimestampResponse {
  int32 status = 1;
}

message SetTimingAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  uint32 value = 4;
}

message SetTimingAttributeUInt32Response {
  int32 status = 1;
}

message SetTimingAttributeUInt64Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TimingUInt64Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  uint64 value = 4;
}

message SetTimingAttributeUInt64Response {
  int32 status = 1;
}

message SetTrigAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  bool value = 4;
}

message SetTrigAttributeBoolResponse {
  int32 status = 1;
}

message SetTrigAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  double value = 4;
}

message SetTrigAttributeDoubleResponse {
  int32 status = 1;
}

message SetTrigAttributeDoubleArrayRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerDoubleArrayAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  repeated double value = 4;
}

message SetTrigAttributeDoubleArrayResponse {
  int32 status = 1;
}

message SetTrigAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  oneof value_enum {
    TriggerInt32AttributeValues value = 4;
    int32 value_raw = 5;
  }
}

message SetTrigAttributeInt32Response {
  int32 status = 1;
}

message SetTrigAttributeInt32ArrayRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerInt32ArrayAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  repeated int32 value = 4;
}

message SetTrigAttributeInt32ArrayResponse {
  int32 status = 1;
}

message SetTrigAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  string value = 4;
}

message SetTrigAttributeStringResponse {
  int32 status = 1;
}

message SetTrigAttributeTimestampRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerTimestampAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  google.protobuf.Timestamp value = 4;
}

message SetTrigAttributeTimestampResponse {
  int32 status = 1;
}

message SetTrigAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    TriggerUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  uint32 value = 4;
}

message SetTrigAttributeUInt32Response {
  int32 status = 1;
}

message SetWatchdogAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  string lines = 2;
  oneof attribute_enum {
    WatchdogBoolAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
  bool value = 5;
}

message SetWatchdogAttributeBoolResponse {
  int32 status = 1;
}

message SetWatchdogAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  string lines = 2;
  oneof attribute_enum {
    WatchdogDoubleAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
  double value = 5;
}

message SetWatchdogAttributeDoubleResponse {
  int32 status = 1;
}

message SetWatchdogAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  string lines = 2;
  oneof attribute_enum {
    WatchdogInt32Attribute attribute = 3;
    int32 attribute_raw = 4;
  }
  oneof value_enum {
    WatchdogInt32AttributeValues value = 5;
    int32 value_raw = 6;
  }
}

message SetWatchdogAttributeInt32Response {
  int32 status = 1;
}

message SetWatchdogAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  string lines = 2;
  oneof attribute_enum {
    WatchdogStringAttribute attribute = 3;
    int32 attribute_raw = 4;
  }
  string value = 5;
}

message SetWatchdogAttributeStringResponse {
  int32 status = 1;
}

message SetWriteAttributeBoolRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    WriteBoolAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  bool value = 4;
}

message SetWriteAttributeBoolResponse {
  int32 status = 1;
}

message SetWriteAttributeDoubleRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    WriteDoubleAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  double value = 4;
}

message SetWriteAttributeDoubleResponse {
  int32 status = 1;
}

message SetWriteAttributeInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    WriteInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  oneof value_enum {
    WriteInt32AttributeValues value = 4;
    int32 value_raw = 5;
  }
}

message SetWriteAttributeInt32Response {
  int32 status = 1;
}

message SetWriteAttributeStringRequest {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    WriteStringAttribute attribute = 2;
    int32 attribute_raw = 3;
  }
  string value = 4;
}

message SetWriteAttributeStringResponse {
  int32 status = 1;
}

message SetWriteAttributeUInt32Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    WriteUInt32Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  uint32 value = 4;
}

message SetWriteAttributeUInt32Response {
  int32 status = 1;
}

message SetWriteAttributeUInt64Request {
  nidevice_grpc.Session task = 1;
  oneof attribute_enum {
    WriteUInt64Attribute attribute = 2;
    int32 attribute_raw = 3;
  }
  uint64 value = 4;
}

message SetWriteAttributeUInt64Response {
  int32 status = 1;
}

message StartNewFileRequest {
  nidevice_grpc.Session task = 1;
  string file_path = 2;
}

message StartNewFileResponse {
  int32 status = 1;
}

message StartTaskRequest {
  nidevice_grpc.Session task = 1;
}

message StartTaskResponse {
  int32 status = 1;
}

message StopTaskRequest {
  nidevice_grpc.Session task = 1;
}

message StopTaskResponse {
  int32 status = 1;
}

message TaskControlRequest {
  nidevice_grpc.Session task = 1;
  oneof action_enum {
    TaskControlAction action = 2;
    int32 action_raw = 3;
  }
}

message TaskControlResponse {
  int32 status = 1;
}

message TristateOutputTermRequest {
  string output_terminal = 1;
}

message TristateOutputTermResponse {
  int32 status = 1;
}

message UnregisterDoneEventRequest {
  nidevice_grpc.Session task = 1;
}

message UnregisterDoneEventResponse {
  int32 status = 1;
}

message UnregisterEveryNSamplesEventRequest {
  nidevice_grpc.Session task = 1;
  oneof every_n_samples_event_type_enum {
    EveryNSamplesEventType every_n_samples_event_type = 2;
    int32 every_n_samples_event_type_raw = 3;
  }
}

message UnregisterEveryNSamplesEventResponse {
  int32 status = 1;
}

message UnregisterSignalEventRequest {
  nidevice_grpc.Session task = 1;
  oneof signal_id_enum {
    Signal2 signal_id = 2;
    int32 signal_id_raw = 3;
  }
}

message UnregisterSignalEventResponse {
  int32 status = 1;
}

message UnreserveNetworkDeviceRequest {
  string device_name = 1;
}

message UnreserveNetworkDeviceResponse {
  int32 status = 1;
}

message WaitForNextSampleClockRequest {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message WaitForNextSampleClockResponse {
  int32 status = 1;
  bool is_late = 2;
}

message BeginWaitForNextSampleClockRequest {
  nidevice_grpc.Session task = 1;
  double timeout = 2;
}

message BeginWaitForNextSampleClockResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWaitForNextSampleClockResponse {
  int32 status = 1;
  bool is_late = 2;
}

message WaitForValidTimestampRequest {
  nidevice_grpc.Session task = 1;
  oneof timestamp_event_enum {
    TimestampEvent timestamp_event = 2;
    int32 timestamp_event_raw = 3;
  }
  double timeout = 4;
}

message WaitForValidTimestampResponse {
  int32 status = 1;
  google.protobuf.Timestamp timestamp = 2;
}

message WaitUntilTaskDoneRequest {
  nidevice_grpc.Session task = 1;
  double time_to_wait = 2;
}

message WaitUntilTaskDoneResponse {
  int32 status = 1;
}

message WriteAnalogF64Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
  repeated double write_array = 7;
}

message WriteAnalogF64Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message BeginWriteAnalogF64Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
}

message BeginWriteAnalogF64Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteAnalogF64Request {
  repeated double write_array = 1;
}

message MonikerWriteAnalogF64Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message WriteAnalogScalarF64Request {
  nidevice_grpc.Session task = 1;
  bool auto_start = 2;
  double timeout = 3;
  double value = 4;
}

message WriteAnalogScalarF64Response {
  int32 status = 1;
}

message BeginWriteAnalogScalarF64Request {
  nidevice_grpc.Session task = 1;
  bool auto_start = 2;
  double timeout = 3;
}

message BeginWriteAnalogScalarF64Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteAnalogScalarF64Request {
  double value = 1;
}

message MonikerWriteAnalogScalarF64Response {
  int32 status = 1;
}

message WriteBinaryI16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
  repeated int32 write_array = 7;
}

message WriteBinaryI16Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message BeginWriteBinaryI16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
}

message BeginWriteBinaryI16Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteBinaryI16Request {
  repeated int32 write_array = 1;
}

message MonikerWriteBinaryI16Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message WriteBinaryI32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
  repeated int32 write_array = 7;
}

message WriteBinaryI32Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message BeginWriteBinaryI32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
}

message BeginWriteBinaryI32Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteBinaryI32Request {
  repeated int32 write_array = 1;
}

message MonikerWriteBinaryI32Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message WriteBinaryU16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
  repeated uint32 write_array = 7;
}

message WriteBinaryU16Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message BeginWriteBinaryU16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
}

message BeginWriteBinaryU16Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteBinaryU16Request {
  repeated uint32 write_array = 1;
}

message MonikerWriteBinaryU16Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message WriteBinaryU32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
  repeated uint32 write_array = 7;
}

message WriteBinaryU32Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message BeginWriteBinaryU32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
}

message BeginWriteBinaryU32Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteBinaryU32Request {
  repeated uint32 write_array = 1;
}

message MonikerWriteBinaryU32Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message WriteCtrFreqRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
  repeated double frequency = 7;
  repeated double duty_cycle = 8;
}

message WriteCtrFreqResponse {
  int32 status = 1;
  int32 num_samps_per_chan_written = 2;
}

message BeginWriteCtrFreqRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
}

message BeginWriteCtrFreqResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteCtrFreqRequest {
  repeated double frequency = 1;
  repeated double duty_cycle = 2;
}

message MonikerWriteCtrFreqResponse {
  int32 status = 1;
  int32 num_samps_per_chan_written = 2;
}

message WriteCtrFreqScalarRequest {
  nidevice_grpc.Session task = 1;
  bool auto_start = 2;
  double timeout = 3;
  double frequency = 4;
  double duty_cycle = 5;
}

message WriteCtrFreqScalarResponse {
  int32 status = 1;
}

message BeginWriteCtrFreqScalarRequest {
  nidevice_grpc.Session task = 1;
  bool auto_start = 2;
  double timeout = 3;
}

message BeginWriteCtrFreqScalarResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteCtrFreqScalarRequest {
  double frequency = 1;
  double duty_cycle = 2;
}

message MonikerWriteCtrFreqScalarResponse {
  int32 status = 1;
}

message WriteCtrTicksRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
  repeated uint32 high_ticks = 7;
  repeated uint32 low_ticks = 8;
}

message WriteCtrTicksResponse {
  int32 status = 1;
  int32 num_samps_per_chan_written = 2;
}

message BeginWriteCtrTicksRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
}

message BeginWriteCtrTicksResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteCtrTicksRequest {
  repeated uint32 high_ticks = 1;
  repeated uint32 low_ticks = 2;
}

message MonikerWriteCtrTicksResponse {
  int32 status = 1;
  int32 num_samps_per_chan_written = 2;
}

message WriteCtrTicksScalarRequest {
  nidevice_grpc.Session task = 1;
  bool auto_start = 2;
  double timeout = 3;
  uint32 high_ticks = 4;
  uint32 low_ticks = 5;
}

message WriteCtrTicksScalarResponse {
  int32 status = 1;
}

message BeginWriteCtrTicksScalarRequest {
  nidevice_grpc.Session task = 1;
  bool auto_start = 2;
  double timeout = 3;
}

message BeginWriteCtrTicksScalarResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteCtrTicksScalarRequest {
  uint32 high_ticks = 1;
  uint32 low_ticks = 2;
}

message MonikerWriteCtrTicksScalarResponse {
  int32 status = 1;
}

message WriteCtrTimeRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
  repeated double high_time = 7;
  repeated double low_time = 8;
}

message WriteCtrTimeResponse {
  int32 status = 1;
  int32 num_samps_per_chan_written = 2;
}

message BeginWriteCtrTimeRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
}

message BeginWriteCtrTimeResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteCtrTimeRequest {
  repeated double high_time = 1;
  repeated double low_time = 2;
}

message MonikerWriteCtrTimeResponse {
  int32 status = 1;
  int32 num_samps_per_chan_written = 2;
}

message WriteCtrTimeScalarRequest {
  nidevice_grpc.Session task = 1;
  bool auto_start = 2;
  double timeout = 3;
  double high_time = 4;
  double low_time = 5;
}

message WriteCtrTimeScalarResponse {
  int32 status = 1;
}

message BeginWriteCtrTimeScalarRequest {
  nidevice_grpc.Session task = 1;
  bool auto_start = 2;
  double timeout = 3;
}

message BeginWriteCtrTimeScalarResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteCtrTimeScalarRequest {
  double high_time = 1;
  double low_time = 2;
}

message MonikerWriteCtrTimeScalarResponse {
  int32 status = 1;
}

message WriteDigitalLinesRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
  bytes write_array = 7;
}

message WriteDigitalLinesResponse {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message BeginWriteDigitalLinesRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
}

message BeginWriteDigitalLinesResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteDigitalLinesRequest {
  bytes write_array = 1;
}

message MonikerWriteDigitalLinesResponse {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message WriteDigitalScalarU32Request {
  nidevice_grpc.Session task = 1;
  bool auto_start = 2;
  double timeout = 3;
  uint32 value = 4;
}

message WriteDigitalScalarU32Response {
  int32 status = 1;
}

message BeginWriteDigitalScalarU32Request {
  nidevice_grpc.Session task = 1;
  bool auto_start = 2;
  double timeout = 3;
}

message BeginWriteDigitalScalarU32Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteDigitalScalarU32Request {
  uint32 value = 1;
}

message MonikerWriteDigitalScalarU32Response {
  int32 status = 1;
}

message WriteDigitalU16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
  repeated uint32 write_array = 7;
}

message WriteDigitalU16Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message BeginWriteDigitalU16Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
}

message BeginWriteDigitalU16Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteDigitalU16Request {
  repeated uint32 write_array = 1;
}

message MonikerWriteDigitalU16Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message WriteDigitalU32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
  repeated uint32 write_array = 7;
}

message WriteDigitalU32Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message BeginWriteDigitalU32Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
}

message BeginWriteDigitalU32Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteDigitalU32Request {
  repeated uint32 write_array = 1;
}

message MonikerWriteDigitalU32Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message WriteDigitalU8Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
  bytes write_array = 7;
}

message WriteDigitalU8Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message BeginWriteDigitalU8Request {
  nidevice_grpc.Session task = 1;
  int32 num_samps_per_chan = 2;
  bool auto_start = 3;
  double timeout = 4;
  oneof data_layout_enum {
    GroupBy data_layout = 5;
    int32 data_layout_raw = 6;
  }
}

message BeginWriteDigitalU8Response {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteDigitalU8Request {
  bytes write_array = 1;
}

message MonikerWriteDigitalU8Response {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message WriteIDPinMemoryRequest {
  string device_name = 1;
  string id_pin_name = 2;
  bytes data = 3;
  uint32 format_code = 4;
}

message WriteIDPinMemoryResponse {
  int32 status = 1;
}

message WriteRawRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps = 2;
  bool auto_start = 3;
  double timeout = 4;
  bytes write_array = 5;
}

message WriteRawResponse {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message BeginWriteRawRequest {
  nidevice_grpc.Session task = 1;
  int32 num_samps = 2;
  bool auto_start = 3;
  double timeout = 4;
}

message BeginWriteRawResponse {
  int32 status = 1;
  ni.data_monikers.Moniker moniker = 2;
}

message MonikerWriteRawRequest {
  bytes write_array = 1;
}

message MonikerWriteRawResponse {
  int32 status = 1;
  int32 samps_per_chan_written = 2;
}

message WriteToTEDSFromArrayRequest {
  string physical_channel = 1;
  bytes bit_stream = 2;
  oneof basic_teds_options_enum {
    WriteBasicTEDSOptions basic_teds_options = 3;
    int32 basic_teds_options_raw = 4;
  }
}

message WriteToTEDSFromArrayResponse {
  int32 status = 1;
}

message WriteToTEDSFromFileRequest {
  string physical_channel = 1;
  string file_path = 2;
  oneof basic_teds_options_enum {
    WriteBasicTEDSOptions basic_teds_options = 3;
    int32 basic_teds_options_raw = 4;
  }
}

message WriteToTEDSFromFileResponse {
  int32 status = 1;
}

