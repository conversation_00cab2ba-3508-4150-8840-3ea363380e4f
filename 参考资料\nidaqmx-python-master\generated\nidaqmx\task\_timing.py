# Do not edit this file; it was automatically generated.

from __future__ import annotations

from typing import <PERSON>Return

from nidaqmx.system.physical_channel import _PhysicalChannelAlternateConstructor
from nidaqmx.system.device import Device
from nidaqmx.errors import DaqError
from nidaqmx.error_codes import DAQmxErrors
from nidaqmx.constants import (
    AcquisitionType, DigitalWidthUnits, Edge, HandshakeStartCondition, Level,
    MIOAIConvertTimebaseSource, OverflowBehavior, Polarity,
    SampleInputDataWhen, SampleTimingType, SyncPulseType, Timescale,
    UnderflowBehavior)


class Timing:
    """
    Represents the timing configurations for a DAQmx task.
    """
    __slots__ = ('_handle', '_interpreter', '_active_devs')

    def __init__(self, task_handle, interpreter, active_devs: str | Device | None = None):
        if isinstance(active_devs, Device):
            active_devs = active_devs.name
        self._handle = task_handle
        self._interpreter = interpreter
        self._active_devs = active_devs

    def __getitem__(self, dev: str | Device) -> Timing:
        if self._active_devs:
            raise DaqError( 
                f"Cannot set active device '{dev}' because active device '{self._active_devs}' is already set.",
                DAQmxErrors.UNKNOWN)
        if isinstance(dev, (str, Device)):
            return Timing(self._handle, self._interpreter, active_devs=dev)
        else:
            raise TypeError(f"Invalid active_devs input: {dev!r} (type: {type(dev).__name__}). Expected str or Device.")

    def _raise_device_context_not_supported_error(self) -> NoReturn:
        raise DaqError(
            'Operation must be performed on the entire task. It cannot be '
            'performed only on specific devices in the task.',
            DAQmxErrors.M_STUDIO_OPERATION_DOES_NOT_SUPPORT_DEVICE_CONTEXT)

    @property
    def ai_conv_active_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies on which edge of the
            clock pulse an analog-to-digital conversion takes place.
        """

        if self._active_devs:
            val = self._interpreter.get_timing_attribute_ex_int32(self._handle, self._active_devs, 0x1853)
        else:
            val = self._interpreter.get_timing_attribute_int32(self._handle, 0x1853)
        return Edge(val)

    @ai_conv_active_edge.setter
    def ai_conv_active_edge(self, val):
        val = val.value
        if self._active_devs:
            self._interpreter.set_timing_attribute_ex_int32(self._handle, self._active_devs, 0x1853, val)
        else:
            self._interpreter.set_timing_attribute_int32(self._handle, 0x1853, val)

    @ai_conv_active_edge.deleter
    def ai_conv_active_edge(self):
        if self._active_devs:
            self._interpreter.reset_timing_attribute_ex(self._handle, self._active_devs, 0x1853)
        else:
            self._interpreter.reset_timing_attribute(self._handle, 0x1853)

    @property
    def ai_conv_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply a digital filter to the AI
            Convert Clock.
        """

        if self._active_devs:
            val = self._interpreter.get_timing_attribute_ex_bool(self._handle, self._active_devs, 0x2edc)
        else:
            val = self._interpreter.get_timing_attribute_bool(self._handle, 0x2edc)
        return val

    @ai_conv_dig_fltr_enable.setter
    def ai_conv_dig_fltr_enable(self, val):
        if self._active_devs:
            self._interpreter.set_timing_attribute_ex_bool(self._handle, self._active_devs, 0x2edc, val)
        else:
            self._interpreter.set_timing_attribute_bool(self._handle, 0x2edc, val)

    @ai_conv_dig_fltr_enable.deleter
    def ai_conv_dig_fltr_enable(self):
        if self._active_devs:
            self._interpreter.reset_timing_attribute_ex(self._handle, self._active_devs, 0x2edc)
        else:
            self._interpreter.reset_timing_attribute(self._handle, 0x2edc)

    @property
    def ai_conv_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        if self._active_devs:
            val = self._interpreter.get_timing_attribute_ex_double(self._handle, self._active_devs, 0x2edd)
        else:
            val = self._interpreter.get_timing_attribute_double(self._handle, 0x2edd)
        return val

    @ai_conv_dig_fltr_min_pulse_width.setter
    def ai_conv_dig_fltr_min_pulse_width(self, val):
        if self._active_devs:
            self._interpreter.set_timing_attribute_ex_double(self._handle, self._active_devs, 0x2edd, val)
        else:
            self._interpreter.set_timing_attribute_double(self._handle, 0x2edd, val)

    @ai_conv_dig_fltr_min_pulse_width.deleter
    def ai_conv_dig_fltr_min_pulse_width(self):
        if self._active_devs:
            self._interpreter.reset_timing_attribute_ex(self._handle, self._active_devs, 0x2edd)
        else:
            self._interpreter.reset_timing_attribute(self._handle, 0x2edd)

    @property
    def ai_conv_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the digital filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        if self._active_devs:
            val = self._interpreter.get_timing_attribute_ex_double(self._handle, self._active_devs, 0x2edf)
        else:
            val = self._interpreter.get_timing_attribute_double(self._handle, 0x2edf)
        return val

    @ai_conv_dig_fltr_timebase_rate.setter
    def ai_conv_dig_fltr_timebase_rate(self, val):
        if self._active_devs:
            self._interpreter.set_timing_attribute_ex_double(self._handle, self._active_devs, 0x2edf, val)
        else:
            self._interpreter.set_timing_attribute_double(self._handle, 0x2edf, val)

    @ai_conv_dig_fltr_timebase_rate.deleter
    def ai_conv_dig_fltr_timebase_rate(self):
        if self._active_devs:
            self._interpreter.reset_timing_attribute_ex(self._handle, self._active_devs, 0x2edf)
        else:
            self._interpreter.reset_timing_attribute(self._handle, 0x2edf)

    @property
    def ai_conv_dig_fltr_timebase_src(self):
        """
        str: Specifies the terminal of the signal to use as the timebase
            of the digital filter.
        """

        if self._active_devs:
            val = self._interpreter.get_timing_attribute_ex_string(self._handle, self._active_devs, 0x2ede)
        else:
            val = self._interpreter.get_timing_attribute_string(self._handle, 0x2ede)
        return val

    @ai_conv_dig_fltr_timebase_src.setter
    def ai_conv_dig_fltr_timebase_src(self, val):
        if self._active_devs:
            self._interpreter.set_timing_attribute_ex_string(self._handle, self._active_devs, 0x2ede, val)
        else:
            self._interpreter.set_timing_attribute_string(self._handle, 0x2ede, val)

    @ai_conv_dig_fltr_timebase_src.deleter
    def ai_conv_dig_fltr_timebase_src(self):
        if self._active_devs:
            self._interpreter.reset_timing_attribute_ex(self._handle, self._active_devs, 0x2ede)
        else:
            self._interpreter.reset_timing_attribute(self._handle, 0x2ede)

    @property
    def ai_conv_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        if self._active_devs:
            val = self._interpreter.get_timing_attribute_ex_bool(self._handle, self._active_devs, 0x2ee0)
        else:
            val = self._interpreter.get_timing_attribute_bool(self._handle, 0x2ee0)
        return val

    @ai_conv_dig_sync_enable.setter
    def ai_conv_dig_sync_enable(self, val):
        if self._active_devs:
            self._interpreter.set_timing_attribute_ex_bool(self._handle, self._active_devs, 0x2ee0, val)
        else:
            self._interpreter.set_timing_attribute_bool(self._handle, 0x2ee0, val)

    @ai_conv_dig_sync_enable.deleter
    def ai_conv_dig_sync_enable(self):
        if self._active_devs:
            self._interpreter.reset_timing_attribute_ex(self._handle, self._active_devs, 0x2ee0)
        else:
            self._interpreter.reset_timing_attribute(self._handle, 0x2ee0)

    @property
    def ai_conv_max_rate(self):
        """
        float: Indicates the maximum convert rate supported by the task,
            given the current devices and channel count.
        """

        if self._active_devs:
            val = self._interpreter.get_timing_attribute_ex_double(self._handle, self._active_devs, 0x22c9)
        else:
            val = self._interpreter.get_timing_attribute_double(self._handle, 0x22c9)
        return val

    @property
    def ai_conv_rate(self):
        """
        float: Specifies in Hertz the rate at which to clock the analog-
            to-digital converter. This clock is specific to the analog
            input section of multiplexed devices.
        """

        if self._active_devs:
            val = self._interpreter.get_timing_attribute_ex_double(self._handle, self._active_devs, 0x1848)
        else:
            val = self._interpreter.get_timing_attribute_double(self._handle, 0x1848)
        return val

    @ai_conv_rate.setter
    def ai_conv_rate(self, val):
        if self._active_devs:
            self._interpreter.set_timing_attribute_ex_double(self._handle, self._active_devs, 0x1848, val)
        else:
            self._interpreter.set_timing_attribute_double(self._handle, 0x1848, val)

    @ai_conv_rate.deleter
    def ai_conv_rate(self):
        if self._active_devs:
            self._interpreter.reset_timing_attribute_ex(self._handle, self._active_devs, 0x1848)
        else:
            self._interpreter.reset_timing_attribute(self._handle, 0x1848)

    @property
    def ai_conv_src(self):
        """
        str: Specifies the terminal of the signal to use as the AI
            Convert Clock.
        """

        if self._active_devs:
            val = self._interpreter.get_timing_attribute_ex_string(self._handle, self._active_devs, 0x1502)
        else:
            val = self._interpreter.get_timing_attribute_string(self._handle, 0x1502)
        return val

    @ai_conv_src.setter
    def ai_conv_src(self, val):
        if self._active_devs:
            self._interpreter.set_timing_attribute_ex_string(self._handle, self._active_devs, 0x1502, val)
        else:
            self._interpreter.set_timing_attribute_string(self._handle, 0x1502, val)

    @ai_conv_src.deleter
    def ai_conv_src(self):
        if self._active_devs:
            self._interpreter.reset_timing_attribute_ex(self._handle, self._active_devs, 0x1502)
        else:
            self._interpreter.reset_timing_attribute(self._handle, 0x1502)

    @property
    def ai_conv_timebase_div(self):
        """
        int: Specifies the number of AI Convert Clock Timebase pulses
            needed to produce a single AI Convert Clock pulse.
        """

        if self._active_devs:
            val = self._interpreter.get_timing_attribute_ex_uint32(self._handle, self._active_devs, 0x1335)
        else:
            val = self._interpreter.get_timing_attribute_uint32(self._handle, 0x1335)
        return val

    @ai_conv_timebase_div.setter
    def ai_conv_timebase_div(self, val):
        if self._active_devs:
            self._interpreter.set_timing_attribute_ex_uint32(self._handle, self._active_devs, 0x1335, val)
        else:
            self._interpreter.set_timing_attribute_uint32(self._handle, 0x1335, val)

    @ai_conv_timebase_div.deleter
    def ai_conv_timebase_div(self):
        if self._active_devs:
            self._interpreter.reset_timing_attribute_ex(self._handle, self._active_devs, 0x1335)
        else:
            self._interpreter.reset_timing_attribute(self._handle, 0x1335)

    @property
    def ai_conv_timebase_src(self):
        """
        :class:`nidaqmx.constants.MIOAIConvertTimebaseSource`: Specifies
            the terminal  of the signal to use as the AI Convert Clock
            Timebase.
        """

        if self._active_devs:
            val = self._interpreter.get_timing_attribute_ex_int32(self._handle, self._active_devs, 0x1339)
        else:
            val = self._interpreter.get_timing_attribute_int32(self._handle, 0x1339)
        return MIOAIConvertTimebaseSource(val)

    @ai_conv_timebase_src.setter
    def ai_conv_timebase_src(self, val):
        val = val.value
        if self._active_devs:
            self._interpreter.set_timing_attribute_ex_int32(self._handle, self._active_devs, 0x1339, val)
        else:
            self._interpreter.set_timing_attribute_int32(self._handle, 0x1339, val)

    @ai_conv_timebase_src.deleter
    def ai_conv_timebase_src(self):
        if self._active_devs:
            self._interpreter.reset_timing_attribute_ex(self._handle, self._active_devs, 0x1339)
        else:
            self._interpreter.reset_timing_attribute(self._handle, 0x1339)

    @property
    def change_detect_di_falling_edge_physical_chans(self):
        """
        :class:`nidaqmx.system.physical_channel.PhysicalChannel`:
            Specifies the names of the digital lines or ports on which
            to detect falling edges. The lines or ports must be used by
            virtual channels in the task. You also can specify a string
            that contains a list or range of digital lines or ports.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_string(self._handle, 0x2196)
        return _PhysicalChannelAlternateConstructor(val, self._interpreter)

    @change_detect_di_falling_edge_physical_chans.setter
    def change_detect_di_falling_edge_physical_chans(self, val):
        val = val.name
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_string(self._handle, 0x2196, val)

    @change_detect_di_falling_edge_physical_chans.deleter
    def change_detect_di_falling_edge_physical_chans(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x2196)

    @property
    def change_detect_di_rising_edge_physical_chans(self):
        """
        :class:`nidaqmx.system.physical_channel.PhysicalChannel`:
            Specifies the names of the digital lines or ports on which
            to detect rising edges. The lines or ports must be used by
            virtual channels in the task. You also can specify a string
            that contains a list or range of digital lines or ports.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_string(self._handle, 0x2195)
        return _PhysicalChannelAlternateConstructor(val, self._interpreter)

    @change_detect_di_rising_edge_physical_chans.setter
    def change_detect_di_rising_edge_physical_chans(self, val):
        val = val.name
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_string(self._handle, 0x2195, val)

    @change_detect_di_rising_edge_physical_chans.deleter
    def change_detect_di_rising_edge_physical_chans(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x2195)

    @property
    def change_detect_di_tristate(self):
        """
        bool: Specifies whether to tristate lines specified with
            **change_detect_di_rising_edge_physical_chans** and
            **change_detect_di_falling_edge_physical_chans** that are
            not in a virtual channel in the task. If you set this
            property to True, NI-DAQmx tristates rising/falling edge
            lines that are not in a virtual channel in the task. If you
            set this property to False, NI-DAQmx does not modify the
            configuration of rising/falling edge lines that are not in a
            virtual channel in the task, even if the lines were
            previously tristated. Set this property to False to detect
            changes on lines in other tasks or to detect changes on
            output-only lines.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_bool(self._handle, 0x2efa)
        return val

    @change_detect_di_tristate.setter
    def change_detect_di_tristate(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_bool(self._handle, 0x2efa, val)

    @change_detect_di_tristate.deleter
    def change_detect_di_tristate(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x2efa)

    @property
    def delay_from_samp_clk_delay(self):
        """
        float: Specifies the amount of time to wait after receiving a
            Sample Clock edge before beginning to acquire the sample.
            This value is in the units you specify with
            **delay_from_samp_clk_delay_units**.
        """

        if self._active_devs:
            val = self._interpreter.get_timing_attribute_ex_double(self._handle, self._active_devs, 0x1317)
        else:
            val = self._interpreter.get_timing_attribute_double(self._handle, 0x1317)
        return val

    @delay_from_samp_clk_delay.setter
    def delay_from_samp_clk_delay(self, val):
        if self._active_devs:
            self._interpreter.set_timing_attribute_ex_double(self._handle, self._active_devs, 0x1317, val)
        else:
            self._interpreter.set_timing_attribute_double(self._handle, 0x1317, val)

    @delay_from_samp_clk_delay.deleter
    def delay_from_samp_clk_delay(self):
        if self._active_devs:
            self._interpreter.reset_timing_attribute_ex(self._handle, self._active_devs, 0x1317)
        else:
            self._interpreter.reset_timing_attribute(self._handle, 0x1317)

    @property
    def delay_from_samp_clk_delay_units(self):
        """
        :class:`nidaqmx.constants.DigitalWidthUnits`: Specifies the
            units of **delay_from_samp_clk_delay**.
        """

        if self._active_devs:
            val = self._interpreter.get_timing_attribute_ex_int32(self._handle, self._active_devs, 0x1304)
        else:
            val = self._interpreter.get_timing_attribute_int32(self._handle, 0x1304)
        return DigitalWidthUnits(val)

    @delay_from_samp_clk_delay_units.setter
    def delay_from_samp_clk_delay_units(self, val):
        val = val.value
        if self._active_devs:
            self._interpreter.set_timing_attribute_ex_int32(self._handle, self._active_devs, 0x1304, val)
        else:
            self._interpreter.set_timing_attribute_int32(self._handle, 0x1304, val)

    @delay_from_samp_clk_delay_units.deleter
    def delay_from_samp_clk_delay_units(self):
        if self._active_devs:
            self._interpreter.reset_timing_attribute_ex(self._handle, self._active_devs, 0x1304)
        else:
            self._interpreter.reset_timing_attribute(self._handle, 0x1304)

    @property
    def first_samp_clk_offset(self):
        """
        float: Specifies, in seconds, the offset to apply to the
            **first_samp_clk_when** value. This offset modifies when the
            first sample clock occurs and is used to account for known
            delays in the signal path.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_double(self._handle, 0x31aa)
        return val

    @first_samp_clk_offset.setter
    def first_samp_clk_offset(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_double(self._handle, 0x31aa, val)

    @first_samp_clk_offset.deleter
    def first_samp_clk_offset(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x31aa)

    @property
    def first_samp_clk_timescale(self):
        """
        :class:`nidaqmx.constants.Timescale`: Specifies the timescale to
            be used for the value of **first_samp_clk_when**.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_int32(self._handle, 0x3183)
        return Timescale(val)

    @first_samp_clk_timescale.setter
    def first_samp_clk_timescale(self, val):
        val = val.value
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_int32(self._handle, 0x3183, val)

    @first_samp_clk_timescale.deleter
    def first_samp_clk_timescale(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x3183)

    @property
    def first_samp_clk_when(self):
        """
        datetime: Specifies the time of the first sample clock pulse.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_timestamp(self._handle, 0x3182)
        return val

    @first_samp_clk_when.setter
    def first_samp_clk_when(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_timestamp(self._handle, 0x3182, val)

    @first_samp_clk_when.deleter
    def first_samp_clk_when(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x3182)

    @property
    def first_samp_timestamp_enable(self):
        """
        bool: Specifies whether to enable the first sample timestamp.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_bool(self._handle, 0x3139)
        return val

    @first_samp_timestamp_enable.setter
    def first_samp_timestamp_enable(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_bool(self._handle, 0x3139, val)

    @first_samp_timestamp_enable.deleter
    def first_samp_timestamp_enable(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x3139)

    @property
    def first_samp_timestamp_timescale(self):
        """
        :class:`nidaqmx.constants.Timescale`: Specifies the timescale to
            be used for the first sample timestamp.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_int32(self._handle, 0x313b)
        return Timescale(val)

    @first_samp_timestamp_timescale.setter
    def first_samp_timestamp_timescale(self, val):
        val = val.value
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_int32(self._handle, 0x313b, val)

    @first_samp_timestamp_timescale.deleter
    def first_samp_timestamp_timescale(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x313b)

    @property
    def first_samp_timestamp_val(self):
        """
        datetime: Indicates the timestamp of the first sample.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_timestamp(self._handle, 0x313a)
        return val

    @property
    def hshk_delay_after_xfer(self):
        """
        float: Specifies the number of seconds to wait after a handshake
            cycle before starting a new handshake cycle.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_double(self._handle, 0x22c2)
        return val

    @hshk_delay_after_xfer.setter
    def hshk_delay_after_xfer(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_double(self._handle, 0x22c2, val)

    @hshk_delay_after_xfer.deleter
    def hshk_delay_after_xfer(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x22c2)

    @property
    def hshk_sample_input_data_when(self):
        """
        :class:`nidaqmx.constants.SampleInputDataWhen`: Specifies on
            which edge of the Handshake Trigger an input task latches
            the data from the peripheral device.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_int32(self._handle, 0x22c4)
        return SampleInputDataWhen(val)

    @hshk_sample_input_data_when.setter
    def hshk_sample_input_data_when(self, val):
        val = val.value
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_int32(self._handle, 0x22c4, val)

    @hshk_sample_input_data_when.deleter
    def hshk_sample_input_data_when(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x22c4)

    @property
    def hshk_start_cond(self):
        """
        :class:`nidaqmx.constants.HandshakeStartCondition`: Specifies
            the point in the handshake cycle that the device is in when
            the task starts.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_int32(self._handle, 0x22c3)
        return HandshakeStartCondition(val)

    @hshk_start_cond.setter
    def hshk_start_cond(self, val):
        val = val.value
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_int32(self._handle, 0x22c3, val)

    @hshk_start_cond.deleter
    def hshk_start_cond(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x22c3)

    @property
    def implicit_underflow_behavior(self):
        """
        :class:`nidaqmx.constants.UnderflowBehavior`: Specifies the
            action to take when the onboard memory of the device becomes
            empty.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_int32(self._handle, 0x2efd)
        return UnderflowBehavior(val)

    @implicit_underflow_behavior.setter
    def implicit_underflow_behavior(self, val):
        val = val.value
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_int32(self._handle, 0x2efd, val)

    @implicit_underflow_behavior.deleter
    def implicit_underflow_behavior(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x2efd)

    @property
    def master_timebase_rate(self):
        """
        float: Specifies the rate of the Master Timebase.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_double(self._handle, 0x1495)
        return val

    @master_timebase_rate.setter
    def master_timebase_rate(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_double(self._handle, 0x1495, val)

    @master_timebase_rate.deleter
    def master_timebase_rate(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x1495)

    @property
    def master_timebase_src(self):
        """
        str: Specifies the terminal of the signal to use as the Master
            Timebase. On an E Series device, you can choose only between
            the onboard 20MHz Timebase or the RTSI7 terminal.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_string(self._handle, 0x1343)
        return val

    @master_timebase_src.setter
    def master_timebase_src(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_string(self._handle, 0x1343, val)

    @master_timebase_src.deleter
    def master_timebase_src(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x1343)

    @property
    def ref_clk_rate(self):
        """
        float: Specifies the frequency of the Reference Clock.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_double(self._handle, 0x1315)
        return val

    @ref_clk_rate.setter
    def ref_clk_rate(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_double(self._handle, 0x1315, val)

    @ref_clk_rate.deleter
    def ref_clk_rate(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x1315)

    @property
    def ref_clk_src(self):
        """
        str: Specifies the terminal of the signal to use as the
            Reference Clock.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_string(self._handle, 0x1316)
        return val

    @ref_clk_src.setter
    def ref_clk_src(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_string(self._handle, 0x1316, val)

    @ref_clk_src.deleter
    def ref_clk_src(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x1316)

    @property
    def samp_clk_active_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies on which edge of a
            clock pulse sampling takes place. This property is useful
            primarily when the signal you use as the Sample Clock is not
            a periodic clock.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_int32(self._handle, 0x1301)
        return Edge(val)

    @samp_clk_active_edge.setter
    def samp_clk_active_edge(self, val):
        val = val.value
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_int32(self._handle, 0x1301, val)

    @samp_clk_active_edge.deleter
    def samp_clk_active_edge(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x1301)

    @property
    def samp_clk_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_bool(self._handle, 0x221e)
        return val

    @samp_clk_dig_fltr_enable.setter
    def samp_clk_dig_fltr_enable(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_bool(self._handle, 0x221e, val)

    @samp_clk_dig_fltr_enable.deleter
    def samp_clk_dig_fltr_enable(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x221e)

    @property
    def samp_clk_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_double(self._handle, 0x221f)
        return val

    @samp_clk_dig_fltr_min_pulse_width.setter
    def samp_clk_dig_fltr_min_pulse_width(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_double(self._handle, 0x221f, val)

    @samp_clk_dig_fltr_min_pulse_width.deleter
    def samp_clk_dig_fltr_min_pulse_width(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x221f)

    @property
    def samp_clk_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_double(self._handle, 0x2221)
        return val

    @samp_clk_dig_fltr_timebase_rate.setter
    def samp_clk_dig_fltr_timebase_rate(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_double(self._handle, 0x2221, val)

    @samp_clk_dig_fltr_timebase_rate.deleter
    def samp_clk_dig_fltr_timebase_rate(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x2221)

    @property
    def samp_clk_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_string(self._handle, 0x2220)
        return val

    @samp_clk_dig_fltr_timebase_src.setter
    def samp_clk_dig_fltr_timebase_src(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_string(self._handle, 0x2220, val)

    @samp_clk_dig_fltr_timebase_src.deleter
    def samp_clk_dig_fltr_timebase_src(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x2220)

    @property
    def samp_clk_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_bool(self._handle, 0x2222)
        return val

    @samp_clk_dig_sync_enable.setter
    def samp_clk_dig_sync_enable(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_bool(self._handle, 0x2222, val)

    @samp_clk_dig_sync_enable.deleter
    def samp_clk_dig_sync_enable(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x2222)

    @property
    def samp_clk_max_rate(self):
        """
        float: Indicates the maximum Sample Clock rate supported by the
            task, based on other timing settings. For output tasks, the
            maximum Sample Clock rate is the maximum rate of the DAC.
            For input tasks, NI-DAQmx calculates the maximum sampling
            rate differently for multiplexed devices than simultaneous
            sampling devices.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_double(self._handle, 0x22c8)
        return val

    @property
    def samp_clk_overrun_behavior(self):
        """
        :class:`nidaqmx.constants.OverflowBehavior`: Specifies the
            action to take if Sample Clock edges occur faster than the
            device can handle them.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_int32(self._handle, 0x2efc)
        return OverflowBehavior(val)

    @samp_clk_overrun_behavior.setter
    def samp_clk_overrun_behavior(self, val):
        val = val.value
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_int32(self._handle, 0x2efc, val)

    @samp_clk_overrun_behavior.deleter
    def samp_clk_overrun_behavior(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x2efc)

    @property
    def samp_clk_rate(self):
        """
        float: Specifies the sampling rate in samples per channel per
            second. If you use an external source for the Sample Clock,
            set this input to the maximum expected rate of that clock.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_double(self._handle, 0x1344)
        return val

    @samp_clk_rate.setter
    def samp_clk_rate(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_double(self._handle, 0x1344, val)

    @samp_clk_rate.deleter
    def samp_clk_rate(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x1344)

    @property
    def samp_clk_src(self):
        """
        str: Specifies the terminal of the signal to use as the Sample
            Clock.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_string(self._handle, 0x1852)
        return val

    @samp_clk_src.setter
    def samp_clk_src(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_string(self._handle, 0x1852, val)

    @samp_clk_src.deleter
    def samp_clk_src(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x1852)

    @property
    def samp_clk_term(self):
        """
        str: Indicates the name of the internal Sample Clock terminal
            for the task. This property does not return the name of the
            Sample Clock source terminal specified with
            **samp_clk_src**.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_string(self._handle, 0x2f1b)
        return val

    @property
    def samp_clk_timebase_active_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies on which edge to
            recognize a Sample Clock Timebase pulse. This property is
            useful primarily when the signal you use as the Sample Clock
            Timebase is not a periodic clock.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_int32(self._handle, 0x18ec)
        return Edge(val)

    @samp_clk_timebase_active_edge.setter
    def samp_clk_timebase_active_edge(self, val):
        val = val.value
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_int32(self._handle, 0x18ec, val)

    @samp_clk_timebase_active_edge.deleter
    def samp_clk_timebase_active_edge(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x18ec)

    @property
    def samp_clk_timebase_div(self):
        """
        int: Specifies the number of Sample Clock Timebase pulses needed
            to produce a single Sample Clock pulse.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_uint32(self._handle, 0x18eb)
        return val

    @samp_clk_timebase_div.setter
    def samp_clk_timebase_div(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_uint32(self._handle, 0x18eb, val)

    @samp_clk_timebase_div.deleter
    def samp_clk_timebase_div(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x18eb)

    @property
    def samp_clk_timebase_master_timebase_div(self):
        """
        int: Specifies the number of pulses of the Master Timebase
            needed to produce a single pulse of the Sample Clock
            Timebase.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_uint32(self._handle, 0x1305)
        return val

    @samp_clk_timebase_master_timebase_div.setter
    def samp_clk_timebase_master_timebase_div(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_uint32(self._handle, 0x1305, val)

    @samp_clk_timebase_master_timebase_div.deleter
    def samp_clk_timebase_master_timebase_div(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x1305)

    @property
    def samp_clk_timebase_rate(self):
        """
        float: Specifies the rate of the Sample Clock Timebase. Some
            applications require that you specify a rate when you use
            any signal other than the onboard Sample Clock Timebase. NI-
            DAQmx requires this rate to calculate other timing
            parameters.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_double(self._handle, 0x1303)
        return val

    @samp_clk_timebase_rate.setter
    def samp_clk_timebase_rate(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_double(self._handle, 0x1303, val)

    @samp_clk_timebase_rate.deleter
    def samp_clk_timebase_rate(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x1303)

    @property
    def samp_clk_timebase_src(self):
        """
        str: Specifies the terminal of the signal to use as the Sample
            Clock Timebase.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_string(self._handle, 0x1308)
        return val

    @samp_clk_timebase_src.setter
    def samp_clk_timebase_src(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_string(self._handle, 0x1308, val)

    @samp_clk_timebase_src.deleter
    def samp_clk_timebase_src(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x1308)

    @property
    def samp_clk_timebase_term(self):
        """
        str: Indicates the name of the internal Sample Clock Timebase
            terminal for the task. This property does not return the
            name of the Sample Clock Timebase source terminal specified
            with **samp_clk_timebase_src**.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_string(self._handle, 0x2f1c)
        return val

    @property
    def samp_clk_underflow_behavior(self):
        """
        :class:`nidaqmx.constants.UnderflowBehavior`: Specifies the
            action to take when the onboard memory of the device becomes
            empty. In either case, the sample clock does not stop.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_int32(self._handle, 0x2961)
        return UnderflowBehavior(val)

    @samp_clk_underflow_behavior.setter
    def samp_clk_underflow_behavior(self, val):
        val = val.value
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_int32(self._handle, 0x2961, val)

    @samp_clk_underflow_behavior.deleter
    def samp_clk_underflow_behavior(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x2961)

    @property
    def samp_clk_write_wfm_use_initial_wfm_dt(self):
        """
        bool: Specifies that the value of **samp_clk_rate** will be
            determined by the dt component of the initial DAQmx Write
            waveform input for Output tasks.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_bool(self._handle, 0x30fc)
        return val

    @samp_clk_write_wfm_use_initial_wfm_dt.setter
    def samp_clk_write_wfm_use_initial_wfm_dt(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_bool(self._handle, 0x30fc, val)

    @samp_clk_write_wfm_use_initial_wfm_dt.deleter
    def samp_clk_write_wfm_use_initial_wfm_dt(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x30fc)

    @property
    def samp_quant_samp_mode(self):
        """
        :class:`nidaqmx.constants.AcquisitionType`: Specifies if a task
            acquires or generates a finite number of samples or if it
            continuously acquires or generates samples.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_int32(self._handle, 0x1300)
        return AcquisitionType(val)

    @samp_quant_samp_mode.setter
    def samp_quant_samp_mode(self, val):
        val = val.value
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_int32(self._handle, 0x1300, val)

    @samp_quant_samp_mode.deleter
    def samp_quant_samp_mode(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x1300)

    @property
    def samp_quant_samp_per_chan(self):
        """
        int: Specifies the number of samples to acquire or generate for
            each channel if **samp_quant_samp_mode** is
            **AcquisitionType.FINITE**. If **samp_quant_samp_mode** is
            **AcquisitionType.CONTINUOUS**, NI-DAQmx uses this value to
            determine the buffer size.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_uint64(self._handle, 0x1310)
        return val

    @samp_quant_samp_per_chan.setter
    def samp_quant_samp_per_chan(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_uint64(self._handle, 0x1310, val)

    @samp_quant_samp_per_chan.deleter
    def samp_quant_samp_per_chan(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x1310)

    @property
    def samp_timing_engine(self):
        """
        int: Specifies which timing engine to use for the task.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_uint32(self._handle, 0x2a26)
        return val

    @samp_timing_engine.setter
    def samp_timing_engine(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_uint32(self._handle, 0x2a26, val)

    @samp_timing_engine.deleter
    def samp_timing_engine(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x2a26)

    @property
    def samp_timing_type(self):
        """
        :class:`nidaqmx.constants.SampleTimingType`: Specifies the type
            of sample timing to use for the task.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_int32(self._handle, 0x1347)
        return SampleTimingType(val)

    @samp_timing_type.setter
    def samp_timing_type(self, val):
        val = val.value
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_int32(self._handle, 0x1347, val)

    @samp_timing_type.deleter
    def samp_timing_type(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x1347)

    @property
    def simultaneous_ao_enable(self):
        """
        bool: Specifies whether to update all channels in the task
            simultaneously, rather than updating channels independently
            when you write a sample to that channel.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_bool(self._handle, 0x21a0)
        return val

    @simultaneous_ao_enable.setter
    def simultaneous_ao_enable(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_bool(self._handle, 0x21a0, val)

    @simultaneous_ao_enable.deleter
    def simultaneous_ao_enable(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x21a0)

    @property
    def sync_clk_interval(self):
        """
        int: Specifies the interval, in Sample Clock periods, between
            each internal Synchronization Clock pulse. NI-DAQmx uses
            this pulse for synchronization of triggers between multiple
            devices at different rates. Refer to device documentation
            for information about how to calculate this value.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_uint32(self._handle, 0x2f7e)
        return val

    @sync_clk_interval.setter
    def sync_clk_interval(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_uint32(self._handle, 0x2f7e, val)

    @sync_clk_interval.deleter
    def sync_clk_interval(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x2f7e)

    @property
    def sync_pulse_min_delay_to_start(self):
        """
        float: Specifies in seconds the amount of time that elapses
            after the master device issues the synchronization pulse
            before the task starts.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_double(self._handle, 0x223f)
        return val

    @sync_pulse_min_delay_to_start.setter
    def sync_pulse_min_delay_to_start(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_double(self._handle, 0x223f, val)

    @sync_pulse_min_delay_to_start.deleter
    def sync_pulse_min_delay_to_start(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x223f)

    @property
    def sync_pulse_reset_delay(self):
        """
        float: Specifies in seconds the amount of time to wait after the
            Synchronization Pulse before resetting the ADCs or DACs on
            the device. When synchronizing devices, query
            **sync_pulse_reset_time** on all devices and note the
            largest reset time. Then, for each device, subtract the
            reset time from the largest reset time and set this property
            to the resulting value.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_double(self._handle, 0x2f7d)
        return val

    @sync_pulse_reset_delay.setter
    def sync_pulse_reset_delay(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_double(self._handle, 0x2f7d, val)

    @sync_pulse_reset_delay.deleter
    def sync_pulse_reset_delay(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x2f7d)

    @property
    def sync_pulse_reset_time(self):
        """
        float: Indicates in seconds the amount of time required for the
            ADCs or DACs on the device to reset. When synchronizing
            devices, query this property on all devices and note the
            largest reset time. Then, for each device, subtract the
            value of this property from the largest reset time and set
            **sync_pulse_reset_delay** to the resulting value.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_double(self._handle, 0x2f7c)
        return val

    @property
    def sync_pulse_src(self):
        """
        str: Specifies the terminal of the signal to use as the
            synchronization pulse. The synchronization pulse resets the
            clock dividers and the ADCs/DACs on the device.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_string(self._handle, 0x223d)
        return val

    @sync_pulse_src.setter
    def sync_pulse_src(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_string(self._handle, 0x223d, val)

    @sync_pulse_src.deleter
    def sync_pulse_src(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x223d)

    @property
    def sync_pulse_sync_time(self):
        """
        float: Indicates in seconds the delay required to reset the
            ADCs/DACs after the device receives the synchronization
            pulse.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_double(self._handle, 0x223e)
        return val

    @property
    def sync_pulse_term(self):
        """
        str: Indicates the name of the internal Synchronization Pulse
            terminal for the task. This property does not return the
            name of the source terminal.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_string(self._handle, 0x2f85)
        return val

    @property
    def sync_pulse_time_timescale(self):
        """
        :class:`nidaqmx.constants.Timescale`: Specifies the timescale to
            be used for timestamps for a sync pulse.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_int32(self._handle, 0x3138)
        return Timescale(val)

    @sync_pulse_time_timescale.setter
    def sync_pulse_time_timescale(self, val):
        val = val.value
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_int32(self._handle, 0x3138, val)

    @sync_pulse_time_timescale.deleter
    def sync_pulse_time_timescale(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x3138)

    @property
    def sync_pulse_time_when(self):
        """
        datetime: Specifies the start time of the sync pulse.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_timestamp(self._handle, 0x3137)
        return val

    @sync_pulse_time_when.setter
    def sync_pulse_time_when(self, val):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_timestamp(self._handle, 0x3137, val)

    @sync_pulse_time_when.deleter
    def sync_pulse_time_when(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x3137)

    @property
    def sync_pulse_type(self):
        """
        :class:`nidaqmx.constants.SyncPulseType`: Specifies the type of
            sync pulse used in the task.
        """

        if self._active_devs:
            self._raise_device_context_not_supported_error()
        val = self._interpreter.get_timing_attribute_int32(self._handle, 0x3136)
        return SyncPulseType(val)

    @sync_pulse_type.setter
    def sync_pulse_type(self, val):
        val = val.value
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.set_timing_attribute_int32(self._handle, 0x3136, val)

    @sync_pulse_type.deleter
    def sync_pulse_type(self):
        if self._active_devs:
            self._raise_device_context_not_supported_error()
        self._interpreter.reset_timing_attribute(self._handle, 0x3136)

    def cfg_burst_handshaking_timing_export_clock(
            self, sample_clk_rate, sample_clk_outp_term,
            sample_mode=AcquisitionType.FINITE, samps_per_chan=1000,
            sample_clk_pulse_polarity=Polarity.ACTIVE_HIGH,
            pause_when=Level.HIGH,
            ready_event_active_level=Polarity.ACTIVE_HIGH):
        """
        Configures when the DAQ device transfers data to a peripheral
        device, using the onboard Sample Clock of the DAQ device to
        control burst handshake timing and exporting that clock for use
        by the peripheral device.

        Args:
            sample_clk_rate (float): Specifies in hertz the rate of the
                Sample Clock.
            sample_clk_outp_term (str): Specifies the terminal to which
                to export the Sample Clock.
            sample_mode (Optional[nidaqmx.constants.AcquisitionType]): 
                Specifies if the task acquires or generates samples
                continuously or if it acquires or generates a finite
                number of samples.
            samps_per_chan (Optional[int]): Specifies the number of
                samples to acquire or generate for each channel in the
                task if **sample_mode** is **FINITE_SAMPLES**. If
                **sample_mode** is **CONTINUOUS_SAMPLES**, NI-DAQmx uses
                this value to determine the buffer size. This function
                returns an error if the specified value is negative.
            sample_clk_pulse_polarity (Optional[nidaqmx.constants.Polarity]): 
                Specifies the polarity of the exported Sample Clock.
            pause_when (Optional[nidaqmx.constants.Level]): Specifies
                whether the task pauses while the trigger signal is high
                or low.
            ready_event_active_level (Optional[nidaqmx.constants.Polarity]): 
                Specifies the polarity of the Ready for Transfer Event.
        """

        self._interpreter.cfg_burst_handshaking_timing_export_clock(
            self._handle, sample_clk_rate, sample_clk_outp_term,
            sample_mode.value, samps_per_chan,
            sample_clk_pulse_polarity.value, pause_when.value,
            ready_event_active_level.value)

    def cfg_burst_handshaking_timing_import_clock(
            self, sample_clk_rate, sample_clk_src,
            sample_mode=AcquisitionType.FINITE, samps_per_chan=1000,
            sample_clk_active_edge=Edge.RISING, pause_when=Level.HIGH,
            ready_event_active_level=Polarity.ACTIVE_HIGH):
        """
        Configures when the DAQ device transfers data to a peripheral
        device, using an imported sample clock to control burst
        handshake timing.

        Args:
            sample_clk_rate (float): Specifies in hertz the rate of the
                Sample Clock.
            sample_clk_src (str): Specifies the source terminal of the
                Sample Clock. Leave this input unspecified to use the
                default onboard clock of the device.
            sample_mode (Optional[nidaqmx.constants.AcquisitionType]): 
                Specifies if the task acquires or generates samples
                continuously or if it acquires or generates a finite
                number of samples.
            samps_per_chan (Optional[int]): Specifies the number of
                samples to acquire or generate for each channel in the
                task if **sample_mode** is **FINITE_SAMPLES**. If
                **sample_mode** is **CONTINUOUS_SAMPLES**, NI-DAQmx uses
                this value to determine the buffer size. This function
                returns an error if the specified value is negative.
            sample_clk_active_edge (Optional[nidaqmx.constants.Edge]): 
                Specifies on which edges of Sample Clock pulses to
                acquire or generate samples.
            pause_when (Optional[nidaqmx.constants.Level]): Specifies
                whether the task pauses while the trigger signal is high
                or low.
            ready_event_active_level (Optional[nidaqmx.constants.Polarity]): 
                Specifies the polarity of the Ready for Transfer Event.
        """

        self._interpreter.cfg_burst_handshaking_timing_import_clock(
            self._handle, sample_clk_rate, sample_clk_src, sample_mode.value,
            samps_per_chan, sample_clk_active_edge.value, pause_when.value,
            ready_event_active_level.value)

    def cfg_change_detection_timing(
            self, rising_edge_chan="", falling_edge_chan="",
            sample_mode=AcquisitionType.FINITE, samps_per_chan=1000):
        """
        Configures the task to acquire samples on the rising and/or
        falling edges of the lines or ports you specify. To detect both
        rising and falling edges on a line or port, specify the name of
        that line or port to both **rising_edge_chan** and
        **falling_edge_chan**.

        Args:
            rising_edge_chan (Optional[str]): Specifies the names of the
                digital lines or ports on which to detect rising edges.
                The DAQmx physical channel constant lists all lines and
                ports for devices installed in your system.
            falling_edge_chan (Optional[str]): Specifies the names of
                the digital lines or ports on which to detect falling
                edges. The DAQmx physical channel constant lists all
                lines and ports for devices installed in your system.
            sample_mode (Optional[nidaqmx.constants.AcquisitionType]): 
                Specifies if the task acquires samples continuously or
                if it acquires a finite number of samples.
            samps_per_chan (Optional[int]): Specifies the number of
                samples to acquire from each channel in the task if
                **sample_mode** is **FINITE_SAMPLES**. This function
                returns an error if the specified value is negative.
        """

        self._interpreter.cfg_change_detection_timing(
            self._handle, rising_edge_chan, falling_edge_chan,
            sample_mode.value, samps_per_chan)

    def cfg_handshaking_timing(
            self, sample_mode=AcquisitionType.FINITE, samps_per_chan=1000):
        """
        Determines the number of digital samples to acquire or generate
        using digital handshaking between the device and a peripheral
        device.

        Args:
            sample_mode (Optional[nidaqmx.constants.AcquisitionType]): 
                Specifies if the task acquires or generates samples
                continuously or if it acquires or generates a finite
                number of samples.
            samps_per_chan (Optional[int]): Specifies the number of
                samples to acquire or generate for each channel in the
                task if **sample_mode** is **FINITE_SAMPLES**. If
                **sample_mode** is **CONTINUOUS_SAMPLES**, NI-DAQmx uses
                this value to determine the buffer size. This function
                returns an error if the specified value is negative.
        """

        self._interpreter.cfg_handshaking_timing(
            self._handle, sample_mode.value, samps_per_chan)

    def cfg_implicit_timing(
            self, sample_mode=AcquisitionType.FINITE, samps_per_chan=1000):
        """
        Sets only the number of samples to acquire or generate without
        specifying timing. Typically, you should use this instance when
        the task does not require sample timing, such as tasks that use
        counters for buffered frequency measurement, buffered period
        measurement, or pulse train generation. For finite counter
        output tasks, **samps_per_chan** is the number of pulses to
        generate.

        Args:
            sample_mode (Optional[nidaqmx.constants.AcquisitionType]): 
                Specifies if the task acquires or generates samples
                continuously or if it acquires or generates a finite
                number of samples.
            samps_per_chan (Optional[int]): Specifies the number of
                samples to acquire or generate for each channel in the
                task if **sample_mode** is **FINITE_SAMPLES**. If
                **sample_mode** is **CONTINUOUS_SAMPLES**, NI-DAQmx uses
                this value to determine the buffer size. This function
                returns an error if the specified value is negative.
        """

        self._interpreter.cfg_implicit_timing(
            self._handle, sample_mode.value, samps_per_chan)

    def cfg_pipelined_samp_clk_timing(
            self, rate, source="", active_edge=Edge.RISING,
            sample_mode=AcquisitionType.FINITE, samps_per_chan=1000):
        """
        Sets the source of the Sample Clock, the rate of the Sample
        Clock, and the number of samples to acquire or generate. The
        device acquires or generates samples on each Sample Clock edge,
        but it does not respond to certain triggers until a few Sample
        Clock edges later. Pipelining allows higher data transfer rates
        at the cost of increased trigger response latency. Refer to the
        device documentation for information about which triggers
        pipelining affects.  This timing type allows handshaking using
        the Pause trigger and either the Ready for Transfer event or the
        Data Active event. Refer to the device documentation for more
        information.  This timing type is supported only by the NI 6536
        and NI 6537.

        Args:
            rate (float): Specifies the sampling rate in samples per
                channel per second. If you use an external source for
                the Sample Clock, set this input to the maximum expected
                rate of that clock.
            source (Optional[str]): Specifies the source terminal of the
                Sample Clock. Leave this input unspecified to use the
                default onboard clock of the device.
            active_edge (Optional[nidaqmx.constants.Edge]): Specifies on
                which edges of Sample Clock pulses to acquire or
                generate samples.
            sample_mode (Optional[nidaqmx.constants.AcquisitionType]): 
                Specifies if the task acquires or generates samples
                continuously or if it acquires or generates a finite
                number of samples.
            samps_per_chan (Optional[int]): Specifies the number of
                samples to acquire or generate for each channel in the
                task if **sample_mode** is **FINITE_SAMPLES**. If
                **sample_mode** is **CONTINUOUS_SAMPLES**, NI-DAQmx uses
                this value to determine the buffer size. This function
                returns an error if the specified value is negative.
        """

        self._interpreter.cfg_pipelined_samp_clk_timing(
            self._handle, rate, source, active_edge.value, sample_mode.value,
            samps_per_chan)

    def cfg_samp_clk_timing(
            self, rate, source="", active_edge=Edge.RISING,
            sample_mode=AcquisitionType.FINITE, samps_per_chan=1000):
        """
        Sets the source of the Sample Clock, the rate of the Sample
        Clock, and the number of samples to acquire or generate.

        Args:
            rate (float): Specifies the sampling rate in samples per
                channel per second. If you use an external source for
                the Sample Clock, set this input to the maximum expected
                rate of that clock.
            source (Optional[str]): Specifies the source terminal of the
                Sample Clock. Leave this input unspecified to use the
                default onboard clock of the device.
            active_edge (Optional[nidaqmx.constants.Edge]): Specifies on
                which edges of Sample Clock pulses to acquire or
                generate samples.
            sample_mode (Optional[nidaqmx.constants.AcquisitionType]): 
                Specifies if the task acquires or generates samples
                continuously or if it acquires or generates a finite
                number of samples.
            samps_per_chan (Optional[int]): Specifies the number of
                samples to acquire or generate for each channel in the
                task if **sample_mode** is **FINITE_SAMPLES**. If
                **sample_mode** is **CONTINUOUS_SAMPLES**, NI-DAQmx uses
                this value to determine the buffer size. This function
                returns an error if the specified value is negative.
        """

        self._interpreter.cfg_samp_clk_timing(
            self._handle, rate, source, active_edge.value, sample_mode.value,
            samps_per_chan)

