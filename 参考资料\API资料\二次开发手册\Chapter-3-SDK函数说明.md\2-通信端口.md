3.2 通信端口 
 
3.2.1 
MT_Close_UART 
功能描述：关闭串口通信口，在需要切换通信端口、退出软件或者释放占用的串口时调用 
 
VC 
INT32 
MT_Close_UART 
(void) 
VB 
Function MT_Close_UART () As Long 
Delphi 
function MT_Close_UART():Integer; 
C# 
public static 
extern int MT_Close_UART(); 
输入参数 
无 
 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
 
 
3.2.2 MT_Open_UART 
功能描述：打开串口通信口，在需要通过串口和板卡通信前调用 
 
VC 
INT32 
MT_Open_UART 
(char* sCOM) 
VB 
Function MT_Open_UART (ByVal sCOM As String) As Long 
Delphi 
function MT_Open_UART(sCOM:PAnsiChar):Integer; 
C# 
public 
static extern int MT_Open_UART(string sCOM); 
输入参数 
sCOM 
字符串，串口名称，例如COM1，COM2 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
函数执行成功不代表串口已经能工作，只是完成了相关的通信初始化工作 
 
 
3.2.3 MT_Close_USB 
功能描述：关闭USB通信口，在需要切换通信端口或者退出软件时调用, 
 
VC 
INT32 
MT_Close_USB 
(void) 
VB 
Function MT_Close_USB 
() As Long 
Delphi 
function MT_Close_USB():Integer; 
C# 
public static extern int 
MT_Close_USB(); 
输入参数 
无 
 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
 
 
3.2.4 MT_Open_USB 
功能描述：打开USB通信口，在需要通过USB和板卡通信前调用 
 
VC 
INT32 
MT_Open_USB 
(void) 
VB 
Function MT_Open_USB 
() As Long 
Delphi 
function MT_Open_USB():Integer; 
C# 
public static extern int 
MT_Open_USB(); 
输入参数 
无 
 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
函数执行成功不代表USB已经能工作，只是完成了相关的通信初始化工作 
 
3.2.5 
MT_Close_Net 
功能描述：关闭网络通信口，在需要切换通信端口或者退出软件时调用, 
 
VC 
INT32 
MT_Close_Net 
(void) 
VB 
Function MT_Close_Net 
() As Long 
Delphi 
function MT_Close_Net():Integer; 
C# 
public static extern int 
MT_Close_Net(); 
输入参数 
无 
 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
 
 
3.2.6 MT_Open_Net 
功能描述：打开网络通信口，在需要通过网络和板卡通信前调用 

VC 
INT32 
MT_Open_USB 
(BYTE IP0,BYTE IP1,BYTE IP2,BYTE IP3,WORD IPPort) 
VB 
Function MT_Open_USB 
(ByVal 
IP0 As Byte, ByVal 
IP1 As Byte, 
ByVal 
IP2 As Byte, 
ByVal 
IP3 As Byte, 
ByVal 
IPPort As Integer) As Long 
Delphi 
function MT_Open_USB(IP0,IP1,IP2,IP3:Byte;IPPort:Word):Integer; 
C# 
public static extern int 
MT_Open_USB(BYTE IP0,BYTE IP1,BYTE IP2,BYTE IP3,WORD 
IPPort); 
输入参数 
IP0,IP1,IP2,IP3 
IP地址,例如*********** 
 
IPPort 
端口地址,例如8888 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
函数执行成功不代表网络已经能工作，只是完成了相关的通信初始化工作 
 
 
 
