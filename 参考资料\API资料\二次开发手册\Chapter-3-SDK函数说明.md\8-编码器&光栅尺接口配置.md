3.8 编码器/光栅尺接口配置 
 
3.8.1 MT_Set_Encoder_Z_Polarity 
功能描述：设置编码器/光栅尺接口Z信号的电平定义,一般情况下无需设置 
 
VC 
INT32 
MT_Set_Encoder_Z_Polarity 
(WORD AObj,INT32 
Value); 
VB 
Function 
MT_Set_Encoder_Z_Polarity 
(ByVal AObj As Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function 
MT_Set_Encoder_Z_Polarity 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Encoder_Z_Polarity 
(UInt16 AObj,Int32 Value); 
输入参数 
AObj 
编码器/光栅尺接口序号 
输出参数 
Value 
0:正常电平 
1：反向电平 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
编码器/光栅尺接口的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
编码器/光栅尺接口序号的函数需要注意不要越界 
 
3.8.2 MT_Set_Encoder_Dir_Polarity 
功能描述：设置编码器/光栅尺接口计数方向，一般情况下无需设置 
 
VC 
INT32 
MT_Set_Encoder_Dir_Polarity 
(WORD AObj,INT32 
Value); 
VB 
Function 
MT_Set_Encoder_Dir_Polarity 
(ByVal AObj As Integer, ByVal 
Value As Long) 
As Long 
Delphi 
function 
MT_Set_Encoder_Z_Polarity 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Encoder_Z_Polarity 
(UInt16 AObj,Int32 Value); 
输入参数 
AObj 
编码器/光栅尺接口序号 
输出参数 
Value 
0：正常方向 
1：反向方向 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
编码器/光栅尺接口的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
编码器/光栅尺接口序号的函数需要注意不要越界 
 
