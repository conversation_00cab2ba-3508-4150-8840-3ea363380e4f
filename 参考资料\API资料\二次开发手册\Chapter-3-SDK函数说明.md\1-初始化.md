3.1 初始化 

3.1.1 
MT_Init 
 
功能描述：创建Dll工作需要的资源，在使用其它函数前必须调用本函数，一般放在软件的初始化部分执行 
 
VC 
INT32 MT_Init(void) 
VB 
Function MT_Init () As Long 
Delphi 
function MT_Init():Integer; 
C# 
public static extern int MT_Init(); 
输入参数 
无 
 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
 
 
3.1.2 MT_DeInit 
 
功能描述：释放Dll工作需要的资源，在软件退出时执行 
 
VC 
INT32 
MT_DeInit 
(void) 
VB 
Function 
MT_DeInit 
() As Long 
Delphi 
function 
MT_DeInit 
():Integer; 
C# 
public static extern int 
MT_DeInit 
(); 
输入参数 
无 
 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
 
3.1.3 
MT_Get_Dll_Version 
 
功能描述：读取本Dll的版本号，也可以通过Windows资源管理器查看Dll版本号,一般不使用，做兼容性时的判断依据 
 
VC 
INT32 
MT_Get_Dll_Version(char** sVer) 
VB 
Function MT_Get_Dll_Version (ByRef sVer As String) As Long 
Delphi 
function MT_Get_Dll_Version(sVer:PPChar):Integer; 
C# 
public static extern int MT_Get_Dll_Version(ref String 
sVer); 
输入参数 
无 
 
输出参数 
sVer 
版本号字符串 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
 
