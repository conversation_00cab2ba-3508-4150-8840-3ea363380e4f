name: Build

on:
  workflow_call:
  workflow_dispatch:
jobs:
  build:
    name: Build
    runs-on:
      - ubuntu-latest
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Set up Python
        uses: ni/python-actions/setup-python@5286c12d65d90b2ea738bd57d452dc4366497581 # v0.4.1
        id: setup-python
        with:
          # The codegen scripts require Python 3.9 or later.
          python-version: "3.9"
      - name: Set up Poetry
        uses: ni/python-actions/setup-poetry@5286c12d65d90b2ea738bd57d452dc4366497581 # v0.4.1
      - name: Cache virtualenv (all extras)
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: .venv
          key: nidaqmx-all-extras-${{ runner.os }}-py${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('poetry.lock') }}
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          poetry install --all-extras
      - name: Run linters
        run: poetry run ni-python-styleguide lint
      - name: Run mypy (Linux)
        run: poetry run mypy
      - name: Run mypy (Windows)
        run: poetry run mypy --platform win32
      - name: Run Bandit security checks
        run: poetry run bandit -c pyproject.toml -r generated/nidaqmx
      - name: Generate ni-daqmx files
        run: |
          rm -fr generated/nidaqmx
          poetry run python src/codegen --dest generated/nidaqmx
      - name: Check for files dirtied by codegen
        run: git diff --exit-code
