3.6 速度模式 

3.6.1 MT_Set_Axis_Mode_Velocity 
功能描述：设置指定的轴为速度模式 
 
VC 
INT32 
MT_Set_Axis_Mode_Velocity(WORD AObj) 
VB 
Function MT_Set_Axis_Mode_Velocity (ByVal AObj As Integer) As Long 
Delphi 
function MT_Set_Axis_Mode_Velocity(AObj:Word):Integer; 
C# 
public static extern int MT_Set_Axis_Mode_Velocity(UInt16 AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
 
3.6.2 MT_Get_Axis_Velocity_V_Target 
功能描述：读取速度模式下的目标速度 
 
VC 
INT32 
MT_Get_Axis_Velocity_V_Target 
(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_Velocity_V_Target 
(ByVal AObj As Integer, ByRef Value As 
Long) As Long 
Delphi 
function 
MT_Get_Axis_Velocity_V_Target 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Velocity_V_Target 
(UInt16 AObj, ref Int32 
Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的目标速度 
Hz/S 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.6.3 MT_Set_Axis_Velocity_V_Target_Abs 
功能描述：设置速度模式下的绝对目标速度 
 
VC 
INT32 
MT_Set_Axis_Velocity_V_Target_Abs 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Axis_Velocity_V_Target_Abs 
(ByVal AObj As Integer, ByVal 
Value As 
Long) As Long 
Delphi 
function 
MT_Set_Axis_Velocity_V_Target_Abs 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Velocity_V_Target_Abs 
(UInt16 AObj,Int32 
Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的绝对目标速度 
Hz/S 
可正可负 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.6.4 MT_Set_Axis_Velocity_V_Target_Rel 
功能描述：设置速度模式下的相对当前速度的相对速度 
 
VC 
INT32 
MT_Set_Axis_Velocity_V_Target_Rel 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Axis_Velocity_V_Target_Rel 
(ByVal AObj As Integer, ByVal 
Value As 
Long) As Long 
Delphi 
function 
MT_Set_Axis_Velocity_V_Target_Rel(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Velocity_V_Target_Rel(UInt16 AObj,Int32 
Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的相对当前速度的速度 
Hz/S 
可正可负 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.6.5 MT_Set_Axis_Velocity_Stop 
功能描述：停止指定轴的速度运动模式 

VC 
INT32 
MT_Set_Axis_Velocity_Stop 
(WORD AObj) 
VB 
Function 
MT_Set_Axis_Velocity_Stop 
(ByVal AObj As Integer) As Long 
Delphi 
function 
MT_Set_Axis_Velocity_Stop 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Velocity_Stop 
(UInt16 AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
 
3.6.6 MT_Set_Axis_Velocity_V_Start 
功能描述：设置指定轴的速度模式启动初始速度，默认为50Hz/s,一般无需修改。 
 
VC 
INT32 
MT_Set_Axis_Velocity_V_Start 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Axis_Velocity_V 
_Start(ByVal AObj As Integer, ByVal 
Value As Long) 
As Long 
Delphi 
function 
MT_Set_Axis_Velocity_V_Start 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Velocity_V_Start 
(UInt16 AObj,Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的启动速度 
Hz/S 
只能正 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.6.7 MT_Set_Axis_Velocity_Acc 
功能描述：设置指定轴的速度模式的加速度值，默认为500Hz/s2。 
 
VC 
INT32 
MT_Set_Axis_Velocity_Acc 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Axis_Velocity_Acc(ByVal AObj As Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function 
MT_Set_Axis_Velocity_Acc 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Velocity_Acc 
(UInt16 AObj,Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的加速度 
Hz/S2 
只能正 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 

3.6.8 MT_Set_Axis_Velocity_Dec 
功能描述：设置指定轴的速度模式的减速度值，默认为500Hz/s2。 
 
VC 
INT32 
MT_Set_Axis_Velocity_Dec 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Axis_Velocity_Dec(ByVal AObj As Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function 
MT_Set_Axis_Velocity_Dec 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Velocity_Dec 
(UInt16 AObj,Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的减速度 
Hz/S2 
只能正 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.6.9 MT_Get_Axis_Velocity_Acc 
功能描述：读取指定控制轴速度模式的加速度 
 
VC 
INT32 
MT_Get_Axis_Velocity_Acc(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_ 
Velocity_Acc 
(ByVal AObj As Integer, ByRef Value As Long) As 
Long 
Delphi 
function MT_Get_Axis_ 
Velocity_Acc 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_ 
Velocity_Acc 
(UInt16 AObj, ref Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的当前加速度 
Hz/S2 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.6.10 MT_Get_Axis_Velocity_Dec 
功能描述：读取指定控制轴速度模式的减速度 
 
VC 
INT32 
MT_Get_Axis_Velocity_Dec(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_ 
Velocity_Dec 
(ByVal AObj As Integer, ByRef Value As Long) As 
Long 
Delphi 
function MT_Get_Axis_ 
Velocity_Dec 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_ 
Velocity_Decc 
(UInt16 AObj, ref Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的当前减速度 
Hz/S2 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
