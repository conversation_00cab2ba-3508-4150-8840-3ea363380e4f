# Do not edit this file; it was automatically generated.

from enum import IntEnum

__all__ = ['DAQmxErrors', 'DAQmxWarnings']


class DAQmxErrors(IntEnum):
    ID_PIN_NO_EEPROM = -209904
    ID_PIN_NAME_INVALID = -209903
    ID_PIN_DATA_WRITE_ERROR = -209902
    ID_PIN_UNSUPPORTED_FORMAT_CODE = -209901
    ID_PIN_DATA_TOO_LARGE = -209900
    ID_PIN_UNSUPPORTED_FAMILY_CODE = -209899
    PROPERTY_NOT_SPECD_FOR_ENTIRE_PORT = -209898
    CANNOT_SET_PROPERTY_WHEN_DA_QMX_TASK_RUNNING = -209897
    ATTR_NOT_SUPPORTED_USE_PHYSICAL_CHANNEL_PROPERTY = -209896
    AI_CALCULATED_POWER_MIN_MAX_ATTR_NOT_SUPPORTED = -209895
    AI_MIN_MAX_ATTR_WRITE_NOT_SUPPORTED_FOR_CALC_POWER = -209894
    POWER_VOLTAGE_AND_CURRENT_CONFIGURATION_MISMATCH = -209893
    VOLTAGE_AND_CURRENT_CHANNELS_NOT_SAME_DEVICE = -209892
    VOLTAGE_AND_CURRENT_CHANNEL_COUNT_MISMATCH = -209891
    BREAKPOINT_MODES_INCONSISTENT = -209890
    NEEDS_USB_SUPER_SPEED = -209889
    REMOTE_SENSE = -209888
    OVER_TEMPERATURE_PROTECTION_ACTIVATED = -209887
    MULTI_TASK_CFG_SAMP_RATE_NOT_SUPPORTED_WITH_PROP_SET = -209886
    MULTI_TASK_CFG_SAMP_RATE_CONFLICTING_PROP = -209885
    NO_COMMON_SAMP_RATE_FOUND_NO_REPEAT_SAMPS = -209884
    NO_COMMON_SAMP_RATE_FOUND = -209883
    MULTI_TASK_CFG_DOES_NOT_SUPPORT_MULTI_DEV_TASK = -209882
    MULTI_TASK_SAMP_RATE_CFG_NOT_SUPPORTED = -209881
    DEBUG_SESSION_NOT_ALLOWED_TIMING_SOURCE_REGISTERED = -209880
    DEBUG_SESSION_NOT_ALLOWED_WHEN_LOGGING = -209879
    DEBUG_SESSION_NOT_ALLOWED_EVENT_REGISTERED = -209878
    INVALID_TARGET_TASK_FOR_DEBUG_SESSION = -209877
    FUNCTION_NOT_SUPPORTED_FOR_DEVICE = -209876
    MULTIPLE_TARGET_TASKS_FOUND_FOR_DEBUG_SESSION = -209875
    TARGET_TASK_NOT_FOUND_FOR_DEBUG_SESSION = -209874
    OPERATION_NOT_SUPPORTED_IN_DEBUG_SESSION = -209873
    OPERATION_NOT_PERMITTED_IN_MONITOR_MODE_FOR_DEBUG_SESSION = -209872
    GET_ACTIVE_DEV_PRPTY_FAILED_DUE_TO_DIFFT_VALS = -209871
    TASK_ALREADY_REGISTERED_A_TIMING_SOURCE = -209870
    FILTER_NOT_SUPPORTED_ON_HW_REV = -209869
    SENSOR_POWER_SUPPLY_VOLTAGE_LEVEL = -209868
    SENSOR_POWER_SUPPLY = -209867
    INVALID_SCANLIST = -209866
    TIME_RESOURCE_CANNOT_BE_ROUTED = -209865
    INVALID_RESET_DELAY_REQUESTED = -209864
    EXCEEDED_TOTAL_TIMETRIGGERS_AVAILABLE = -209863
    EXCEEDED_TOTAL_TIMESTAMPS_AVAILABLE = -209862
    NO_SYNCHRONIZATION_PROTOCOL_RUNNING = -209861
    CONFLICTING_COHERENCY_REQUIREMENTS = -209860
    NO_SHARED_TIMESCALE = -209859
    INVALID_FIELD_DAQ_BANK_NAME = -209858
    DEVICE_DOES_NOT_SUPPORT_HWTSP = -209857
    BANK_TYPE_DOES_NOT_MATCH_BANK_TYPE_IN_DESTINATION = -209856
    INVALID_FIELD_DAQ_BANK_NUMBER_SPECD = -209855
    UNSUPPORTED_SIMULATED_BANK_FOR_SIMULATED_FIELD_DAQ = -209854
    FIELD_DAQ_BANK_SIM_MUST_MATCH_FIELD_DAQ_SIM = -209853
    DEV_NO_LONGER_SUPPORTED_WITHIN_DA_QMX_API = -209852
    TIMING_ENGINE_DOES_NOT_SUPPORT_ON_BOARD_MEMORY = -209851
    DUPLICATE_TASK_CROSS_PROJECT = -209850
    TIME_START_TRIGGER_BEFORE_ARM_START_TRIGGER = -209849
    TIME_TRIGGER_CANNOT_BE_SET = -209848
    INVALID_TRIGGER_WINDOW_VALUE = -209847
    CANNOT_QUERY_PROPERTY_BEFORE_OR_DURING_ACQUISITION = -209846
    SAMPLE_CLOCK_TIMEBASE_NOT_SUPPORTED = -209845
    TIMESTAMP_NOT_YET_RECEIVED = -209844
    TIME_TRIGGER_NOT_SUPPORTED = -209843
    TIMESTAMP_NOT_ENABLED = -209842
    TIME_TRIGGERS_INCONSISTENT = -209841
    TRIGGER_CONFIGURED_IS_IN_THE_PAST = -209840
    TRIGGER_CONFIGURED_IS_TOO_FAR_FROM_CURRENT_TIME = -209839
    SYNCHRONIZATION_LOCK_LOST = -209838
    INCONSISTENT_TIMESCALES = -209837
    CANNOT_SYNCHRONIZE_DEVICES = -209836
    ASSOCIATED_CHANS_HAVE_ATTRIBUTE_CONFLICT_WITH_MULTIPLE_MAX_MIN_RANGES = -209835
    SAMPLE_RATE_NUM_CHANS_OR_ATTRIBUTE_VALUES = -209834
    WAIT_FOR_VALID_TIMESTAMP_NOT_SUPPORTED = -209833
    TRIG_WIN_TIMEOUT_EXPIRED = -209832
    INVALID_TRIGGER_CFG_FOR_DEVICE = -209831
    INVALID_DATA_TRANSFER_MECHANISM_FOR_DEVICE = -209830
    INPUT_FIFO_OVERFLOW_3 = -209829
    TOO_MANY_DEVICES_FOR_ANALOG_MULTI_EDGE_TRIG_CDAQ = -209828
    TOO_MANY_TRIGGERS_TYPES_SPECIFIED_IN_TASK = -209827
    MISMATCHED_MULTI_TRIGGER_CONFIG_VALUES = -209826
    INCONSISTENT_AO_DAC_RANGE_ACROSS_TASKS = -209825
    INCONSISTENT_DT_TO_WRITE = -209824
    FUNCTION_OBSOLETE = -209823
    NEGATIVE_DURATION_NOT_SUPPORTED = -209822
    DURATION_TOO_SMALL = -209821
    DURATION_TOO_LONG = -209820
    DURATION_BASED_NOT_SUPPORTED_FOR_SPECIFIED_TIMING_MODE = -209819
    INVALID_LED_STATE = -209818
    WATCHDOG_STATES_NOT_UNIFORM = -209817
    SELF_TEST_FAILED_POWER_SUPPLY_OUT_OF_TOLERANCE = -209816
    HWTSP_MULTI_SAMPLE_WRITE = -209815
    ONBOARD_REGEN_EXCEEDS_CHANNEL_LIMIT = -209814
    WATCHDOG_CHANNEL_EXPIRATION_STATE_NOT_SPECIFIED = -209813
    INVALID_SHUNT_SOURCE_FOR_CALIBRATION = -209812
    INVALID_SHUNT_SELECT_FOR_CALIBRATION = -209811
    INVALID_SHUNT_CALIBRATION_CONFIGURATION = -209810
    BUFFERED_OPERATIONS_NOT_SUPPORTED_ON_CHANNEL_STANDALONE = -209809
    FEATURE_NOT_AVAILABLE_ON_ACCESSORY = -209808
    INCONSISTENT_THRESH_VOLTAGE_ACROSS_TERMINALS = -209807
    DA_QMX_IS_NOT_INSTALLED_ON_TARGET = -209806
    CO_CANNOT_KEEP_UP_IN_HW_TIMED_SINGLE_POINT = -209805
    WAIT_FOR_NEXT_SAMP_CLK_DETECTED_3_OR_MORE_SAMP_CLKS = -209803
    WAIT_FOR_NEXT_SAMP_CLK_DETECTED_MISSED_SAMP_CLK = -209802
    WRITE_NOT_COMPLETE_BEFORE_SAMP_CLK = -209801
    READ_NOT_COMPLETE_BEFORE_SAMP_CLK = -209800
    INCONSISTENT_DIGITAL_FILTERING_ACROSS_TERMINALS = -201510
    INCONSISTENT_PULL_UP_CFG_ACROSS_TERMINALS = -201509
    INCONSISTENT_TERM_CFG_ACROSS_TERMINALS = -201508
    VCXO_DCM_BECAME_UNLOCKED = -201507
    PLL_DAC_UPDATE_FAILED = -201506
    NO_CABLED_DEVICE = -201505
    LOST_REF_CLK = -201504
    CANT_USE_AI_TIMING_ENGINE_WITH_COUNTERS = -201503
    DAC_OFFSET_VAL_NOT_SET = -201502
    CAL_ADJUST_REF_VAL_OUT_OF_RANGE = -201501
    CHANS_FOR_CAL_ADJUST_MUST_PERFORM_SET_CONTEXT = -201500
    GET_CAL_DATA_INVALID_FOR_CAL_MODE = -201499
    NO_IEPE_WITH_AC_NOT_ALLOWED = -201498
    SETUP_CAL_NEEDED_BEFORE_GET_CAL_DATA_POINTS = -201497
    VOLTAGE_NOT_CALIBRATED = -201496
    MISSING_RANGE_FOR_CALIBRATION = -201495
    MULTIPLE_CHANS_NOT_SUPPORTED_DURING_CAL_ADJUST = -201494
    SHUNT_CAL_FAILED_OUT_OF_RANGE = -201493
    OPERATION_NOT_SUPPORTED_ON_SIMULATED_DEVICE = -201492
    FIRMWARE_VERSION_SAME_AS_INSTALLED_VERSION = -201491
    FIRMWARE_VERSION_OLDER_THAN_INSTALLED_VERSION = -201490
    FIRMWARE_UPDATE_INVALID_STATE = -201489
    FIRMWARE_UPDATE_INVALID_ID = -201488
    FIRMWARE_UPDATE_AUTOMATIC_MANAGEMENT_ENABLED = -201487
    SETUP_CALIBRATION_NOT_CALLED = -201486
    CAL_MEASURED_DATA_SIZE_VS_ACTUAL_DATA_SIZE_MISMATCH = -201485
    CDAQ_MISSING_DSA_MASTER_FOR_CHAN_EXPANSION = -201484
    CDAQ_MASTER_NOT_FOUND_FOR_CHAN_EXPANSION = -201483
    ALL_CHANS_SHOULD_BE_PROVIDED_FOR_CALIBRATION = -201482
    MUST_SPECIFY_EXPIRATION_STATE_FOR_ALL_LINES_IN_RANGE = -201481
    OPEN_SESSION_EXISTS = -201480
    CANNOT_QUERY_TERMINAL_FOR_SW_ARM_START = -201479
    CHASSIS_WATCHDOG_TIMER_EXPIRED = -201478
    CANT_RESERVE_WATCHDOG_TASK_WHILE_OTHER_TASKS_RESERVED = -201477
    CANT_RESERVE_TASK_WHILE_WATCHDOG_TASK_RESERVING = -201476
    AUX_POWER_SOURCE_REQUIRED = -201475
    DEVICE_NOT_SUPPORTED_ON_LOCAL_SYSTEM = -201474
    ONE_TIMESTAMP_CHANNEL_REQUIRED_FOR_COMBINED_NAVIGATION_READ = -201472
    MULT_DEVS_MULT_PHYS_CHANS = -201471
    INVALID_CAL_ADJUSTMENT_POINT_VALUES = -201470
    DIFFERENT_DIGITIZER_FROM_COMMUNICATOR = -201469
    CDAQ_SYNC_MASTER_CLOCK_NOT_PRESENT = -201468
    ASSOCIATED_CHANS_HAVE_CONFLICTING_PROPS = -201467
    AUTO_CONFIG_BETWEEN_MULTIPLE_DEVICE_STATES_INVALID = -201466
    AUTO_CONFIG_OF_OFFLINE_DEVICES_INVALID = -201465
    EXTERNAL_FIFO_FAULT = -201464
    CONNECTIONS_NOT_RECIPROCAL = -201463
    INVALID_OUTPUT_TO_INPUT_CDAQ_SYNC_CONNECTION = -201462
    REFERENCE_CLOCK_NOT_PRESENT = -201461
    BLANK_STRING_EXPANSION_FOUND_NO_SUPPORTED_CDAQ_SYNC_CONNECTION_DEVICES = -201460
    NO_DEVICES_SUPPORT_CDAQ_SYNC_CONNECTIONS = -201459
    INVALID_CDAQ_SYNC_TIMEOUT_VALUE = -201458
    CDAQ_SYNC_CONNECTION_TO_SAME_PORT = -201457
    DEVS_WITHOUT_COMMON_SYNC_CONNECTION_STRATEGY = -201456
    NO_CDAQ_SYNC_BETWEEN_PHYS_AND_SIMULATED_DEVS = -201455
    UNABLE_TO_CONTAIN_CARDS = -201454
    FIND_DISCONNECTED_BETWEEN_PHYS_AND_SIM_DEVICE_STATES_INVALID = -201453
    OPERATION_ABORTED = -201452
    TWO_PORTS_REQUIRED = -201451
    DEVICE_DOES_NOT_SUPPORT_CDAQ_SYNC_CONNECTIONS = -201450
    INVALIDC_DAQ_SYNC_PORT_CONNECTION_FORMAT = -201449
    ROSETTE_MEASUREMENTS_NOT_SPECIFIED = -201448
    INVALID_NUM_OF_PHYS_CHANS_FOR_DELTA_ROSETTE = -201447
    INVALID_NUM_OF_PHYS_CHANS_FOR_TEE_ROSETTE = -201446
    ROSETTE_STRAIN_CHAN_NAMES_NEEDED = -201445
    MULTIDEVICE_WITH_ON_DEMAND_TIMING = -201444
    FREQOUT_CANNOT_PRODUCE_DESIRED_FREQUENCY_3 = -201443
    TWO_EDGE_SEPARATION_SAME_TERMINAL_SAME_EDGE = -201442
    DONT_MIX_SYNC_PULSE_AND_SAMP_CLK_TIMEBASE_ON_449_X = -201441
    NEITHER_REF_CLK_NOR_SAMP_CLK_TIMEBASE_CONFIGURED_FOR_DSA_SYNC = -201440
    RETRIGGERING_FINITE_CO_NOT_ALLOWED = -201439
    DEVICE_REBOOTED_FROM_WDT_TIMEOUT = -201438
    TIMEOUT_VALUE_EXCEEDS_MAXIMUM = -201437
    SHARING_DIFFERENT_WIRE_MODES = -201436
    CANT_PRIME_WITH_EMPTY_BUFFER = -201435
    CONFIG_FAILED_BECAUSE_WATCHDOG_EXPIRED = -201434
    WRITE_FAILED_BECAUSE_WATCHDOG_CHANGED_LINE_DIRECTION = -201433
    MULTIPLE_SUBSYTEM_CALIBRATION = -201432
    INCORRECT_CHANNEL_FOR_OFFSET_ADJUSTMENT = -201431
    INVALID_NUM_REF_VOLTAGES_TO_WRITE = -201430
    START_TRIG_DELAY_WITH_DSA_MODULE = -201429
    MORE_THAN_ONE_SYNC_PULSE_DETECTED = -201428
    DEV_NOT_SUPPORTED_WITHIN_DA_QMX_API = -201427
    DEVS_WITHOUT_SYNC_STRATEGIES = -201426
    DEVS_WITHOUT_COMMON_SYNC_STRATEGY = -201425
    SYNC_STRATEGIES_CANNOT_SYNC = -201424
    CHASSIS_COMMUNICATION_INTERRUPTED = -201423
    UNKNOWN_CARD_POWER_PROFILE_IN_CARRIER = -201422
    ATTR_NOT_SUPPORTED_ON_ACCESSORY = -201421
    NETWORK_DEVICE_RESERVED_BY_ANOTHER_HOST = -201420
    INCORRECT_FIRMWARE_FILE_UPLOADED = -201419
    INVALID_FIRMWARE_FILE_UPLOADED = -201418
    IN_TIMER_TIMEOUT_ON_ARM = -201417
    CANT_EXCEED_SLOT_RELAY_DRIVE_LIMIT = -201416
    MODULE_UNSUPPORTED_FOR_9163 = -201415
    CONNECTIONS_NOT_SUPPORTED = -201414
    ACCESSORY_NOT_PRESENT = -201413
    SPECIFIED_ACCESSORY_CHANNELS_NOT_PRESENT_ON_DEVICE = -201412
    CONNECTIONS_NOT_SUPPORTED_ON_ACCESSORY = -201411
    RATE_TOO_FAST_FOR_HWTSP = -201410
    DELAY_FROM_SAMPLE_CLOCK_OUT_OF_RANGE_FOR_HWTSP = -201409
    AVERAGING_WHEN_NOT_INTERNAL_HWTSP = -201408
    ATTRIBUTE_NOT_SUPPORTED_UNLESS_HWTSP = -201407
    FIVE_VOLT_DETECT_FAILED = -201406
    ANALOG_BUS_STATE_INCONSISTENT = -201405
    CARD_DETECTED_DOES_NOT_MATCH_EXPECTED_CARD = -201404
    LOGGING_START_NEW_FILE_NOT_CALLED = -201403
    LOGGING_SAMPS_PER_FILE_NOT_DIVISIBLE = -201402
    RETRIEVING_NETWORK_DEVICE_PROPERTIES = -201401
    FILE_PREALLOCATION_FAILED = -201400
    MODULE_MISMATCH_IN_SAME_TIMED_TASK = -201399
    INVALID_ATTRIBUTE_VALUE_POSSIBLY_DUE_TO_OTHER_ATTRIBUTE_VALUES = -201398
    CHANGE_DETECTION_STOPPED_TO_PREVENT_DEVICE_HANG = -201397
    FILTER_DELAY_REMOVAL_NOT_POSSSIBLE_WITH_ANALOG_TRIGGER = -201396
    NONBUFFERED_OR_NO_CHANNELS = -201395
    TRISTATE_LOGIC_LEVEL_NOT_SPECD_FOR_ENTIRE_PORT = -201394
    TRISTATE_LOGIC_LEVEL_NOT_SUPPORTED_ON_DIG_OUT_CHAN = -201393
    TRISTATE_LOGIC_LEVEL_NOT_SUPPORTED = -201392
    INCOMPLETE_GAIN_AND_COUPLING_CAL_ADJUSTMENT = -201391
    NETWORK_STATUS_CONNECTION_LOST = -201390
    MODULE_CHANGE_DURING_CONNECTION_LOSS = -201389
    NETWORK_DEVICE_NOT_RESERVED_BY_HOST = -201388
    DUPLICATE_CALIBRATION_ADJUSTMENT_INPUT = -201387
    SELF_CAL_FAILED_CONTACT_TECH_SUPPORT = -201386
    SELF_CAL_FAILED_TO_CONVERGE = -201385
    UNSUPPORTED_SIMULATED_MODULE_FOR_SIMULATED_CHASSIS = -201384
    LOGGING_WRITE_SIZE_TOO_BIG = -201383
    LOGGING_WRITE_SIZE_NOT_DIVISIBLE = -201382
    MY_DAQ_POWER_RAIL_FAULT = -201381
    DEVICE_DOES_NOT_SUPPORT_THIS_OPERATION = -201380
    NETWORK_DEVICES_NOT_SUPPORTED_ON_THIS_PLATFORM = -201379
    UNKNOWN_FIRMWARE_VERSION = -201378
    FIRMWARE_IS_UPDATING = -201377
    ACCESSORY_EEPROM_IS_CORRUPT = -201376
    THRMCPL_LEAD_OFFSET_NULLING_CAL_NOT_SUPPORTED = -201375
    SELF_CAL_FAILED_TRY_EXT_CAL = -201374
    OUTPUT_P_2_P_NOT_SUPPORTED_WITH_MULTITHREADED_SCRIPTS = -201373
    THRMCPL_CALIBRATION_CHANNELS_OPEN = -201372
    MDNS_SERVICE_INSTANCE_ALREADY_IN_USE = -201371
    IP_ADDRESS_ALREADY_IN_USE = -201370
    HOSTNAME_ALREADY_IN_USE = -201369
    INVALID_NUMBER_OF_CAL_ADJUSTMENT_POINTS = -201368
    FILTER_OR_DIGITAL_SYNC_INTERNAL_SIGNAL = -201367
    BAD_DDS_SOURCE = -201366
    ONBOARD_REGEN_WITH_MORE_THAN_16_CHANNELS = -201365
    TRIGGER_TOO_FAST = -201364
    MIN_MAX_OUTSIDE_TABLE_RANGE = -201363
    CHANNEL_EXPANSION_WITH_INVALID_ANALOG_TRIGGER_DEVICE = -201362
    SYNC_PULSE_SRC_INVALID_FOR_TASK = -201361
    INVALID_CARRIER_SLOT_NUMBER_SPECD = -201360
    CARDS_MUST_BE_IN_SAME_CARRIER = -201359
    CARD_DEV_CARRIER_SIM_MUST_MATCH = -201358
    DEV_MUST_HAVE_AT_LEAST_ONE_CARD = -201357
    CARD_TOPOLOGY_ERROR = -201356
    EXCEEDED_CARRIER_POWER_LIMIT = -201355
    CARDS_INCOMPATIBLE = -201354
    ANALOG_BUS_NOT_VALID = -201353
    RESERVATION_CONFLICT = -201352
    MEM_MAPPED_ON_DEMAND_NOT_SUPPORTED = -201351
    SLAVE_WITH_NO_START_TRIGGER_CONFIGURED = -201350
    CHANNEL_EXPANSION_WITH_DIFFERENT_TRIGGER_DEVICES = -201349
    COUNTER_SYNC_AND_RETRIGGERED = -201348
    NO_EXTERNAL_SYNC_PULSE_DETECTED = -201347
    SLAVE_AND_NO_EXTERNAL_SYNC_PULSE = -201346
    CUSTOM_TIMING_REQUIRED_FOR_ATTRIBUTE = -201345
    CUSTOM_TIMING_MODE_NOT_SET = -201344
    ACCESSORY_POWER_TRIPPED = -201343
    UNSUPPORTED_ACCESSORY = -201342
    INVALID_ACCESSORY_CHANGE = -201341
    FIRMWARE_REQUIRES_UPGRADE = -201340
    FAST_EXTERNAL_TIMEBASE_NOT_SUPPORTED_FOR_DEVICE = -201339
    INVALID_SHUNT_LOCATION_FOR_CALIBRATION = -201338
    DEVICE_NAME_TOO_LONG = -201337
    BRIDGE_SCALES_UNSUPPORTED = -201336
    MISMATCHED_ELEC_PHYS_VALUES = -201335
    LINEAR_REQUIRES_UNIQUE_POINTS = -201334
    MISSING_REQUIRED_SCALING_PARAMETER = -201333
    LOGGING_NOT_SUPPORT_ON_OUTPUT_TASKS = -201332
    MEMORY_MAPPED_HARDWARE_TIMED_NON_BUFFERED_UNSUPPORTED = -201331
    CANNOT_UPDATE_PULSE_TRAIN_WITH_AUTO_INCREMENT_ENABLED = -201330
    HW_TIMED_SINGLE_POINT_AND_DATA_XFER_NOT_DMA = -201329
    SCC_SECOND_STAGE_EMPTY = -201328
    SCC_INVALID_DUAL_STAGE_COMBO = -201327
    SCC_INVALID_SECOND_STAGE = -201326
    SCC_INVALID_FIRST_STAGE = -201325
    COUNTER_MULTIPLE_SAMPLE_CLOCKED_CHANNELS = -201324
    TWO_COUNTER_MEASUREMENT_MODE_AND_SAMPLE_CLOCKED = -201323
    CANT_HAVE_BOTH_MEM_MAPPED_AND_NON_MEM_MAPPED_TASKS = -201322
    MEM_MAPPED_DATA_READ_BY_ANOTHER_PROCESS = -201321
    RETRIGGERING_INVALID_FOR_GIVEN_SETTINGS = -201320
    AI_OVERRUN = -201319
    CO_OVERRUN = -201318
    COUNTER_MULTIPLE_BUFFERED_CHANNELS = -201317
    INVALID_TIMEBASE_FOR_COHWTSP = -201316
    WRITE_BEFORE_EVENT = -201315
    CI_OVERRUN = -201314
    COUNTER_NON_RESPONSIVE_AND_RESET = -201313
    MEAS_TYPE_OR_CHANNEL_NOT_SUPPORTED_FOR_LOGGING = -201312
    FILE_ALREADY_OPENED_FOR_WRITE = -201311
    TDMS_NOT_FOUND = -201310
    GENERIC_FILE_IO = -201309
    FINITE_STC_COUNTER_NOT_SUPPORTED_FOR_LOGGING = -201308
    MEASUREMENT_TYPE_NOT_SUPPORTED_FOR_LOGGING = -201307
    FILE_ALREADY_OPENED = -201306
    DISK_FULL = -201305
    FILE_PATH_INVALID = -201304
    FILE_VERSION_MISMATCH = -201303
    FILE_WRITE_PROTECTED = -201302
    READ_NOT_SUPPORTED_FOR_LOGGING_MODE = -201301
    ATTRIBUTE_NOT_SUPPORTED_WHEN_LOGGING = -201300
    LOGGING_MODE_NOT_SUPPORTED_NON_BUFFERED = -201299
    PROPERTY_NOT_SUPPORTED_WITH_CONFLICTING_PROPERTY = -201298
    PARALLEL_SSH_ON_CONNECTOR_1 = -201297
    CO_ONLY_IMPLICIT_SAMPLE_TIMING_TYPE_SUPPORTED = -201296
    CALIBRATION_FAILED_AO_OUT_OF_RANGE = -201295
    CALIBRATION_FAILED_AI_OUT_OF_RANGE = -201294
    CAL_PWM_LINEARITY_FAILED = -201293
    OVERRUN_UNDERFLOW_CONFIGURATION_COMBO = -201292
    CANNOT_WRITE_TO_FINITE_CO_TASK = -201291
    NETWORK_DAQ_INVALID_WEP_KEY_LENGTH = -201290
    CAL_INPUTS_SHORTED_NOT_SUPPORTED = -201289
    CANNOT_SET_PROPERTY_WHEN_TASK_IS_RESERVED = -201288
    MINUS_12_V_FUSE_BLOWN = -201287
    PLUS_12_V_FUSE_BLOWN = -201286
    PLUS_5_V_FUSE_BLOWN = -201285
    PLUS_3_V_FUSE_BLOWN = -201284
    DEVICE_SERIAL_PORT_ERROR = -201283
    POWER_UP_STATE_MACHINE_NOT_DONE = -201282
    TOO_MANY_TRIGGERS_SPECIFIED_IN_TASK = -201281
    VERTICAL_OFFSET_NOT_SUPPORTED_ON_DEVICE = -201280
    INVALID_COUPLING_FOR_MEASUREMENT_TYPE = -201279
    DIGITAL_LINE_UPDATE_TOO_FAST_FOR_DEVICE = -201278
    CERTIFICATE_IS_TOO_BIG_TO_TRANSFER = -201277
    ONLY_PEM_OR_DER_CERTITICATES_ACCEPTED = -201276
    CAL_COUPLING_NOT_SUPPORTED = -201275
    DEVICE_NOT_SUPPORTED_IN_64_BIT = -201274
    NETWORK_DEVICE_IN_USE = -201273
    INVALID_IPV_4_ADDRESS_FORMAT = -201272
    NETWORK_PRODUCT_TYPE_MISMATCH = -201271
    ONLY_PEM_CERTIFICATES_ACCEPTED = -201270
    CALIBRATION_REQUIRES_PROTOTYPING_BOARD_ENABLED = -201269
    ALL_CURRENT_LIMITING_RESOURCES_ALREADY_TAKEN = -201268
    USER_DEF_INFO_STRING_BAD_LENGTH = -201267
    PROPERTY_NOT_FOUND = -201266
    OVER_VOLTAGE_PROTECTION_ACTIVATED = -201265
    SCALED_IQ_WAVEFORM_TOO_LARGE = -201264
    FIRMWARE_FAILED_TO_DOWNLOAD = -201263
    PROPERTY_NOT_SUPPORTED_FOR_BUS_TYPE = -201262
    CHANGE_RATE_WHILE_RUNNING_COULD_NOT_BE_COMPLETED = -201261
    CANNOT_QUERY_MANUAL_CONTROL_ATTRIBUTE = -201260
    INVALID_NETWORK_CONFIGURATION = -201259
    INVALID_WIRELESS_CONFIGURATION = -201258
    INVALID_WIRELESS_COUNTRY_CODE = -201257
    INVALID_WIRELESS_CHANNEL = -201256
    NETWORK_EEPROM_HAS_CHANGED = -201255
    NETWORK_SERIAL_NUMBER_MISMATCH = -201254
    NETWORK_STATUS_DOWN = -201253
    NETWORK_TARGET_UNREACHABLE = -201252
    NETWORK_TARGET_NOT_FOUND = -201251
    NETWORK_STATUS_TIMED_OUT = -201250
    INVALID_WIRELESS_SECURITY_SELECTION = -201249
    NETWORK_DEVICE_CONFIGURATION_LOCKED = -201248
    NETWORK_DAQ_DEVICE_NOT_SUPPORTED = -201247
    NETWORK_DAQ_CANNOT_CREATE_EMPTY_SLEEVE = -201246
    USER_DEF_INFO_STRING_TOO_LONG = -201245
    MODULE_TYPE_DOES_NOT_MATCH_MODULE_TYPE_IN_DESTINATION = -201244
    INVALID_TEDS_INTERFACE_ADDRESS = -201243
    DEV_DOES_NOT_SUPPORT_SCXI_COMM = -201242
    SCXI_COMM_DEV_CONNECTOR_0_MUST_BE_CABLED_TO_MODULE = -201241
    SCXI_MODULE_DOES_NOT_SUPPORT_DIGITIZATION_MODE = -201240
    DEV_DOES_NOT_SUPPORT_MULTIPLEXED_SCXI_DIGITIZATION_MODE = -201239
    DEV_OR_DEV_PHYS_CHAN_DOES_NOT_SUPPORT_SCXI_DIGITIZATION = -201238
    INVALID_PHYS_CHAN_NAME = -201237
    SCXI_CHASSIS_COMM_MODE_INVALID = -201236
    REQUIRED_DEPENDENCY_NOT_FOUND = -201235
    INVALID_STORAGE = -201234
    INVALID_OBJECT = -201233
    STORAGE_ALTERED_PRIOR_TO_SAVE = -201232
    TASK_DOES_NOT_REFERENCE_LOCAL_CHANNEL = -201231
    REFERENCED_DEV_SIM_MUST_MATCH_TARGET = -201230
    PROGRAMMED_IO_FAILS_BECAUSE_OF_WATCHDOG_TIMER = -201229
    WATCHDOG_TIMER_FAILS_BECAUSE_OF_PROGRAMMED_IO = -201228
    CANT_USE_THIS_TIMING_ENGINE_WITH_A_PORT = -201227
    PROGRAMMED_IO_CONFLICT = -201226
    CHANGE_DETECTION_INCOMPATIBLE_WITH_PROGRAMMED_IO = -201225
    TRISTATE_NOT_ENOUGH_LINES = -201224
    TRISTATE_CONFLICT = -201223
    GENERATE_OR_FINITE_WAIT_EXPECTED_BEFORE_BREAK_BLOCK = -201222
    BREAK_BLOCK_NOT_ALLOWED_IN_LOOP = -201221
    CLEAR_TRIGGER_NOT_ALLOWED_IN_BREAK_BLOCK = -201220
    NESTING_NOT_ALLOWED_IN_BREAK_BLOCK = -201219
    IF_ELSE_BLOCK_NOT_ALLOWED_IN_BREAK_BLOCK = -201218
    REPEAT_UNTIL_TRIGGER_LOOP_NOT_ALLOWED_IN_BREAK_BLOCK = -201217
    WAIT_UNTIL_TRIGGER_NOT_ALLOWED_IN_BREAK_BLOCK = -201216
    MARKER_POS_INVALID_IN_BREAK_BLOCK = -201215
    INVALID_WAIT_DURATION_IN_BREAK_BLOCK = -201214
    INVALID_SUBSET_LENGTH_IN_BREAK_BLOCK = -201213
    INVALID_WAVEFORM_LENGTH_IN_BREAK_BLOCK = -201212
    INVALID_WAIT_DURATION_BEFORE_BREAK_BLOCK = -201211
    INVALID_SUBSET_LENGTH_BEFORE_BREAK_BLOCK = -201210
    INVALID_WAVEFORM_LENGTH_BEFORE_BREAK_BLOCK = -201209
    SAMPLE_RATE_TOO_HIGH_FOR_ADC_TIMING_MODE = -201208
    ACTIVE_DEV_NOT_SUPPORTED_WITH_MULTI_DEV_TASK = -201207
    REAL_DEV_AND_SIM_DEV_NOT_SUPPORTED_IN_SAME_TASK = -201206
    RTSI_SIM_MUST_MATCH_DEV_SIM = -201205
    BRIDGE_SHUNT_CA_NOT_SUPPORTED = -201204
    STRAIN_SHUNT_CA_NOT_SUPPORTED = -201203
    GAIN_TOO_LARGE_FOR_GAIN_CAL_CONST = -201202
    OFFSET_TOO_LARGE_FOR_OFFSET_CAL_CONST = -201201
    ELVIS_PROTOTYPING_BOARD_REMOVED = -201200
    ELVIS_2_POWER_RAIL_FAULT = -201199
    ELVIS_2_PHYSICAL_CHANS_FAULT = -201198
    ELVIS_2_PHYSICAL_CHANS_THERMAL_EVENT = -201197
    RX_BIT_ERROR_RATE_LIMIT_EXCEEDED = -201196
    PHY_BIT_ERROR_RATE_LIMIT_EXCEEDED = -201195
    TWO_PART_ATTRIBUTE_CALLED_OUT_OF_ORDER = -201194
    INVALID_SCXI_CHASSIS_ADDRESS = -201193
    COULD_NOT_CONNECT_TO_REMOTE_MXS = -201192
    EXCITATION_STATE_REQUIRED_FOR_ATTRIBUTES = -201191
    DEVICE_NOT_USABLE_UNTIL_USB_REPLUG = -201190
    INPUT_FIFO_OVERFLOW_DURING_CALIBRATION_ON_FULL_SPEED_USB = -201189
    INPUT_FIFO_OVERFLOW_DURING_CALIBRATION = -201188
    CJC_CHAN_CONFLICTS_WITH_NON_THERMOCOUPLE_CHAN = -201187
    COMM_DEVICE_FOR_PXI_BACKPLANE_NOT_IN_RIGHTMOST_SLOT = -201186
    COMM_DEVICE_FOR_PXI_BACKPLANE_NOT_IN_SAME_CHASSIS = -201185
    COMM_DEVICE_FOR_PXI_BACKPLANE_NOT_PXI = -201184
    INVALID_CAL_EXCIT_FREQUENCY = -201183
    INVALID_CAL_EXCIT_VOLTAGE = -201182
    INVALID_AI_INPUT_SRC = -201181
    INVALID_CAL_INPUT_REF = -201180
    D_B_REFERENCE_VALUE_NOT_GREATER_THAN_ZERO = -201179
    SAMPLE_CLOCK_RATE_IS_TOO_FAST_FOR_SAMPLE_CLOCK_TIMING = -201178
    DEVICE_NOT_USABLE_UNTIL_COLD_START = -201177
    SAMPLE_CLOCK_RATE_IS_TOO_FAST_FOR_BURST_TIMING = -201176
    DEV_IMPORT_FAILED_ASSOCIATED_RESOURCE_IDS_NOT_SUPPORTED = -201175
    SCXI_1600_IMPORT_NOT_SUPPORTED = -201174
    POWER_SUPPLY_CONFIGURATION_FAILED = -201173
    IEPE_WITH_DC_NOT_ALLOWED = -201172
    MIN_TEMP_FOR_THERMOCOUPLE_TYPE_OUTSIDE_ACCURACY_FOR_POLY_SCALING = -201171
    DEV_IMPORT_FAILED_NO_DEVICE_TO_OVERWRITE_AND_SIMULATION_NOT_SUPPORTED = -201170
    DEV_IMPORT_FAILED_DEVICE_NOT_SUPPORTED_ON_DESTINATION = -201169
    FIRMWARE_IS_TOO_OLD = -201168
    FIRMWARE_COULDNT_UPDATE = -201167
    FIRMWARE_IS_CORRUPT = -201166
    FIRMWARE_TOO_NEW = -201165
    SAMP_CLOCK_CANNOT_BE_EXPORTED_FROM_EXTERNAL_SAMP_CLOCK_SRC = -201164
    PHYS_CHAN_RESERVED_FOR_INPUT_WHEN_DESIRED_FOR_OUTPUT = -201163
    PHYS_CHAN_RESERVED_FOR_OUTPUT_WHEN_DESIRED_FOR_INPUT = -201162
    SPECIFIED_CDAQ_SLOT_NOT_EMPTY = -201161
    DEVICE_DOES_NOT_SUPPORT_SIMULATION = -201160
    INVALID_CDAQ_SLOT_NUMBER_SPECD = -201159
    C_SERIES_MOD_SIM_MUST_MATCH_CDAQ_CHASSIS_SIM = -201158
    SCC_CABLED_DEV_MUST_NOT_BE_SIM_WHEN_SCC_CARRIER_IS_NOT_SIM = -201157
    SCC_MOD_SIM_MUST_MATCH_SCC_CARRIER_SIM = -201156
    SCXI_MODULE_DOES_NOT_SUPPORT_SIMULATION = -201155
    SCXI_CABLE_DEV_MUST_NOT_BE_SIM_WHEN_MOD_IS_NOT_SIM = -201154
    SCXI_DIGITIZER_SIM_MUST_NOT_BE_SIM_WHEN_MOD_IS_NOT_SIM = -201153
    SCXI_MOD_SIM_MUST_MATCH_SCXI_CHASSIS_SIM = -201152
    SIM_PXI_DEV_REQ_SLOT_AND_CHASSIS_SPECD = -201151
    SIM_DEV_CONFLICT_WITH_REAL_DEV = -201150
    INSUFFICIENT_DATA_FOR_CALIBRATION = -201149
    TRIGGER_CHANNEL_MUST_BE_ENABLED = -201148
    CALIBRATION_DATA_CONFLICT_COULD_NOT_BE_RESOLVED = -201147
    SOFTWARE_TOO_NEW_FOR_SELF_CALIBRATION_DATA = -201146
    SOFTWARE_TOO_NEW_FOR_EXT_CALIBRATION_DATA = -201145
    SELF_CALIBRATION_DATA_TOO_NEW_FOR_SOFTWARE = -201144
    EXT_CALIBRATION_DATA_TOO_NEW_FOR_SOFTWARE = -201143
    SOFTWARE_TOO_NEW_FOR_EEPROM = -201142
    EEPROM_TOO_NEW_FOR_SOFTWARE = -201141
    SOFTWARE_TOO_NEW_FOR_HARDWARE = -201140
    HARDWARE_TOO_NEW_FOR_SOFTWARE = -201139
    TASK_CANNOT_RESTART_FIRST_SAMP_NOT_AVAIL_TO_GENERATE = -201138
    ONLY_USE_START_TRIG_SRC_PRPTY_WITH_DEV_DATA_LINES = -201137
    ONLY_USE_PAUSE_TRIG_SRC_PRPTY_WITH_DEV_DATA_LINES = -201136
    ONLY_USE_REF_TRIG_SRC_PRPTY_WITH_DEV_DATA_LINES = -201135
    PAUSE_TRIG_DIG_PATTERN_SIZE_DOES_NOT_MATCH_SRC_SIZE = -201134
    LINE_CONFLICT_CDAQ = -201133
    CANNOT_WRITE_BEYOND_FINAL_FINITE_SAMPLE = -201132
    REF_AND_START_TRIGGER_SRC_CANT_BE_SAME = -201131
    MEM_MAPPING_INCOMPATIBLE_WITH_PHYS_CHANS_IN_TASK = -201130
    OUTPUT_DRIVE_TYPE_MEM_MAPPING_CONFLICT = -201129
    CAPI_DEVICE_INDEX_INVALID = -201128
    RATIOMETRIC_DEVICES_MUST_USE_EXCITATION_FOR_SCALING = -201127
    PROPERTY_REQUIRES_PER_DEVICE_CFG = -201126
    AI_COUPLING_AND_AI_INPUT_SOURCE_CONFLICT = -201125
    ONLY_ONE_TASK_CAN_PERFORM_DO_MEMORY_MAPPING_AT_A_TIME = -201124
    TOO_MANY_CHANS_FOR_ANALOG_REF_TRIG_CDAQ = -201123
    SPECD_PROPERTY_VALUE_IS_INCOMPATIBLE_WITH_SAMPLE_TIMING_TYPE = -201122
    CPU_NOT_SUPPORTED_REQUIRE_SSE = -201121
    SPECD_PROPERTY_VALUE_IS_INCOMPATIBLE_WITH_SAMPLE_TIMING_RESPONSE_MODE = -201120
    CONFLICTING_NEXT_WRITE_IS_LAST_AND_REGEN_MODE_PROPERTIES = -201119
    M_STUDIO_OPERATION_DOES_NOT_SUPPORT_DEVICE_CONTEXT = -201118
    PROPERTY_VALUE_IN_CHANNEL_EXPANSION_CONTEXT_INVALID = -201117
    HW_TIMED_NON_BUFFERED_AO_NOT_SUPPORTED = -201116
    WAVEFORM_LENGTH_NOT_MULT_OF_QUANTUM = -201115
    DSA_EXPANSION_MIXED_BOARDS_WRONG_ORDER_IN_PXI_CHASSIS = -201114
    POWER_LEVEL_TOO_LOW_FOR_OOK = -201113
    DEVICE_COMPONENT_TEST_FAILURE = -201112
    USER_DEFINED_WFM_WITH_OOK_UNSUPPORTED = -201111
    INVALID_DIGITAL_MODULATION_USER_DEFINED_WAVEFORM = -201110
    BOTH_REF_IN_AND_REF_OUT_ENABLED = -201109
    BOTH_ANALOG_AND_DIGITAL_MODULATION_ENABLED = -201108
    BUFFERED_OPS_NOT_SUPPORTED_IN_SPECD_SLOT_FOR_CDAQ = -201107
    PHYS_CHAN_NOT_SUPPORTED_IN_SPECD_SLOT_FOR_CDAQ = -201106
    RESOURCE_RESERVED_WITH_CONFLICTING_SETTINGS = -201105
    INCONSISTENT_ANALOG_TRIG_SETTINGS_CDAQ = -201104
    TOO_MANY_CHANS_FOR_ANALOG_PAUSE_TRIG_CDAQ = -201103
    ANALOG_TRIG_NOT_FIRST_IN_SCAN_LIST_CDAQ = -201102
    TOO_MANY_CHANS_GIVEN_TIMING_TYPE = -201101
    SAMP_CLK_TIMEBASE_DIV_WITH_EXT_SAMP_CLK = -201100
    CANT_SAVE_TASK_WITH_PER_DEVICE_TIMING_PROPERTIES = -201099
    CONFLICTING_AUTO_ZERO_MODE = -201098
    SAMP_CLK_RATE_NOT_SUPPORTED_WITH_EAR_ENABLED = -201097
    SAMP_CLK_TIMEBASE_RATE_NOT_SPECD = -201096
    SESSION_CORRUPTED_BY_DLL_RELOAD = -201095
    ACTIVE_DEV_NOT_SUPPORTED_WITH_CHAN_EXPANSION = -201094
    SAMP_CLK_RATE_INVALID = -201093
    EXT_SYNC_PULSE_SRC_CANNOT_BE_EXPORTED = -201092
    SYNC_PULSE_MIN_DELAY_TO_START_NEEDED_FOR_EXT_SYNC_PULSE_SRC = -201091
    SYNC_PULSE_SRC_INVALID = -201090
    SAMP_CLK_TIMEBASE_RATE_INVALID = -201089
    SAMP_CLK_TIMEBASE_SRC_INVALID = -201088
    SAMP_CLK_RATE_MUST_BE_SPECD = -201087
    INVALID_ATTRIBUTE_NAME = -201086
    CJC_CHAN_NAME_MUST_BE_SET_WHEN_CJC_SRC_IS_SCANNABLE_CHAN = -201085
    HIDDEN_CHAN_MISSING_IN_CHANS_PROPERTY_IN_CFG_FILE = -201084
    CHAN_NAMES_NOT_SPECD_IN_CFG_FILE = -201083
    DUPLICATE_HIDDEN_CHAN_NAMES_IN_CFG_FILE = -201082
    DUPLICATE_CHAN_NAME_IN_CFG_FILE = -201081
    INVALID_SCC_MODULE_FOR_SLOT_SPECD = -201080
    INVALID_SCC_SLOT_NUMBER_SPECD = -201079
    INVALID_SECTION_IDENTIFIER = -201078
    INVALID_SECTION_NAME = -201077
    DA_QMX_VERSION_NOT_SUPPORTED = -201076
    SW_OBJECTS_FOUND_IN_FILE = -201075
    HW_OBJECTS_FOUND_IN_FILE = -201074
    LOCAL_CHANNEL_SPECD_WITH_NO_PARENT_TASK = -201073
    TASK_REFERENCES_MISSING_LOCAL_CHANNEL = -201072
    TASK_REFERENCES_LOCAL_CHANNEL_FROM_OTHER_TASK = -201071
    TASK_MISSING_CHANNEL_PROPERTY = -201070
    INVALID_LOCAL_CHAN_NAME = -201069
    INVALID_ESCAPE_CHARACTER_IN_STRING = -201068
    INVALID_TABLE_IDENTIFIER = -201067
    VALUE_FOUND_IN_INVALID_COLUMN = -201066
    MISSING_START_OF_TABLE = -201065
    FILE_MISSING_REQUIRED_DA_QMX_HEADER = -201064
    DEVICE_ID_DOES_NOT_MATCH = -201063
    BUFFERED_OPERATIONS_NOT_SUPPORTED_ON_SELECTED_LINES = -201062
    PROPERTY_CONFLICTS_WITH_SCALE = -201061
    INVALID_INI_FILE_SYNTAX = -201060
    DEVICE_INFO_FAILED_PXI_CHASSIS_NOT_IDENTIFIED = -201059
    INVALID_HW_PRODUCT_NUMBER = -201058
    INVALID_HW_PRODUCT_TYPE = -201057
    INVALID_NUMERIC_FORMAT_SPECD = -201056
    DUPLICATE_PROPERTY_IN_OBJECT = -201055
    INVALID_ENUM_VALUE_SPECD = -201054
    TEDS_SENSOR_PHYSICAL_CHANNEL_CONFLICT = -201053
    TOO_MANY_PHYSICAL_CHANS_FOR_TEDS_INTERFACE_SPECD = -201052
    INCAPABLE_TEDS_INTERFACE_CONTROLLING_DEVICE_SPECD = -201051
    SCC_CARRIER_SPECD_IS_MISSING = -201050
    INCAPABLE_SCC_DIGITIZING_DEVICE_SPECD = -201049
    ACCESSORY_SETTING_NOT_APPLICABLE = -201048
    DEVICE_AND_CONNECTOR_SPECD_ALREADY_OCCUPIED = -201047
    ILLEGAL_ACCESSORY_TYPE_FOR_DEVICE_SPECD = -201046
    INVALID_DEVICE_CONNECTOR_NUMBER_SPECD = -201045
    INVALID_ACCESSORY_NAME = -201044
    MORE_THAN_ONE_MATCH_FOR_SPECD_DEVICE = -201043
    NO_MATCH_FOR_SPECD_DEVICE = -201042
    PRODUCT_TYPE_AND_PRODUCT_NUMBER_CONFLICT = -201041
    EXTRA_PROPERTY_DETECTED_IN_SPECD_OBJECT = -201040
    REQUIRED_PROPERTY_MISSING = -201039
    CANT_SET_AUTHOR_FOR_LOCAL_CHAN = -201038
    INVALID_TIME_VALUE = -201037
    INVALID_TIME_FORMAT = -201036
    DIG_DEV_CHANS_SPECD_IN_MODE_OTHER_THAN_PARALLEL = -201035
    CASCADE_DIGITIZATION_MODE_NOT_SUPPORTED = -201034
    SPECD_SLOT_ALREADY_OCCUPIED = -201033
    INVALID_SCXI_SLOT_NUMBER_SPECD = -201032
    ADDRESS_ALREADY_IN_USE = -201031
    SPECD_DEVICE_DOES_NOT_SUPPORT_RTSI = -201030
    SPECD_DEVICE_IS_ALREADY_ON_RTSI_BUS = -201029
    IDENTIFIER_IN_USE = -201028
    WAIT_FOR_NEXT_SAMPLE_CLOCK_OR_READ_DETECTED_3_OR_MORE_MISSED_SAMP_CLKS = -201027
    HW_TIMED_AND_DATA_XFER_PIO = -201026
    NON_BUFFERED_AND_HW_TIMED = -201025
    CTR_OUT_SAMP_CLK_PERIOD_SHORTER_THAN_GEN_PULSE_TRAIN_PERIOD_POLLED = -201024
    CTR_OUT_SAMP_CLK_PERIOD_SHORTER_THAN_GEN_PULSE_TRAIN_PERIOD_2 = -201023
    CO_CANNOT_KEEP_UP_IN_HW_TIMED_SINGLE_POINT_POLLED = -201022
    WRITE_RECOVERY_CANNOT_KEEP_UP_IN_HW_TIMED_SINGLE_POINT = -201021
    NO_CHANGE_DETECTION_ON_SELECTED_LINE_FOR_DEVICE = -201020
    SMIO_PAUSE_TRIGGERS_NOT_SUPPORTED_WITH_CHANNEL_EXPANSION = -201019
    CLOCK_MASTER_FOR_EXTERNAL_CLOCK_NOT_LONGEST_PIPELINE = -201018
    UNSUPPORTED_UNICODE_BYTE_ORDER_MARKER = -201017
    TOO_MANY_INSTRUCTIONS_IN_LOOP_IN_SCRIPT = -201016
    PLL_NOT_LOCKED = -201015
    IF_ELSE_BLOCK_NOT_ALLOWED_IN_FINITE_REPEAT_LOOP_IN_SCRIPT = -201014
    IF_ELSE_BLOCK_NOT_ALLOWED_IN_CONDITIONAL_REPEAT_LOOP_IN_SCRIPT = -201013
    CLEAR_IS_LAST_INSTRUCTION_IN_IF_ELSE_BLOCK_IN_SCRIPT = -201012
    INVALID_WAIT_DURATION_BEFORE_IF_ELSE_BLOCK_IN_SCRIPT = -201011
    MARKER_POS_INVALID_BEFORE_IF_ELSE_BLOCK_IN_SCRIPT = -201010
    INVALID_SUBSET_LENGTH_BEFORE_IF_ELSE_BLOCK_IN_SCRIPT = -201009
    INVALID_WAVEFORM_LENGTH_BEFORE_IF_ELSE_BLOCK_IN_SCRIPT = -201008
    GENERATE_OR_FINITE_WAIT_INSTRUCTION_EXPECTED_BEFORE_IF_ELSE_BLOCK_IN_SCRIPT = -201007
    CAL_PASSWORD_NOT_SUPPORTED = -201006
    SETUP_CAL_NEEDED_BEFORE_ADJUST_CAL = -201005
    MULTIPLE_CHANS_NOT_SUPPORTED_DURING_CAL_SETUP = -201004
    DEV_CANNOT_BE_ACCESSED = -201003
    SAMP_CLK_RATE_DOESNT_MATCH_SAMP_CLK_SRC = -201002
    SAMP_CLK_RATE_NOT_SUPPORTED_WITH_EAR_DISABLED = -201001
    LAB_VIEW_VERSION_DOESNT_SUPPORT_DA_QMX_EVENTS = -201000
    CO_READY_FOR_NEW_VAL_NOT_SUPPORTED_WITH_ON_DEMAND = -200999
    CIHW_TIMED_SINGLE_POINT_NOT_SUPPORTED_FOR_MEAS_TYPE = -200998
    ON_DEMAND_NOT_SUPPORTED_WITH_HW_TIMED_SINGLE_POINT = -200997
    HW_TIMED_SINGLE_POINT_AND_DATA_XFER_NOT_PROG_IO = -200996
    MEM_MAP_AND_HW_TIMED_SINGLE_POINT = -200995
    CANNOT_SET_PROPERTY_WHEN_HW_TIMED_SINGLE_POINT_TASK_IS_RUNNING = -200994
    CTR_OUT_SAMP_CLK_PERIOD_SHORTER_THAN_GEN_PULSE_TRAIN_PERIOD = -200993
    TOO_MANY_EVENTS_GENERATED = -200992
    M_STUDIO_CPP_REMOVE_EVENTS_BEFORE_STOP = -200991
    CAPI_CANNOT_REGISTER_SYNC_EVENTS_FROM_MULTIPLE_THREADS = -200990
    READ_WAIT_NEXT_SAMP_CLK_WAIT_MISMATCH_TWO = -200989
    READ_WAIT_NEXT_SAMP_CLK_WAIT_MISMATCH_ONE = -200988
    DA_QMX_SIGNAL_EVENT_TYPE_NOT_SUPPORTED_BY_CHAN_TYPES_OR_DEVICES_IN_TASK = -200987
    CANNOT_UNREGISTER_DA_QMX_SOFTWARE_EVENT_WHILE_TASK_IS_RUNNING = -200986
    AUTO_START_WRITE_NOT_ALLOWED_EVENT_REGISTERED = -200985
    AUTO_START_READ_NOT_ALLOWED_EVENT_REGISTERED = -200984
    CANNOT_GET_PROPERTY_WHEN_TASK_NOT_RESERVED_COMMITTED_OR_RUNNING = -200983
    SIGNAL_EVENTS_NOT_SUPPORTED_BY_DEVICE = -200982
    EVERY_N_SAMPLES_ACQ_INTO_BUFFER_EVENT_NOT_SUPPORTED_BY_DEVICE = -200981
    EVERY_N_SAMPS_TRANSFERRED_FROM_BUFFER_EVENT_NOT_SUPPORTED_BY_DEVICE = -200980
    CAPI_SYNC_EVENTS_TASK_STATE_CHANGE_NOT_ALLOWED_FROM_DIFFERENT_THREAD = -200979
    DA_QMX_SW_EVENTS_WITH_DIFFERENT_CALL_MECHANISMS = -200978
    CANT_SAVE_CHAN_WITH_POLY_CAL_SCALE_AND_ALLOW_INTERACTIVE_EDIT = -200977
    CHAN_DOES_NOT_SUPPORT_CJC = -200976
    CO_READY_FOR_NEW_VAL_NOT_SUPPORTED_WITH_HW_TIMED_SINGLE_POINT = -200975
    DAC_ALLOW_CONN_TO_GND_NOT_SUPPORTED_BY_DEV_WHEN_REF_SRC_EXT = -200974
    CANT_GET_PROPERTY_TASK_NOT_RUNNING = -200973
    CANT_SET_PROPERTY_TASK_NOT_RUNNING = -200972
    CANT_SET_PROPERTY_TASK_NOT_RUNNING_COMMITTED = -200971
    AI_EVERY_N_SAMPS_EVENT_INTERVAL_NOT_MULTIPLE_OF_2 = -200970
    INVALID_TEDS_PHYS_CHAN_NOT_AI = -200969
    CAPI_CANNOT_PERFORM_TASK_OPERATION_IN_ASYNC_CALLBACK = -200968
    EVERY_N_SAMPS_TRANSFERRED_FROM_BUFFER_EVENT_ALREADY_REGISTERED = -200967
    EVERY_N_SAMPS_ACQ_INTO_BUFFER_EVENT_ALREADY_REGISTERED = -200966
    EVERY_N_SAMPS_TRANSFERRED_FROM_BUFFER_NOT_FOR_INPUT = -200965
    EVERY_N_SAMPS_ACQ_INTO_BUFFER_NOT_FOR_OUTPUT = -200964
    AO_SAMP_TIMING_TYPE_DIFFERENT_IN_2_TASKS = -200963
    COULD_NOT_DOWNLOAD_FIRMWARE_HW_DAMAGED = -200962
    COULD_NOT_DOWNLOAD_FIRMWARE_FILE_MISSING_OR_DAMAGED = -200961
    CANNOT_REGISTER_DA_QMX_SOFTWARE_EVENT_WHILE_TASK_IS_RUNNING = -200960
    DIFFERENT_RAW_DATA_COMPRESSION = -200959
    CONFIGURED_TEDS_INTERFACE_DEV_NOT_DETECTED = -200958
    COMPRESSED_SAMP_SIZE_EXCEEDS_RESOLUTION = -200957
    CHAN_DOES_NOT_SUPPORT_COMPRESSION = -200956
    DIFFERENT_RAW_DATA_FORMATS = -200955
    SAMP_CLK_OUTPUT_TERM_INCLUDES_START_TRIG_SRC = -200954
    START_TRIG_SRC_EQUAL_TO_SAMP_CLK_SRC = -200953
    EVENT_OUTPUT_TERM_INCLUDES_TRIG_SRC = -200952
    CO_MULTIPLE_WRITES_BETWEEN_SAMP_CLKS = -200951
    DONE_EVENT_ALREADY_REGISTERED = -200950
    SIGNAL_EVENT_ALREADY_REGISTERED = -200949
    CANNOT_HAVE_TIMED_LOOP_AND_DA_QMX_SIGNAL_EVENTS_IN_SAME_TASK = -200948
    NEED_LAB_VIEW_711_PATCH_TO_USE_DA_QMX_EVENTS = -200947
    START_FAILED_DUE_TO_WRITE_FAILURE = -200946
    DATA_XFER_CUSTOM_THRESHOLD_NOT_DMA_XFER_METHOD_SPECIFIED_FOR_DEV = -200945
    DATA_XFER_REQUEST_CONDITION_NOT_SPECIFIED_FOR_CUSTOM_THRESHOLD = -200944
    DATA_XFER_CUSTOM_THRESHOLD_NOT_SPECIFIED = -200943
    CAPI_SYNC_CALLBACK_NOT_SUPPORTED_ON_THIS_PLATFORM = -200942
    CAL_CHAN_REVERSE_POLY_COEF_NOT_SPECD = -200941
    CAL_CHAN_FORWARD_POLY_COEF_NOT_SPECD = -200940
    CHAN_CAL_REPEATED_NUMBER_IN_PRE_SCALED_VALS = -200939
    CHAN_CAL_TABLE_NUM_SCALED_NOT_EQUAL_NUM_PRESCALED_VALS = -200938
    CHAN_CAL_TABLE_SCALED_VALS_NOT_SPECD = -200937
    CHAN_CAL_TABLE_PRE_SCALED_VALS_NOT_SPECD = -200936
    CHAN_CAL_SCALE_TYPE_NOT_SET = -200935
    CHAN_CAL_EXPIRED = -200934
    CHAN_CAL_EXPIRATION_DATE_NOT_SET = -200933
    THREE_OUTPUT_PORT_COMBINATION_GIVEN_SAMP_TIMING_TYPE_653_X = -200932
    THREE_INPUT_PORT_COMBINATION_GIVEN_SAMP_TIMING_TYPE_653_X = -200931
    TWO_OUTPUT_PORT_COMBINATION_GIVEN_SAMP_TIMING_TYPE_653_X = -200930
    TWO_INPUT_PORT_COMBINATION_GIVEN_SAMP_TIMING_TYPE_653_X = -200929
    PATTERN_MATCHER_MAY_BE_USED_BY_ONE_TRIG_ONLY = -200928
    NO_CHANS_SPECD_FOR_PATTERN_SOURCE = -200927
    CHANGE_DETECTION_CHAN_NOT_IN_TASK = -200926
    CHANGE_DETECTION_CHAN_NOT_TRISTATED = -200925
    WAIT_MODE_VALUE_NOT_SUPPORTED_NON_BUFFERED = -200924
    WAIT_MODE_PROPERTY_NOT_SUPPORTED_NON_BUFFERED = -200923
    CANT_SAVE_PER_LINE_CONFIG_DIG_CHAN_SO_INTERACTIVE_EDITS_ALLOWED = -200922
    CANT_SAVE_NON_PORT_MULTI_LINE_DIG_CHAN_SO_INTERACTIVE_EDITS_ALLOWED = -200921
    BUFFER_SIZE_NOT_MULTIPLE_OF_EVERY_N_SAMPS_EVENT_INTERVAL_NO_IRQ_ON_DEV = -200920
    GLOBAL_TASK_NAME_ALREADY_CHAN_NAME = -200919
    GLOBAL_CHAN_NAME_ALREADY_TASK_NAME = -200918
    AO_EVERY_N_SAMPS_EVENT_INTERVAL_NOT_MULTIPLE_OF_2 = -200917
    SAMPLE_TIMEBASE_DIVISOR_NOT_SUPPORTED_GIVEN_TIMING_TYPE = -200916
    HANDSHAKE_EVENT_OUTPUT_TERM_NOT_SUPPORTED_GIVEN_TIMING_TYPE = -200915
    CHANGE_DETECTION_OUTPUT_TERM_NOT_SUPPORTED_GIVEN_TIMING_TYPE = -200914
    READY_FOR_TRANSFER_OUTPUT_TERM_NOT_SUPPORTED_GIVEN_TIMING_TYPE = -200913
    REF_TRIG_OUTPUT_TERM_NOT_SUPPORTED_GIVEN_TIMING_TYPE = -200912
    START_TRIG_OUTPUT_TERM_NOT_SUPPORTED_GIVEN_TIMING_TYPE = -200911
    SAMP_CLOCK_OUTPUT_TERM_NOT_SUPPORTED_GIVEN_TIMING_TYPE = -200910
    TWENTY_MHZ_TIMEBASE_NOT_SUPPORTED_GIVEN_TIMING_TYPE = -200909
    SAMP_CLOCK_SOURCE_NOT_SUPPORTED_GIVEN_TIMING_TYPE = -200908
    REF_TRIG_TYPE_NOT_SUPPORTED_GIVEN_TIMING_TYPE = -200907
    PAUSE_TRIG_TYPE_NOT_SUPPORTED_GIVEN_TIMING_TYPE = -200906
    HANDSHAKE_TRIG_TYPE_NOT_SUPPORTED_GIVEN_TIMING_TYPE = -200905
    START_TRIG_TYPE_NOT_SUPPORTED_GIVEN_TIMING_TYPE = -200904
    REF_CLK_SRC_NOT_SUPPORTED = -200903
    DATA_VOLTAGE_LOW_AND_HIGH_INCOMPATIBLE = -200902
    INVALID_CHAR_IN_DIG_PATTERN_STRING = -200901
    CANT_USE_PORT_3_ALONE_GIVEN_SAMP_TIMING_TYPE_ON_653_X = -200900
    CANT_USE_PORT_1_ALONE_GIVEN_SAMP_TIMING_TYPE_ON_653_X = -200899
    PARTIAL_USE_OF_PHYSICAL_LINES_WITHIN_PORT_NOT_SUPPORTED_653_X = -200898
    PHYSICAL_CHAN_NOT_SUPPORTED_GIVEN_SAMP_TIMING_TYPE_653_X = -200897
    CAN_EXPORT_ONLY_DIG_EDGE_TRIGS = -200896
    REF_TRIG_DIG_PATTERN_SIZE_DOES_NOT_MATCH_SOURCE_SIZE = -200895
    START_TRIG_DIG_PATTERN_SIZE_DOES_NOT_MATCH_SOURCE_SIZE = -200894
    CHANGE_DETECTION_RISING_AND_FALLING_EDGE_CHAN_DONT_MATCH = -200893
    PHYSICAL_CHANS_FOR_CHANGE_DETECTION_AND_PATTERN_MATCH_653_X = -200892
    CAN_EXPORT_ONLY_ONBOARD_SAMP_CLK = -200891
    INTERNAL_SAMP_CLK_NOT_RISING_EDGE = -200890
    REF_TRIG_DIG_PATTERN_CHAN_NOT_IN_TASK = -200889
    REF_TRIG_DIG_PATTERN_CHAN_NOT_TRISTATED = -200888
    START_TRIG_DIG_PATTERN_CHAN_NOT_IN_TASK = -200887
    START_TRIG_DIG_PATTERN_CHAN_NOT_TRISTATED = -200886
    PXI_STAR_AND_CLOCK_10_SYNC = -200885
    GLOBAL_CHAN_CANNOT_BE_SAVED_SO_INTERACTIVE_EDITS_ALLOWED = -200884
    TASK_CANNOT_BE_SAVED_SO_INTERACTIVE_EDITS_ALLOWED = -200883
    INVALID_GLOBAL_CHAN = -200882
    EVERY_N_SAMPS_EVENT_ALREADY_REGISTERED = -200881
    EVERY_N_SAMPS_EVENT_INTERVAL_ZERO_NOT_SUPPORTED = -200880
    CHAN_SIZE_TOO_BIG_FOR_U_16_PORT_WRITE = -200879
    CHAN_SIZE_TOO_BIG_FOR_U_16_PORT_READ = -200878
    BUFFER_SIZE_NOT_MULTIPLE_OF_EVERY_N_SAMPS_EVENT_INTERVAL_WHEN_DMA = -200877
    WRITE_WHEN_TASK_NOT_RUNNING_CO_TICKS = -200876
    WRITE_WHEN_TASK_NOT_RUNNING_CO_FREQ = -200875
    WRITE_WHEN_TASK_NOT_RUNNING_CO_TIME = -200874
    AO_MIN_MAX_NOT_SUPPORTED_DAC_RANGE_TOO_SMALL = -200873
    AO_MIN_MAX_NOT_SUPPORTED_GIVEN_DAC_RANGE = -200872
    AO_MIN_MAX_NOT_SUPPORTED_GIVEN_DAC_RANGE_AND_OFFSET_VAL = -200871
    AO_MIN_MAX_NOT_SUPPORTED_DAC_OFFSET_VAL_INAPPROPRIATE = -200870
    AO_MIN_MAX_NOT_SUPPORTED_GIVEN_DAC_OFFSET_VAL = -200869
    AO_MIN_MAX_NOT_SUPPORTED_DAC_REF_VAL_TOO_SMALL = -200868
    AO_MIN_MAX_NOT_SUPPORTED_GIVEN_DAC_REF_VAL = -200867
    AO_MIN_MAX_NOT_SUPPORTED_GIVEN_DAC_REF_AND_OFFSET_VAL = -200866
    WHEN_ACQ_COMP_AND_NUM_SAMPS_PER_CHAN_EXCEEDS_ON_BRD_BUF_SIZE = -200865
    WHEN_ACQ_COMP_AND_NO_REF_TRIG = -200864
    WAIT_FOR_NEXT_SAMP_CLK_NOT_SUPPORTED = -200863
    DEV_IN_UNIDENTIFIED_PXI_CHASSIS = -200862
    MAX_SOUND_PRESSURE_MIC_SENSITIVIT_RELATED_AI_PROPERTIES_NOT_SUPPORTED_BY_DEV = -200861
    MAX_SOUND_PRESSURE_AND_MIC_SENSITIVITY_NOT_SUPPORTED_BY_DEV = -200860
    AO_BUFFER_SIZE_ZERO_FOR_SAMP_CLK_TIMING_TYPE = -200859
    AO_CALL_WRITE_BEFORE_START_FOR_SAMP_CLK_TIMING_TYPE = -200858
    INVALID_CAL_LOW_PASS_CUTOFF_FREQ = -200857
    SIMULATION_CANNOT_BE_DISABLED_FOR_DEV_CREATED_AS_SIMULATED_DEV = -200856
    CANNOT_ADD_NEW_DEVS_AFTER_TASK_CONFIGURATION = -200855
    DIFFT_SYNC_PULSE_SRC_AND_SAMP_CLK_TIMEBASE_SRC_DEV_MULTI_DEV_TASK = -200854
    TERM_WITHOUT_DEV_IN_MULTI_DEV_TASK = -200853
    SYNC_NO_DEV_SAMP_CLK_TIMEBASE_OR_SYNC_PULSE_IN_PXI_SLOT_2 = -200852
    PHYSICAL_CHAN_NOT_ON_THIS_CONNECTOR = -200851
    NUM_SAMPS_TO_WAIT_NOT_GREATER_THAN_ZERO_IN_SCRIPT = -200850
    NUM_SAMPS_TO_WAIT_NOT_MULTIPLE_OF_ALIGNMENT_QUANTUM_IN_SCRIPT = -200849
    EVERY_N_SAMPLES_EVENT_NOT_SUPPORTED_FOR_NON_BUFFERED_TASKS = -200848
    BUFFERED_AND_DATA_XFER_PIO = -200847
    CANNOT_WRITE_WHEN_AUTO_START_FALSE_AND_TASK_NOT_RUNNING = -200846
    NON_BUFFERED_AND_DATA_XFER_INTERRUPTS = -200845
    WRITE_FAILED_MULTIPLE_CTRS_WITH_FREQOUT = -200844
    READ_NOT_COMPLETE_BEFORE_3_SAMP_CLK_EDGES = -200843
    CTR_HW_TIMED_SINGLE_POINT_AND_DATA_XFER_NOT_PROG_IO = -200842
    PRESCALER_NOT_1_FOR_INPUT_TERMINAL = -200841
    PRESCALER_NOT_1_FOR_TIMEBASE_SRC = -200840
    SAMP_CLK_TIMING_TYPE_WHEN_TRISTATE_IS_FALSE = -200839
    OUTPUT_BUFFER_SIZE_NOT_MULT_OF_XFER_SIZE = -200838
    SAMP_PER_CHAN_NOT_MULT_OF_XFER_SIZE = -200837
    WRITE_TO_TEDS_FAILED = -200836
    SCXI_DEV_NOT_USABLE_POWER_TURNED_OFF = -200835
    CANNOT_READ_WHEN_AUTO_START_FALSE_BUF_SIZE_ZERO_AND_TASK_NOT_RUNNING = -200834
    CANNOT_READ_WHEN_AUTO_START_FALSE_HW_TIMED_SINGLE_PT_AND_TASK_NOT_RUNNING = -200833
    CANNOT_READ_WHEN_AUTO_START_FALSE_ON_DEMAND_AND_TASK_NOT_RUNNING = -200832
    SIMULTANEOUS_AO_WHEN_NOT_ON_DEMAND_TIMING = -200831
    MEM_MAP_AND_SIMULTANEOUS_AO = -200830
    WRITE_FAILED_MULTIPLE_CO_OUTPUT_TYPES = -200829
    WRITE_TO_TEDS_NOT_SUPPORTED_ON_RT = -200828
    VIRTUAL_TEDS_DATA_FILE_ERROR = -200827
    TEDS_SENSOR_DATA_ERROR = -200826
    DATA_SIZE_MORE_THAN_SIZE_OF_EEPROM_ON_TEDS = -200825
    PROM_ON_TEDS_CONTAINS_BASIC_TEDS_DATA = -200824
    PROM_ON_TEDS_ALREADY_WRITTEN = -200823
    TEDS_DOES_NOT_CONTAIN_PROM = -200822
    HW_TIMED_SINGLE_POINT_NOT_SUPPORTED_AI = -200821
    HW_TIMED_SINGLE_POINT_ODD_NUM_CHANS_IN_AI_TASK = -200820
    CANT_USE_ONLY_ON_BOARD_MEM_WITH_PROGRAMMED_IO = -200819
    SWITCH_DEV_SHUT_DOWN_DUE_TO_HIGH_TEMP = -200818
    EXCITATION_NOT_SUPPORTED_WHEN_TERM_CFG_DIFF = -200817
    TEDS_MIN_ELEC_VAL_GE_MAX_ELEC_VAL = -200816
    TEDS_MIN_PHYS_VAL_GE_MAX_PHYS_VAL = -200815
    CI_ONBOARD_CLOCK_NOT_SUPPORTED_AS_INPUT_TERM = -200814
    INVALID_SAMP_MODE_FOR_POSITION_MEAS = -200813
    TRIG_WHEN_AOHW_TIMED_SINGLE_PT_SAMP_MODE = -200812
    DA_QMX_CANT_USE_STRING_DUE_TO_UNKNOWN_CHAR = -200811
    DA_QMX_CANT_RETRIEVE_STRING_DUE_TO_UNKNOWN_CHAR = -200810
    CLEAR_TEDS_NOT_SUPPORTED_ON_RT = -200809
    CFG_TEDS_NOT_SUPPORTED_ON_RT = -200808
    PROG_FILTER_CLK_CFGD_TO_DIFFERENT_MIN_PULSE_WIDTH_BY_SAME_TASK_1_PER_DEV = -200807
    PROG_FILTER_CLK_CFGD_TO_DIFFERENT_MIN_PULSE_WIDTH_BY_ANOTHER_TASK_1_PER_DEV = -200806
    NO_LAST_EXT_CAL_DATE_TIME_LAST_EXT_CAL_NOT_DA_QMX = -200804
    CANNOT_WRITE_NOT_STARTED_AUTO_START_FALSE_NOT_ON_DEMAND_HW_TIMED_SGL_PT = -200803
    CANNOT_WRITE_NOT_STARTED_AUTO_START_FALSE_NOT_ON_DEMAND_BUF_SIZE_ZERO = -200802
    CO_INVALID_TIMING_SRC_DUE_TO_SIGNAL = -200801
    CI_INVALID_TIMING_SRC_FOR_SAMP_CLK_DUE_TO_SAMP_TIMING_TYPE = -200800
    CI_INVALID_TIMING_SRC_FOR_EVENT_CNT_DUE_TO_SAMP_MODE = -200799
    NO_CHANGE_DETECT_ON_NON_INPUT_DIG_LINE_FOR_DEV = -200798
    EMPTY_STRING_TERM_NAME_NOT_SUPPORTED = -200797
    MEM_MAP_ENABLED_FOR_HW_TIMED_NON_BUFFERED_AO = -200796
    DEV_ONBOARD_MEM_OVERFLOW_DURING_HW_TIMED_NON_BUFFERED_GEN = -200795
    CODA_QMX_WRITE_MULTIPLE_CHANS = -200794
    CANT_MAINTAIN_EXISTING_VALUE_AO_SYNC = -200793
    M_STUDIO_MULTIPLE_PHYS_CHANS_NOT_SUPPORTED = -200792
    CANT_CONFIGURE_TEDS_FOR_CHAN = -200791
    WRITE_DATA_TYPE_TOO_SMALL = -200790
    READ_DATA_TYPE_TOO_SMALL = -200789
    MEASURED_BRIDGE_OFFSET_TOO_HIGH = -200788
    START_TRIG_CONFLICT_WITH_COHW_TIMED_SINGLE_PT = -200787
    SAMP_CLK_RATE_EXT_SAMP_CLK_TIMEBASE_RATE_MISMATCH = -200786
    INVALID_TIMING_SRC_DUE_TO_SAMP_TIMING_TYPE = -200785
    VIRTUAL_TEDS_FILE_NOT_FOUND = -200784
    M_STUDIO_NO_FORWARD_POLY_SCALE_COEFFS = -200783
    M_STUDIO_NO_REVERSE_POLY_SCALE_COEFFS = -200782
    M_STUDIO_NO_POLY_SCALE_COEFFS_USE_CALC = -200781
    M_STUDIO_NO_FORWARD_POLY_SCALE_COEFFS_USE_CALC = -200780
    M_STUDIO_NO_REVERSE_POLY_SCALE_COEFFS_USE_CALC = -200779
    CO_SAMP_MODE_SAMP_TIMING_TYPE_SAMP_CLK_CONFLICT = -200778
    DEV_CANNOT_PRODUCE_MIN_PULSE_WIDTH = -200777
    CANNOT_PRODUCE_MIN_PULSE_WIDTH_GIVEN_PROPERTY_VALUES = -200776
    TERM_CFGD_TO_DIFFERENT_MIN_PULSE_WIDTH_BY_ANOTHER_TASK = -200775
    TERM_CFGD_TO_DIFFERENT_MIN_PULSE_WIDTH_BY_ANOTHER_PROPERTY = -200774
    DIG_SYNC_NOT_AVAILABLE_ON_TERM = -200773
    DIG_FILTER_NOT_AVAILABLE_ON_TERM = -200772
    DIG_FILTER_ENABLED_MIN_PULSE_WIDTH_NOT_CFG = -200771
    DIG_FILTER_AND_SYNC_BOTH_ENABLED = -200770
    HW_TIMED_SINGLE_POINT_AO_AND_DATA_XFER_NOT_PROG_IO = -200769
    NON_BUFFERED_AO_AND_DATA_XFER_NOT_PROG_IO = -200768
    PROG_IO_DATA_XFER_FOR_BUFFERED_AO = -200767
    TEDS_LEGACY_TEMPLATE_ID_INVALID_OR_UNSUPPORTED = -200766
    TEDS_MAPPING_METHOD_INVALID_OR_UNSUPPORTED = -200765
    TEDS_LINEAR_MAPPING_SLOPE_ZERO = -200764
    AI_INPUT_BUFFER_SIZE_NOT_MULT_OF_XFER_SIZE = -200763
    NO_SYNC_PULSE_EXT_SAMP_CLK_TIMEBASE = -200762
    NO_SYNC_PULSE_ANOTHER_TASK_RUNNING = -200761
    AO_MIN_MAX_NOT_IN_GAIN_RANGE = -200760
    AO_MIN_MAX_NOT_IN_DAC_RANGE = -200759
    DEV_ONLY_SUPPORTS_SAMP_CLK_TIMING_AO = -200758
    DEV_ONLY_SUPPORTS_SAMP_CLK_TIMING_AI = -200757
    TEDS_INCOMPATIBLE_SENSOR_AND_MEAS_TYPE = -200756
    TEDS_MULTIPLE_CAL_TEMPLATES_NOT_SUPPORTED = -200755
    TEDS_TEMPLATE_PARAMETERS_NOT_SUPPORTED = -200754
    PARSING_TEDS_DATA = -200753
    MULTIPLE_ACTIVE_PHYS_CHANS_NOT_SUPPORTED = -200752
    NO_CHANS_SPECD_FOR_CHANGE_DETECT = -200751
    INVALID_CAL_VOLTAGE_FOR_GIVEN_GAIN = -200750
    INVALID_CAL_GAIN = -200749
    MULTIPLE_WRITES_BETWEEN_SAMP_CLKS = -200748
    INVALID_ACQ_TYPE_FOR_FREQOUT = -200747
    SUITABLE_TIMEBASE_NOT_FOUND_TIME_COMBO_2 = -200746
    SUITABLE_TIMEBASE_NOT_FOUND_FREQUENCY_COMBO_2 = -200745
    REF_CLK_RATE_REF_CLK_SRC_MISMATCH = -200744
    NO_TEDS_TERMINAL_BLOCK = -200743
    CORRUPTED_TEDS_MEMORY = -200742
    TEDS_NOT_SUPPORTED = -200741
    TIMING_SRC_TASK_STARTED_BEFORE_TIMED_LOOP = -200740
    PROPERTY_NOT_SUPPORTED_FOR_TIMING_SRC = -200739
    TIMING_SRC_DOES_NOT_EXIST = -200738
    INPUT_BUFFER_SIZE_NOT_EQUAL_SAMPS_PER_CHAN_FOR_FINITE_SAMP_MODE = -200737
    FREQOUT_CANNOT_PRODUCE_DESIRED_FREQUENCY_2 = -200736
    EXT_REF_CLK_RATE_NOT_SPECIFIED = -200735
    DEVICE_DOES_NOT_SUPPORT_DMA_DATA_XFER_FOR_NON_BUFFERED_ACQ = -200734
    DIG_FILTER_MIN_PULSE_WIDTH_SET_WHEN_TRISTATE_IS_FALSE = -200733
    DIG_FILTER_ENABLE_SET_WHEN_TRISTATE_IS_FALSE = -200732
    NO_HW_TIMING_WITH_ON_DEMAND = -200731
    CANNOT_DETECT_CHANGES_WHEN_TRISTATE_IS_FALSE = -200730
    CANNOT_HANDSHAKE_WHEN_TRISTATE_IS_FALSE = -200729
    LINES_USED_FOR_STATIC_INPUT_NOT_FOR_HANDSHAKING_CONTROL = -200728
    LINES_USED_FOR_HANDSHAKING_CONTROL_NOT_FOR_STATIC_INPUT = -200727
    LINES_USED_FOR_STATIC_INPUT_NOT_FOR_HANDSHAKING_INPUT = -200726
    LINES_USED_FOR_HANDSHAKING_INPUT_NOT_FOR_STATIC_INPUT = -200725
    DIFFERENT_DI_TRISTATE_VALS_FOR_CHANS_IN_TASK = -200724
    TIMEBASE_CAL_FREQ_VARIANCE_TOO_LARGE = -200723
    TIMEBASE_CAL_FAILED_TO_CONVERGE = -200722
    INADEQUATE_RESOLUTION_FOR_TIMEBASE_CAL = -200721
    INVALID_AO_GAIN_CAL_CONST = -200720
    INVALID_AO_OFFSET_CAL_CONST = -200719
    INVALID_AI_GAIN_CAL_CONST = -200718
    INVALID_AI_OFFSET_CAL_CONST = -200717
    DIG_OUTPUT_OVERRUN = -200716
    DIG_INPUT_OVERRUN = -200715
    ACQ_STOPPED_DRIVER_CANT_XFER_DATA_FAST_ENOUGH = -200714
    CHANS_CANT_APPEAR_IN_SAME_TASK = -200713
    INPUT_CFG_FAILED_BECAUSE_WATCHDOG_EXPIRED = -200712
    ANALOG_TRIG_CHAN_NOT_EXTERNAL = -200711
    TOO_MANY_CHANS_FOR_INTERNAL_AI_INPUT_SRC = -200710
    TEDS_SENSOR_NOT_DETECTED = -200709
    PRPTY_GET_SPECD_ACTIVE_ITEM_FAILED_DUE_TO_DIFFT_VALUES = -200708
    ROUTING_DEST_TERM_PXI_CLK_10_IN_NOT_IN_SLOT_2 = -200706
    ROUTING_DEST_TERM_PXI_STAR_X_NOT_IN_SLOT_2 = -200705
    ROUTING_SRC_TERM_PXI_STAR_X_NOT_IN_SLOT_2 = -200704
    ROUTING_SRC_TERM_PXI_STAR_IN_SLOT_16_AND_ABOVE = -200703
    ROUTING_DEST_TERM_PXI_STAR_IN_SLOT_16_AND_ABOVE = -200702
    ROUTING_DEST_TERM_PXI_STAR_IN_SLOT_2 = -200701
    ROUTING_SRC_TERM_PXI_STAR_IN_SLOT_2 = -200700
    ROUTING_DEST_TERM_PXI_CHASSIS_NOT_IDENTIFIED = -200699
    ROUTING_SRC_TERM_PXI_CHASSIS_NOT_IDENTIFIED = -200698
    FAILED_TO_ACQUIRE_CAL_DATA = -200697
    BRIDGE_OFFSET_NULLING_CAL_NOT_SUPPORTED = -200696
    AI_MAX_NOT_SPECIFIED = -200695
    AI_MIN_NOT_SPECIFIED = -200694
    ODD_TOTAL_BUFFER_SIZE_TO_WRITE = -200693
    ODD_TOTAL_NUM_SAMPS_TO_WRITE = -200692
    BUFFER_WITH_WAIT_MODE = -200691
    BUFFER_WITH_HW_TIMED_SINGLE_POINT_SAMP_MODE = -200690
    CO_WRITE_PULSE_LOW_TICKS_NOT_SUPPORTED = -200689
    CO_WRITE_PULSE_HIGH_TICKS_NOT_SUPPORTED = -200688
    CO_WRITE_PULSE_LOW_TIME_OUT_OF_RANGE = -200687
    CO_WRITE_PULSE_HIGH_TIME_OUT_OF_RANGE = -200686
    CO_WRITE_FREQ_OUT_OF_RANGE = -200685
    CO_WRITE_DUTY_CYCLE_OUT_OF_RANGE = -200684
    INVALID_INSTALLATION = -200683
    REF_TRIG_MASTER_SESSION_UNAVAILABLE = -200682
    ROUTE_FAILED_BECAUSE_WATCHDOG_EXPIRED = -200681
    DEVICE_SHUT_DOWN_DUE_TO_HIGH_TEMP = -200680
    NO_MEM_MAP_WHEN_HW_TIMED_SINGLE_POINT = -200679
    WRITE_FAILED_BECAUSE_WATCHDOG_EXPIRED = -200678
    DIFFT_INTERNAL_AI_INPUT_SRCS = -200677
    DIFFT_AI_INPUT_SRC_IN_ONE_CHAN_GROUP = -200676
    INTERNAL_AI_INPUT_SRC_IN_MULT_CHAN_GROUPS = -200675
    SWITCH_OP_FAILED_DUE_TO_PREV_ERROR = -200674
    WROTE_MULTI_SAMPS_USING_SINGLE_SAMP_WRITE = -200673
    MISMATCHED_INPUT_ARRAY_SIZES = -200672
    CANT_EXCEED_RELAY_DRIVE_LIMIT = -200671
    DAC_RNG_LOW_NOT_EQUAL_TO_MINUS_REF_VAL = -200670
    CANT_ALLOW_CONNECT_DAC_TO_GND = -200669
    WATCHDOG_TIMEOUT_OUT_OF_RANGE_AND_NOT_SPECIAL_VAL = -200668
    NO_WATCHDOG_OUTPUT_ON_PORT_RESERVED_FOR_INPUT = -200667
    NO_INPUT_ON_PORT_CFGD_FOR_WATCHDOG_OUTPUT = -200666
    WATCHDOG_EXPIRATION_STATE_NOT_EQUAL_FOR_LINES_IN_PORT = -200665
    CANNOT_PERFORM_OP_WHEN_TASK_NOT_RESERVED = -200664
    POWERUP_STATE_NOT_SUPPORTED = -200663
    WATCHDOG_TIMER_NOT_SUPPORTED = -200662
    OP_NOT_SUPPORTED_WHEN_REF_CLK_SRC_NONE = -200661
    SAMP_CLK_RATE_UNAVAILABLE = -200660
    PRPTY_GET_SPECD_SINGLE_ACTIVE_CHAN_FAILED_DUE_TO_DIFFT_VALS = -200659
    PRPTY_GET_IMPLIED_ACTIVE_CHAN_FAILED_DUE_TO_DIFFT_VALS = -200658
    PRPTY_GET_SPECD_ACTIVE_CHAN_FAILED_DUE_TO_DIFFT_VALS = -200657
    NO_REGEN_WHEN_USING_BRD_MEM = -200656
    NONBUFFERED_READ_MORE_THAN_SAMPS_PER_CHAN = -200655
    WATCHDOG_EXPIRATION_TRISTATE_NOT_SPECD_FOR_ENTIRE_PORT = -200654
    POWERUP_TRISTATE_NOT_SPECD_FOR_ENTIRE_PORT = -200653
    POWERUP_STATE_NOT_SPECD_FOR_ENTIRE_PORT = -200652
    CANT_SET_WATCHDOG_EXPIRATION_ON_DIG_IN_CHAN = -200651
    CANT_SET_POWERUP_STATE_ON_DIG_IN_CHAN = -200650
    PHYS_CHAN_NOT_IN_TASK = -200649
    PHYS_CHAN_DEV_NOT_IN_TASK = -200648
    DIG_INPUT_NOT_SUPPORTED = -200647
    DIG_FILTER_INTERVAL_NOT_EQUAL_FOR_LINES = -200646
    DIG_FILTER_INTERVAL_ALREADY_CFGD = -200645
    CANT_RESET_EXPIRED_WATCHDOG = -200644
    ACTIVE_CHAN_TOO_MANY_LINES_SPECD_WHEN_GETTING_PRPTY = -200643
    ACTIVE_CHAN_NOT_SPECD_WHEN_GETTING_1_LINE_PRPTY = -200642
    DIG_PRPTY_CANNOT_BE_SET_PER_LINE = -200641
    SEND_ADV_CMPLT_AFTER_WAIT_FOR_TRIG_IN_SCANLIST = -200640
    DISCONNECTION_REQUIRED_IN_SCANLIST = -200639
    TWO_WAIT_FOR_TRIGS_AFTER_CONNECTION_IN_SCANLIST = -200638
    ACTION_SEPARATOR_REQUIRED_AFTER_BREAKING_CONNECTION_IN_SCANLIST = -200637
    CONNECTION_IN_SCANLIST_MUST_WAIT_FOR_TRIG = -200636
    ACTION_NOT_SUPPORTED_TASK_NOT_WATCHDOG = -200635
    WFM_NAME_SAME_AS_SCRIPT_NAME = -200634
    SCRIPT_NAME_SAME_AS_WFM_NAME = -200633
    DSF_STOP_CLOCK = -200632
    DSF_READY_FOR_START_CLOCK = -200631
    WRITE_OFFSET_NOT_MULT_OF_INCR = -200630
    DIFFERENT_PRPTY_VALS_NOT_SUPPORTED_ON_DEV = -200629
    REF_AND_PAUSE_TRIG_CONFIGURED = -200628
    FAILED_TO_ENABLE_HIGH_SPEED_INPUT_CLOCK = -200627
    EMPTY_PHYS_CHAN_IN_POWER_UP_STATES_ARRAY = -200626
    ACTIVE_PHYS_CHAN_TOO_MANY_LINES_SPECD_WHEN_GETTING_PRPTY = -200625
    ACTIVE_PHYS_CHAN_NOT_SPECD_WHEN_GETTING_1_LINE_PRPTY = -200624
    PXI_DEV_TEMP_CAUSED_SHUT_DOWN = -200623
    INVALID_NUM_SAMPS_TO_WRITE = -200622
    OUTPUT_FIFO_UNDERFLOW_2 = -200621
    REPEATED_AI_PHYSICAL_CHAN = -200620
    MULT_SCAN_OPS_IN_ONE_CHASSIS = -200619
    INVALID_AI_CHAN_ORDER = -200618
    REVERSE_POWER_PROTECTION_ACTIVATED = -200617
    INVALID_ASYN_OP_HANDLE = -200616
    FAILED_TO_ENABLE_HIGH_SPEED_OUTPUT = -200615
    CANNOT_READ_PAST_END_OF_RECORD = -200614
    ACQ_STOPPED_TO_PREVENT_INPUT_BUFFER_OVERWRITE_ONE_DATA_XFER_MECH = -200613
    ZERO_BASED_CHAN_INDEX_INVALID = -200612
    NO_CHANS_OF_GIVEN_TYPE_IN_TASK = -200611
    SAMP_CLK_SRC_INVALID_FOR_OUTPUT_VALID_FOR_INPUT = -200610
    OUTPUT_BUF_SIZE_TOO_SMALL_TO_START_GEN = -200609
    INPUT_BUF_SIZE_TOO_SMALL_TO_START_ACQ = -200608
    EXPORT_TWO_SIGNALS_ON_SAME_TERMINAL = -200607
    CHAN_INDEX_INVALID = -200606
    RANGE_SYNTAX_NUMBER_TOO_BIG = -200605
    NULL_PTR = -200604
    SCALED_MIN_EQUAL_MAX = -200603
    PRE_SCALED_MIN_EQUAL_MAX = -200602
    PROPERTY_NOT_SUPPORTED_FOR_SCALE_TYPE = -200601
    CHANNEL_NAME_GENERATION_NUMBER_TOO_BIG = -200600
    REPEATED_NUMBER_IN_SCALED_VALUES = -200599
    REPEATED_NUMBER_IN_PRE_SCALED_VALUES = -200598
    LINES_ALREADY_RESERVED_FOR_OUTPUT = -200597
    SWITCH_OPERATION_CHANS_SPAN_MULTIPLE_DEVS_IN_LIST = -200596
    INVALID_ID_IN_LIST_AT_BEGINNING_OF_SWITCH_OPERATION = -200595
    M_STUDIO_INVALID_POLY_DIRECTION = -200594
    M_STUDIO_PROPERTY_GET_WHILE_TASK_NOT_VERIFIED = -200593
    RANGE_WITH_TOO_MANY_OBJECTS = -200592
    CPP_DOT_NET_API_NEGATIVE_BUFFER_SIZE = -200591
    CPP_CANT_REMOVE_INVALID_EVENT_HANDLER = -200590
    CPP_CANT_REMOVE_EVENT_HANDLER_TWICE = -200589
    CPP_CANT_REMOVE_OTHER_OBJECTS_EVENT_HANDLER = -200588
    DIG_LINES_RESERVED_OR_UNAVAILABLE = -200587
    DSF_FAILED_TO_RESET_STREAM = -200586
    DSF_READY_FOR_OUTPUT_NOT_ASSERTED = -200585
    SAMP_TO_WRITE_PER_CHAN_NOT_MULTIPLE_OF_INCR = -200584
    AO_PROPERTIES_CAUSE_VOLTAGE_BELOW_MIN = -200583
    AO_PROPERTIES_CAUSE_VOLTAGE_OVER_MAX = -200582
    PROPERTY_NOT_SUPPORTED_WHEN_REF_CLK_SRC_NONE = -200581
    AI_MAX_TOO_SMALL = -200580
    AI_MAX_TOO_LARGE = -200579
    AI_MIN_TOO_SMALL = -200578
    AI_MIN_TOO_LARGE = -200577
    BUILT_IN_CJC_SRC_NOT_SUPPORTED = -200576
    TOO_MANY_POST_TRIG_SAMPS_PER_CHAN = -200575
    TRIG_LINE_NOT_FOUND_SINGLE_DEV_ROUTE = -200574
    DIFFERENT_INTERNAL_AI_INPUT_SOURCES = -200573
    DIFFERENT_AI_INPUT_SRC_IN_ONE_CHAN_GROUP = -200572
    INTERNAL_AI_INPUT_SRC_IN_MULTIPLE_CHAN_GROUPS = -200571
    CAPI_CHAN_INDEX_INVALID = -200570
    COLLECTION_DOES_NOT_MATCH_CHAN_TYPE = -200569
    OUTPUT_CANT_START_CHANGED_REGENERATION_MODE = -200568
    OUTPUT_CANT_START_CHANGED_BUFFER_SIZE = -200567
    CHAN_SIZE_TOO_BIG_FOR_U_32_PORT_WRITE = -200566
    CHAN_SIZE_TOO_BIG_FOR_U_8_PORT_WRITE = -200565
    CHAN_SIZE_TOO_BIG_FOR_U_32_PORT_READ = -200564
    CHAN_SIZE_TOO_BIG_FOR_U_8_PORT_READ = -200563
    INVALID_DIG_DATA_WRITE = -200562
    INVALID_AO_DATA_WRITE = -200561
    WAIT_UNTIL_DONE_DOES_NOT_INDICATE_DONE = -200560
    MULTI_CHAN_TYPES_IN_TASK = -200559
    MULTI_DEVS_IN_TASK = -200558
    CANNOT_SET_PROPERTY_WHEN_TASK_RUNNING = -200557
    CANNOT_GET_PROPERTY_WHEN_TASK_NOT_COMMITTED_OR_RUNNING = -200556
    LEADING_UNDERSCORE_IN_STRING = -200555
    TRAILING_SPACE_IN_STRING = -200554
    LEADING_SPACE_IN_STRING = -200553
    INVALID_CHAR_IN_STRING = -200552
    DLL_BECAME_UNLOCKED = -200551
    DLL_LOCK = -200550
    SELF_CAL_CONSTS_INVALID = -200549
    INVALID_TRIG_COUPLING_EXCEPT_FOR_EXT_TRIG_CHAN = -200548
    WRITE_FAILS_BUFFER_SIZE_AUTO_CONFIGURED = -200547
    EXT_CAL_ADJUST_EXT_REF_VOLTAGE_FAILED = -200546
    SELF_CAL_FAILED_EXT_NOISE_OR_REF_VOLTAGE_OUT_OF_CAL = -200545
    EXT_CAL_TEMPERATURE_NOT_DA_QMX = -200544
    EXT_CAL_DATE_TIME_NOT_DA_QMX = -200543
    SELF_CAL_TEMPERATURE_NOT_DA_QMX = -200542
    SELF_CAL_DATE_TIME_NOT_DA_QMX = -200541
    DAC_REF_VAL_NOT_SET = -200540
    ANALOG_MULTI_SAMP_WRITE_NOT_SUPPORTED = -200539
    INVALID_ACTION_IN_CONTROL_TASK = -200538
    POLY_COEFFS_INCONSISTENT = -200537
    SENSOR_VAL_TOO_LOW = -200536
    SENSOR_VAL_TOO_HIGH = -200535
    WAVEFORM_NAME_TOO_LONG = -200534
    IDENTIFIER_TOO_LONG_IN_SCRIPT = -200533
    UNEXPECTED_ID_FOLLOWING_SWITCH_CHAN_NAME = -200532
    RELAY_NAME_NOT_SPECIFIED_IN_LIST = -200531
    UNEXPECTED_ID_FOLLOWING_RELAY_NAME_IN_LIST = -200530
    UNEXPECTED_ID_FOLLOWING_SWITCH_OP_IN_LIST = -200529
    INVALID_LINE_GROUPING = -200528
    CTR_MIN_MAX = -200527
    WRITE_CHAN_TYPE_MISMATCH = -200526
    READ_CHAN_TYPE_MISMATCH = -200525
    WRITE_NUM_CHANS_MISMATCH = -200524
    ONE_CHAN_READ_FOR_MULTI_CHAN_TASK = -200523
    CANNOT_SELF_CAL_DURING_EXT_CAL = -200522
    MEAS_CAL_ADJUST_OSCILLATOR_PHASE_DAC = -200521
    INVALID_CAL_CONST_CAL_ADC_ADJUSTMENT = -200520
    INVALID_CAL_CONST_OSCILLATOR_FREQ_DAC_VALUE = -200519
    INVALID_CAL_CONST_OSCILLATOR_PHASE_DAC_VALUE = -200518
    INVALID_CAL_CONST_OFFSET_DAC_VALUE = -200517
    INVALID_CAL_CONST_GAIN_DAC_VALUE = -200516
    INVALID_NUM_CAL_ADC_READS_TO_AVERAGE = -200515
    INVALID_CFG_CAL_ADJUST_DIRECT_PATH_OUTPUT_IMPEDANCE = -200514
    INVALID_CFG_CAL_ADJUST_MAIN_PATH_OUTPUT_IMPEDANCE = -200513
    INVALID_CFG_CAL_ADJUST_MAIN_PATH_POST_AMP_GAIN_AND_OFFSET = -200512
    INVALID_CFG_CAL_ADJUST_MAIN_PATH_PRE_AMP_GAIN = -200511
    INVALID_CFG_CAL_ADJUST_MAIN_PRE_AMP_OFFSET = -200510
    MEAS_CAL_ADJUST_CAL_ADC = -200509
    MEAS_CAL_ADJUST_OSCILLATOR_FREQUENCY = -200508
    MEAS_CAL_ADJUST_DIRECT_PATH_OUTPUT_IMPEDANCE = -200507
    MEAS_CAL_ADJUST_MAIN_PATH_OUTPUT_IMPEDANCE = -200506
    MEAS_CAL_ADJUST_DIRECT_PATH_GAIN = -200505
    MEAS_CAL_ADJUST_MAIN_PATH_POST_AMP_GAIN_AND_OFFSET = -200504
    MEAS_CAL_ADJUST_MAIN_PATH_PRE_AMP_GAIN = -200503
    MEAS_CAL_ADJUST_MAIN_PATH_PRE_AMP_OFFSET = -200502
    INVALID_DATE_TIME_IN_EEPROM = -200501
    UNABLE_TO_LOCATE_ERROR_RESOURCES = -200500
    DOT_NET_API_NOT_UNSIGNED_32_BIT_NUMBER = -200499
    INVALID_RANGE_OF_OBJECTS_SYNTAX_IN_STRING = -200498
    ATTEMPT_TO_ENABLE_LINE_NOT_PREVIOUSLY_DISABLED = -200497
    INVALID_CHAR_IN_PATTERN = -200496
    INTERMEDIATE_BUFFER_FULL = -200495
    LOAD_TASK_FAILS_BECAUSE_NO_TIMING_ON_DEV = -200494
    CAPI_RESERVED_PARAM_NOT_NULL_NOR_EMPTY = -200493
    CAPI_RESERVED_PARAM_NOT_NULL = -200492
    CAPI_RESERVED_PARAM_NOT_ZERO = -200491
    SAMPLE_VALUE_OUT_OF_RANGE = -200490
    CHAN_ALREADY_IN_TASK = -200489
    VIRTUAL_CHAN_DOES_NOT_EXIST = -200488
    CHAN_NOT_IN_TASK = -200486
    TASK_NOT_IN_DATA_NEIGHBORHOOD = -200485
    CANT_SAVE_TASK_WITHOUT_REPLACE = -200484
    CANT_SAVE_CHAN_WITHOUT_REPLACE = -200483
    DEV_NOT_IN_TASK = -200482
    DEV_ALREADY_IN_TASK = -200481
    CAN_NOT_PERFORM_OP_WHILE_TASK_RUNNING = -200479
    CAN_NOT_PERFORM_OP_WHEN_NO_CHANS_IN_TASK = -200478
    CAN_NOT_PERFORM_OP_WHEN_NO_DEV_IN_TASK = -200477
    CANNOT_PERFORM_OP_WHEN_TASK_NOT_RUNNING = -200475
    OPERATION_TIMED_OUT = -200474
    CANNOT_READ_WHEN_AUTO_START_FALSE_AND_TASK_NOT_RUNNING_OR_COMMITTED = -200473
    CANNOT_WRITE_WHEN_AUTO_START_FALSE_AND_TASK_NOT_RUNNING_OR_COMMITTED = -200472
    TASK_VERSION_NEW = -200470
    CHAN_VERSION_NEW = -200469
    EMPTY_STRING = -200467
    CHANNEL_SIZE_TOO_BIG_FOR_PORT_READ_TYPE = -200466
    CHANNEL_SIZE_TOO_BIG_FOR_PORT_WRITE_TYPE = -200465
    EXPECTED_NUMBER_OF_CHANNELS_VERIFICATION_FAILED = -200464
    NUM_LINES_MISMATCH_IN_READ_OR_WRITE = -200463
    OUTPUT_BUFFER_EMPTY = -200462
    INVALID_CHAN_NAME = -200461
    READ_NO_INPUT_CHANS_IN_TASK = -200460
    WRITE_NO_OUTPUT_CHANS_IN_TASK = -200459
    PROPERTY_NOT_SUPPORTED_NOT_INPUT_TASK = -200457
    PROPERTY_NOT_SUPPORTED_NOT_OUTPUT_TASK = -200456
    GET_PROPERTY_NOT_INPUT_BUFFERED_TASK = -200455
    GET_PROPERTY_NOT_OUTPUT_BUFFERED_TASK = -200454
    INVALID_TIMEOUT_VAL = -200453
    ATTRIBUTE_NOT_SUPPORTED_IN_TASK_CONTEXT = -200452
    ATTRIBUTE_NOT_QUERYABLE_UNLESS_TASK_IS_COMMITTED = -200451
    ATTRIBUTE_NOT_SETTABLE_WHEN_TASK_IS_RUNNING = -200450
    DAC_RNG_LOW_NOT_MINUS_REF_VAL_NOR_ZERO = -200449
    DAC_RNG_HIGH_NOT_EQUAL_REF_VAL = -200448
    UNITS_NOT_FROM_CUSTOM_SCALE = -200447
    INVALID_VOLTAGE_READING_DURING_EXT_CAL = -200446
    CAL_FUNCTION_NOT_SUPPORTED = -200445
    INVALID_PHYSICAL_CHAN_FOR_CAL = -200444
    EXT_CAL_NOT_COMPLETE = -200443
    CANT_SYNC_TO_EXT_STIMULUS_FREQ_DURING_CAL = -200442
    UNABLE_TO_DETECT_EXT_STIMULUS_FREQ_DURING_CAL = -200441
    INVALID_CLOSE_ACTION = -200440
    EXT_CAL_FUNCTION_OUTSIDE_EXT_CAL_SESSION = -200439
    INVALID_CAL_AREA = -200438
    EXT_CAL_CONSTS_INVALID = -200437
    START_TRIG_DELAY_WITH_EXT_SAMP_CLK = -200436
    DELAY_FROM_SAMP_CLK_WITH_EXT_CONV = -200435
    FEWER_THAN_2_PRE_SCALED_VALS = -200434
    FEWER_THAN_2_SCALED_VALUES = -200433
    PHYS_CHAN_OUTPUT_TYPE = -200432
    PHYS_CHAN_MEAS_TYPE = -200431
    INVALID_PHYS_CHAN_TYPE = -200430
    LAB_VIEW_EMPTY_TASK_OR_CHANS = -200429
    LAB_VIEW_INVALID_TASK_OR_CHANS = -200428
    INVALID_REF_CLK_RATE = -200427
    INVALID_EXT_TRIG_IMPEDANCE = -200426
    HYST_TRIG_LEVEL_AI_MAX = -200425
    LINE_NUM_INCOMPATIBLE_WITH_VIDEO_SIGNAL_FORMAT = -200424
    TRIG_WINDOW_AI_MIN_AI_MAX_COMBO = -200423
    TRIG_AI_MIN_AI_MAX = -200422
    HYST_TRIG_LEVEL_AI_MIN = -200421
    INVALID_SAMP_RATE_CONSIDER_RIS = -200420
    INVALID_READ_POS_DURING_RIS = -200419
    IMMED_TRIG_DURING_RIS_MODE = -200418
    TDC_NOT_ENABLED_DURING_RIS_MODE = -200417
    MULTI_REC_WITH_RIS = -200416
    INVALID_REF_CLK_SRC = -200415
    INVALID_SAMP_CLK_SRC = -200414
    INSUFFICIENT_ON_BOARD_MEM_FOR_NUM_RECS_AND_SAMPS = -200413
    INVALID_AI_ATTENUATION = -200412
    AC_COUPLING_NOT_ALLOWED_WITH_50_OHM_IMPEDANCE = -200411
    INVALID_RECORD_NUM = -200410
    ZERO_SLOPE_LINEAR_SCALE = -200409
    ZERO_REVERSE_POLY_SCALE_COEFFS = -200408
    ZERO_FORWARD_POLY_SCALE_COEFFS = -200407
    NO_REVERSE_POLY_SCALE_COEFFS = -200406
    NO_FORWARD_POLY_SCALE_COEFFS = -200405
    NO_POLY_SCALE_COEFFS = -200404
    REVERSE_POLY_ORDER_LESS_THAN_NUM_PTS_TO_COMPUTE = -200403
    REVERSE_POLY_ORDER_NOT_POSITIVE = -200402
    NUM_PTS_TO_COMPUTE_NOT_POSITIVE = -200401
    WAVEFORM_LENGTH_NOT_MULTIPLE_OF_INCR = -200400
    CAPI_NO_EXTENDED_ERROR_INFO_AVAILABLE = -200399
    CVI_FUNCTION_NOT_FOUND_IN_DA_QMX_DLL = -200398
    CVI_FAILED_TO_LOAD_DA_QMX_DLL = -200397
    NO_COMMON_TRIG_LINE_FOR_IMMED_ROUTE = -200396
    NO_COMMON_TRIG_LINE_FOR_TASK_ROUTE = -200395
    F_64_PRPTY_VAL_NOT_UNSIGNED_INT = -200394
    REGISTER_NOT_WRITABLE = -200393
    INVALID_OUTPUT_VOLTAGE_AT_SAMP_CLK_RATE = -200392
    STROBE_PHASE_SHIFT_DCM_BECAME_UNLOCKED = -200391
    DRIVE_PHASE_SHIFT_DCM_BECAME_UNLOCKED = -200390
    CLK_OUT_PHASE_SHIFT_DCM_BECAME_UNLOCKED = -200389
    OUTPUT_BOARD_CLK_DCM_BECAME_UNLOCKED = -200388
    INPUT_BOARD_CLK_DCM_BECAME_UNLOCKED = -200387
    INTERNAL_CLK_DCM_BECAME_UNLOCKED = -200386
    DCM_LOCK = -200385
    DATA_LINE_RESERVED_FOR_DYNAMIC_OUTPUT = -200384
    INVALID_REF_CLK_SRC_GIVEN_SAMP_CLK_SRC = -200383
    NO_PATTERN_MATCHER_AVAILABLE = -200382
    INVALID_DELAY_SAMP_RATE_BELOW_PHASE_SHIFT_DCM_THRESH = -200381
    STRAIN_GAGE_CALIBRATION = -200380
    INVALID_EXT_CLOCK_FREQ_AND_DIV_COMBO = -200379
    CUSTOM_SCALE_DOES_NOT_EXIST = -200378
    ONLY_FRONT_END_CHAN_OPS_DURING_SCAN = -200377
    INVALID_OPTION_FOR_DIGITAL_PORT_CHANNEL = -200376
    UNSUPPORTED_SIGNAL_TYPE_EXPORT_SIGNAL = -200375
    INVALID_SIGNAL_TYPE_EXPORT_SIGNAL = -200374
    UNSUPPORTED_TRIG_TYPE_SENDS_SW_TRIG = -200373
    INVALID_TRIG_TYPE_SENDS_SW_TRIG = -200372
    REPEATED_PHYSICAL_CHAN = -200371
    RESOURCES_IN_USE_FOR_ROUTE_IN_TASK = -200370
    RESOURCES_IN_USE_FOR_ROUTE = -200369
    ROUTE_NOT_SUPPORTED_BY_HW = -200368
    RESOURCES_IN_USE_FOR_EXPORT_SIGNAL_POLARITY = -200367
    RESOURCES_IN_USE_FOR_INVERSION_IN_TASK = -200366
    RESOURCES_IN_USE_FOR_INVERSION = -200365
    EXPORT_SIGNAL_POLARITY_NOT_SUPPORTED_BY_HW = -200364
    INVERSION_NOT_SUPPORTED_BY_HW = -200363
    OVERLOADED_CHANS_EXIST_NOT_READ = -200362
    INPUT_FIFO_OVERFLOW_2 = -200361
    CJC_CHAN_NOT_SPECD = -200360
    CTR_EXPORT_SIGNAL_NOT_POSSIBLE = -200359
    REF_TRIG_WHEN_CONTINUOUS = -200358
    INCOMPATIBLE_SENSOR_OUTPUT_AND_DEVICE_INPUT_RANGES = -200357
    CUSTOM_SCALE_NAME_USED = -200356
    PROPERTY_VAL_NOT_SUPPORTED_BY_HW = -200355
    PROPERTY_VAL_NOT_VALID_TERM_NAME = -200354
    RESOURCES_IN_USE_FOR_PROPERTY = -200353
    CJC_CHAN_ALREADY_USED = -200352
    FORWARD_POLYNOMIAL_COEF_NOT_SPECD = -200351
    TABLE_SCALE_NUM_PRE_SCALED_AND_SCALED_VALS_NOT_EQUAL = -200350
    TABLE_SCALE_PRE_SCALED_VALS_NOT_SPECD = -200349
    TABLE_SCALE_SCALED_VALS_NOT_SPECD = -200348
    INTERMEDIATE_BUFFER_SIZE_NOT_MULTIPLE_OF_INCR = -200347
    EVENT_PULSE_WIDTH_OUT_OF_RANGE = -200346
    EVENT_DELAY_OUT_OF_RANGE = -200345
    SAMP_PER_CHAN_NOT_MULTIPLE_OF_INCR = -200344
    CANNOT_CALCULATE_NUM_SAMPS_TASK_NOT_STARTED = -200343
    SCRIPT_NOT_IN_MEM = -200342
    ONBOARD_MEM_TOO_SMALL = -200341
    READ_ALL_AVAILABLE_DATA_WITHOUT_BUFFER = -200340
    PULSE_ACTIVE_AT_START = -200339
    CAL_TEMP_NOT_SUPPORTED = -200338
    DELAY_FROM_SAMP_CLK_TOO_LONG = -200337
    DELAY_FROM_SAMP_CLK_TOO_SHORT = -200336
    AI_CONV_RATE_TOO_HIGH = -200335
    DELAY_FROM_START_TRIG_TOO_LONG = -200334
    DELAY_FROM_START_TRIG_TOO_SHORT = -200333
    SAMP_RATE_TOO_HIGH = -200332
    SAMP_RATE_TOO_LOW = -200331
    PFI_0_USED_FOR_ANALOG_AND_DIGITAL_SRC = -200330
    PRIMING_CFG_FIFO = -200329
    CANNOT_OPEN_TOPOLOGY_CFG_FILE = -200328
    INVALID_DT_INSIDE_WFM_DATA_TYPE = -200327
    ROUTE_SRC_AND_DEST_SAME = -200326
    REVERSE_POLYNOMIAL_COEF_NOT_SPECD = -200325
    DEV_ABSENT_OR_UNAVAILABLE = -200324
    NO_ADV_TRIG_FOR_MULTI_DEV_SCAN = -200323
    INTERRUPTS_INSUFFICIENT_DATA_XFER_MECH = -200322
    INVALID_ATTENTUATION_BASED_ON_MIN_MAX = -200321
    CABLED_MODULE_CANNOT_ROUTE_SSH = -200320
    CABLED_MODULE_CANNOT_ROUTE_CONV_CLK = -200319
    INVALID_EXCIT_VAL_FOR_SCALING = -200318
    NO_DEV_MEM_FOR_SCRIPT = -200317
    SCRIPT_DATA_UNDERFLOW = -200316
    NO_DEV_MEM_FOR_WAVEFORM = -200315
    STREAM_DCM_BECAME_UNLOCKED = -200314
    STREAM_DCM_LOCK = -200313
    WAVEFORM_NOT_IN_MEM = -200312
    WAVEFORM_WRITE_OUT_OF_BOUNDS = -200311
    WAVEFORM_PREVIOUSLY_ALLOCATED = -200310
    SAMP_CLK_TB_MASTER_TB_DIV_NOT_APPROPRIATE_FOR_SAMP_TB_SRC = -200309
    SAMP_TB_RATE_SAMP_TB_SRC_MISMATCH = -200308
    MASTER_TB_RATE_MASTER_TB_SRC_MISMATCH = -200307
    SAMPS_PER_CHAN_TOO_BIG = -200306
    FINITE_PULSE_TRAIN_NOT_POSSIBLE = -200305
    EXT_MASTER_TIMEBASE_RATE_NOT_SPECIFIED = -200304
    EXT_SAMP_CLK_SRC_NOT_SPECIFIED = -200303
    INPUT_SIGNAL_SLOWER_THAN_MEAS_TIME = -200302
    CANNOT_UPDATE_PULSE_GEN_PROPERTY = -200301
    INVALID_TIMING_TYPE = -200300
    PROPERTY_UNAVAIL_WHEN_USING_ONBOARD_MEMORY = -200297
    CANNOT_WRITE_AFTER_START_WITH_ONBOARD_MEMORY = -200295
    NOT_ENOUGH_SAMPS_WRITTEN_FOR_INITIAL_XFER_RQST_CONDITION = -200294
    NO_MORE_SPACE = -200293
    SAMPLES_CAN_NOT_YET_BE_WRITTEN = -200292
    GEN_STOPPED_TO_PREVENT_INTERMEDIATE_BUFFER_REGEN_OF_OLD_SAMPLES = -200291
    GEN_STOPPED_TO_PREVENT_REGEN_OF_OLD_SAMPLES = -200290
    SAMPLES_NO_LONGER_WRITEABLE = -200289
    SAMPLES_WILL_NEVER_BE_GENERATED = -200288
    NEGATIVE_WRITE_SAMPLE_NUMBER = -200287
    NO_ACQ_STARTED = -200286
    SAMPLES_NOT_YET_AVAILABLE = -200284
    ACQ_STOPPED_TO_PREVENT_INTERMEDIATE_BUFFER_OVERFLOW = -200283
    NO_REF_TRIG_CONFIGURED = -200282
    CANNOT_READ_RELATIVE_TO_REF_TRIG_UNTIL_DONE = -200281
    SAMPLES_NO_LONGER_AVAILABLE = -200279
    SAMPLES_WILL_NEVER_BE_AVAILABLE = -200278
    NEGATIVE_READ_SAMPLE_NUMBER = -200277
    EXTERNAL_SAMP_CLK_AND_REF_CLK_THRU_SAME_TERM = -200276
    EXT_SAMP_CLK_RATE_TOO_LOW_FOR_CLK_IN = -200275
    EXT_SAMP_CLK_RATE_TOO_HIGH_FOR_BACKPLANE = -200274
    SAMP_CLK_RATE_AND_DIV_COMBO = -200273
    SAMP_CLK_RATE_TOO_LOW_FOR_DIV_DOWN = -200272
    PRODUCT_OF_AO_MIN_AND_GAIN_TOO_SMALL = -200271
    INTERPOLATION_RATE_NOT_POSSIBLE = -200270
    OFFSET_TOO_LARGE = -200269
    OFFSET_TOO_SMALL = -200268
    PRODUCT_OF_AO_MAX_AND_GAIN_TOO_LARGE = -200267
    MIN_AND_MAX_NOT_SYMMETRIC = -200266
    INVALID_ANALOG_TRIG_SRC = -200265
    TOO_MANY_CHANS_FOR_ANALOG_REF_TRIG = -200264
    TOO_MANY_CHANS_FOR_ANALOG_PAUSE_TRIG = -200263
    TRIG_WHEN_ON_DEMAND_SAMP_TIMING = -200262
    INCONSISTENT_ANALOG_TRIG_SETTINGS = -200261
    MEM_MAP_DATA_XFER_MODE_SAMP_TIMING_COMBO = -200260
    INVALID_JUMPERED_ATTR = -200259
    INVALID_GAIN_BASED_ON_MIN_MAX = -200258
    INCONSISTENT_EXCIT = -200257
    TOPOLOGY_NOT_SUPPORTED_BY_CFG_TERM_BLOCK = -200256
    BUILT_IN_TEMP_SENSOR_NOT_SUPPORTED = -200255
    INVALID_TERM = -200254
    CANNOT_TRISTATE_TERM = -200253
    CANNOT_TRISTATE_BUSY_TERM = -200252
    NO_DMA_CHANS_AVAILABLE = -200251
    INVALID_WAVEFORM_LENGTH_WITHIN_LOOP_IN_SCRIPT = -200250
    INVALID_SUBSET_LENGTH_WITHIN_LOOP_IN_SCRIPT = -200249
    MARKER_POS_INVALID_FOR_LOOP_IN_SCRIPT = -200248
    INTEGER_EXPECTED_IN_SCRIPT = -200247
    PLL_BECAME_UNLOCKED = -200246
    PLL_LOCK = -200245
    DDC_CLK_OUT_DCM_BECAME_UNLOCKED = -200244
    DDC_CLK_OUT_DCM_LOCK = -200243
    CLK_DOUBLER_DCM_BECAME_UNLOCKED = -200242
    CLK_DOUBLER_DCM_LOCK = -200241
    SAMP_CLK_DCM_BECAME_UNLOCKED = -200240
    SAMP_CLK_DCM_LOCK = -200239
    SAMP_CLK_TIMEBASE_DCM_BECAME_UNLOCKED = -200238
    SAMP_CLK_TIMEBASE_DCM_LOCK = -200237
    ATTR_CANNOT_BE_RESET = -200236
    EXPLANATION_NOT_FOUND = -200235
    WRITE_BUFFER_TOO_SMALL = -200234
    SPECIFIED_ATTR_NOT_VALID = -200233
    ATTR_CANNOT_BE_READ = -200232
    ATTR_CANNOT_BE_SET = -200231
    NULL_PTR_FOR_C_API = -200230
    READ_BUFFER_TOO_SMALL = -200229
    BUFFER_TOO_SMALL_FOR_STRING = -200228
    NO_AVAIL_TRIG_LINES_ON_DEVICE = -200227
    TRIG_BUS_LINE_NOT_AVAIL = -200226
    COULD_NOT_RESERVE_REQUESTED_TRIG_LINE = -200225
    TRIG_LINE_NOT_FOUND = -200224
    SCXI_1126_THRESH_HYST_COMBINATION = -200223
    ACQ_STOPPED_TO_PREVENT_INPUT_BUFFER_OVERWRITE = -200222
    TIMEOUT_EXCEEDED = -200221
    INVALID_DEVICE_ID = -200220
    INVALID_AO_CHAN_ORDER = -200219
    SAMPLE_TIMING_TYPE_AND_DATA_XFER_MODE = -200218
    BUFFER_WITH_ON_DEMAND_SAMP_TIMING = -200217
    BUFFER_AND_DATA_XFER_MODE = -200216
    MEM_MAP_AND_BUFFER = -200215
    NO_ANALOG_TRIG_HW = -200214
    TOO_MANY_PRETRIG_PLUS_MIN_POST_TRIG_SAMPS = -200213
    INCONSISTENT_UNITS_SPECIFIED = -200212
    MULTIPLE_RELAYS_FOR_SINGLE_RELAY_OP = -200211
    MULTIPLE_DEV_IDS_PER_CHASSIS_SPECIFIED_IN_LIST = -200210
    DUPLICATE_DEV_ID_IN_LIST = -200209
    INVALID_RANGE_STATEMENT_CHAR_IN_LIST = -200208
    INVALID_DEVICE_ID_IN_LIST = -200207
    TRIGGER_POLARITY_CONFLICT = -200206
    CANNOT_SCAN_WITH_CURRENT_TOPOLOGY = -200205
    UNEXPECTED_IDENTIFIER_IN_FULLY_SPECIFIED_PATH_IN_LIST = -200204
    SWITCH_CANNOT_DRIVE_MULTIPLE_TRIG_LINES = -200203
    INVALID_RELAY_NAME = -200202
    SWITCH_SCANLIST_TOO_BIG = -200201
    SWITCH_CHAN_IN_USE = -200200
    SWITCH_NOT_RESET_BEFORE_SCAN = -200199
    INVALID_TOPOLOGY = -200198
    ATTR_NOT_SUPPORTED = -200197
    UNEXPECTED_END_OF_ACTIONS_IN_LIST = -200196
    POWER_LIMIT_EXCEEDED = -200195
    HW_UNEXPECTEDLY_POWERED_OFF_AND_ON = -200194
    SWITCH_OPERATION_NOT_SUPPORTED = -200193
    ONLY_CONTINUOUS_SCAN_SUPPORTED = -200192
    SWITCH_DIFFERENT_TOPOLOGY_WHEN_SCANNING = -200191
    DISCONNECT_PATH_NOT_SAME_AS_EXISTING_PATH = -200190
    CONNECTION_NOT_PERMITTED_ON_CHAN_RESERVED_FOR_ROUTING = -200189
    CANNOT_CONNECT_SRC_CHANS = -200188
    CANNOT_CONNECT_CHANNEL_TO_ITSELF = -200187
    CHANNEL_NOT_RESERVED_FOR_ROUTING = -200186
    CANNOT_CONNECT_CHANS_DIRECTLY = -200185
    CHANS_ALREADY_CONNECTED = -200184
    CHAN_DUPLICATED_IN_PATH = -200183
    NO_PATH_TO_DISCONNECT = -200182
    INVALID_SWITCH_CHAN = -200181
    NO_PATH_AVAILABLE_BETWEEN_2_SWITCH_CHANS = -200180
    EXPLICIT_CONNECTION_EXISTS = -200179
    SWITCH_DIFFERENT_SETTLING_TIME_WHEN_SCANNING = -200178
    OPERATION_ONLY_PERMITTED_WHILE_SCANNING = -200177
    OPERATION_NOT_PERMITTED_WHILE_SCANNING = -200176
    HARDWARE_NOT_RESPONDING = -200175
    INVALID_SAMP_AND_MASTER_TIMEBASE_RATE_COMBO = -200173
    NON_ZERO_BUFFER_SIZE_IN_PROG_IO_XFER = -200172
    VIRTUAL_CHAN_NAME_USED = -200171
    PHYSICAL_CHAN_DOES_NOT_EXIST = -200170
    MEM_MAP_ONLY_FOR_PROG_IO_XFER = -200169
    TOO_MANY_CHANS = -200168
    CANNOT_HAVE_CJ_TEMP_WITH_OTHER_CHANS = -200167
    OUTPUT_BUFFER_UNDERWRITE = -200166
    SENSOR_INVALID_COMPLETION_RESISTANCE = -200163
    VOLTAGE_EXCIT_INCOMPATIBLE_WITH_2_WIRE_CFG = -200162
    INT_EXCIT_SRC_NOT_AVAILABLE = -200161
    CANNOT_CREATE_CHANNEL_AFTER_TASK_VERIFIED = -200160
    LINES_RESERVED_FOR_SCXI_CONTROL = -200159
    COULD_NOT_RESERVE_LINES_FOR_SCXI_CONTROL = -200158
    CALIBRATION_FAILED = -200157
    REFERENCE_FREQUENCY_INVALID = -200156
    REFERENCE_RESISTANCE_INVALID = -200155
    REFERENCE_CURRENT_INVALID = -200154
    REFERENCE_VOLTAGE_INVALID = -200153
    EEPROM_DATA_INVALID = -200152
    CABLED_MODULE_NOT_CAPABLE_OF_ROUTING_AI = -200151
    CHANNEL_NOT_AVAILABLE_IN_PARALLEL_MODE = -200150
    EXTERNAL_TIMEBASE_RATE_NOT_KNOWN_FOR_DELAY = -200149
    FREQOUT_CANNOT_PRODUCE_DESIRED_FREQUENCY = -200148
    MULTIPLE_COUNTER_INPUT_TASK = -200147
    COUNTER_START_PAUSE_TRIGGER_CONFLICT = -200146
    COUNTER_INPUT_PAUSE_TRIGGER_AND_SAMPLE_CLOCK_INVALID = -200145
    COUNTER_OUTPUT_PAUSE_TRIGGER_INVALID = -200144
    COUNTER_TIMEBASE_RATE_NOT_SPECIFIED = -200143
    COUNTER_TIMEBASE_RATE_NOT_FOUND = -200142
    COUNTER_OVERFLOW = -200141
    COUNTER_NO_TIMEBASE_EDGES_BETWEEN_GATES = -200140
    COUNTER_MAX_MIN_RANGE_FREQ = -200139
    COUNTER_MAX_MIN_RANGE_TIME = -200138
    SUITABLE_TIMEBASE_NOT_FOUND_TIME_COMBO = -200137
    SUITABLE_TIMEBASE_NOT_FOUND_FREQUENCY_COMBO = -200136
    INTERNAL_TIMEBASE_SOURCE_DIVISOR_COMBO = -200135
    INTERNAL_TIMEBASE_SOURCE_RATE_COMBO = -200134
    INTERNAL_TIMEBASE_RATE_DIVISOR_SOURCE_COMBO = -200133
    EXTERNAL_TIMEBASE_RATE_NOTKNOWN_FOR_RATE = -200132
    ANALOG_TRIG_CHAN_NOT_FIRST_IN_SCAN_LIST = -200131
    NO_DIVISOR_FOR_EXTERNAL_SIGNAL = -200130
    ATTRIBUTE_INCONSISTENT_ACROSS_REPEATED_PHYSICAL_CHANNELS = -200128
    CANNOT_HANDSHAKE_WITH_PORT_0 = -200127
    CONTROL_LINE_CONFLICT_ON_PORT_C = -200126
    LINES_4_TO_7_CONFIGURED_FOR_OUTPUT = -200125
    LINES_4_TO_7_CONFIGURED_FOR_INPUT = -200124
    LINES_0_TO_3_CONFIGURED_FOR_OUTPUT = -200123
    LINES_0_TO_3_CONFIGURED_FOR_INPUT = -200122
    PORT_CONFIGURED_FOR_OUTPUT = -200121
    PORT_CONFIGURED_FOR_INPUT = -200120
    PORT_CONFIGURED_FOR_STATIC_DIGITAL_OPS = -200119
    PORT_RESERVED_FOR_HANDSHAKING = -200118
    PORT_DOES_NOT_SUPPORT_HANDSHAKING_DATA_IO = -200117
    CANNOT_TRISTATE_8255_OUTPUT_LINES = -200116
    TEMPERATURE_OUT_OF_RANGE_FOR_CALIBRATION = -200113
    CALIBRATION_HANDLE_INVALID = -200112
    PASSWORD_REQUIRED = -200111
    INCORRECT_PASSWORD = -200110
    PASSWORD_TOO_LONG = -200109
    CALIBRATION_SESSION_ALREADY_OPEN = -200108
    SCXI_MODULE_INCORRECT = -200107
    ATTRIBUTE_INCONSISTENT_ACROSS_CHANNELS_ON_DEVICE = -200106
    SCXI_1122_RESISTANCE_CHAN_NOT_SUPPORTED_FOR_CFG = -200105
    BRACKET_PAIRING_MISMATCH_IN_LIST = -200104
    INCONSISTENT_NUM_SAMPLES_TO_WRITE = -200103
    INCORRECT_DIGITAL_PATTERN = -200102
    INCORRECT_NUM_CHANNELS_TO_WRITE = -200101
    INCORRECT_READ_FUNCTION = -200100
    PHYSICAL_CHANNEL_NOT_SPECIFIED = -200099
    MORE_THAN_ONE_TERMINAL = -200098
    MORE_THAN_ONE_ACTIVE_CHANNEL_SPECIFIED = -200097
    INVALID_NUMBER_SAMPLES_TO_READ = -200096
    ANALOG_WAVEFORM_EXPECTED = -200095
    DIGITAL_WAVEFORM_EXPECTED = -200094
    ACTIVE_CHANNEL_NOT_SPECIFIED = -200093
    FUNCTION_NOT_SUPPORTED_FOR_DEVICE_TASKS = -200092
    FUNCTION_NOT_IN_LIBRARY = -200091
    LIBRARY_NOT_PRESENT = -200090
    DUPLICATE_TASK = -200089
    INVALID_TASK = -200088
    INVALID_CHANNEL = -200087
    INVALID_SYNTAX_FOR_PHYSICAL_CHANNEL_RANGE = -200086
    MIN_NOT_LESS_THAN_MAX = -200082
    SAMPLE_RATE_NUM_CHANS_CONVERT_PERIOD_COMBO = -200081
    AO_DURING_COUNTER_1_DMA_CONFLICT = -200079
    AI_DURING_COUNTER_0_DMA_CONFLICT = -200078
    INVALID_ATTRIBUTE_VALUE = -200077
    SUPPLIED_CURRENT_DATA_OUTSIDE_SPECIFIED_RANGE = -200076
    SUPPLIED_VOLTAGE_DATA_OUTSIDE_SPECIFIED_RANGE = -200075
    CANNOT_STORE_CAL_CONST = -200074
    SCXI_MODULE_NOT_FOUND = -200073
    DUPLICATE_PHYSICAL_CHANS_NOT_SUPPORTED = -200072
    TOO_MANY_PHYSICAL_CHANS_IN_LIST = -200071
    INVALID_ADVANCE_EVENT_TRIGGER_TYPE = -200070
    DEVICE_IS_NOT_A_VALID_SWITCH = -200069
    DEVICE_DOES_NOT_SUPPORT_SCANNING = -200068
    SCAN_LIST_CANNOT_BE_TIMED = -200067
    CONNECT_OPERATOR_INVALID_AT_POINT_IN_LIST = -200066
    UNEXPECTED_SWITCH_ACTION_IN_LIST = -200065
    UNEXPECTED_SEPARATOR_IN_LIST = -200064
    EXPECTED_TERMINATOR_IN_LIST = -200063
    EXPECTED_CONNECT_OPERATOR_IN_LIST = -200062
    EXPECTED_SEPARATOR_IN_LIST = -200061
    FULLY_SPECIFIED_PATH_IN_LIST_CONTAINS_RANGE = -200060
    CONNECTION_SEPARATOR_AT_END_OF_LIST = -200059
    IDENTIFIER_IN_LIST_TOO_LONG = -200058
    DUPLICATE_DEVICE_ID_IN_LIST_WHEN_SETTLING = -200057
    CHANNEL_NAME_NOT_SPECIFIED_IN_LIST = -200056
    DEVICE_ID_NOT_SPECIFIED_IN_LIST = -200055
    SEMICOLON_DOES_NOT_FOLLOW_RANGE_IN_LIST = -200054
    SWITCH_ACTION_IN_LIST_SPANS_MULTIPLE_DEVICES = -200053
    RANGE_WITHOUT_A_CONNECT_ACTION_IN_LIST = -200052
    INVALID_IDENTIFIER_FOLLOWING_SEPARATOR_IN_LIST = -200051
    INVALID_CHANNEL_NAME_IN_LIST = -200050
    INVALID_NUMBER_IN_REPEAT_STATEMENT_IN_LIST = -200049
    INVALID_TRIGGER_LINE_IN_LIST = -200048
    INVALID_IDENTIFIER_IN_LIST_FOLLOWING_DEVICE_ID = -200047
    INVALID_IDENTIFIER_IN_LIST_AT_END_OF_SWITCH_ACTION = -200046
    DEVICE_REMOVED = -200045
    ROUTING_PATH_NOT_AVAILABLE = -200044
    ROUTING_HARDWARE_BUSY = -200043
    REQUESTED_SIGNAL_INVERSION_FOR_ROUTING_NOT_POSSIBLE = -200042
    INVALID_ROUTING_DESTINATION_TERMINAL_NAME = -200041
    INVALID_ROUTING_SOURCE_TERMINAL_NAME = -200040
    ROUTING_NOT_SUPPORTED_FOR_DEVICE = -200039
    WAIT_IS_LAST_INSTRUCTION_OF_LOOP_IN_SCRIPT = -200038
    CLEAR_IS_LAST_INSTRUCTION_OF_LOOP_IN_SCRIPT = -200037
    INVALID_LOOP_ITERATIONS_IN_SCRIPT = -200036
    REPEAT_LOOP_NESTING_TOO_DEEP_IN_SCRIPT = -200035
    MARKER_POSITION_OUTSIDE_SUBSET_IN_SCRIPT = -200034
    SUBSET_START_OFFSET_NOT_ALIGNED_IN_SCRIPT = -200033
    INVALID_SUBSET_LENGTH_IN_SCRIPT = -200032
    MARKER_POSITION_NOT_ALIGNED_IN_SCRIPT = -200031
    SUBSET_OUTSIDE_WAVEFORM_IN_SCRIPT = -200030
    MARKER_OUTSIDE_WAVEFORM_IN_SCRIPT = -200029
    WAVEFORM_IN_SCRIPT_NOT_IN_MEM = -200028
    KEYWORD_EXPECTED_IN_SCRIPT = -200027
    BUFFER_NAME_EXPECTED_IN_SCRIPT = -200026
    PROCEDURE_NAME_EXPECTED_IN_SCRIPT = -200025
    SCRIPT_HAS_INVALID_IDENTIFIER = -200024
    SCRIPT_HAS_INVALID_CHARACTER = -200023
    RESOURCE_ALREADY_RESERVED = -200022
    SELF_TEST_FAILED = -200020
    ADC_OVERRUN = -200019
    DAC_UNDERFLOW = -200018
    INPUT_FIFO_UNDERFLOW = -200017
    OUTPUT_FIFO_UNDERFLOW = -200016
    SCXI_SERIAL_COMMUNICATION = -200015
    DIGITAL_TERMINAL_SPECIFIED_MORE_THAN_ONCE = -200014
    DIGITAL_OUTPUT_NOT_SUPPORTED = -200012
    INCONSISTENT_CHANNEL_DIRECTIONS = -200011
    INPUT_FIFO_OVERFLOW = -200010
    TIME_STAMP_OVERWRITTEN = -200009
    STOP_TRIGGER_HAS_NOT_OCCURRED = -200008
    RECORD_NOT_AVAILABLE = -200007
    RECORD_OVERWRITTEN = -200006
    DATA_NOT_AVAILABLE = -200005
    DATA_OVERWRITTEN_IN_DEVICE_MEMORY = -200004
    DUPLICATED_CHANNEL = -200003
    INTERFACE_OBSOLETED_ROUTING = -89169
    RO_CO_SERVICE_NOT_AVAILABLE_ROUTING = -89168
    ROUTING_DEST_TERM_PXI_DSTAR_X_NOT_IN_SYSTEM_TIMING_SLOT_ROUTING = -89167
    ROUTING_SRC_TERM_PXI_DSTAR_X_NOT_IN_SYSTEM_TIMING_SLOT_ROUTING = -89166
    ROUTING_SRC_TERM_PXI_DSTAR_IN_NON_D_STAR_TRIGGER_SLOT_ROUTING = -89165
    ROUTING_DEST_TERM_PXI_DSTAR_IN_NON_D_STAR_TRIGGER_SLOT_ROUTING = -89164
    ROUTING_DEST_TERM_PXI_CLK_10_IN_NOT_IN_STAR_TRIGGER_SLOT_ROUTING = -89162
    ROUTING_DEST_TERM_PXI_CLK_10_IN_NOT_IN_SYSTEM_TIMING_SLOT_ROUTING = -89161
    ROUTING_DEST_TERM_PXI_STAR_X_NOT_IN_STAR_TRIGGER_SLOT_ROUTING = -89160
    ROUTING_DEST_TERM_PXI_STAR_X_NOT_IN_SYSTEM_TIMING_SLOT_ROUTING = -89159
    ROUTING_SRC_TERM_PXI_STAR_X_NOT_IN_STAR_TRIGGER_SLOT_ROUTING = -89158
    ROUTING_SRC_TERM_PXI_STAR_X_NOT_IN_SYSTEM_TIMING_SLOT_ROUTING = -89157
    ROUTING_SRC_TERM_PXI_STAR_IN_NON_STAR_TRIGGER_SLOT_ROUTING = -89156
    ROUTING_DEST_TERM_PXI_STAR_IN_NON_STAR_TRIGGER_SLOT_ROUTING = -89155
    ROUTING_DEST_TERM_PXI_STAR_IN_STAR_TRIGGER_SLOT_ROUTING = -89154
    ROUTING_DEST_TERM_PXI_STAR_IN_SYSTEM_TIMING_SLOT_ROUTING = -89153
    ROUTING_SRC_TERM_PXI_STAR_IN_STAR_TRIGGER_SLOT_ROUTING = -89152
    ROUTING_SRC_TERM_PXI_STAR_IN_SYSTEM_TIMING_SLOT_ROUTING = -89151
    INVALID_SIGNAL_MODIFIER_ROUTING = -89150
    ROUTING_DEST_TERM_PXI_CLK_10_IN_NOT_IN_SLOT_2_ROUTING = -89149
    ROUTING_DEST_TERM_PXI_STAR_X_NOT_IN_SLOT_2_ROUTING = -89148
    ROUTING_SRC_TERM_PXI_STAR_X_NOT_IN_SLOT_2_ROUTING = -89147
    ROUTING_SRC_TERM_PXI_STAR_IN_SLOT_16_AND_ABOVE_ROUTING = -89146
    ROUTING_DEST_TERM_PXI_STAR_IN_SLOT_16_AND_ABOVE_ROUTING = -89145
    ROUTING_DEST_TERM_PXI_STAR_IN_SLOT_2_ROUTING = -89144
    ROUTING_SRC_TERM_PXI_STAR_IN_SLOT_2_ROUTING = -89143
    ROUTING_DEST_TERM_PXI_CHASSIS_NOT_IDENTIFIED_ROUTING = -89142
    ROUTING_SRC_TERM_PXI_CHASSIS_NOT_IDENTIFIED_ROUTING = -89141
    TRIG_LINE_NOT_FOUND_SINGLE_DEV_ROUTE_ROUTING = -89140
    NO_COMMON_TRIG_LINE_FOR_ROUTE_ROUTING = -89139
    RESOURCES_IN_USE_FOR_ROUTE_IN_TASK_ROUTING = -89138
    RESOURCES_IN_USE_FOR_ROUTE_ROUTING = -89137
    ROUTE_NOT_SUPPORTED_BY_HW_ROUTING = -89136
    RESOURCES_IN_USE_FOR_INVERSION_IN_TASK_ROUTING = -89135
    RESOURCES_IN_USE_FOR_INVERSION_ROUTING = -89134
    INVERSION_NOT_SUPPORTED_BY_HW_ROUTING = -89133
    RESOURCES_IN_USE_FOR_PROPERTY_ROUTING = -89132
    ROUTE_SRC_AND_DEST_SAME_ROUTING = -89131
    DEV_ABSENT_OR_UNAVAILABLE_ROUTING = -89130
    INVALID_TERM_ROUTING = -89129
    CANNOT_TRISTATE_TERM_ROUTING = -89128
    CANNOT_TRISTATE_BUSY_TERM_ROUTING = -89127
    COULD_NOT_RESERVE_REQUESTED_TRIG_LINE_ROUTING = -89126
    TRIG_LINE_NOT_FOUND_ROUTING = -89125
    ROUTING_PATH_NOT_AVAILABLE_ROUTING = -89124
    ROUTING_HARDWARE_BUSY_ROUTING = -89123
    REQUESTED_SIGNAL_INVERSION_FOR_ROUTING_NOT_POSSIBLE_ROUTING = -89122
    INVALID_ROUTING_DESTINATION_TERMINAL_NAME_ROUTING = -89121
    INVALID_ROUTING_SOURCE_TERMINAL_NAME_ROUTING = -89120
    SERVICE_LOCATOR_NOT_AVAILABLE_ROUTING = -88907
    COULD_NOT_CONNECT_TO_SERVER_ROUTING = -88900
    DEVICE_NAME_CONTAINS_SPACES_OR_PUNCTUATION_ROUTING = -88720
    DEVICE_NAME_CONTAINS_NONPRINTABLE_CHARACTERS_ROUTING = -88719
    DEVICE_NAME_IS_EMPTY_ROUTING = -88718
    DEVICE_NAME_NOT_FOUND_ROUTING = -88717
    LOCAL_REMOTE_DRIVER_VERSION_MISMATCH_ROUTING = -88716
    DUPLICATE_DEVICE_NAME_ROUTING = -88715
    RUNTIME_ABORTING_ROUTING = -88710
    RUNTIME_ABORTED_ROUTING = -88709
    RESOURCE_NOT_IN_POOL_ROUTING = -88708
    DRIVER_DEVICE_GUID_NOT_FOUND_ROUTING = -88705
    PAL_USB_TRANSACTION_ERROR = -50808
    PAL_ISOC_STREAM_BUFFER_ERROR = -50807
    PAL_INVALID_ADDRESS_COMPONENT = -50806
    PAL_SHARING_VIOLATION = -50805
    PAL_INVALID_DEVICE_STATE = -50804
    PAL_CONNECTION_RESET = -50803
    PAL_CONNECTION_ABORTED = -50802
    PAL_CONNECTION_REFUSED = -50801
    PAL_BUS_RESET_OCCURRED = -50800
    PAL_WAIT_INTERRUPTED = -50700
    PAL_MESSAGE_UNDERFLOW = -50651
    PAL_MESSAGE_OVERFLOW = -50650
    PAL_THREAD_ALREADY_DEAD = -50604
    PAL_THREAD_STACK_SIZE_NOT_SUPPORTED = -50603
    PAL_THREAD_CONTROLLER_IS_NOT_THREAD_CREATOR = -50602
    PAL_THREAD_HAS_NO_THREAD_OBJECT = -50601
    PAL_THREAD_COULD_NOT_RUN = -50600
    PAL_SYNC_ABANDONED = -50551
    PAL_SYNC_TIMED_OUT = -50550
    PAL_RECEIVER_SOCKET_INVALID = -50503
    PAL_SOCKET_LISTENER_INVALID = -50502
    PAL_SOCKET_LISTENER_ALREADY_REGISTERED = -50501
    PAL_DISPATCHER_ALREADY_EXPORTED = -50500
    PAL_DMA_LINK_EVENT_MISSED = -50450
    PAL_BUS_ERROR = -50413
    PAL_RETRY_LIMIT_EXCEEDED = -50412
    PAL_TRANSFER_OVERREAD = -50411
    PAL_TRANSFER_OVERWRITTEN = -50410
    PAL_PHYSICAL_BUFFER_FULL = -50409
    PAL_PHYSICAL_BUFFER_EMPTY = -50408
    PAL_LOGICAL_BUFFER_FULL = -50407
    PAL_LOGICAL_BUFFER_EMPTY = -50406
    PAL_TRANSFER_ABORTED = -50405
    PAL_TRANSFER_STOPPED = -50404
    PAL_TRANSFER_IN_PROGRESS = -50403
    PAL_TRANSFER_NOT_IN_PROGRESS = -50402
    PAL_COMMUNICATIONS_FAULT = -50401
    PAL_TRANSFER_TIMED_OUT = -50400
    PAL_MEMORY_HEAP_NOT_EMPTY = -50355
    PAL_MEMORY_BLOCK_CHECK_FAILED = -50354
    PAL_MEMORY_PAGE_LOCK_FAILED = -50353
    PAL_MEMORY_FULL = -50352
    PAL_MEMORY_ALIGNMENT_FAULT = -50351
    PAL_MEMORY_CONFIGURATION_FAULT = -50350
    PAL_DEVICE_INITIALIZATION_FAULT = -50303
    PAL_DEVICE_NOT_SUPPORTED = -50302
    PAL_DEVICE_UNKNOWN = -50301
    PAL_DEVICE_NOT_FOUND = -50300
    PAL_FEATURE_DISABLED = -50265
    PAL_COMPONENT_BUSY = -50264
    PAL_COMPONENT_ALREADY_INSTALLED = -50263
    PAL_COMPONENT_NOT_UNLOADABLE = -50262
    PAL_COMPONENT_NEVER_LOADED = -50261
    PAL_COMPONENT_ALREADY_LOADED = -50260
    PAL_COMPONENT_CIRCULAR_DEPENDENCY = -50259
    PAL_COMPONENT_INITIALIZATION_FAULT = -50258
    PAL_COMPONENT_IMAGE_CORRUPT = -50257
    PAL_FEATURE_NOT_SUPPORTED = -50256
    PAL_FUNCTION_NOT_FOUND = -50255
    PAL_FUNCTION_OBSOLETE = -50254
    PAL_COMPONENT_TOO_NEW = -50253
    PAL_COMPONENT_TOO_OLD = -50252
    PAL_COMPONENT_NOT_FOUND = -50251
    PAL_VERSION_MISMATCH = -50250
    PAL_FILE_FAULT = -50209
    PAL_FILE_WRITE_FAULT = -50208
    PAL_FILE_READ_FAULT = -50207
    PAL_FILE_SEEK_FAULT = -50206
    PAL_FILE_CLOSE_FAULT = -50205
    PAL_FILE_OPEN_FAULT = -50204
    PAL_DISK_FULL = -50203
    PAL_OS_FAULT = -50202
    PAL_OS_INITIALIZATION_FAULT = -50201
    PAL_OS_UNSUPPORTED = -50200
    PAL_CALCULATION_OVERFLOW = -50175
    PAL_HARDWARE_FAULT = -50152
    PAL_FIRMWARE_FAULT = -50151
    PAL_SOFTWARE_FAULT = -50150
    PAL_MESSAGE_QUEUE_FULL = -50108
    PAL_RESOURCE_AMBIGUOUS = -50107
    PAL_RESOURCE_BUSY = -50106
    PAL_RESOURCE_INITIALIZED = -50105
    PAL_RESOURCE_NOT_INITIALIZED = -50104
    PAL_RESOURCE_RESERVED = -50103
    PAL_RESOURCE_NOT_RESERVED = -50102
    PAL_RESOURCE_NOT_AVAILABLE = -50101
    PAL_RESOURCE_OWNED_BY_SYSTEM = -50100
    PAL_BAD_TOKEN = -50020
    PAL_BAD_THREAD_MULTITASK = -50019
    PAL_BAD_LIBRARY_SPECIFIER = -50018
    PAL_BAD_ADDRESS_SPACE = -50017
    PAL_BAD_WINDOW_TYPE = -50016
    PAL_BAD_ADDRESS_CLASS = -50015
    PAL_BAD_WRITE_COUNT = -50014
    PAL_BAD_WRITE_OFFSET = -50013
    PAL_BAD_WRITE_MODE = -50012
    PAL_BAD_READ_COUNT = -50011
    PAL_BAD_READ_OFFSET = -50010
    PAL_BAD_READ_MODE = -50009
    PAL_BAD_COUNT = -50008
    PAL_BAD_OFFSET = -50007
    PAL_BAD_MODE = -50006
    PAL_BAD_DATA_SIZE = -50005
    PAL_BAD_POINTER = -50004
    PAL_BAD_SELECTOR = -50003
    PAL_BAD_DEVICE = -50002
    PAL_IRRELEVANT_ATTRIBUTE = -50001
    PAL_VALUE_CONFLICT = -50000
    UNKNOWN = -1


class DAQmxWarnings(IntEnum):
    UNKNOWN = -1
    TIMESTAMP_COUNTER_ROLLED_OVER = 200003
    INPUT_TERMINATION_OVERLOADED = 200004
    ADC_OVERLOADED = 200005
    PLL_UNLOCKED = 200007
    COUNTER_0_DMA_DURING_AI_CONFLICT = 200008
    COUNTER_1_DMA_DURING_AO_CONFLICT = 200009
    STOPPED_BEFORE_DONE = 200010
    RATE_VIOLATES_SETTLING_TIME = 200011
    RATE_VIOLATES_MAX_ADC_RATE = 200012
    USER_DEF_INFO_STRING_TOO_LONG = 200013
    TOO_MANY_INTERRUPTS_PER_SECOND = 200014
    POTENTIAL_GLITCH_DURING_WRITE = 200015
    DEV_NOT_SELF_CALIBRATED_WITH_DA_QMX = 200016
    AI_SAMP_RATE_TOO_LOW = 200017
    AI_CONV_RATE_TOO_LOW = 200018
    READ_OFFSET_COERCION = 200019
    PRETRIG_COERCION = 200020
    SAMP_VAL_COERCED_TO_MAX = 200021
    SAMP_VAL_COERCED_TO_MIN = 200022
    PROPERTY_VERSION_NEW = 200024
    USER_DEFINED_INFO_TOO_LONG = 200025
    CAPI_STRING_TRUNCATED_TO_FIT_BUFFER = 200026
    SAMP_CLK_RATE_TOO_LOW = 200027
    POSSIBLY_INVALID_CTR_SAMPS_IN_FINITE_DMA_ACQ = 200028
    RIS_ACQ_COMPLETED_SOME_BINS_NOT_FILLED = 200029
    PXI_DEV_TEMP_EXCEEDS_MAX_OP_TEMP = 200030
    OUTPUT_GAIN_TOO_LOW_FOR_RF_FREQ = 200031
    OUTPUT_GAIN_TOO_HIGH_FOR_RF_FREQ = 200032
    MULTIPLE_WRITES_BETWEEN_SAMP_CLKS = 200033
    DEVICE_MAY_SHUT_DOWN_DUE_TO_HIGH_TEMP = 200034
    RATE_VIOLATES_MIN_ADC_RATE = 200035
    SAMP_CLK_RATE_ABOVE_DEV_SPECS = 200036
    CO_PREV_DA_QMX_WRITE_SETTINGS_OVERWRITTEN_FOR_HW_TIMED_SINGLE_POINT = 200037
    LOWPASS_FILTER_SETTLING_TIME_EXCEEDS_USER_TIME_BETWEEN_2_ADC_CONVERSIONS = 200038
    LOWPASS_FILTER_SETTLING_TIME_EXCEEDS_DRIVER_TIME_BETWEEN_2_ADC_CONVERSIONS = 200039
    SAMP_CLK_RATE_VIOLATES_SETTLING_TIME_FOR_GEN = 200040
    INVALID_CAL_CONST_VALUE_FOR_AI = 200041
    INVALID_CAL_CONST_VALUE_FOR_AO = 200042
    CHAN_CAL_EXPIRED = 200043
    UNRECOGNIZED_ENUM_VALUE_ENCOUNTERED_IN_STORAGE = 200044
    TABLE_CRC_NOT_CORRECT = 200045
    EXTERNAL_CRC_NOT_CORRECT = 200046
    SELF_CAL_CRC_NOT_CORRECT = 200047
    DEVICE_SPEC_EXCEEDED = 200048
    ONLY_GAIN_CALIBRATED = 200049
    REVERSE_POWER_PROTECTION_ACTIVATED = 200050
    OVER_VOLTAGE_PROTECTION_ACTIVATED = 200051
    BUFFER_SIZE_NOT_MULTIPLE_OF_SECTOR_SIZE = 200052
    SAMPLE_RATE_MAY_CAUSE_ACQ_TO_FAIL = 200053
    USER_AREA_CRC_NOT_CORRECT = 200054
    POWER_UP_INFO_CRC_NOT_CORRECT = 200055
    CONNECTION_COUNT_HAS_EXCEEDED_RECOMMENDED_LIMIT = 200056
    NETWORK_DEVICE_ALREADY_ADDED = 200057
    ACCESSORY_CONNECTION_COUNT_IS_INVALID = 200058
    UNABLE_TO_DISCONNECT_PORTS = 200059
    READ_REPEATED_DATA = 200060
    PXI_5600_NOT_CONFIGURED = 200061
    PXI_5661_INCORRECTLY_CONFIGURED = 200062
    PXIE_5601_NOT_CONFIGURED = 200063
    PXIE_5663_INCORRECTLY_CONFIGURED = 200064
    PXIE_5663_E_INCORRECTLY_CONFIGURED = 200065
    PXIE_5603_NOT_CONFIGURED = 200066
    PXIE_5665_5603_INCORRECTLY_CONFIGURED = 200067
    PXIE_5667_5603_INCORRECTLY_CONFIGURED = 200068
    PXIE_5605_NOT_CONFIGURED = 200069
    PXIE_5665_5605_INCORRECTLY_CONFIGURED = 200070
    PXIE_5667_5605_INCORRECTLY_CONFIGURED = 200071
    PXIE_5606_NOT_CONFIGURED = 200072
    PXIE_5665_5606_INCORRECTLY_CONFIGURED = 200073
    PXI_5610_NOT_CONFIGURED = 200074
    PXI_5610_INCORRECTLY_CONFIGURED = 200075
    PXIE_5611_NOT_CONFIGURED = 200076
    PXIE_5611_INCORRECTLY_CONFIGURED = 200077
    USB_HOTFIX_FOR_DAQ = 200078
    NO_CHANGE_SUPERSEDED_BY_IDLE_BEHAVIOR = 200079
    READ_NOT_COMPLETE_BEFORE_SAMP_CLK = 209800
    WRITE_NOT_COMPLETE_BEFORE_SAMP_CLK = 209801
    WAIT_FOR_NEXT_SAMP_CLK_DETECTED_MISSED_SAMP_CLK = 209802
    OUTPUT_DATA_TRANSFER_CONDITION_NOT_SUPPORTED = 209803
    TIMESTAMP_MAY_BE_INVALID = 209804
    FIRST_SAMPLE_TIMESTAMP_INACCURATE = 209805
    PAL_VALUE_CONFLICT = 50000
    PAL_IRRELEVANT_ATTRIBUTE = 50001
    PAL_BAD_DEVICE = 50002
    PAL_BAD_SELECTOR = 50003
    PAL_BAD_POINTER = 50004
    PAL_BAD_DATA_SIZE = 50005
    PAL_BAD_MODE = 50006
    PAL_BAD_OFFSET = 50007
    PAL_BAD_COUNT = 50008
    PAL_BAD_READ_MODE = 50009
    PAL_BAD_READ_OFFSET = 50010
    PAL_BAD_READ_COUNT = 50011
    PAL_BAD_WRITE_MODE = 50012
    PAL_BAD_WRITE_OFFSET = 50013
    PAL_BAD_WRITE_COUNT = 50014
    PAL_BAD_ADDRESS_CLASS = 50015
    PAL_BAD_WINDOW_TYPE = 50016
    PAL_BAD_THREAD_MULTITASK = 50019
    PAL_RESOURCE_OWNED_BY_SYSTEM = 50100
    PAL_RESOURCE_NOT_AVAILABLE = 50101
    PAL_RESOURCE_NOT_RESERVED = 50102
    PAL_RESOURCE_RESERVED = 50103
    PAL_RESOURCE_NOT_INITIALIZED = 50104
    PAL_RESOURCE_INITIALIZED = 50105
    PAL_RESOURCE_BUSY = 50106
    PAL_RESOURCE_AMBIGUOUS = 50107
    PAL_FIRMWARE_FAULT = 50151
    PAL_HARDWARE_FAULT = 50152
    PAL_OS_UNSUPPORTED = 50200
    PAL_OS_FAULT = 50202
    PAL_FUNCTION_OBSOLETE = 50254
    PAL_FUNCTION_NOT_FOUND = 50255
    PAL_FEATURE_NOT_SUPPORTED = 50256
    PAL_COMPONENT_INITIALIZATION_FAULT = 50258
    PAL_COMPONENT_ALREADY_LOADED = 50260
    PAL_COMPONENT_NOT_UNLOADABLE = 50262
    PAL_MEMORY_ALIGNMENT_FAULT = 50351
    PAL_MEMORY_HEAP_NOT_EMPTY = 50355
    PAL_TRANSFER_NOT_IN_PROGRESS = 50402
    PAL_TRANSFER_IN_PROGRESS = 50403
    PAL_TRANSFER_STOPPED = 50404
    PAL_TRANSFER_ABORTED = 50405
    PAL_LOGICAL_BUFFER_EMPTY = 50406
    PAL_LOGICAL_BUFFER_FULL = 50407
    PAL_PHYSICAL_BUFFER_EMPTY = 50408
    PAL_PHYSICAL_BUFFER_FULL = 50409
    PAL_TRANSFER_OVERWRITTEN = 50410
    PAL_TRANSFER_OVERREAD = 50411
    PAL_DISPATCHER_ALREADY_EXPORTED = 50500
    PAL_SYNC_ABANDONED = 50551
