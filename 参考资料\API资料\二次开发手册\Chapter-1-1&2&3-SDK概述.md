1 SDK概述 

本SDK通过提供一个统一的动态链接函数库（Dll）来实现对MT系列系列的产品进行控制操作，目前支持的开发语言包括VC、VB、C#/VB.NET、Delphi，C++ 
Builder，方便用户进行二次开发，集成到自己的软件系统中。 

1.1 SDK约定 
SDK的函数采用类似COM组件的定义方式，方便以后本司提供COM组件方式的SDK开发包。 
 
SDK所有的设置参数、执行动作的函数形式为 
INT32 MT_Set_xxxx(xxxx) 
返回值为32位有符号整形型，0代表函数执行成功，非0（例如-1）代表执行失败，括号内为设置的一系列参数 
 
SDK所有的读取参数、读取状态的函数形式为 
INT32 MT_Get_xxxx(xxxx) 
返回值为32位有符号整形数，0代表函数执行成功，非0（例如-1）代表执行失败，括号内为以指针或者引用的方式返回的读取值 
 
函数名清楚的表达了本函数的功能 
 
标记为兼容的函数不推荐新软件使用，是为了老用户保持兼容性的函数。 
 
函数所用的单位都以脉冲为单位，对应的速度为Hz/s,加速度为Hz/s2,特殊标明的单位除外 
 
另外提供了一组辅助计算函数，帮助用户在使用平移台、旋转台、编码器和光栅尺时进行物理量单位和脉冲单位之间的相互转换。 
 
1.2 运动控制相关的概念 
1.2.1 运动的类型 
所有的运动都可以简单的抽象为直线运动和圆弧运动，这两种运动组合就可以产生各种运动形式的组合，例如垂直的两个直线运动，如果是等速的，合成的就是一个斜直线运动，如果不等速，就是二维曲线运动。 
对运动控制卡来说，就是驱动电机旋转，再由机械结构转换成其它的运动形式。控制上的一个控制单位一般叫一步（step），在机械上实际的物理量对应控制上的多少步的比例清楚了，就可以抽象下来只关心电机的运动了。 
1.2.2 运动控制系统组成 
一般的运动系统由控制输入（电脑、HMI、MCU、开关、摄像头等）+运动控制器（卡）+电机驱动器（功率放大器）+电机+机械装置+反馈检测（限位开关、零位开关、接近开关、编码器、光栅尺）组成。 
 
 
1.2.3 限位开关、零位开关和软件限位 
 
限位负限位、正限位和零位一般在硬件上为机械开关、光电开关、接近开关等形式。 
负限位和正限位是指物理上的最大位置，通过限位可以通知运动控制卡不超过物理上的限制，可以保护机械装置的安全性，正负不一定都需要有，需要保护的可以加。 
 
零位为机械上的坐标零点。一般的控制系统断电后不能保持当前的位置信息（绝对式编码器和光栅除外），再次上电后需要用原点来校正一次坐标系，为后续的运动做参考。 
软件限位为控制器上通过软件的方式实现的限位，可以在当前坐标线的基础上人工限定一个可以移动的范围，用来做保护。软件限位一般在零位校正后使用，一般比物理上可以移动的范围小。 
1.2.4 加速和减速 
一般的机械装置都有比较大的惯性，为了达到更好的控制效果，都需要在启动的时候有加速过程，在停止的时候有减速过程，一是可以提高控制的精度，二是可以减少惯性对工件的伤害，三是可以保护电控系统的安全。特别是步进系统，可以通过低俗启动提供更大的启动力矩。 
1.2.5 电机控制内部逻辑组成 
 
控制环电机控制从内到外分为三个环：电流环、速度环和位置环。不同的环路代表完成不同的功能。电流环一般由驱动器完成，提供电机运动的动力。速度环和位置环为运动控制器实现，实现对目标的速度或者位置控制，完成用户需求。用户只需要根据应用发出指令，有控制器和驱动器协同完成。 
1.2.6 步进细分 
步进电机一般是每一个整步走一个步距角，一般2相4线的步进电机的步距角为1.8°，也就是200个脉冲转一圈。驱动器在电气上可以实现对每一个整步进行细分，提供细分后的电流，而不是一步到位到需要的电流，从而可以实现步进电机的细分运动，增加精度和平稳度。 
1.2.7 丝杠螺距 
电机通过丝杠可以将电机旋转运动转换为丝杠上载物台的直线运动，丝杠的螺距决定了电机旋转一圈转成的直线运动距离。 
1.2.8 传动比/减速比 
机械上通过减速箱/减速齿轮等方式可以提高旋转的精度，增加力矩。电机旋转多少圈，对应的减速后的机械旋转一圈的数值即为减速比/传动比。 
1.2.9 编码器 
根据检测原理，编码器可分为光学式、磁式、感应式和电容式。根据其刻度方法及信号输出形式，可分为增量式、绝对式以及混合式三种。MT系列运动控制产品目前只支持增量式的编码器。增量式编码器可以测量旋转的角度，通过脉冲方式输出。编码器关键的参数为线数，即旋转一圈输出的脉冲数。 
1.2.10 光栅尺 
光栅尺通过光学的方法可以测量出移动的距离。光栅尺以直线的方式固定时可以测量位移，以圆的方式固定时可以测量角度。光栅尺的关键参数为最小刻度值，即输出一个脉冲对应的位移量。 
1.2.11 开环控制 
开环控制是指不监测控制对象的状态，不进行反馈调整控制，假设目标按设定的目标进行运动。 
1.2.12 闭环控制 
闭环控制是指监测控制对象的状态，根据控制对象的反馈进行控制调整，最终让被控对象实现设定的目标。闭环控制需要增加监测的传感器，根据传感器的状态进行反馈控制，一般传感器为编码器、光栅尺等。 
1.2.13 相对和绝对 
相对是指相对于指令执行的瞬间的状态，比如相对当前的速度再调整的量，相对当前的位置再调整的量；而绝对是指想到达的位置，和当前的速度或者位置等无关。 
1.3 运动模式 
1.3.1 开环零位模式 
在有些系统应用中，系统中需要一个坐标原点，而运动控制器一般把上电时的位置作为当前的坐标原点。利用负限位开关、正限位开关或者零位开关来实现一个坐标原点，后续的定位都是相对这个固定的原点来进行。 
 
零位模式即按用户设定的速度，向正方向或者负方向进行运动，当零位开关有效、限位开关有
快速 
零位 
慢速 
过冲区域 
效时，停止运动，并置当前的位置为运动控制器的坐标原点。 
 
如果设置零位模式速度为正，则电机正向运动，碰到零位或者正限位后停止，设置当前软件位置为0；如果设置零位模式速度为负，则电机负向运动，碰到零位或者负限位后停止，设置软件位置为0。 
 
如果系统需要初始化过程尽可能的短，可以进行一次粗查找零位（有过冲），再进行一次精查找零位动作，如下图所示： 
 
 
PS：也可以用位置模式或者速度模式进行0位查找。位置模式和速度模式在碰到限位后不修改当前的软件计数位置，需要用户自行通过函数设置，而零位模式下碰到零位或者限位后会自动修改当前软件计数位置为0. 
 
 
1.3.2 闭环零位模式 
 
闭环零位模式可开环零位模式类似，不过判断零位停止的信号由传感器提供，传感器包括光栅尺和编码器。 
 
对于光栅尺，一般是通过查找光栅尺零位信号的方式进行归零，碰到光栅尺零位或者限位后，会置光栅尺的读数为0。 
 
对于编码器，一般都有传动机构，可以和开环的零位模式一样，通过零位开关和限位开关来实现零位查找，只不过置编码器的读数为0。 
 
对于编码器和光栅尺，一般不需要进行粗查和精查，即使有过冲，也是正确的光栅尺或者编码器计数。 
 
V 
t 
V1 
V2 
V3 
1.3.3 速度模式 
指定轴工作在指定转速模式下，在这个模式下可以随时调整速度。在加速和减速的过程中会按指定的加速度进行加减速 
 
速度为正的情况，电机向正方向旋转； 
 
速度为负的情况下，向负的方向旋转； 
 
有换向的情况，先减速再加速到指定的反向速度。 
 
在正向运动时，如果正向限位有效，则电机立即停止动作 
 
在负向运动时，如果负向限位有效，则电机立即停止动作 
 
在速度模式下，位置模式相关的指令无效。 
 
速度模式下最小速度为 
+-50Hz/S 
 
以下示意图为三次设置新的速度的工作工程，用户可以随时设置新的运行速度，由控制器自动进行加减速，如果是反向，则自动进行减速再加速的过程。V1，V2，V3为新的目标速度 
 
 
 
1.3.4 开环位置模式 
P 
t 
P1 
P2 
P3 
 
指定轴按指定的步数进行运动，达到指定的位置后停止。在运动过程中会有加减速过程，如果有匀速过程，则按设定的最大速度运动。 
 
如果指定的位置在当前位置的正向，则电机正向运动；如果当前正在负向运动，则负向先减速再加速到正向。 
 
如果指定的位置在当前位置的负向，则电机负向运动；如果当前正在正向运动，则正向先减速再加速到负向。 
 
 
如果指定的位置和当前的位置一样，则电机不动 
 
在正向运动时，如果正向限位有效，则电机立即停止动作 
 
在负向运动时，如果负向限位有效，则电机立即停止动作 
下图为定位运动时速度大小和方向和位置目标的示意图：位置不变时，表示速度为0，停止运动了，P1、P2和P3为设置的目标位置 
 
V 
t 
V1 
V2 
V3 
 
1.3.5 闭环位置模式 
 
闭环位置模式和开环位置模式类似，只不过位置的判断是以传感器计数为准，不是以脉冲数为准。 
 
1.3.6 联动插补模式 
 
联动插补是指多个轴同时启动，同时停止的运动，适合需要多个轴有严格时间要求的场合。操作模式和位置模式类似，只是需要多多个指定的轴同时进行指定目标位置，运动控制器自动判别运动方向；设置的速度为最快的轴的运动速度，最快的轴由运动控制器自动判别；任意一个轴碰到限位后，会立即中止本次插补运动；参与插补的轴可以任意组合，至少2个，最多支持运动控制器的所有的轴同时插补。 
 
注意：联动插补模式和位置模式相比，在运动的过程中不能重新设置新的系列目标值，只能停止或者等待本次插补运动完毕后进行下一个插补。 
1.3.7 圆弧插补模式 
 
圆弧插补模式是指任意两个轴进行一个符合圆弧的插补，可以任意指定运动控制器的2个轴。圆弧插补模式需要指定目标坐标点和插补半径；任意一轴碰到限位后会中止本次插补；同样的目标位置和半径，圆弧插补分为顺时针劣弧、顺时针优弧、逆时针劣弧和逆时针优弧4种情况。 
 
顺时针劣弧 
顺时针优弧 
逆时针劣弧 
逆时针优弧 
 
 
注意：圆弧插补模式，在运动的过程中不能重新设置新的系列目标值，只能停止或者等待本次插补运动完毕后进行下一个插补。 
 
 
1.3.8 流指令模式 
 
流指令模式是指指令可以连续发送，由运动控制器来执行的指令的工作模式，适合于需要响应较快的连续动作。流指令模式相当于其它运动控制器（卡）的FIFO指令模式，但又比一般的FIFO指令模式强大，一般的其它运动控制器（卡）的FIFO指令模式只能支持连续定位点等功能（例如连续的直线插补），而MT系列控制器的流指令模式除了支持连续的定位指令（直线插补、圆弧插补、单轴定点等），还支持通用IO输出和延时（1ms实时精度）。 
 
流指令模式下，会在运动控制器开辟一个指令缓冲区，Stream打头的指令会进入这个缓冲区，由运动控制器来进行连续执行。应用程序只需要查询缓冲区的空间大小，如果有空闲空间，就可以连续发送指令，大大减少了用户应用程序的负担，特别是在需要响应比较快的情况下，或者需要1ms精度延时的情况下（应用程序在windows上做不到这么高的精度）。 
 
一般的应用程序不建议使用这种模式。 
 
每种流指令占用的指令空间不一样长，判断空间剩余建议需要20个单位以上。 
 
1.3.9 PLC模式 
 
PLC模式是指类似PLC的工作模式，在这种模式下，用户可以下载一段程序到运动控制器中，由运动控制器根据自定义的状态自动进行设定的动作，而且在这种状态下还可以继续接收通信口的指令。 
 
独立脱机控制：如果要离开电脑或者液晶触摸屏，可以直接使用PLC模式，这种模式下，用户可以通过外接按钮或者开关来启动、停止、暂停程序，可以执行固定的动作。 
 
配合应用程序：可以将固定的动作编辑到控制器内部，通过全局变量进行通信和控制，可以实现动作时固定的，但是参数时可以调的。 
PLC模式需要MTEditor软件、MTRun软件和MTPLC软件配合。 
 
1.3.10 软件限位模式 
 
软件限位模式是指每个轴可以设定一个软件限位功能，可以在软件上限定这个轴可以移动的范围，从而实现类似硬件限位的功能。 
 
因为软件限位的功能是指在软件脉冲位置，软件脉冲位置在上电的时候默认为0，所以一般作为硬件限位或者零位的补充，经过硬件限位的初始化后，再通过软件限位功能来实现保护。 
 
一般情况下推荐使用硬件限位和零位。 
 
