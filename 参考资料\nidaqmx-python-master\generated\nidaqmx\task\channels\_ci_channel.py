# Do not edit this file; it was automatically generated.

from nidaqmx.scale import _ScaleAlternateConstructor
from nidaqmx.task.channels._channel import Channel
from nidaqmx.constants import (
    AngleUnits, AngularVelocityUnits, CountDirection, CounterFrequencyMethod,
    DataTransferActiveTransferMode, DigitalWidthUnits, Edge, EncoderType,
    EncoderZIndexPhase, FilterResponse, FrequencyUnits, GpsSignalType,
    InputDataTransferCondition, LengthUnits, Level, LogicLvlBehavior,
    SampClkOverrunBehavior, TerminalConfiguration, TimeUnits, UsageTypeCI,
    VelocityUnits)


class CIChannel(Channel):
    """
    Represents one or more counter input virtual channels and their properties.
    """
    __slots__ = ()

    def __repr__(self):
        return f'CIChannel(name={self._name})'

    @property
    def ci_ang_encoder_initial_angle(self):
        """
        float: Specifies the starting angle of the encoder. This value
            is in the units you specify with **ci_ang_encoder_units**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x881)
        return val

    @ci_ang_encoder_initial_angle.setter
    def ci_ang_encoder_initial_angle(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x881, val)

    @ci_ang_encoder_initial_angle.deleter
    def ci_ang_encoder_initial_angle(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x881)

    @property
    def ci_ang_encoder_pulses_per_rev(self):
        """
        int: Specifies the number of pulses the encoder generates per
            revolution. This value is the number of pulses on either
            signal A or signal B, not the total number of pulses on both
            signal A and signal B.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x875)
        return val

    @ci_ang_encoder_pulses_per_rev.setter
    def ci_ang_encoder_pulses_per_rev(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x875, val)

    @ci_ang_encoder_pulses_per_rev.deleter
    def ci_ang_encoder_pulses_per_rev(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x875)

    @property
    def ci_ang_encoder_units(self):
        """
        :class:`nidaqmx.constants.AngleUnits`: Specifies the units to
            use to return angular position measurements from the
            channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x18a6)
        return AngleUnits(val)

    @ci_ang_encoder_units.setter
    def ci_ang_encoder_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x18a6, val)

    @ci_ang_encoder_units.deleter
    def ci_ang_encoder_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18a6)

    @property
    def ci_count(self):
        """
        int: Indicates the current value of the count register.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x148)
        return val

    @property
    def ci_count_edges_active_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies on which edges to
            increment or decrement the counter.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x697)
        return Edge(val)

    @ci_count_edges_active_edge.setter
    def ci_count_edges_active_edge(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x697, val)

    @ci_count_edges_active_edge.deleter
    def ci_count_edges_active_edge(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x697)

    @property
    def ci_count_edges_count_dir_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x21f1)
        return val

    @ci_count_edges_count_dir_dig_fltr_enable.setter
    def ci_count_edges_count_dir_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x21f1, val)

    @ci_count_edges_count_dir_dig_fltr_enable.deleter
    def ci_count_edges_count_dir_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21f1)

    @property
    def ci_count_edges_count_dir_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x21f2)
        return val

    @ci_count_edges_count_dir_dig_fltr_min_pulse_width.setter
    def ci_count_edges_count_dir_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x21f2, val)

    @ci_count_edges_count_dir_dig_fltr_min_pulse_width.deleter
    def ci_count_edges_count_dir_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21f2)

    @property
    def ci_count_edges_count_dir_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x21f4)
        return val

    @ci_count_edges_count_dir_dig_fltr_timebase_rate.setter
    def ci_count_edges_count_dir_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x21f4, val)

    @ci_count_edges_count_dir_dig_fltr_timebase_rate.deleter
    def ci_count_edges_count_dir_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21f4)

    @property
    def ci_count_edges_count_dir_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x21f3)
        return val

    @ci_count_edges_count_dir_dig_fltr_timebase_src.setter
    def ci_count_edges_count_dir_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x21f3, val)

    @ci_count_edges_count_dir_dig_fltr_timebase_src.deleter
    def ci_count_edges_count_dir_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21f3)

    @property
    def ci_count_edges_count_dir_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x21f5)
        return val

    @ci_count_edges_count_dir_dig_sync_enable.setter
    def ci_count_edges_count_dir_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x21f5, val)

    @ci_count_edges_count_dir_dig_sync_enable.deleter
    def ci_count_edges_count_dir_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21f5)

    @property
    def ci_count_edges_count_dir_hyst(self):
        """
        float: Specifies a hysteresis level applied to the
            **ci_count_edges_count_dir_thresh_voltage**. The source
            signal must fall below
            **ci_count_edges_count_dir_thresh_voltage** minus the
            hysteresis before a change in count direction occurs.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31b2)
        return val

    @ci_count_edges_count_dir_hyst.setter
    def ci_count_edges_count_dir_hyst(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31b2, val)

    @ci_count_edges_count_dir_hyst.deleter
    def ci_count_edges_count_dir_hyst(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31b2)

    @property
    def ci_count_edges_count_dir_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the count reset line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x309e)
        return LogicLvlBehavior(val)

    @ci_count_edges_count_dir_logic_lvl_behavior.setter
    def ci_count_edges_count_dir_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x309e, val)

    @ci_count_edges_count_dir_logic_lvl_behavior.deleter
    def ci_count_edges_count_dir_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x309e)

    @property
    def ci_count_edges_count_dir_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x309d)
        return TerminalConfiguration(val)

    @ci_count_edges_count_dir_term_cfg.setter
    def ci_count_edges_count_dir_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x309d, val)

    @ci_count_edges_count_dir_term_cfg.deleter
    def ci_count_edges_count_dir_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x309d)

    @property
    def ci_count_edges_count_dir_thresh_voltage(self):
        """
        float: Specifies the voltage level applied to the Count
            Direction terminal. When the signal is above this threshold,
            the counter counts up. When the signal is below this
            threshold, the counter counts down.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31b1)
        return val

    @ci_count_edges_count_dir_thresh_voltage.setter
    def ci_count_edges_count_dir_thresh_voltage(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31b1, val)

    @ci_count_edges_count_dir_thresh_voltage.deleter
    def ci_count_edges_count_dir_thresh_voltage(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31b1)

    @property
    def ci_count_edges_count_reset_active_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies on which edge of the
            signal to reset the count.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2fb2)
        return Edge(val)

    @ci_count_edges_count_reset_active_edge.setter
    def ci_count_edges_count_reset_active_edge(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2fb2, val)

    @ci_count_edges_count_reset_active_edge.deleter
    def ci_count_edges_count_reset_active_edge(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2fb2)

    @property
    def ci_count_edges_count_reset_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2fb3)
        return val

    @ci_count_edges_count_reset_dig_fltr_enable.setter
    def ci_count_edges_count_reset_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2fb3, val)

    @ci_count_edges_count_reset_dig_fltr_enable.deleter
    def ci_count_edges_count_reset_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2fb3)

    @property
    def ci_count_edges_count_reset_dig_fltr_min_pulse_width(self):
        """
        float: Specifies the minimum pulse width the filter recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2fb4)
        return val

    @ci_count_edges_count_reset_dig_fltr_min_pulse_width.setter
    def ci_count_edges_count_reset_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2fb4, val)

    @ci_count_edges_count_reset_dig_fltr_min_pulse_width.deleter
    def ci_count_edges_count_reset_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2fb4)

    @property
    def ci_count_edges_count_reset_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2fb6)
        return val

    @ci_count_edges_count_reset_dig_fltr_timebase_rate.setter
    def ci_count_edges_count_reset_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2fb6, val)

    @ci_count_edges_count_reset_dig_fltr_timebase_rate.deleter
    def ci_count_edges_count_reset_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2fb6)

    @property
    def ci_count_edges_count_reset_dig_fltr_timebase_src(self):
        """
        str: Specifies the input of the signal to use as the timebase of
            the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2fb5)
        return val

    @ci_count_edges_count_reset_dig_fltr_timebase_src.setter
    def ci_count_edges_count_reset_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2fb5, val)

    @ci_count_edges_count_reset_dig_fltr_timebase_src.deleter
    def ci_count_edges_count_reset_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2fb5)

    @property
    def ci_count_edges_count_reset_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2fb7)
        return val

    @ci_count_edges_count_reset_dig_sync_enable.setter
    def ci_count_edges_count_reset_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2fb7, val)

    @ci_count_edges_count_reset_dig_sync_enable.deleter
    def ci_count_edges_count_reset_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2fb7)

    @property
    def ci_count_edges_count_reset_enable(self):
        """
        bool: Specifies whether to reset the count on the active edge
            specified with **ci_count_edges_count_reset_term**.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2faf)
        return val

    @ci_count_edges_count_reset_enable.setter
    def ci_count_edges_count_reset_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2faf, val)

    @ci_count_edges_count_reset_enable.deleter
    def ci_count_edges_count_reset_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2faf)

    @property
    def ci_count_edges_count_reset_hyst(self):
        """
        float: Specifies a hysteresis level applied to
            **ci_count_edges_count_reset_thresh_voltage**. When
            **ci_count_edges_count_reset_active_edge** is rising, the
            source signal must first fall below
            **ci_count_edges_count_reset_thresh_voltage** minus the
            hysteresis before a rising edge is detected at
            **ci_count_edges_count_reset_thresh_voltage**. When
            **ci_count_edges_count_reset_active_edge** is falling, the
            source signal must first rise above
            **ci_count_edges_count_reset_thresh_voltage** plus the
            hysteresis before a falling edge is detected at
            **ci_count_edges_count_reset_thresh_voltage**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31b4)
        return val

    @ci_count_edges_count_reset_hyst.setter
    def ci_count_edges_count_reset_hyst(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31b4, val)

    @ci_count_edges_count_reset_hyst.deleter
    def ci_count_edges_count_reset_hyst(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31b4)

    @property
    def ci_count_edges_count_reset_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the count reset line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30a0)
        return LogicLvlBehavior(val)

    @ci_count_edges_count_reset_logic_lvl_behavior.setter
    def ci_count_edges_count_reset_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30a0, val)

    @ci_count_edges_count_reset_logic_lvl_behavior.deleter
    def ci_count_edges_count_reset_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30a0)

    @property
    def ci_count_edges_count_reset_reset_cnt(self):
        """
        int: Specifies the value to reset the count to.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x2fb0)
        return val

    @ci_count_edges_count_reset_reset_cnt.setter
    def ci_count_edges_count_reset_reset_cnt(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x2fb0, val)

    @ci_count_edges_count_reset_reset_cnt.deleter
    def ci_count_edges_count_reset_reset_cnt(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2fb0)

    @property
    def ci_count_edges_count_reset_term(self):
        """
        str: Specifies the input terminal of the signal to reset the
            count.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2fb1)
        return val

    @ci_count_edges_count_reset_term.setter
    def ci_count_edges_count_reset_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2fb1, val)

    @ci_count_edges_count_reset_term.deleter
    def ci_count_edges_count_reset_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2fb1)

    @property
    def ci_count_edges_count_reset_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x309f)
        return TerminalConfiguration(val)

    @ci_count_edges_count_reset_term_cfg.setter
    def ci_count_edges_count_reset_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x309f, val)

    @ci_count_edges_count_reset_term_cfg.deleter
    def ci_count_edges_count_reset_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x309f)

    @property
    def ci_count_edges_count_reset_thresh_voltage(self):
        """
        float: Specifies the voltage level at which to recognize the
            counter reset event.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31b3)
        return val

    @ci_count_edges_count_reset_thresh_voltage.setter
    def ci_count_edges_count_reset_thresh_voltage(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31b3, val)

    @ci_count_edges_count_reset_thresh_voltage.deleter
    def ci_count_edges_count_reset_thresh_voltage(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31b3)

    @property
    def ci_count_edges_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x21f6)
        return val

    @ci_count_edges_dig_fltr_enable.setter
    def ci_count_edges_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x21f6, val)

    @ci_count_edges_dig_fltr_enable.deleter
    def ci_count_edges_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21f6)

    @property
    def ci_count_edges_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x21f7)
        return val

    @ci_count_edges_dig_fltr_min_pulse_width.setter
    def ci_count_edges_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x21f7, val)

    @ci_count_edges_dig_fltr_min_pulse_width.deleter
    def ci_count_edges_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21f7)

    @property
    def ci_count_edges_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x21f9)
        return val

    @ci_count_edges_dig_fltr_timebase_rate.setter
    def ci_count_edges_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x21f9, val)

    @ci_count_edges_dig_fltr_timebase_rate.deleter
    def ci_count_edges_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21f9)

    @property
    def ci_count_edges_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x21f8)
        return val

    @ci_count_edges_dig_fltr_timebase_src.setter
    def ci_count_edges_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x21f8, val)

    @ci_count_edges_dig_fltr_timebase_src.deleter
    def ci_count_edges_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21f8)

    @property
    def ci_count_edges_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x21fa)
        return val

    @ci_count_edges_dig_sync_enable.setter
    def ci_count_edges_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x21fa, val)

    @ci_count_edges_dig_sync_enable.deleter
    def ci_count_edges_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21fa)

    @property
    def ci_count_edges_dir(self):
        """
        :class:`nidaqmx.constants.CountDirection`: Specifies whether to
            increment or decrement the counter on each edge.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x696)
        return CountDirection(val)

    @ci_count_edges_dir.setter
    def ci_count_edges_dir(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x696, val)

    @ci_count_edges_dir.deleter
    def ci_count_edges_dir(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x696)

    @property
    def ci_count_edges_dir_term(self):
        """
        str: Specifies the source terminal of the digital signal that
            controls the count direction if **ci_count_edges_dir** is
            **CountDirection1.EXTERNAL_SOURCE**.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x21e1)
        return val

    @ci_count_edges_dir_term.setter
    def ci_count_edges_dir_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x21e1, val)

    @ci_count_edges_dir_term.deleter
    def ci_count_edges_dir_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21e1)

    @property
    def ci_count_edges_gate_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            gate input signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x30f1)
        return val

    @ci_count_edges_gate_dig_fltr_enable.setter
    def ci_count_edges_gate_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x30f1, val)

    @ci_count_edges_gate_dig_fltr_enable.deleter
    def ci_count_edges_gate_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30f1)

    @property
    def ci_count_edges_gate_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the digital
            filter recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30f2)
        return val

    @ci_count_edges_gate_dig_fltr_min_pulse_width.setter
    def ci_count_edges_gate_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30f2, val)

    @ci_count_edges_gate_dig_fltr_min_pulse_width.deleter
    def ci_count_edges_gate_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30f2)

    @property
    def ci_count_edges_gate_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30f4)
        return val

    @ci_count_edges_gate_dig_fltr_timebase_rate.setter
    def ci_count_edges_gate_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30f4, val)

    @ci_count_edges_gate_dig_fltr_timebase_rate.deleter
    def ci_count_edges_gate_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30f4)

    @property
    def ci_count_edges_gate_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x30f3)
        return val

    @ci_count_edges_gate_dig_fltr_timebase_src.setter
    def ci_count_edges_gate_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x30f3, val)

    @ci_count_edges_gate_dig_fltr_timebase_src.deleter
    def ci_count_edges_gate_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30f3)

    @property
    def ci_count_edges_gate_enable(self):
        """
        bool: Specifies whether to enable the functionality to gate the
            counter input signal for a count edges measurement.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x30ed)
        return val

    @ci_count_edges_gate_enable.setter
    def ci_count_edges_gate_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x30ed, val)

    @ci_count_edges_gate_enable.deleter
    def ci_count_edges_gate_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30ed)

    @property
    def ci_count_edges_gate_hyst(self):
        """
        float: Specifies a hysteresis level applied to the
            **ci_count_edges_gate_thresh_voltage**. When
            **ci_count_edges_gate_when** is High, the source signal must
            fall below **ci_count_edges_gate_thresh_voltage** minus the
            hysteresis before the counter resumes counting. When
            **ci_count_edges_gate_when** is Low, the source signal must
            rise above **ci_count_edges_gate_thresh_voltage** plus the
            hysteresis before the counter resumes counting.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31b6)
        return val

    @ci_count_edges_gate_hyst.setter
    def ci_count_edges_gate_hyst(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31b6, val)

    @ci_count_edges_gate_hyst.deleter
    def ci_count_edges_gate_hyst(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31b6)

    @property
    def ci_count_edges_gate_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the gate input line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30f0)
        return LogicLvlBehavior(val)

    @ci_count_edges_gate_logic_lvl_behavior.setter
    def ci_count_edges_gate_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30f0, val)

    @ci_count_edges_gate_logic_lvl_behavior.deleter
    def ci_count_edges_gate_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30f0)

    @property
    def ci_count_edges_gate_term(self):
        """
        str: Specifies the gate terminal.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x30ee)
        return val

    @ci_count_edges_gate_term.setter
    def ci_count_edges_gate_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x30ee, val)

    @ci_count_edges_gate_term.deleter
    def ci_count_edges_gate_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30ee)

    @property
    def ci_count_edges_gate_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            gate terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30ef)
        return TerminalConfiguration(val)

    @ci_count_edges_gate_term_cfg.setter
    def ci_count_edges_gate_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30ef, val)

    @ci_count_edges_gate_term_cfg.deleter
    def ci_count_edges_gate_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30ef)

    @property
    def ci_count_edges_gate_thresh_voltage(self):
        """
        float: Specifies the voltage level at which to recognize the
            counter gate signal.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31b5)
        return val

    @ci_count_edges_gate_thresh_voltage.setter
    def ci_count_edges_gate_thresh_voltage(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31b5, val)

    @ci_count_edges_gate_thresh_voltage.deleter
    def ci_count_edges_gate_thresh_voltage(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31b5)

    @property
    def ci_count_edges_gate_when(self):
        """
        :class:`nidaqmx.constants.Level`: Specifies whether the counter
            gates input pulses while the signal is high or low.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30f5)
        return Level(val)

    @ci_count_edges_gate_when.setter
    def ci_count_edges_gate_when(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30f5, val)

    @ci_count_edges_gate_when.deleter
    def ci_count_edges_gate_when(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30f5)

    @property
    def ci_count_edges_hyst(self):
        """
        float: Specifies a hysteresis level to apply to
            **ci_count_edges_thresh_voltage**. When
            **ci_count_edges_active_edge** is rising, the source signal
            must first fall below **ci_count_edges_thresh_voltage**
            minus the hysteresis before a rising edge is detected at
            **ci_count_edges_thresh_voltage**. When
            **ci_count_edges_active_edge** is falling, the source signal
            must first rise above **ci_count_edges_thresh_voltage** plus
            the hysteresis before a falling edge is detected at
            **ci_count_edges_thresh_voltage**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31b0)
        return val

    @ci_count_edges_hyst.setter
    def ci_count_edges_hyst(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31b0, val)

    @ci_count_edges_hyst.deleter
    def ci_count_edges_hyst(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31b0)

    @property
    def ci_count_edges_initial_cnt(self):
        """
        int: Specifies the starting value from which to count.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x698)
        return val

    @ci_count_edges_initial_cnt.setter
    def ci_count_edges_initial_cnt(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x698, val)

    @ci_count_edges_initial_cnt.deleter
    def ci_count_edges_initial_cnt(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x698)

    @property
    def ci_count_edges_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the input line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x309c)
        return LogicLvlBehavior(val)

    @ci_count_edges_logic_lvl_behavior.setter
    def ci_count_edges_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x309c, val)

    @ci_count_edges_logic_lvl_behavior.deleter
    def ci_count_edges_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x309c)

    @property
    def ci_count_edges_term(self):
        """
        str: Specifies the input terminal of the signal to measure.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x18c7)
        return val

    @ci_count_edges_term.setter
    def ci_count_edges_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x18c7, val)

    @ci_count_edges_term.deleter
    def ci_count_edges_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18c7)

    @property
    def ci_count_edges_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x309b)
        return TerminalConfiguration(val)

    @ci_count_edges_term_cfg.setter
    def ci_count_edges_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x309b, val)

    @ci_count_edges_term_cfg.deleter
    def ci_count_edges_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x309b)

    @property
    def ci_count_edges_thresh_voltage(self):
        """
        float: Specifies the voltage level at which to recognize
            waveform repetitions. Select a voltage level that occurs
            only once within the entire period of a waveform. You also
            can select a voltage that occurs only once while the voltage
            rises or falls.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31af)
        return val

    @ci_count_edges_thresh_voltage.setter
    def ci_count_edges_thresh_voltage(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31af, val)

    @ci_count_edges_thresh_voltage.deleter
    def ci_count_edges_thresh_voltage(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31af)

    @property
    def ci_ctr_timebase_active_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies whether a timebase
            cycle is from rising edge to rising edge or from falling
            edge to falling edge.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x142)
        return Edge(val)

    @ci_ctr_timebase_active_edge.setter
    def ci_ctr_timebase_active_edge(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x142, val)

    @ci_ctr_timebase_active_edge.deleter
    def ci_ctr_timebase_active_edge(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x142)

    @property
    def ci_ctr_timebase_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2271)
        return val

    @ci_ctr_timebase_dig_fltr_enable.setter
    def ci_ctr_timebase_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2271, val)

    @ci_ctr_timebase_dig_fltr_enable.deleter
    def ci_ctr_timebase_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2271)

    @property
    def ci_ctr_timebase_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2272)
        return val

    @ci_ctr_timebase_dig_fltr_min_pulse_width.setter
    def ci_ctr_timebase_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2272, val)

    @ci_ctr_timebase_dig_fltr_min_pulse_width.deleter
    def ci_ctr_timebase_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2272)

    @property
    def ci_ctr_timebase_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2274)
        return val

    @ci_ctr_timebase_dig_fltr_timebase_rate.setter
    def ci_ctr_timebase_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2274, val)

    @ci_ctr_timebase_dig_fltr_timebase_rate.deleter
    def ci_ctr_timebase_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2274)

    @property
    def ci_ctr_timebase_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2273)
        return val

    @ci_ctr_timebase_dig_fltr_timebase_src.setter
    def ci_ctr_timebase_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2273, val)

    @ci_ctr_timebase_dig_fltr_timebase_src.deleter
    def ci_ctr_timebase_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2273)

    @property
    def ci_ctr_timebase_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2275)
        return val

    @ci_ctr_timebase_dig_sync_enable.setter
    def ci_ctr_timebase_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2275, val)

    @ci_ctr_timebase_dig_sync_enable.deleter
    def ci_ctr_timebase_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2275)

    @property
    def ci_ctr_timebase_master_timebase_div(self):
        """
        int: Specifies the divisor for an external counter timebase. You
            can divide the counter timebase in order to measure slower
            signals without causing the count register to roll over.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x18b3)
        return val

    @ci_ctr_timebase_master_timebase_div.setter
    def ci_ctr_timebase_master_timebase_div(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x18b3, val)

    @ci_ctr_timebase_master_timebase_div.deleter
    def ci_ctr_timebase_master_timebase_div(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18b3)

    @property
    def ci_ctr_timebase_rate(self):
        """
        float: Specifies in Hertz the frequency of the counter timebase.
            Specifying the rate of a counter timebase allows you to take
            measurements in terms of time or frequency rather than in
            ticks of the timebase. If you use an external timebase and
            do not specify the rate, you can take measurements only in
            terms of ticks of the timebase.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x18b2)
        return val

    @ci_ctr_timebase_rate.setter
    def ci_ctr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x18b2, val)

    @ci_ctr_timebase_rate.deleter
    def ci_ctr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18b2)

    @property
    def ci_ctr_timebase_src(self):
        """
        str: Specifies the terminal of the timebase to use for the
            counter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x143)
        return val

    @ci_ctr_timebase_src.setter
    def ci_ctr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x143, val)

    @ci_ctr_timebase_src.deleter
    def ci_ctr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x143)

    @property
    def ci_custom_scale(self):
        """
        :class:`nidaqmx.system.scale.Scale`: Specifies the name of a
            custom scale for the channel.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x189e)
        return _ScaleAlternateConstructor(val, self._interpreter)

    @ci_custom_scale.setter
    def ci_custom_scale(self, val):
        val = val.name
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x189e, val)

    @ci_custom_scale.deleter
    def ci_custom_scale(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x189e)

    @property
    def ci_data_xfer_mech(self):
        """
        :class:`nidaqmx.constants.DataTransferActiveTransferMode`:
            Specifies the data transfer mode for the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x200)
        return DataTransferActiveTransferMode(val)

    @ci_data_xfer_mech.setter
    def ci_data_xfer_mech(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x200, val)

    @ci_data_xfer_mech.deleter
    def ci_data_xfer_mech(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x200)

    @property
    def ci_data_xfer_req_cond(self):
        """
        :class:`nidaqmx.constants.InputDataTransferCondition`: Specifies
            under what condition to transfer data from the onboard
            memory of the device to the buffer.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2efb)
        return InputDataTransferCondition(val)

    @ci_data_xfer_req_cond.setter
    def ci_data_xfer_req_cond(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2efb, val)

    @ci_data_xfer_req_cond.deleter
    def ci_data_xfer_req_cond(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2efb)

    @property
    def ci_dup_count_prevention(self):
        """
        bool: Specifies whether to enable duplicate count prevention for
            the channel. Duplicate count prevention is enabled by
            default. Setting  **ci_prescaler** disables duplicate count
            prevention unless you explicitly enable it.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x21ac)
        return val

    @ci_dup_count_prevention.setter
    def ci_dup_count_prevention(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x21ac, val)

    @ci_dup_count_prevention.deleter
    def ci_dup_count_prevention(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21ac)

    @property
    def ci_duty_cycle_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x308e)
        return val

    @ci_duty_cycle_dig_fltr_enable.setter
    def ci_duty_cycle_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x308e, val)

    @ci_duty_cycle_dig_fltr_enable.deleter
    def ci_duty_cycle_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x308e)

    @property
    def ci_duty_cycle_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the digital
            filter recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x308f)
        return val

    @ci_duty_cycle_dig_fltr_min_pulse_width.setter
    def ci_duty_cycle_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x308f, val)

    @ci_duty_cycle_dig_fltr_min_pulse_width.deleter
    def ci_duty_cycle_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x308f)

    @property
    def ci_duty_cycle_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x3091)
        return val

    @ci_duty_cycle_dig_fltr_timebase_rate.setter
    def ci_duty_cycle_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x3091, val)

    @ci_duty_cycle_dig_fltr_timebase_rate.deleter
    def ci_duty_cycle_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3091)

    @property
    def ci_duty_cycle_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x3090)
        return val

    @ci_duty_cycle_dig_fltr_timebase_src.setter
    def ci_duty_cycle_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x3090, val)

    @ci_duty_cycle_dig_fltr_timebase_src.deleter
    def ci_duty_cycle_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3090)

    @property
    def ci_duty_cycle_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the input line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30a2)
        return LogicLvlBehavior(val)

    @ci_duty_cycle_logic_lvl_behavior.setter
    def ci_duty_cycle_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30a2, val)

    @ci_duty_cycle_logic_lvl_behavior.deleter
    def ci_duty_cycle_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30a2)

    @property
    def ci_duty_cycle_starting_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies which edge of the
            input signal to begin the duty cycle measurement.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x3092)
        return Edge(val)

    @ci_duty_cycle_starting_edge.setter
    def ci_duty_cycle_starting_edge(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x3092, val)

    @ci_duty_cycle_starting_edge.deleter
    def ci_duty_cycle_starting_edge(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3092)

    @property
    def ci_duty_cycle_term(self):
        """
        str: Specifies the input terminal of the signal to measure.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x308d)
        return val

    @ci_duty_cycle_term.setter
    def ci_duty_cycle_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x308d, val)

    @ci_duty_cycle_term.deleter
    def ci_duty_cycle_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x308d)

    @property
    def ci_duty_cycle_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30a1)
        return TerminalConfiguration(val)

    @ci_duty_cycle_term_cfg.setter
    def ci_duty_cycle_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30a1, val)

    @ci_duty_cycle_term_cfg.deleter
    def ci_duty_cycle_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30a1)

    @property
    def ci_encoder_a_input_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x21fb)
        return val

    @ci_encoder_a_input_dig_fltr_enable.setter
    def ci_encoder_a_input_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x21fb, val)

    @ci_encoder_a_input_dig_fltr_enable.deleter
    def ci_encoder_a_input_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21fb)

    @property
    def ci_encoder_a_input_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x21fc)
        return val

    @ci_encoder_a_input_dig_fltr_min_pulse_width.setter
    def ci_encoder_a_input_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x21fc, val)

    @ci_encoder_a_input_dig_fltr_min_pulse_width.deleter
    def ci_encoder_a_input_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21fc)

    @property
    def ci_encoder_a_input_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x21fe)
        return val

    @ci_encoder_a_input_dig_fltr_timebase_rate.setter
    def ci_encoder_a_input_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x21fe, val)

    @ci_encoder_a_input_dig_fltr_timebase_rate.deleter
    def ci_encoder_a_input_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21fe)

    @property
    def ci_encoder_a_input_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x21fd)
        return val

    @ci_encoder_a_input_dig_fltr_timebase_src.setter
    def ci_encoder_a_input_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x21fd, val)

    @ci_encoder_a_input_dig_fltr_timebase_src.deleter
    def ci_encoder_a_input_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21fd)

    @property
    def ci_encoder_a_input_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x21ff)
        return val

    @ci_encoder_a_input_dig_sync_enable.setter
    def ci_encoder_a_input_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x21ff, val)

    @ci_encoder_a_input_dig_sync_enable.deleter
    def ci_encoder_a_input_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21ff)

    @property
    def ci_encoder_a_input_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the input line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30a4)
        return LogicLvlBehavior(val)

    @ci_encoder_a_input_logic_lvl_behavior.setter
    def ci_encoder_a_input_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30a4, val)

    @ci_encoder_a_input_logic_lvl_behavior.deleter
    def ci_encoder_a_input_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30a4)

    @property
    def ci_encoder_a_input_term(self):
        """
        str: Specifies the terminal to which signal A is connected.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x219d)
        return val

    @ci_encoder_a_input_term.setter
    def ci_encoder_a_input_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x219d, val)

    @ci_encoder_a_input_term.deleter
    def ci_encoder_a_input_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x219d)

    @property
    def ci_encoder_a_input_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30a3)
        return TerminalConfiguration(val)

    @ci_encoder_a_input_term_cfg.setter
    def ci_encoder_a_input_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30a3, val)

    @ci_encoder_a_input_term_cfg.deleter
    def ci_encoder_a_input_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30a3)

    @property
    def ci_encoder_b_input_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2200)
        return val

    @ci_encoder_b_input_dig_fltr_enable.setter
    def ci_encoder_b_input_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2200, val)

    @ci_encoder_b_input_dig_fltr_enable.deleter
    def ci_encoder_b_input_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2200)

    @property
    def ci_encoder_b_input_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2201)
        return val

    @ci_encoder_b_input_dig_fltr_min_pulse_width.setter
    def ci_encoder_b_input_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2201, val)

    @ci_encoder_b_input_dig_fltr_min_pulse_width.deleter
    def ci_encoder_b_input_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2201)

    @property
    def ci_encoder_b_input_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2203)
        return val

    @ci_encoder_b_input_dig_fltr_timebase_rate.setter
    def ci_encoder_b_input_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2203, val)

    @ci_encoder_b_input_dig_fltr_timebase_rate.deleter
    def ci_encoder_b_input_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2203)

    @property
    def ci_encoder_b_input_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2202)
        return val

    @ci_encoder_b_input_dig_fltr_timebase_src.setter
    def ci_encoder_b_input_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2202, val)

    @ci_encoder_b_input_dig_fltr_timebase_src.deleter
    def ci_encoder_b_input_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2202)

    @property
    def ci_encoder_b_input_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2204)
        return val

    @ci_encoder_b_input_dig_sync_enable.setter
    def ci_encoder_b_input_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2204, val)

    @ci_encoder_b_input_dig_sync_enable.deleter
    def ci_encoder_b_input_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2204)

    @property
    def ci_encoder_b_input_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the input line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30a6)
        return LogicLvlBehavior(val)

    @ci_encoder_b_input_logic_lvl_behavior.setter
    def ci_encoder_b_input_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30a6, val)

    @ci_encoder_b_input_logic_lvl_behavior.deleter
    def ci_encoder_b_input_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30a6)

    @property
    def ci_encoder_b_input_term(self):
        """
        str: Specifies the terminal to which signal B is connected.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x219e)
        return val

    @ci_encoder_b_input_term.setter
    def ci_encoder_b_input_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x219e, val)

    @ci_encoder_b_input_term.deleter
    def ci_encoder_b_input_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x219e)

    @property
    def ci_encoder_b_input_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30a5)
        return TerminalConfiguration(val)

    @ci_encoder_b_input_term_cfg.setter
    def ci_encoder_b_input_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30a5, val)

    @ci_encoder_b_input_term_cfg.deleter
    def ci_encoder_b_input_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30a5)

    @property
    def ci_encoder_decoding_type(self):
        """
        :class:`nidaqmx.constants.EncoderType`: Specifies how to count
            and interpret the pulses the encoder generates on signal A
            and signal B. **EncoderType2.X_1**, **EncoderType2.X_2**,
            and **EncoderType2.X_4** are valid for quadrature encoders
            only. **EncoderType2.TWO_PULSE_COUNTING** is valid for two-
            pulse encoders only.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x21e6)
        return EncoderType(val)

    @ci_encoder_decoding_type.setter
    def ci_encoder_decoding_type(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x21e6, val)

    @ci_encoder_decoding_type.deleter
    def ci_encoder_decoding_type(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21e6)

    @property
    def ci_encoder_z_index_enable(self):
        """
        bool: Specifies whether to use Z indexing for the channel.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x890)
        return val

    @ci_encoder_z_index_enable.setter
    def ci_encoder_z_index_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x890, val)

    @ci_encoder_z_index_enable.deleter
    def ci_encoder_z_index_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x890)

    @property
    def ci_encoder_z_index_phase(self):
        """
        :class:`nidaqmx.constants.EncoderZIndexPhase`: Specifies the
            states at which signal A and signal B must be while signal Z
            is high for NI-DAQmx to reset the measurement. If signal Z
            is never high while signal A and signal B are high, for
            example, you must choose a phase other than
            **EncoderZIndexPhase1.AHIGH_BHIGH**.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x889)
        return EncoderZIndexPhase(val)

    @ci_encoder_z_index_phase.setter
    def ci_encoder_z_index_phase(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x889, val)

    @ci_encoder_z_index_phase.deleter
    def ci_encoder_z_index_phase(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x889)

    @property
    def ci_encoder_z_index_val(self):
        """
        float: Specifies the value to which to reset the measurement
            when signal Z is high and signal A and signal B are at the
            states you specify with **ci_encoder_z_index_phase**.
            Specify this value in the units of the measurement.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x888)
        return val

    @ci_encoder_z_index_val.setter
    def ci_encoder_z_index_val(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x888, val)

    @ci_encoder_z_index_val.deleter
    def ci_encoder_z_index_val(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x888)

    @property
    def ci_encoder_z_input_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2205)
        return val

    @ci_encoder_z_input_dig_fltr_enable.setter
    def ci_encoder_z_input_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2205, val)

    @ci_encoder_z_input_dig_fltr_enable.deleter
    def ci_encoder_z_input_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2205)

    @property
    def ci_encoder_z_input_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2206)
        return val

    @ci_encoder_z_input_dig_fltr_min_pulse_width.setter
    def ci_encoder_z_input_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2206, val)

    @ci_encoder_z_input_dig_fltr_min_pulse_width.deleter
    def ci_encoder_z_input_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2206)

    @property
    def ci_encoder_z_input_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2208)
        return val

    @ci_encoder_z_input_dig_fltr_timebase_rate.setter
    def ci_encoder_z_input_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2208, val)

    @ci_encoder_z_input_dig_fltr_timebase_rate.deleter
    def ci_encoder_z_input_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2208)

    @property
    def ci_encoder_z_input_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2207)
        return val

    @ci_encoder_z_input_dig_fltr_timebase_src.setter
    def ci_encoder_z_input_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2207, val)

    @ci_encoder_z_input_dig_fltr_timebase_src.deleter
    def ci_encoder_z_input_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2207)

    @property
    def ci_encoder_z_input_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2209)
        return val

    @ci_encoder_z_input_dig_sync_enable.setter
    def ci_encoder_z_input_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2209, val)

    @ci_encoder_z_input_dig_sync_enable.deleter
    def ci_encoder_z_input_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2209)

    @property
    def ci_encoder_z_input_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the input line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30a8)
        return LogicLvlBehavior(val)

    @ci_encoder_z_input_logic_lvl_behavior.setter
    def ci_encoder_z_input_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30a8, val)

    @ci_encoder_z_input_logic_lvl_behavior.deleter
    def ci_encoder_z_input_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30a8)

    @property
    def ci_encoder_z_input_term(self):
        """
        str: Specifies the terminal to which signal Z is connected.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x219f)
        return val

    @ci_encoder_z_input_term.setter
    def ci_encoder_z_input_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x219f, val)

    @ci_encoder_z_input_term.deleter
    def ci_encoder_z_input_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x219f)

    @property
    def ci_encoder_z_input_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30a7)
        return TerminalConfiguration(val)

    @ci_encoder_z_input_term_cfg.setter
    def ci_encoder_z_input_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30a7, val)

    @ci_encoder_z_input_term_cfg.deleter
    def ci_encoder_z_input_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30a7)

    @property
    def ci_filter_delay(self):
        """
        float: Indicates the amount of time between when the input
            signal transitions and when the filtered sample is read by
            the host device. This value is in the units specified with
            **ci_filter_delay_units**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31bb)
        return val

    @property
    def ci_filter_delay_units(self):
        """
        :class:`nidaqmx.constants.DigitalWidthUnits`: Specifies the
            units of **ci_filter_delay**.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x31bc)
        return DigitalWidthUnits(val)

    @ci_filter_delay_units.setter
    def ci_filter_delay_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x31bc, val)

    @ci_filter_delay_units.deleter
    def ci_filter_delay_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31bc)

    @property
    def ci_filter_enable(self):
        """
        bool: Specifies the corresponding filter enable/disable state.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x31b7)
        return val

    @ci_filter_enable.setter
    def ci_filter_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x31b7, val)

    @ci_filter_enable.deleter
    def ci_filter_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31b7)

    @property
    def ci_filter_freq(self):
        """
        float: Specifies the corresponding filter frequency (cutoff or
            center) of the filter response.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31b8)
        return val

    @ci_filter_freq.setter
    def ci_filter_freq(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31b8, val)

    @ci_filter_freq.deleter
    def ci_filter_freq(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31b8)

    @property
    def ci_filter_order(self):
        """
        int: Specifies the corresponding filter order and defines the
            slope of the filter response.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x31ba)
        return val

    @ci_filter_order.setter
    def ci_filter_order(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x31ba, val)

    @ci_filter_order.deleter
    def ci_filter_order(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31ba)

    @property
    def ci_filter_response(self):
        """
        :class:`nidaqmx.constants.FilterResponse`: Specifies the
            corresponding filter response and defines the shape of the
            filter response.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x31b9)
        return FilterResponse(val)

    @ci_filter_response.setter
    def ci_filter_response(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x31b9, val)

    @ci_filter_response.deleter
    def ci_filter_response(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31b9)

    @property
    def ci_freq_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x21e7)
        return val

    @ci_freq_dig_fltr_enable.setter
    def ci_freq_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x21e7, val)

    @ci_freq_dig_fltr_enable.deleter
    def ci_freq_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21e7)

    @property
    def ci_freq_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x21e8)
        return val

    @ci_freq_dig_fltr_min_pulse_width.setter
    def ci_freq_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x21e8, val)

    @ci_freq_dig_fltr_min_pulse_width.deleter
    def ci_freq_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21e8)

    @property
    def ci_freq_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x21ea)
        return val

    @ci_freq_dig_fltr_timebase_rate.setter
    def ci_freq_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x21ea, val)

    @ci_freq_dig_fltr_timebase_rate.deleter
    def ci_freq_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21ea)

    @property
    def ci_freq_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x21e9)
        return val

    @ci_freq_dig_fltr_timebase_src.setter
    def ci_freq_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x21e9, val)

    @ci_freq_dig_fltr_timebase_src.deleter
    def ci_freq_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21e9)

    @property
    def ci_freq_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x21eb)
        return val

    @ci_freq_dig_sync_enable.setter
    def ci_freq_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x21eb, val)

    @ci_freq_dig_sync_enable.deleter
    def ci_freq_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21eb)

    @property
    def ci_freq_div(self):
        """
        int: Specifies the value by which to divide the input signal if
            **ci_freq_meas_meth** is
            **CounterFrequencyMethod.LARGE_RANGE_2_COUNTERS**. The
            larger the divisor, the more accurate the measurement.
            However, too large a value could cause the count register to
            roll over, which results in an incorrect measurement.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x147)
        return val

    @ci_freq_div.setter
    def ci_freq_div(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x147, val)

    @ci_freq_div.deleter
    def ci_freq_div(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x147)

    @property
    def ci_freq_enable_averaging(self):
        """
        bool: Specifies whether to enable averaging mode for Sample
            Clock-timed frequency measurements.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2ed0)
        return val

    @ci_freq_enable_averaging.setter
    def ci_freq_enable_averaging(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2ed0, val)

    @ci_freq_enable_averaging.deleter
    def ci_freq_enable_averaging(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2ed0)

    @property
    def ci_freq_hyst(self):
        """
        float: Specifies a hysteresis level to apply to
            **ci_freq_thresh_voltage**. When **ci_freq_starting_edge**
            is rising, the source signal must first fall below
            **ci_freq_thresh_voltage** minus the hysteresis before a
            rising edge is detected at **ci_freq_thresh_voltage**. When
            **ci_freq_starting_edge** is falling, the source signal must
            first rise above **ci_freq_thresh_voltage** plus the
            hysteresis before a falling edge is detected at
            **ci_freq_thresh_voltage**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31ac)
        return val

    @ci_freq_hyst.setter
    def ci_freq_hyst(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31ac, val)

    @ci_freq_hyst.deleter
    def ci_freq_hyst(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31ac)

    @property
    def ci_freq_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the input line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x3098)
        return LogicLvlBehavior(val)

    @ci_freq_logic_lvl_behavior.setter
    def ci_freq_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x3098, val)

    @ci_freq_logic_lvl_behavior.deleter
    def ci_freq_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3098)

    @property
    def ci_freq_meas_meth(self):
        """
        :class:`nidaqmx.constants.CounterFrequencyMethod`: Specifies the
            method to use to measure the frequency of the signal.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x144)
        return CounterFrequencyMethod(val)

    @ci_freq_meas_meth.setter
    def ci_freq_meas_meth(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x144, val)

    @ci_freq_meas_meth.deleter
    def ci_freq_meas_meth(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x144)

    @property
    def ci_freq_meas_time(self):
        """
        float: Specifies in seconds the length of time to measure the
            frequency of the signal if **ci_freq_meas_meth** is
            **CounterFrequencyMethod.HIGH_FREQUENCY_2_COUNTERS**.
            Measurement accuracy increases with increased measurement
            time and with increased signal frequency. If you measure a
            high-frequency signal for too long, however, the count
            register could roll over, which results in an incorrect
            measurement.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x145)
        return val

    @ci_freq_meas_time.setter
    def ci_freq_meas_time(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x145, val)

    @ci_freq_meas_time.deleter
    def ci_freq_meas_time(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x145)

    @property
    def ci_freq_starting_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies between which edges
            to measure the frequency of the signal.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x799)
        return Edge(val)

    @ci_freq_starting_edge.setter
    def ci_freq_starting_edge(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x799, val)

    @ci_freq_starting_edge.deleter
    def ci_freq_starting_edge(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x799)

    @property
    def ci_freq_term(self):
        """
        str: Specifies the input terminal of the signal to measure.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x18a2)
        return val

    @ci_freq_term.setter
    def ci_freq_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x18a2, val)

    @ci_freq_term.deleter
    def ci_freq_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18a2)

    @property
    def ci_freq_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x3097)
        return TerminalConfiguration(val)

    @ci_freq_term_cfg.setter
    def ci_freq_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x3097, val)

    @ci_freq_term_cfg.deleter
    def ci_freq_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3097)

    @property
    def ci_freq_thresh_voltage(self):
        """
        float: Specifies the voltage level at which to recognize
            waveform repetitions. Select a voltage level that occurs
            only once within the entire period of a waveform. You also
            can select a voltage that occurs only once while the voltage
            rises or falls.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31ab)
        return val

    @ci_freq_thresh_voltage.setter
    def ci_freq_thresh_voltage(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31ab, val)

    @ci_freq_thresh_voltage.deleter
    def ci_freq_thresh_voltage(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31ab)

    @property
    def ci_freq_units(self):
        """
        :class:`nidaqmx.constants.FrequencyUnits`: Specifies the units
            to use to return frequency measurements.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x18a1)
        return FrequencyUnits(val)

    @ci_freq_units.setter
    def ci_freq_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x18a1, val)

    @ci_freq_units.deleter
    def ci_freq_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18a1)

    @property
    def ci_gps_sync_method(self):
        """
        :class:`nidaqmx.constants.GpsSignalType`: Specifies the method
            to use to synchronize the counter to a GPS receiver.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x1092)
        return GpsSignalType(val)

    @ci_gps_sync_method.setter
    def ci_gps_sync_method(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x1092, val)

    @ci_gps_sync_method.deleter
    def ci_gps_sync_method(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1092)

    @property
    def ci_gps_sync_src(self):
        """
        str: Specifies the terminal to which the GPS synchronization
            signal is connected.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x1093)
        return val

    @ci_gps_sync_src.setter
    def ci_gps_sync_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x1093, val)

    @ci_gps_sync_src.deleter
    def ci_gps_sync_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1093)

    @property
    def ci_lin_encoder_dist_per_pulse(self):
        """
        float: Specifies the distance to measure for each pulse the
            encoder generates on signal A or signal B. This value is in
            the units you specify with **ci_lin_encoder_units**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x911)
        return val

    @ci_lin_encoder_dist_per_pulse.setter
    def ci_lin_encoder_dist_per_pulse(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x911, val)

    @ci_lin_encoder_dist_per_pulse.deleter
    def ci_lin_encoder_dist_per_pulse(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x911)

    @property
    def ci_lin_encoder_initial_pos(self):
        """
        float: Specifies the position of the encoder when the
            measurement begins. This value is in the units you specify
            with **ci_lin_encoder_units**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x915)
        return val

    @ci_lin_encoder_initial_pos.setter
    def ci_lin_encoder_initial_pos(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x915, val)

    @ci_lin_encoder_initial_pos.deleter
    def ci_lin_encoder_initial_pos(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x915)

    @property
    def ci_lin_encoder_units(self):
        """
        :class:`nidaqmx.constants.LengthUnits`: Specifies the units to
            use to return linear encoder measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x18a9)
        return LengthUnits(val)

    @ci_lin_encoder_units.setter
    def ci_lin_encoder_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x18a9, val)

    @ci_lin_encoder_units.deleter
    def ci_lin_encoder_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18a9)

    @property
    def ci_max(self):
        """
        float: Specifies the maximum value you expect to measure. This
            value is in the units you specify with a units property.
            When you query this property, it returns the coerced maximum
            value that the hardware can measure with the current
            settings.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x189c)
        return val

    @ci_max.setter
    def ci_max(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x189c, val)

    @ci_max.deleter
    def ci_max(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x189c)

    @property
    def ci_max_meas_period(self):
        """
        float: Specifies the maximum period (in seconds) in which the
            device will recognize signals. For frequency measurements, a
            signal with a higher period than the one set in this
            property will return 0 Hz. For duty cycle, the device will
            return 0 or 1 depending on the state of the line during the
            max defined period of time. Period measurements will return
            NaN. Pulse width measurement will return zero.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x3095)
        return val

    @ci_max_meas_period.setter
    def ci_max_meas_period(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x3095, val)

    @ci_max_meas_period.deleter
    def ci_max_meas_period(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3095)

    @property
    def ci_meas_type(self):
        """
        :class:`nidaqmx.constants.UsageTypeCI`: Indicates the
            measurement to take with the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x18a0)
        return UsageTypeCI(val)

    @property
    def ci_mem_map_enable(self):
        """
        bool: Specifies for NI-DAQmx to map hardware registers to the
            memory space of the application, if possible. Normally, NI-
            DAQmx maps hardware registers to memory accessible only to
            the kernel. Mapping the registers to the memory space of the
            application increases performance. However, if the
            application accesses the memory space mapped to the
            registers, it can adversely affect the operation of the
            device and possibly result in a system crash.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2ed2)
        return val

    @ci_mem_map_enable.setter
    def ci_mem_map_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2ed2, val)

    @ci_mem_map_enable.deleter
    def ci_mem_map_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2ed2)

    @property
    def ci_min(self):
        """
        float: Specifies the minimum value you expect to measure. This
            value is in the units you specify with a units property.
            When you query this property, it returns the coerced minimum
            value that the hardware can measure with the current
            settings.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x189d)
        return val

    @ci_min.setter
    def ci_min(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x189d, val)

    @ci_min.deleter
    def ci_min(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x189d)

    @property
    def ci_num_possibly_invalid_samps(self):
        """
        int: Indicates the number of samples that the device might have
            overwritten before it could transfer them to the buffer.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x193c)
        return val

    @property
    def ci_output_state(self):
        """
        :class:`nidaqmx.constants.Level`: Indicates the current state of
            the out terminal of the counter.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x149)
        return Level(val)

    @property
    def ci_period_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x21ec)
        return val

    @ci_period_dig_fltr_enable.setter
    def ci_period_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x21ec, val)

    @ci_period_dig_fltr_enable.deleter
    def ci_period_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21ec)

    @property
    def ci_period_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x21ed)
        return val

    @ci_period_dig_fltr_min_pulse_width.setter
    def ci_period_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x21ed, val)

    @ci_period_dig_fltr_min_pulse_width.deleter
    def ci_period_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21ed)

    @property
    def ci_period_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x21ef)
        return val

    @ci_period_dig_fltr_timebase_rate.setter
    def ci_period_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x21ef, val)

    @ci_period_dig_fltr_timebase_rate.deleter
    def ci_period_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21ef)

    @property
    def ci_period_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x21ee)
        return val

    @ci_period_dig_fltr_timebase_src.setter
    def ci_period_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x21ee, val)

    @ci_period_dig_fltr_timebase_src.deleter
    def ci_period_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21ee)

    @property
    def ci_period_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x21f0)
        return val

    @ci_period_dig_sync_enable.setter
    def ci_period_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x21f0, val)

    @ci_period_dig_sync_enable.deleter
    def ci_period_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21f0)

    @property
    def ci_period_div(self):
        """
        int: Specifies the value by which to divide the input signal if
            **ci_period_meas_meth** is
            **CounterFrequencyMethod.LARGE_RANGE_2_COUNTERS**. The
            larger the divisor, the more accurate the measurement.
            However, too large a value could cause the count register to
            roll over, which results in an incorrect measurement.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x192e)
        return val

    @ci_period_div.setter
    def ci_period_div(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x192e, val)

    @ci_period_div.deleter
    def ci_period_div(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x192e)

    @property
    def ci_period_enable_averaging(self):
        """
        bool: Specifies whether to enable averaging mode for Sample
            Clock-timed period measurements.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2ed1)
        return val

    @ci_period_enable_averaging.setter
    def ci_period_enable_averaging(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2ed1, val)

    @ci_period_enable_averaging.deleter
    def ci_period_enable_averaging(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2ed1)

    @property
    def ci_period_hyst(self):
        """
        float: Specifies a hysteresis level to apply to
            **ci_period_thresh_voltage**. When
            **ci_period_starting_edge** is rising, the source signal
            must first fall below **ci_period_thresh_voltage** minus the
            hysteresis before a rising edge is detected at
            **ci_period_thresh_voltage**. When
            **ci_period_starting_edge** is falling, the source signal
            must first rise above **ci_period_thresh_voltage** plus the
            hysteresis before a falling edge is detected at
            **ci_period_thresh_voltage**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31ae)
        return val

    @ci_period_hyst.setter
    def ci_period_hyst(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31ae, val)

    @ci_period_hyst.deleter
    def ci_period_hyst(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31ae)

    @property
    def ci_period_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the input line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x309a)
        return LogicLvlBehavior(val)

    @ci_period_logic_lvl_behavior.setter
    def ci_period_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x309a, val)

    @ci_period_logic_lvl_behavior.deleter
    def ci_period_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x309a)

    @property
    def ci_period_meas_meth(self):
        """
        :class:`nidaqmx.constants.CounterFrequencyMethod`: Specifies the
            method to use to measure the period of the signal.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x192c)
        return CounterFrequencyMethod(val)

    @ci_period_meas_meth.setter
    def ci_period_meas_meth(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x192c, val)

    @ci_period_meas_meth.deleter
    def ci_period_meas_meth(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x192c)

    @property
    def ci_period_meas_time(self):
        """
        float: Specifies in seconds the length of time to measure the
            period of the signal if **ci_period_meas_meth** is
            **CounterFrequencyMethod.HIGH_FREQUENCY_2_COUNTERS**.
            Measurement accuracy increases with increased measurement
            time and with increased signal frequency. If you measure a
            high-frequency signal for too long, however, the count
            register could roll over, which results in an incorrect
            measurement.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x192d)
        return val

    @ci_period_meas_time.setter
    def ci_period_meas_time(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x192d, val)

    @ci_period_meas_time.deleter
    def ci_period_meas_time(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x192d)

    @property
    def ci_period_starting_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies between which edges
            to measure the period of the signal.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x852)
        return Edge(val)

    @ci_period_starting_edge.setter
    def ci_period_starting_edge(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x852, val)

    @ci_period_starting_edge.deleter
    def ci_period_starting_edge(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x852)

    @property
    def ci_period_term(self):
        """
        str: Specifies the input terminal of the signal to measure.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x18a4)
        return val

    @ci_period_term.setter
    def ci_period_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x18a4, val)

    @ci_period_term.deleter
    def ci_period_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18a4)

    @property
    def ci_period_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x3099)
        return TerminalConfiguration(val)

    @ci_period_term_cfg.setter
    def ci_period_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x3099, val)

    @ci_period_term_cfg.deleter
    def ci_period_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3099)

    @property
    def ci_period_thresh_voltage(self):
        """
        float: Specifies the voltage level at which to recognize
            waveform repetitions. Select a voltage level that occurs
            only once within the entire period of a waveform. You also
            can select a voltage that occurs only once while the voltage
            rises or falls.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31ad)
        return val

    @ci_period_thresh_voltage.setter
    def ci_period_thresh_voltage(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31ad, val)

    @ci_period_thresh_voltage.deleter
    def ci_period_thresh_voltage(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31ad)

    @property
    def ci_period_units(self):
        """
        :class:`nidaqmx.constants.TimeUnits`: Specifies the unit to use
            to return period measurements.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x18a3)
        return TimeUnits(val)

    @ci_period_units.setter
    def ci_period_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x18a3, val)

    @ci_period_units.deleter
    def ci_period_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18a3)

    @property
    def ci_prescaler(self):
        """
        int: Specifies the divisor to apply to the signal you connect to
            the counter source terminal. Scaled data that you read takes
            this setting into account. You should use a prescaler only
            when you connect an external signal to the counter source
            terminal and when that signal has a higher frequency than
            the fastest onboard timebase. Setting this value disables
            duplicate count prevention unless you explicitly set
            **ci_dup_count_prevention** to True.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x2239)
        return val

    @ci_prescaler.setter
    def ci_prescaler(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x2239, val)

    @ci_prescaler.deleter
    def ci_prescaler(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2239)

    @property
    def ci_pulse_freq_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply a digital filter to the signal
            to measure.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2f06)
        return val

    @ci_pulse_freq_dig_fltr_enable.setter
    def ci_pulse_freq_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2f06, val)

    @ci_pulse_freq_dig_fltr_enable.deleter
    def ci_pulse_freq_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f06)

    @property
    def ci_pulse_freq_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f07)
        return val

    @ci_pulse_freq_dig_fltr_min_pulse_width.setter
    def ci_pulse_freq_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f07, val)

    @ci_pulse_freq_dig_fltr_min_pulse_width.deleter
    def ci_pulse_freq_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f07)

    @property
    def ci_pulse_freq_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the digital filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f09)
        return val

    @ci_pulse_freq_dig_fltr_timebase_rate.setter
    def ci_pulse_freq_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f09, val)

    @ci_pulse_freq_dig_fltr_timebase_rate.deleter
    def ci_pulse_freq_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f09)

    @property
    def ci_pulse_freq_dig_fltr_timebase_src(self):
        """
        str: Specifies the terminal of the signal to use as the timebase
            of the digital filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2f08)
        return val

    @ci_pulse_freq_dig_fltr_timebase_src.setter
    def ci_pulse_freq_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2f08, val)

    @ci_pulse_freq_dig_fltr_timebase_src.deleter
    def ci_pulse_freq_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f08)

    @property
    def ci_pulse_freq_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2f0a)
        return val

    @ci_pulse_freq_dig_sync_enable.setter
    def ci_pulse_freq_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2f0a, val)

    @ci_pulse_freq_dig_sync_enable.deleter
    def ci_pulse_freq_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f0a)

    @property
    def ci_pulse_freq_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the count reset line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30b2)
        return LogicLvlBehavior(val)

    @ci_pulse_freq_logic_lvl_behavior.setter
    def ci_pulse_freq_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30b2, val)

    @ci_pulse_freq_logic_lvl_behavior.deleter
    def ci_pulse_freq_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30b2)

    @property
    def ci_pulse_freq_starting_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies on which edge of the
            input signal to begin pulse measurement.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2f05)
        return Edge(val)

    @ci_pulse_freq_starting_edge.setter
    def ci_pulse_freq_starting_edge(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2f05, val)

    @ci_pulse_freq_starting_edge.deleter
    def ci_pulse_freq_starting_edge(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f05)

    @property
    def ci_pulse_freq_term(self):
        """
        str: Specifies the input terminal of the signal to measure.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2f04)
        return val

    @ci_pulse_freq_term.setter
    def ci_pulse_freq_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2f04, val)

    @ci_pulse_freq_term.deleter
    def ci_pulse_freq_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f04)

    @property
    def ci_pulse_freq_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30b1)
        return TerminalConfiguration(val)

    @ci_pulse_freq_term_cfg.setter
    def ci_pulse_freq_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30b1, val)

    @ci_pulse_freq_term_cfg.deleter
    def ci_pulse_freq_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30b1)

    @property
    def ci_pulse_freq_units(self):
        """
        :class:`nidaqmx.constants.FrequencyUnits`: Specifies the units
            to use to return pulse specifications in terms of frequency.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2f0b)
        return FrequencyUnits(val)

    @ci_pulse_freq_units.setter
    def ci_pulse_freq_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2f0b, val)

    @ci_pulse_freq_units.deleter
    def ci_pulse_freq_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f0b)

    @property
    def ci_pulse_ticks_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply a digital filter to the signal
            to measure.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2f16)
        return val

    @ci_pulse_ticks_dig_fltr_enable.setter
    def ci_pulse_ticks_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2f16, val)

    @ci_pulse_ticks_dig_fltr_enable.deleter
    def ci_pulse_ticks_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f16)

    @property
    def ci_pulse_ticks_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f17)
        return val

    @ci_pulse_ticks_dig_fltr_min_pulse_width.setter
    def ci_pulse_ticks_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f17, val)

    @ci_pulse_ticks_dig_fltr_min_pulse_width.deleter
    def ci_pulse_ticks_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f17)

    @property
    def ci_pulse_ticks_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the digital filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f19)
        return val

    @ci_pulse_ticks_dig_fltr_timebase_rate.setter
    def ci_pulse_ticks_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f19, val)

    @ci_pulse_ticks_dig_fltr_timebase_rate.deleter
    def ci_pulse_ticks_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f19)

    @property
    def ci_pulse_ticks_dig_fltr_timebase_src(self):
        """
        str: Specifies the terminal of the signal to use as the timebase
            of the digital filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2f18)
        return val

    @ci_pulse_ticks_dig_fltr_timebase_src.setter
    def ci_pulse_ticks_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2f18, val)

    @ci_pulse_ticks_dig_fltr_timebase_src.deleter
    def ci_pulse_ticks_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f18)

    @property
    def ci_pulse_ticks_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2f1a)
        return val

    @ci_pulse_ticks_dig_sync_enable.setter
    def ci_pulse_ticks_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2f1a, val)

    @ci_pulse_ticks_dig_sync_enable.deleter
    def ci_pulse_ticks_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f1a)

    @property
    def ci_pulse_ticks_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the count reset line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30b6)
        return LogicLvlBehavior(val)

    @ci_pulse_ticks_logic_lvl_behavior.setter
    def ci_pulse_ticks_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30b6, val)

    @ci_pulse_ticks_logic_lvl_behavior.deleter
    def ci_pulse_ticks_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30b6)

    @property
    def ci_pulse_ticks_starting_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies on which edge of the
            input signal to begin pulse measurement.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2f15)
        return Edge(val)

    @ci_pulse_ticks_starting_edge.setter
    def ci_pulse_ticks_starting_edge(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2f15, val)

    @ci_pulse_ticks_starting_edge.deleter
    def ci_pulse_ticks_starting_edge(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f15)

    @property
    def ci_pulse_ticks_term(self):
        """
        str: Specifies the input terminal of the signal to measure.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2f14)
        return val

    @ci_pulse_ticks_term.setter
    def ci_pulse_ticks_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2f14, val)

    @ci_pulse_ticks_term.deleter
    def ci_pulse_ticks_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f14)

    @property
    def ci_pulse_ticks_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30b5)
        return TerminalConfiguration(val)

    @ci_pulse_ticks_term_cfg.setter
    def ci_pulse_ticks_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30b5, val)

    @ci_pulse_ticks_term_cfg.deleter
    def ci_pulse_ticks_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30b5)

    @property
    def ci_pulse_time_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply a digital filter to the signal
            to measure.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2f0e)
        return val

    @ci_pulse_time_dig_fltr_enable.setter
    def ci_pulse_time_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2f0e, val)

    @ci_pulse_time_dig_fltr_enable.deleter
    def ci_pulse_time_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f0e)

    @property
    def ci_pulse_time_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f0f)
        return val

    @ci_pulse_time_dig_fltr_min_pulse_width.setter
    def ci_pulse_time_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f0f, val)

    @ci_pulse_time_dig_fltr_min_pulse_width.deleter
    def ci_pulse_time_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f0f)

    @property
    def ci_pulse_time_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the digital filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f11)
        return val

    @ci_pulse_time_dig_fltr_timebase_rate.setter
    def ci_pulse_time_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f11, val)

    @ci_pulse_time_dig_fltr_timebase_rate.deleter
    def ci_pulse_time_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f11)

    @property
    def ci_pulse_time_dig_fltr_timebase_src(self):
        """
        str: Specifies the terminal of the signal to use as the timebase
            of the digital filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2f10)
        return val

    @ci_pulse_time_dig_fltr_timebase_src.setter
    def ci_pulse_time_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2f10, val)

    @ci_pulse_time_dig_fltr_timebase_src.deleter
    def ci_pulse_time_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f10)

    @property
    def ci_pulse_time_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2f12)
        return val

    @ci_pulse_time_dig_sync_enable.setter
    def ci_pulse_time_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2f12, val)

    @ci_pulse_time_dig_sync_enable.deleter
    def ci_pulse_time_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f12)

    @property
    def ci_pulse_time_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the count reset line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30b4)
        return LogicLvlBehavior(val)

    @ci_pulse_time_logic_lvl_behavior.setter
    def ci_pulse_time_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30b4, val)

    @ci_pulse_time_logic_lvl_behavior.deleter
    def ci_pulse_time_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30b4)

    @property
    def ci_pulse_time_starting_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies on which edge of the
            input signal to begin pulse measurement.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2f0d)
        return Edge(val)

    @ci_pulse_time_starting_edge.setter
    def ci_pulse_time_starting_edge(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2f0d, val)

    @ci_pulse_time_starting_edge.deleter
    def ci_pulse_time_starting_edge(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f0d)

    @property
    def ci_pulse_time_term(self):
        """
        str: Specifies the input terminal of the signal to measure.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2f0c)
        return val

    @ci_pulse_time_term.setter
    def ci_pulse_time_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2f0c, val)

    @ci_pulse_time_term.deleter
    def ci_pulse_time_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f0c)

    @property
    def ci_pulse_time_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30b3)
        return TerminalConfiguration(val)

    @ci_pulse_time_term_cfg.setter
    def ci_pulse_time_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30b3, val)

    @ci_pulse_time_term_cfg.deleter
    def ci_pulse_time_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30b3)

    @property
    def ci_pulse_time_units(self):
        """
        :class:`nidaqmx.constants.TimeUnits`: Specifies the units to use
            to return pulse specifications in terms of high time and low
            time.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2f13)
        return TimeUnits(val)

    @ci_pulse_time_units.setter
    def ci_pulse_time_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2f13, val)

    @ci_pulse_time_units.deleter
    def ci_pulse_time_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f13)

    @property
    def ci_pulse_width_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x220a)
        return val

    @ci_pulse_width_dig_fltr_enable.setter
    def ci_pulse_width_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x220a, val)

    @ci_pulse_width_dig_fltr_enable.deleter
    def ci_pulse_width_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x220a)

    @property
    def ci_pulse_width_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x220b)
        return val

    @ci_pulse_width_dig_fltr_min_pulse_width.setter
    def ci_pulse_width_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x220b, val)

    @ci_pulse_width_dig_fltr_min_pulse_width.deleter
    def ci_pulse_width_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x220b)

    @property
    def ci_pulse_width_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x220d)
        return val

    @ci_pulse_width_dig_fltr_timebase_rate.setter
    def ci_pulse_width_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x220d, val)

    @ci_pulse_width_dig_fltr_timebase_rate.deleter
    def ci_pulse_width_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x220d)

    @property
    def ci_pulse_width_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x220c)
        return val

    @ci_pulse_width_dig_fltr_timebase_src.setter
    def ci_pulse_width_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x220c, val)

    @ci_pulse_width_dig_fltr_timebase_src.deleter
    def ci_pulse_width_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x220c)

    @property
    def ci_pulse_width_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x220e)
        return val

    @ci_pulse_width_dig_sync_enable.setter
    def ci_pulse_width_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x220e, val)

    @ci_pulse_width_dig_sync_enable.deleter
    def ci_pulse_width_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x220e)

    @property
    def ci_pulse_width_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the input line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30aa)
        return LogicLvlBehavior(val)

    @ci_pulse_width_logic_lvl_behavior.setter
    def ci_pulse_width_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30aa, val)

    @ci_pulse_width_logic_lvl_behavior.deleter
    def ci_pulse_width_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30aa)

    @property
    def ci_pulse_width_starting_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies on which edge of the
            input signal to begin each pulse width measurement.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x825)
        return Edge(val)

    @ci_pulse_width_starting_edge.setter
    def ci_pulse_width_starting_edge(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x825, val)

    @ci_pulse_width_starting_edge.deleter
    def ci_pulse_width_starting_edge(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x825)

    @property
    def ci_pulse_width_term(self):
        """
        str: Specifies the input terminal of the signal to measure.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x18aa)
        return val

    @ci_pulse_width_term.setter
    def ci_pulse_width_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x18aa, val)

    @ci_pulse_width_term.deleter
    def ci_pulse_width_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18aa)

    @property
    def ci_pulse_width_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30a9)
        return TerminalConfiguration(val)

    @ci_pulse_width_term_cfg.setter
    def ci_pulse_width_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30a9, val)

    @ci_pulse_width_term_cfg.deleter
    def ci_pulse_width_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30a9)

    @property
    def ci_pulse_width_units(self):
        """
        :class:`nidaqmx.constants.TimeUnits`: Specifies the units to use
            to return pulse width measurements.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x823)
        return TimeUnits(val)

    @ci_pulse_width_units.setter
    def ci_pulse_width_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x823, val)

    @ci_pulse_width_units.deleter
    def ci_pulse_width_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x823)

    @property
    def ci_samp_clk_overrun_behavior(self):
        """
        :class:`nidaqmx.constants.SampClkOverrunBehavior`: Specifies the
            counter behavior when data is read but a new value was not
            detected during a sample clock.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x3093)
        return SampClkOverrunBehavior(val)

    @ci_samp_clk_overrun_behavior.setter
    def ci_samp_clk_overrun_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x3093, val)

    @ci_samp_clk_overrun_behavior.deleter
    def ci_samp_clk_overrun_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3093)

    @property
    def ci_samp_clk_overrun_sentinel_val(self):
        """
        int: Specifies the sentinel value returned when the No New
            Sample Behavior is set to Sentinel Value.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x3094)
        return val

    @ci_samp_clk_overrun_sentinel_val.setter
    def ci_samp_clk_overrun_sentinel_val(self, val):
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x3094, val)

    @ci_samp_clk_overrun_sentinel_val.deleter
    def ci_samp_clk_overrun_sentinel_val(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3094)

    @property
    def ci_semi_period_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2219)
        return val

    @ci_semi_period_dig_fltr_enable.setter
    def ci_semi_period_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2219, val)

    @ci_semi_period_dig_fltr_enable.deleter
    def ci_semi_period_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2219)

    @property
    def ci_semi_period_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x221a)
        return val

    @ci_semi_period_dig_fltr_min_pulse_width.setter
    def ci_semi_period_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x221a, val)

    @ci_semi_period_dig_fltr_min_pulse_width.deleter
    def ci_semi_period_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x221a)

    @property
    def ci_semi_period_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x221c)
        return val

    @ci_semi_period_dig_fltr_timebase_rate.setter
    def ci_semi_period_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x221c, val)

    @ci_semi_period_dig_fltr_timebase_rate.deleter
    def ci_semi_period_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x221c)

    @property
    def ci_semi_period_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x221b)
        return val

    @ci_semi_period_dig_fltr_timebase_src.setter
    def ci_semi_period_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x221b, val)

    @ci_semi_period_dig_fltr_timebase_src.deleter
    def ci_semi_period_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x221b)

    @property
    def ci_semi_period_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x221d)
        return val

    @ci_semi_period_dig_sync_enable.setter
    def ci_semi_period_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x221d, val)

    @ci_semi_period_dig_sync_enable.deleter
    def ci_semi_period_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x221d)

    @property
    def ci_semi_period_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the count reset line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30b0)
        return LogicLvlBehavior(val)

    @ci_semi_period_logic_lvl_behavior.setter
    def ci_semi_period_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30b0, val)

    @ci_semi_period_logic_lvl_behavior.deleter
    def ci_semi_period_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30b0)

    @property
    def ci_semi_period_starting_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies on which edge of the
            input signal to begin semi-period measurement. Semi-period
            measurements alternate between high time and low time,
            starting on this edge.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x22fe)
        return Edge(val)

    @ci_semi_period_starting_edge.setter
    def ci_semi_period_starting_edge(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x22fe, val)

    @ci_semi_period_starting_edge.deleter
    def ci_semi_period_starting_edge(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x22fe)

    @property
    def ci_semi_period_term(self):
        """
        str: Specifies the input terminal of the signal to measure.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x18b0)
        return val

    @ci_semi_period_term.setter
    def ci_semi_period_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x18b0, val)

    @ci_semi_period_term.deleter
    def ci_semi_period_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18b0)

    @property
    def ci_semi_period_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30af)
        return TerminalConfiguration(val)

    @ci_semi_period_term_cfg.setter
    def ci_semi_period_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30af, val)

    @ci_semi_period_term_cfg.deleter
    def ci_semi_period_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30af)

    @property
    def ci_semi_period_units(self):
        """
        :class:`nidaqmx.constants.TimeUnits`: Specifies the units to use
            to return semi-period measurements.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x18af)
        return TimeUnits(val)

    @ci_semi_period_units.setter
    def ci_semi_period_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x18af, val)

    @ci_semi_period_units.deleter
    def ci_semi_period_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18af)

    @property
    def ci_tc_reached(self):
        """
        bool: Indicates whether the counter rolled over. When you query
            this property, NI-DAQmx resets it to False.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x150)
        return val

    @property
    def ci_thresh_voltage(self):
        """
        float: Specifies the digital threshold value in Volts for high
            and low input transitions. Some devices do not support this
            for differential channels.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30b7)
        return val

    @ci_thresh_voltage.setter
    def ci_thresh_voltage(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30b7, val)

    @ci_thresh_voltage.deleter
    def ci_thresh_voltage(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30b7)

    @property
    def ci_timestamp_initial_seconds(self):
        """
        int: Specifies the number of seconds that elapsed since the
            beginning of the current year. This value is ignored if
            **ci_gps_sync_method** is **GpsSignalType1.IRIGB**.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x22b4)
        return val

    @ci_timestamp_initial_seconds.setter
    def ci_timestamp_initial_seconds(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x22b4, val)

    @ci_timestamp_initial_seconds.deleter
    def ci_timestamp_initial_seconds(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x22b4)

    @property
    def ci_timestamp_units(self):
        """
        :class:`nidaqmx.constants.TimeUnits`: Specifies the units to use
            to return timestamp measurements.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x22b3)
        return TimeUnits(val)

    @ci_timestamp_units.setter
    def ci_timestamp_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x22b3, val)

    @ci_timestamp_units.deleter
    def ci_timestamp_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x22b3)

    @property
    def ci_two_edge_sep_first_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x220f)
        return val

    @ci_two_edge_sep_first_dig_fltr_enable.setter
    def ci_two_edge_sep_first_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x220f, val)

    @ci_two_edge_sep_first_dig_fltr_enable.deleter
    def ci_two_edge_sep_first_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x220f)

    @property
    def ci_two_edge_sep_first_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2210)
        return val

    @ci_two_edge_sep_first_dig_fltr_min_pulse_width.setter
    def ci_two_edge_sep_first_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2210, val)

    @ci_two_edge_sep_first_dig_fltr_min_pulse_width.deleter
    def ci_two_edge_sep_first_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2210)

    @property
    def ci_two_edge_sep_first_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2212)
        return val

    @ci_two_edge_sep_first_dig_fltr_timebase_rate.setter
    def ci_two_edge_sep_first_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2212, val)

    @ci_two_edge_sep_first_dig_fltr_timebase_rate.deleter
    def ci_two_edge_sep_first_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2212)

    @property
    def ci_two_edge_sep_first_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2211)
        return val

    @ci_two_edge_sep_first_dig_fltr_timebase_src.setter
    def ci_two_edge_sep_first_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2211, val)

    @ci_two_edge_sep_first_dig_fltr_timebase_src.deleter
    def ci_two_edge_sep_first_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2211)

    @property
    def ci_two_edge_sep_first_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2213)
        return val

    @ci_two_edge_sep_first_dig_sync_enable.setter
    def ci_two_edge_sep_first_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2213, val)

    @ci_two_edge_sep_first_dig_sync_enable.deleter
    def ci_two_edge_sep_first_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2213)

    @property
    def ci_two_edge_sep_first_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies on which edge of the
            first signal to start each measurement.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x833)
        return Edge(val)

    @ci_two_edge_sep_first_edge.setter
    def ci_two_edge_sep_first_edge(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x833, val)

    @ci_two_edge_sep_first_edge.deleter
    def ci_two_edge_sep_first_edge(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x833)

    @property
    def ci_two_edge_sep_first_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the input line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30ac)
        return LogicLvlBehavior(val)

    @ci_two_edge_sep_first_logic_lvl_behavior.setter
    def ci_two_edge_sep_first_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30ac, val)

    @ci_two_edge_sep_first_logic_lvl_behavior.deleter
    def ci_two_edge_sep_first_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30ac)

    @property
    def ci_two_edge_sep_first_term(self):
        """
        str: Specifies the source terminal of the digital signal that
            starts each measurement.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x18ad)
        return val

    @ci_two_edge_sep_first_term.setter
    def ci_two_edge_sep_first_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x18ad, val)

    @ci_two_edge_sep_first_term.deleter
    def ci_two_edge_sep_first_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18ad)

    @property
    def ci_two_edge_sep_first_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30ab)
        return TerminalConfiguration(val)

    @ci_two_edge_sep_first_term_cfg.setter
    def ci_two_edge_sep_first_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30ab, val)

    @ci_two_edge_sep_first_term_cfg.deleter
    def ci_two_edge_sep_first_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30ab)

    @property
    def ci_two_edge_sep_second_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2214)
        return val

    @ci_two_edge_sep_second_dig_fltr_enable.setter
    def ci_two_edge_sep_second_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2214, val)

    @ci_two_edge_sep_second_dig_fltr_enable.deleter
    def ci_two_edge_sep_second_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2214)

    @property
    def ci_two_edge_sep_second_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2215)
        return val

    @ci_two_edge_sep_second_dig_fltr_min_pulse_width.setter
    def ci_two_edge_sep_second_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2215, val)

    @ci_two_edge_sep_second_dig_fltr_min_pulse_width.deleter
    def ci_two_edge_sep_second_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2215)

    @property
    def ci_two_edge_sep_second_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2217)
        return val

    @ci_two_edge_sep_second_dig_fltr_timebase_rate.setter
    def ci_two_edge_sep_second_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2217, val)

    @ci_two_edge_sep_second_dig_fltr_timebase_rate.deleter
    def ci_two_edge_sep_second_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2217)

    @property
    def ci_two_edge_sep_second_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2216)
        return val

    @ci_two_edge_sep_second_dig_fltr_timebase_src.setter
    def ci_two_edge_sep_second_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2216, val)

    @ci_two_edge_sep_second_dig_fltr_timebase_src.deleter
    def ci_two_edge_sep_second_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2216)

    @property
    def ci_two_edge_sep_second_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2218)
        return val

    @ci_two_edge_sep_second_dig_sync_enable.setter
    def ci_two_edge_sep_second_dig_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2218, val)

    @ci_two_edge_sep_second_dig_sync_enable.deleter
    def ci_two_edge_sep_second_dig_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2218)

    @property
    def ci_two_edge_sep_second_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies on which edge of the
            second signal to stop each measurement.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x834)
        return Edge(val)

    @ci_two_edge_sep_second_edge.setter
    def ci_two_edge_sep_second_edge(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x834, val)

    @ci_two_edge_sep_second_edge.deleter
    def ci_two_edge_sep_second_edge(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x834)

    @property
    def ci_two_edge_sep_second_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior on the count reset line.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30ae)
        return LogicLvlBehavior(val)

    @ci_two_edge_sep_second_logic_lvl_behavior.setter
    def ci_two_edge_sep_second_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30ae, val)

    @ci_two_edge_sep_second_logic_lvl_behavior.deleter
    def ci_two_edge_sep_second_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30ae)

    @property
    def ci_two_edge_sep_second_term(self):
        """
        str: Specifies the source terminal of the digital signal that
            stops each measurement.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x18ae)
        return val

    @ci_two_edge_sep_second_term.setter
    def ci_two_edge_sep_second_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x18ae, val)

    @ci_two_edge_sep_second_term.deleter
    def ci_two_edge_sep_second_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18ae)

    @property
    def ci_two_edge_sep_second_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30ad)
        return TerminalConfiguration(val)

    @ci_two_edge_sep_second_term_cfg.setter
    def ci_two_edge_sep_second_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30ad, val)

    @ci_two_edge_sep_second_term_cfg.deleter
    def ci_two_edge_sep_second_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30ad)

    @property
    def ci_two_edge_sep_units(self):
        """
        :class:`nidaqmx.constants.TimeUnits`: Specifies the units to use
            to return two-edge separation measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x18ac)
        return TimeUnits(val)

    @ci_two_edge_sep_units.setter
    def ci_two_edge_sep_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x18ac, val)

    @ci_two_edge_sep_units.deleter
    def ci_two_edge_sep_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18ac)

    @property
    def ci_usb_xfer_req_count(self):
        """
        int: Specifies the maximum number of simultaneous USB transfers
            used to stream data. Modify this value to affect performance
            under different combinations of operating system and device.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x3004)
        return val

    @ci_usb_xfer_req_count.setter
    def ci_usb_xfer_req_count(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x3004, val)

    @ci_usb_xfer_req_count.deleter
    def ci_usb_xfer_req_count(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3004)

    @property
    def ci_usb_xfer_req_size(self):
        """
        int: Specifies the maximum size of a USB transfer request in
            bytes. Modify this value to affect performance under
            different combinations of operating system and device.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x2a92)
        return val

    @ci_usb_xfer_req_size.setter
    def ci_usb_xfer_req_size(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x2a92, val)

    @ci_usb_xfer_req_size.deleter
    def ci_usb_xfer_req_size(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2a92)

    @property
    def ci_velocity_a_input_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x30e0)
        return val

    @ci_velocity_a_input_dig_fltr_enable.setter
    def ci_velocity_a_input_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x30e0, val)

    @ci_velocity_a_input_dig_fltr_enable.deleter
    def ci_velocity_a_input_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30e0)

    @property
    def ci_velocity_a_input_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the digital
            filter recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30e1)
        return val

    @ci_velocity_a_input_dig_fltr_min_pulse_width.setter
    def ci_velocity_a_input_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30e1, val)

    @ci_velocity_a_input_dig_fltr_min_pulse_width.deleter
    def ci_velocity_a_input_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30e1)

    @property
    def ci_velocity_a_input_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30e3)
        return val

    @ci_velocity_a_input_dig_fltr_timebase_rate.setter
    def ci_velocity_a_input_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30e3, val)

    @ci_velocity_a_input_dig_fltr_timebase_rate.deleter
    def ci_velocity_a_input_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30e3)

    @property
    def ci_velocity_a_input_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x30e2)
        return val

    @ci_velocity_a_input_dig_fltr_timebase_src.setter
    def ci_velocity_a_input_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x30e2, val)

    @ci_velocity_a_input_dig_fltr_timebase_src.deleter
    def ci_velocity_a_input_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30e2)

    @property
    def ci_velocity_a_input_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior of the input terminal.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30df)
        return LogicLvlBehavior(val)

    @ci_velocity_a_input_logic_lvl_behavior.setter
    def ci_velocity_a_input_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30df, val)

    @ci_velocity_a_input_logic_lvl_behavior.deleter
    def ci_velocity_a_input_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30df)

    @property
    def ci_velocity_a_input_term(self):
        """
        str: Specifies the terminal to which signal A is connected.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x30dd)
        return val

    @ci_velocity_a_input_term.setter
    def ci_velocity_a_input_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x30dd, val)

    @ci_velocity_a_input_term.deleter
    def ci_velocity_a_input_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30dd)

    @property
    def ci_velocity_a_input_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30de)
        return TerminalConfiguration(val)

    @ci_velocity_a_input_term_cfg.setter
    def ci_velocity_a_input_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30de, val)

    @ci_velocity_a_input_term_cfg.deleter
    def ci_velocity_a_input_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30de)

    @property
    def ci_velocity_ang_encoder_pulses_per_rev(self):
        """
        int: Specifies the number of pulses the encoder generates per
            revolution. This value is the number of pulses on either
            signal A or signal B, not the total number of pulses on both
            signal A and signal B.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x30d9)
        return val

    @ci_velocity_ang_encoder_pulses_per_rev.setter
    def ci_velocity_ang_encoder_pulses_per_rev(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x30d9, val)

    @ci_velocity_ang_encoder_pulses_per_rev.deleter
    def ci_velocity_ang_encoder_pulses_per_rev(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30d9)

    @property
    def ci_velocity_ang_encoder_units(self):
        """
        :class:`nidaqmx.constants.AngularVelocityUnits`: Specifies the
            units to use to return angular velocity counter
            measurements.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30d8)
        return AngularVelocityUnits(val)

    @ci_velocity_ang_encoder_units.setter
    def ci_velocity_ang_encoder_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30d8, val)

    @ci_velocity_ang_encoder_units.deleter
    def ci_velocity_ang_encoder_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30d8)

    @property
    def ci_velocity_b_input_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply the pulse width filter to the
            signal.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x30e7)
        return val

    @ci_velocity_b_input_dig_fltr_enable.setter
    def ci_velocity_b_input_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x30e7, val)

    @ci_velocity_b_input_dig_fltr_enable.deleter
    def ci_velocity_b_input_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30e7)

    @property
    def ci_velocity_b_input_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the digital
            filter recognizes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30e8)
        return val

    @ci_velocity_b_input_dig_fltr_min_pulse_width.setter
    def ci_velocity_b_input_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30e8, val)

    @ci_velocity_b_input_dig_fltr_min_pulse_width.deleter
    def ci_velocity_b_input_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30e8)

    @property
    def ci_velocity_b_input_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the pulse width filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30ea)
        return val

    @ci_velocity_b_input_dig_fltr_timebase_rate.setter
    def ci_velocity_b_input_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30ea, val)

    @ci_velocity_b_input_dig_fltr_timebase_rate.deleter
    def ci_velocity_b_input_dig_fltr_timebase_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30ea)

    @property
    def ci_velocity_b_input_dig_fltr_timebase_src(self):
        """
        str: Specifies the input terminal of the signal to use as the
            timebase of the pulse width filter.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x30e9)
        return val

    @ci_velocity_b_input_dig_fltr_timebase_src.setter
    def ci_velocity_b_input_dig_fltr_timebase_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x30e9, val)

    @ci_velocity_b_input_dig_fltr_timebase_src.deleter
    def ci_velocity_b_input_dig_fltr_timebase_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30e9)

    @property
    def ci_velocity_b_input_logic_lvl_behavior(self):
        """
        :class:`nidaqmx.constants.LogicLvlBehavior`: Specifies the logic
            level behavior of the input terminal.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30e6)
        return LogicLvlBehavior(val)

    @ci_velocity_b_input_logic_lvl_behavior.setter
    def ci_velocity_b_input_logic_lvl_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30e6, val)

    @ci_velocity_b_input_logic_lvl_behavior.deleter
    def ci_velocity_b_input_logic_lvl_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30e6)

    @property
    def ci_velocity_b_input_term(self):
        """
        str: Specifies the terminal to which signal B is connected.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x30e4)
        return val

    @ci_velocity_b_input_term.setter
    def ci_velocity_b_input_term(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x30e4, val)

    @ci_velocity_b_input_term.deleter
    def ci_velocity_b_input_term(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30e4)

    @property
    def ci_velocity_b_input_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            input terminal configuration.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30e5)
        return TerminalConfiguration(val)

    @ci_velocity_b_input_term_cfg.setter
    def ci_velocity_b_input_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30e5, val)

    @ci_velocity_b_input_term_cfg.deleter
    def ci_velocity_b_input_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30e5)

    @property
    def ci_velocity_div(self):
        """
        int: Specifies the value by which to divide the input signal.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x30ec)
        return val

    @ci_velocity_div.setter
    def ci_velocity_div(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x30ec, val)

    @ci_velocity_div.deleter
    def ci_velocity_div(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30ec)

    @property
    def ci_velocity_encoder_decoding_type(self):
        """
        :class:`nidaqmx.constants.EncoderType`: Specifies how to count
            and interpret the pulses the encoder generates on signal A
            and signal B. X1, X2, and X4 are valid for quadrature
            encoders only. Two Pulse Counting is valid for two-pulse
            encoders only.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30dc)
        return EncoderType(val)

    @ci_velocity_encoder_decoding_type.setter
    def ci_velocity_encoder_decoding_type(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30dc, val)

    @ci_velocity_encoder_decoding_type.deleter
    def ci_velocity_encoder_decoding_type(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30dc)

    @property
    def ci_velocity_lin_encoder_dist_per_pulse(self):
        """
        float: Specifies the distance to measure for each pulse the
            encoder generates on signal A or signal B. This value is in
            the units you specify in CI.Velocity.LinEncoder.DistUnits.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30db)
        return val

    @ci_velocity_lin_encoder_dist_per_pulse.setter
    def ci_velocity_lin_encoder_dist_per_pulse(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30db, val)

    @ci_velocity_lin_encoder_dist_per_pulse.deleter
    def ci_velocity_lin_encoder_dist_per_pulse(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30db)

    @property
    def ci_velocity_lin_encoder_units(self):
        """
        :class:`nidaqmx.constants.VelocityUnits`: Specifies the units to
            use to return linear encoder velocity measurements from the
            channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30da)
        return VelocityUnits(val)

    @ci_velocity_lin_encoder_units.setter
    def ci_velocity_lin_encoder_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30da, val)

    @ci_velocity_lin_encoder_units.deleter
    def ci_velocity_lin_encoder_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30da)

    @property
    def ci_velocity_meas_time(self):
        """
        float: Specifies in seconds the length of time to measure the
            velocity of the signal.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30eb)
        return val

    @ci_velocity_meas_time.setter
    def ci_velocity_meas_time(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30eb, val)

    @ci_velocity_meas_time.deleter
    def ci_velocity_meas_time(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30eb)

