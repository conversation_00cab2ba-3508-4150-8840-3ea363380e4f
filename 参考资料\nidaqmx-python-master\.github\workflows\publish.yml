name: Publish nidaqmx

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      environment:
        description: The environment to publish to.
        default: 'none'
        required: true
        type: choice
        options:
          - none
          - pypi
          - testpypi

env:
  dist-artifact-name: nidaqmx-distribution-packages
  environment: ${{ github.event_name == 'release' && 'pypi' || inputs.environment }}
  environment-info: |
    {
      "pypi": {
        "base-url": "https://pypi.org",
        "upload-url": "https://upload.pypi.org/legacy/"
      },
      "testpypi": {
        "base-url": "https://test.pypi.org",
        "upload-url": "https://test.pypi.org/legacy/"
      }
    }

jobs:
  check_nidaqmx:
    name: Check nidaqmx
    uses: ./.github/workflows/build.yml
  check_docs:
    name: Check docs
    uses: ./.github/workflows/generate_docs.yml
  build_nidaqmx:
    name: Build nidaqmx
    runs-on: ubuntu-latest
    needs: [check_nidaqmx, check_docs]
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Set up Python
        uses: ni/python-actions/setup-python@5286c12d65d90b2ea738bd57d452dc4366497581 # v0.4.1
      - name: Set up Poetry
        uses: ni/python-actions/setup-poetry@5286c12d65d90b2ea738bd57d452dc4366497581 # v0.4.1
      - name: Check project version
        if: github.event_name == 'release'
        uses: ni/python-actions/check-project-version@5286c12d65d90b2ea738bd57d452dc4366497581 # v0.4.1
      - name: Build distribution packages
        run: poetry build
      - name: Upload build artifacts
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: ${{ env.dist-artifact-name }}
          path: dist/*
  publish_to_pypi:
    name: Publish nidaqmx to PyPI
    if: github.event_name == 'release' || inputs.environment != 'none'
    runs-on: ubuntu-latest
    needs: [build_nidaqmx]
    environment:
      # This logic is duplicated because `name` doesn't support the `env` context.
      name: ${{ github.event_name == 'release' && 'pypi' || inputs.environment }}
      url: ${{ fromJson(env.environment-info)[env.environment].base-url }}/p/nidaqmx
    permissions:
      id-token: write
    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
      with:
        name: ${{ env.dist-artifact-name }}
        path: dist/
    - run: ls -lR
    - name: Upload to ${{ env.environment }}
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        repository-url: ${{ fromJson(env.environment-info)[env.environment].upload-url }} 
  update_version:
    name: Update nidaqmx version
    runs-on: ubuntu-latest
    needs: [build_nidaqmx]
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Set up Python
        uses: ni/python-actions/setup-python@5286c12d65d90b2ea738bd57d452dc4366497581 # v0.4.1
      - name: Set up Poetry
        uses: ni/python-actions/setup-poetry@5286c12d65d90b2ea738bd57d452dc4366497581 # v0.4.1
      - name: Update project version
        uses: ni/python-actions/update-project-version@5286c12d65d90b2ea738bd57d452dc4366497581 # v0.4.1
