# Do not edit this file; it was automatically generated.

import numpy

from nidaqmx.system.physical_channel import _PhysicalChannelAlternateConstructor
from nidaqmx.constants import (
    Coupling, DigitalPatternCondition, Edge, Slope, Timescale, TriggerType,
    WindowTriggerCondition1)


class ReferenceTrigger:
    """
    Represents the reference trigger configurations for a DAQmx task.
    """
    __slots__ = ('_handle', '_interpreter')

    def __init__(self, task_handle, interpreter):
        self._handle = task_handle
        self._interpreter = interpreter

    @property
    def anlg_edge_coupling(self):
        """
        :class:`nidaqmx.constants.Coupling`: Specifies the coupling for
            the source signal of the trigger if the source is a terminal
            rather than a virtual channel.
        """

        val = self._interpreter.get_trig_attribute_int32(self._handle, 0x2235)
        return Coupling(val)

    @anlg_edge_coupling.setter
    def anlg_edge_coupling(self, val):
        val = val.value
        self._interpreter.set_trig_attribute_int32(self._handle, 0x2235, val)

    @anlg_edge_coupling.deleter
    def anlg_edge_coupling(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2235)

    @property
    def anlg_edge_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply a digital filter to the digital
            output of the analog triggering circuitry (the Analog
            Comparison Event). When enabled, the analog signal must stay
            above or below the trigger level for the minimum pulse width
            before being recognized. Use filtering  for noisy trigger
            signals that transition in and out of the hysteresis window
            rapidly.
        """

        val = self._interpreter.get_trig_attribute_bool(self._handle, 0x2ee6)
        return val

    @anlg_edge_dig_fltr_enable.setter
    def anlg_edge_dig_fltr_enable(self, val):
        self._interpreter.set_trig_attribute_bool(self._handle, 0x2ee6, val)

    @anlg_edge_dig_fltr_enable.deleter
    def anlg_edge_dig_fltr_enable(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2ee6)

    @property
    def anlg_edge_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width thefilter
            recognizes.
        """

        val = self._interpreter.get_trig_attribute_double(self._handle, 0x2ee7)
        return val

    @anlg_edge_dig_fltr_min_pulse_width.setter
    def anlg_edge_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_trig_attribute_double(self._handle, 0x2ee7, val)

    @anlg_edge_dig_fltr_min_pulse_width.deleter
    def anlg_edge_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2ee7)

    @property
    def anlg_edge_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the digital filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_trig_attribute_double(self._handle, 0x2ee9)
        return val

    @anlg_edge_dig_fltr_timebase_rate.setter
    def anlg_edge_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_trig_attribute_double(self._handle, 0x2ee9, val)

    @anlg_edge_dig_fltr_timebase_rate.deleter
    def anlg_edge_dig_fltr_timebase_rate(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2ee9)

    @property
    def anlg_edge_dig_fltr_timebase_src(self):
        """
        str: Specifies the terminal of the signal to use as the timebase
            of the digital filter.
        """

        val = self._interpreter.get_trig_attribute_string(self._handle, 0x2ee8)
        return val

    @anlg_edge_dig_fltr_timebase_src.setter
    def anlg_edge_dig_fltr_timebase_src(self, val):
        self._interpreter.set_trig_attribute_string(self._handle, 0x2ee8, val)

    @anlg_edge_dig_fltr_timebase_src.deleter
    def anlg_edge_dig_fltr_timebase_src(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2ee8)

    @property
    def anlg_edge_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_trig_attribute_bool(self._handle, 0x2eea)
        return val

    @anlg_edge_dig_sync_enable.setter
    def anlg_edge_dig_sync_enable(self, val):
        self._interpreter.set_trig_attribute_bool(self._handle, 0x2eea, val)

    @anlg_edge_dig_sync_enable.deleter
    def anlg_edge_dig_sync_enable(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2eea)

    @property
    def anlg_edge_hyst(self):
        """
        float: Specifies a hysteresis level in the units of the
            measurement. If **anlg_edge_slope** is **Slope1.RISING**,
            the trigger does not deassert until the source signal passes
            below **anlg_edge_lvl** minus the hysteresis. If
            **anlg_edge_slope** is **Slope1.FALLING**, the trigger does
            not deassert until the source signal passes above
            **anlg_edge_lvl** plus the hysteresis. Hysteresis is always
            enabled. Set this property to a non-zero value to use
            hysteresis.
        """

        val = self._interpreter.get_trig_attribute_double(self._handle, 0x1421)
        return val

    @anlg_edge_hyst.setter
    def anlg_edge_hyst(self, val):
        self._interpreter.set_trig_attribute_double(self._handle, 0x1421, val)

    @anlg_edge_hyst.deleter
    def anlg_edge_hyst(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1421)

    @property
    def anlg_edge_lvl(self):
        """
        float: Specifies in the units of the measurement the threshold
            at which the Reference Trigger occurs.  Use
            **anlg_edge_slope** to specify on which slope to trigger at
            this threshold.
        """

        val = self._interpreter.get_trig_attribute_double(self._handle, 0x1422)
        return val

    @anlg_edge_lvl.setter
    def anlg_edge_lvl(self, val):
        self._interpreter.set_trig_attribute_double(self._handle, 0x1422, val)

    @anlg_edge_lvl.deleter
    def anlg_edge_lvl(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1422)

    @property
    def anlg_edge_slope(self):
        """
        :class:`nidaqmx.constants.Slope`: Specifies on which slope of
            the source signal the Reference Trigger occurs.
        """

        val = self._interpreter.get_trig_attribute_int32(self._handle, 0x1423)
        return Slope(val)

    @anlg_edge_slope.setter
    def anlg_edge_slope(self, val):
        val = val.value
        self._interpreter.set_trig_attribute_int32(self._handle, 0x1423, val)

    @anlg_edge_slope.deleter
    def anlg_edge_slope(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1423)

    @property
    def anlg_edge_src(self):
        """
        str: Specifies the name of a virtual channel or terminal where
            there is an analog signal to use as the source of the
            Reference Trigger.
        """

        val = self._interpreter.get_trig_attribute_string(self._handle, 0x1424)
        return val

    @anlg_edge_src.setter
    def anlg_edge_src(self, val):
        self._interpreter.set_trig_attribute_string(self._handle, 0x1424, val)

    @anlg_edge_src.deleter
    def anlg_edge_src(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1424)

    @property
    def anlg_multi_edge_couplings(self):
        """
        List[:class:`nidaqmx.constants.Coupling`]: Specifies an list
            that describes the couplings for the corresponding source
            signal of the trigger if the source is a terminal rather
            than a virtual channel. Each element of the list corresponds
            to a source in Ref.AnlgMultiEdge.Srcs and an element in each
            of the other Analog Multi Edge property lists, if they are
            not empty.
        """

        val = self._interpreter.get_trig_attribute_int32_array(self._handle, 0x312a)
        return [Coupling(e) for e in val]

    @anlg_multi_edge_couplings.setter
    def anlg_multi_edge_couplings(self, val):
        val = numpy.array([e.value for e in val], dtype=numpy.int32)
        self._interpreter.set_trig_attribute_int32_array(self._handle, 0x312a, val)

    @anlg_multi_edge_couplings.deleter
    def anlg_multi_edge_couplings(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x312a)

    @property
    def anlg_multi_edge_hysts(self):
        """
        List[float]: Specifies an list of hysteresis levels in the units
            of the measurement or generation. If the corresponding
            element of Ref.AnlgMultiEdge.Slopes is Rising, the trigger
            does not deassert until the source signal passes below the
            corresponding element of Ref.AnlgMultiEdge.Lvls minus the
            hysteresis. If Ref.AnlgEdge.Slope is Falling, the trigger
            does not deassert until the source signal passes above
            Ref.AnlgEdge.Lvl plus the hysteresis. Hysteresis is always
            enabled. Set this property to a non-zero value to use
            hysteresis. Each element of the list corresponds to a source
            in Ref.AnlgMultiEdge.Srcs and an element in each of the
            other Analog Multi Edge property lists, if they are not
            empty.
        """

        val = self._interpreter.get_trig_attribute_double_array(self._handle, 0x3129)
        return val

    @anlg_multi_edge_hysts.setter
    def anlg_multi_edge_hysts(self, val):
        val = numpy.array(val, dtype=numpy.float64)
        self._interpreter.set_trig_attribute_double_array(self._handle, 0x3129, val)

    @anlg_multi_edge_hysts.deleter
    def anlg_multi_edge_hysts(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x3129)

    @property
    def anlg_multi_edge_lvls(self):
        """
        List[float]: Specifies an list of thresholds in the units of the
            measurement or generation to start acquiring or generating
            samples. Each element of the list corresponds to a source in
            Ref.AnlgMultiEdge.Srcs and an element in each of the other
            Analog Multi Edge property lists, if they are not empty.
        """

        val = self._interpreter.get_trig_attribute_double_array(self._handle, 0x3128)
        return val

    @anlg_multi_edge_lvls.setter
    def anlg_multi_edge_lvls(self, val):
        val = numpy.array(val, dtype=numpy.float64)
        self._interpreter.set_trig_attribute_double_array(self._handle, 0x3128, val)

    @anlg_multi_edge_lvls.deleter
    def anlg_multi_edge_lvls(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x3128)

    @property
    def anlg_multi_edge_slopes(self):
        """
        List[:class:`nidaqmx.constants.Slope`]: Specifies an list of
            slopes on which to trigger task to start generating or
            acquiring samples. Each element of the list corresponds to a
            source in Ref.AnlgMultiEdge.Srcs and an element in each of
            the other Analog Multi Edge property lists, if they are not
            empty.
        """

        val = self._interpreter.get_trig_attribute_int32_array(self._handle, 0x3127)
        return [Slope(e) for e in val]

    @anlg_multi_edge_slopes.setter
    def anlg_multi_edge_slopes(self, val):
        val = numpy.array([e.value for e in val], dtype=numpy.int32)
        self._interpreter.set_trig_attribute_int32_array(self._handle, 0x3127, val)

    @anlg_multi_edge_slopes.deleter
    def anlg_multi_edge_slopes(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x3127)

    @property
    def anlg_multi_edge_srcs(self):
        """
        str: Specifies a List and/or range of analog sources that are
            going to be used for Analog triggering. Each source
            corresponds to an element in each of the Analog Multi Edge
            property lists, if they are not empty.
        """

        val = self._interpreter.get_trig_attribute_string(self._handle, 0x3126)
        return val

    @anlg_multi_edge_srcs.setter
    def anlg_multi_edge_srcs(self, val):
        self._interpreter.set_trig_attribute_string(self._handle, 0x3126, val)

    @anlg_multi_edge_srcs.deleter
    def anlg_multi_edge_srcs(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x3126)

    @property
    def anlg_win_btm(self):
        """
        float: Specifies the lower limit of the window. Specify this
            value in the units of the measurement.
        """

        val = self._interpreter.get_trig_attribute_double(self._handle, 0x1428)
        return val

    @anlg_win_btm.setter
    def anlg_win_btm(self, val):
        self._interpreter.set_trig_attribute_double(self._handle, 0x1428, val)

    @anlg_win_btm.deleter
    def anlg_win_btm(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1428)

    @property
    def anlg_win_coupling(self):
        """
        :class:`nidaqmx.constants.Coupling`: Specifies the coupling for
            the source signal of the trigger if the source is a terminal
            rather than a virtual channel.
        """

        val = self._interpreter.get_trig_attribute_int32(self._handle, 0x1857)
        return Coupling(val)

    @anlg_win_coupling.setter
    def anlg_win_coupling(self, val):
        val = val.value
        self._interpreter.set_trig_attribute_int32(self._handle, 0x1857, val)

    @anlg_win_coupling.deleter
    def anlg_win_coupling(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1857)

    @property
    def anlg_win_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply a digital filter to the digital
            output of the analog triggering circuitry (the Analog
            Comparison Event). When enabled, the analog signal must stay
            within the trigger window for the minimum pulse width before
            being recognized. Use filtering for noisy trigger signals
            that transition in and out of the window rapidly.
        """

        val = self._interpreter.get_trig_attribute_bool(self._handle, 0x2eeb)
        return val

    @anlg_win_dig_fltr_enable.setter
    def anlg_win_dig_fltr_enable(self, val):
        self._interpreter.set_trig_attribute_bool(self._handle, 0x2eeb, val)

    @anlg_win_dig_fltr_enable.deleter
    def anlg_win_dig_fltr_enable(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2eeb)

    @property
    def anlg_win_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_trig_attribute_double(self._handle, 0x2eec)
        return val

    @anlg_win_dig_fltr_min_pulse_width.setter
    def anlg_win_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_trig_attribute_double(self._handle, 0x2eec, val)

    @anlg_win_dig_fltr_min_pulse_width.deleter
    def anlg_win_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2eec)

    @property
    def anlg_win_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the digital filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_trig_attribute_double(self._handle, 0x2eee)
        return val

    @anlg_win_dig_fltr_timebase_rate.setter
    def anlg_win_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_trig_attribute_double(self._handle, 0x2eee, val)

    @anlg_win_dig_fltr_timebase_rate.deleter
    def anlg_win_dig_fltr_timebase_rate(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2eee)

    @property
    def anlg_win_dig_fltr_timebase_src(self):
        """
        str: Specifies the terminal of the signal to use as the timebase
            of the digital filter.
        """

        val = self._interpreter.get_trig_attribute_string(self._handle, 0x2eed)
        return val

    @anlg_win_dig_fltr_timebase_src.setter
    def anlg_win_dig_fltr_timebase_src(self, val):
        self._interpreter.set_trig_attribute_string(self._handle, 0x2eed, val)

    @anlg_win_dig_fltr_timebase_src.deleter
    def anlg_win_dig_fltr_timebase_src(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2eed)

    @property
    def anlg_win_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_trig_attribute_bool(self._handle, 0x2eef)
        return val

    @anlg_win_dig_sync_enable.setter
    def anlg_win_dig_sync_enable(self, val):
        self._interpreter.set_trig_attribute_bool(self._handle, 0x2eef, val)

    @anlg_win_dig_sync_enable.deleter
    def anlg_win_dig_sync_enable(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2eef)

    @property
    def anlg_win_src(self):
        """
        str: Specifies the name of a virtual channel or terminal where
            there is an analog signal to use as the source of the
            Reference Trigger.
        """

        val = self._interpreter.get_trig_attribute_string(self._handle, 0x1426)
        return val

    @anlg_win_src.setter
    def anlg_win_src(self, val):
        self._interpreter.set_trig_attribute_string(self._handle, 0x1426, val)

    @anlg_win_src.deleter
    def anlg_win_src(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1426)

    @property
    def anlg_win_top(self):
        """
        float: Specifies the upper limit of the window. Specify this
            value in the units of the measurement.
        """

        val = self._interpreter.get_trig_attribute_double(self._handle, 0x1429)
        return val

    @anlg_win_top.setter
    def anlg_win_top(self, val):
        self._interpreter.set_trig_attribute_double(self._handle, 0x1429, val)

    @anlg_win_top.deleter
    def anlg_win_top(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1429)

    @property
    def anlg_win_trig_when(self):
        """
        :class:`nidaqmx.constants.WindowTriggerCondition1`: Specifies
            whether the Reference Trigger occurs when the source signal
            enters the window or when it leaves the window. Use
            **anlg_win_btm** and **anlg_win_top** to specify the window.
        """

        val = self._interpreter.get_trig_attribute_int32(self._handle, 0x1427)
        return WindowTriggerCondition1(val)

    @anlg_win_trig_when.setter
    def anlg_win_trig_when(self, val):
        val = val.value
        self._interpreter.set_trig_attribute_int32(self._handle, 0x1427, val)

    @anlg_win_trig_when.deleter
    def anlg_win_trig_when(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1427)

    @property
    def auto_trig_enable(self):
        """
        bool: Specifies whether to send a software trigger to the device
            when a hardware trigger is no longer active in order to
            prevent a timeout.
        """

        val = self._interpreter.get_trig_attribute_bool(self._handle, 0x2ec1)
        return val

    @auto_trig_enable.setter
    def auto_trig_enable(self, val):
        self._interpreter.set_trig_attribute_bool(self._handle, 0x2ec1, val)

    @auto_trig_enable.deleter
    def auto_trig_enable(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2ec1)

    @property
    def auto_triggered(self):
        """
        bool: Indicates whether a completed acquisition was triggered by
            the auto trigger. If an acquisition has not completed after
            the task starts, this property returns False. This property
            is only applicable when **auto_trig_enable**  is True.
        """

        val = self._interpreter.get_trig_attribute_bool(self._handle, 0x2ec2)
        return val

    @property
    def delay(self):
        """
        float: Specifies in seconds the time to wait after the device
            receives the Reference Trigger before switching from
            pretrigger to posttrigger samples.
        """

        val = self._interpreter.get_trig_attribute_double(self._handle, 0x1483)
        return val

    @delay.setter
    def delay(self, val):
        self._interpreter.set_trig_attribute_double(self._handle, 0x1483, val)

    @delay.deleter
    def delay(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1483)

    @property
    def dig_edge_dig_fltr_enable(self):
        """
        bool: Specifies whether to apply a digital filter to the trigger
            signal.
        """

        val = self._interpreter.get_trig_attribute_bool(self._handle, 0x2ed7)
        return val

    @dig_edge_dig_fltr_enable.setter
    def dig_edge_dig_fltr_enable(self, val):
        self._interpreter.set_trig_attribute_bool(self._handle, 0x2ed7, val)

    @dig_edge_dig_fltr_enable.deleter
    def dig_edge_dig_fltr_enable(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2ed7)

    @property
    def dig_edge_dig_fltr_min_pulse_width(self):
        """
        float: Specifies in seconds the minimum pulse width the filter
            recognizes.
        """

        val = self._interpreter.get_trig_attribute_double(self._handle, 0x2ed8)
        return val

    @dig_edge_dig_fltr_min_pulse_width.setter
    def dig_edge_dig_fltr_min_pulse_width(self, val):
        self._interpreter.set_trig_attribute_double(self._handle, 0x2ed8, val)

    @dig_edge_dig_fltr_min_pulse_width.deleter
    def dig_edge_dig_fltr_min_pulse_width(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2ed8)

    @property
    def dig_edge_dig_fltr_timebase_rate(self):
        """
        float: Specifies in hertz the rate of the digital filter
            timebase. NI-DAQmx uses this value to compute settings for
            the filter.
        """

        val = self._interpreter.get_trig_attribute_double(self._handle, 0x2eda)
        return val

    @dig_edge_dig_fltr_timebase_rate.setter
    def dig_edge_dig_fltr_timebase_rate(self, val):
        self._interpreter.set_trig_attribute_double(self._handle, 0x2eda, val)

    @dig_edge_dig_fltr_timebase_rate.deleter
    def dig_edge_dig_fltr_timebase_rate(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2eda)

    @property
    def dig_edge_dig_fltr_timebase_src(self):
        """
        str: Specifies the terminal of the signal to use as the timebase
            of the digital filter.
        """

        val = self._interpreter.get_trig_attribute_string(self._handle, 0x2ed9)
        return val

    @dig_edge_dig_fltr_timebase_src.setter
    def dig_edge_dig_fltr_timebase_src(self, val):
        self._interpreter.set_trig_attribute_string(self._handle, 0x2ed9, val)

    @dig_edge_dig_fltr_timebase_src.deleter
    def dig_edge_dig_fltr_timebase_src(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2ed9)

    @property
    def dig_edge_dig_sync_enable(self):
        """
        bool: Specifies whether to synchronize recognition of
            transitions in the signal to the internal timebase of the
            device.
        """

        val = self._interpreter.get_trig_attribute_bool(self._handle, 0x2edb)
        return val

    @dig_edge_dig_sync_enable.setter
    def dig_edge_dig_sync_enable(self, val):
        self._interpreter.set_trig_attribute_bool(self._handle, 0x2edb, val)

    @dig_edge_dig_sync_enable.deleter
    def dig_edge_dig_sync_enable(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2edb)

    @property
    def dig_edge_edge(self):
        """
        :class:`nidaqmx.constants.Edge`: Specifies on what edge of a
            digital pulse the Reference Trigger occurs.
        """

        val = self._interpreter.get_trig_attribute_int32(self._handle, 0x1430)
        return Edge(val)

    @dig_edge_edge.setter
    def dig_edge_edge(self, val):
        val = val.value
        self._interpreter.set_trig_attribute_int32(self._handle, 0x1430, val)

    @dig_edge_edge.deleter
    def dig_edge_edge(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1430)

    @property
    def dig_edge_src(self):
        """
        str: Specifies the name of a terminal where there is a digital
            signal to use as the source of the Reference Trigger.
        """

        val = self._interpreter.get_trig_attribute_string(self._handle, 0x1434)
        return val

    @dig_edge_src.setter
    def dig_edge_src(self, val):
        self._interpreter.set_trig_attribute_string(self._handle, 0x1434, val)

    @dig_edge_src.deleter
    def dig_edge_src(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1434)

    @property
    def dig_pattern_pattern(self):
        """
        str: Specifies the digital pattern that must be met for the
            Reference Trigger to occur.
        """

        val = self._interpreter.get_trig_attribute_string(self._handle, 0x2187)
        return val

    @dig_pattern_pattern.setter
    def dig_pattern_pattern(self, val):
        self._interpreter.set_trig_attribute_string(self._handle, 0x2187, val)

    @dig_pattern_pattern.deleter
    def dig_pattern_pattern(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x2187)

    @property
    def dig_pattern_src(self):
        """
        :class:`nidaqmx.system.physical_channel.PhysicalChannel`:
            Specifies the physical channels to use for pattern matching.
            The order of the physical channels determines the order of
            the pattern. If a port is included, the order of the
            physical channels within the port is in ascending order.
        """

        val = self._interpreter.get_trig_attribute_string(self._handle, 0x1437)
        return _PhysicalChannelAlternateConstructor(val, self._interpreter)

    @dig_pattern_src.setter
    def dig_pattern_src(self, val):
        val = val.name
        self._interpreter.set_trig_attribute_string(self._handle, 0x1437, val)

    @dig_pattern_src.deleter
    def dig_pattern_src(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1437)

    @property
    def dig_pattern_trig_when(self):
        """
        :class:`nidaqmx.constants.DigitalPatternCondition`: Specifies
            whether the Reference Trigger occurs when the physical
            channels specified with **dig_pattern_src** match or differ
            from the digital pattern specified with
            **dig_pattern_pattern**.
        """

        val = self._interpreter.get_trig_attribute_int32(self._handle, 0x1438)
        return DigitalPatternCondition(val)

    @dig_pattern_trig_when.setter
    def dig_pattern_trig_when(self, val):
        val = val.value
        self._interpreter.set_trig_attribute_int32(self._handle, 0x1438, val)

    @dig_pattern_trig_when.deleter
    def dig_pattern_trig_when(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1438)

    @property
    def max_num_trigs_to_detect(self):
        """
        int: Specifies the maximum number of times the task will detect
            a reference trigger during the task. The number of times a
            trigger is detected and acted upon by the module may be less
            than the specified amount if the task stops early because of
            trigger/retrigger window expiration. Specifying the Maximum
            Number of Triggers to Detect to be 0 causes the driver to
            automatically set this value to the maximum possible number
            of triggers detectable by the device and configuration
            combination. Note: The number of detected triggers may be
            less than number of trigger events occurring, because the
            devices were unable to respond to the trigger.
        """

        val = self._interpreter.get_trig_attribute_uint32(self._handle, 0x3120)
        return val

    @max_num_trigs_to_detect.setter
    def max_num_trigs_to_detect(self, val):
        self._interpreter.set_trig_attribute_uint32(self._handle, 0x3120, val)

    @max_num_trigs_to_detect.deleter
    def max_num_trigs_to_detect(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x3120)

    @property
    def pretrig_samples(self):
        """
        int: Specifies the minimum number of pretrigger samples to
            acquire from each channel before recognizing the reference
            trigger. Post-trigger samples per channel are equal to
            **samp_quant_samp_per_chan** minus the number of pretrigger
            samples per channel.
        """

        val = self._interpreter.get_trig_attribute_uint32(self._handle, 0x1445)
        return val

    @pretrig_samples.setter
    def pretrig_samples(self, val):
        self._interpreter.set_trig_attribute_uint32(self._handle, 0x1445, val)

    @pretrig_samples.deleter
    def pretrig_samples(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1445)

    @property
    def retrigger_win(self):
        """
        float: Specifies the duration in seconds after each trigger
            during which the device may trigger. Once the window has
            passed, the device stops detecting triggers, and the task
            will stop after the device finishes acquiring post-trigger
            samples that it already started. Specifying a Retrigger
            Window of -1 causes the window to be infinite.
        """

        val = self._interpreter.get_trig_attribute_double(self._handle, 0x311f)
        return val

    @retrigger_win.setter
    def retrigger_win(self, val):
        self._interpreter.set_trig_attribute_double(self._handle, 0x311f, val)

    @retrigger_win.deleter
    def retrigger_win(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x311f)

    @property
    def retriggerable(self):
        """
        bool: Specifies whether a finite task resets, acquires
            pretrigger samples, and waits for another Reference Trigger
            after the task completes. When you set this property to
            True, the device will acquire post-trigger samples, reset,
            and acquire pretrigger samples each time the Reference
            Trigger occurs until the task stops. The device ignores a
            trigger if it is in the process of acquiring signals.
        """

        val = self._interpreter.get_trig_attribute_bool(self._handle, 0x311d)
        return val

    @retriggerable.setter
    def retriggerable(self, val):
        self._interpreter.set_trig_attribute_bool(self._handle, 0x311d, val)

    @retriggerable.deleter
    def retriggerable(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x311d)

    @property
    def term(self):
        """
        str: Indicates the name of the internal Reference Trigger
            terminal for the task. This property does not return the
            name of the trigger source terminal.
        """

        val = self._interpreter.get_trig_attribute_string(self._handle, 0x2f1f)
        return val

    @property
    def timestamp_enable(self):
        """
        bool: Specifies whether the reference trigger timestamp is
            enabled. If the timestamp is enabled but no resources are
            available, an error will be returned at run time.
        """

        val = self._interpreter.get_trig_attribute_bool(self._handle, 0x312e)
        return val

    @timestamp_enable.setter
    def timestamp_enable(self, val):
        self._interpreter.set_trig_attribute_bool(self._handle, 0x312e, val)

    @timestamp_enable.deleter
    def timestamp_enable(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x312e)

    @property
    def timestamp_timescale(self):
        """
        :class:`nidaqmx.constants.Timescale`: Specifies the reference
            trigger timestamp timescale.
        """

        val = self._interpreter.get_trig_attribute_int32(self._handle, 0x3130)
        return Timescale(val)

    @timestamp_timescale.setter
    def timestamp_timescale(self, val):
        val = val.value
        self._interpreter.set_trig_attribute_int32(self._handle, 0x3130, val)

    @timestamp_timescale.deleter
    def timestamp_timescale(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x3130)

    @property
    def timestamp_val(self):
        """
        datetime: Indicates the reference trigger timestamp value.
        """

        val = self._interpreter.get_trig_attribute_timestamp(self._handle, 0x312f)
        return val

    @property
    def trig_type(self):
        """
        :class:`nidaqmx.constants.TriggerType`: Specifies the type of
            trigger to use to mark a reference point for the
            measurement.
        """

        val = self._interpreter.get_trig_attribute_int32(self._handle, 0x1419)
        return TriggerType(val)

    @trig_type.setter
    def trig_type(self, val):
        val = val.value
        self._interpreter.set_trig_attribute_int32(self._handle, 0x1419, val)

    @trig_type.deleter
    def trig_type(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x1419)

    @property
    def trig_win(self):
        """
        float: Specifies the duration in seconds after the task starts
            during which the device may trigger. Once the window has
            passed, the device stops detecting triggers, and the task
            will stop after the device finishes acquiring post-trigger
            samples that it already started. If no triggers are detected
            during the entire period, then no data will be returned.
            Specifying a Trigger Window of -1 causes the window to be
            infinite.
        """

        val = self._interpreter.get_trig_attribute_double(self._handle, 0x311e)
        return val

    @trig_win.setter
    def trig_win(self, val):
        self._interpreter.set_trig_attribute_double(self._handle, 0x311e, val)

    @trig_win.deleter
    def trig_win(self):
        self._interpreter.reset_trig_attribute(self._handle, 0x311e)

    def cfg_anlg_edge_ref_trig(
            self, trigger_source, pretrigger_samples,
            trigger_slope=Slope.RISING, trigger_level=0.0):
        """
        Configures the task to stop the acquisition when the device
        acquires all pretrigger samples; an analog signal reaches the
        level you specify; and the device acquires all post-trigger
        samples. When you use a Reference Trigger, the default for the
        read RelativeTo property is **first_pretrigger_sample** with a
        read Offset of 0.

        Args:
            trigger_source (str): Is the name of a virtual channel or
                terminal where there is an analog signal to use as the
                source of the trigger.
            pretrigger_samples (int): Specifies the minimum number of
                samples to acquire per channel before recognizing the
                Reference Trigger. The number of post-trigger samples
                per channel is equal to **number of samples per
                channel** in the DAQmx Timing function minus
                **pretrigger_samples**.
            trigger_slope (Optional[nidaqmx.constants.Slope]): Specifies
                on which slope of the signal the Reference Trigger
                occurs.
            trigger_level (Optional[float]): Specifies at what threshold
                to trigger. Specify this value in the units of the
                measurement or generation. Use **trigger_slope** to
                specify on which slope to trigger at this threshold.
        """

        self._interpreter.cfg_anlg_edge_ref_trig(
            self._handle, trigger_source, pretrigger_samples,
            trigger_slope.value, trigger_level)

    def cfg_anlg_multi_edge_ref_trig(
            self, trigger_sources, pretrigger_samples,
            trigger_slope_array=None, trigger_level_array=None):
        """
        Configures the task to stop the acquisition when the device
        acquires all pretrigger samples; any of the configured analog
        signals cross the respective levels you specified; and the
        device acquires all post-trigger samples. When you use a
        Reference Trigger, the default for the read RelativeTo property
        is First Pretrigger Sample with a read Offset of 0. Multi-edge
        triggering treats the specified triggers as if a logical OR is
        applied.

        Args:
            trigger_sources (str): Is the name of a virtual channel or
                terminal where there is an analog signal to use as the
                source of the trigger.
            pretrigger_samples (int): Specifies the minimum number of
                samples to acquire per channel before recognizing the
                Reference Trigger. The number of post-trigger samples
                per channel is equal to **number of samples per
                channel** in the DAQmx Timing VI minus **pretrigger
                samples per channel**.
            trigger_slope_array (Optional[List[nidaqmx.constants.Slope]]): 
                Specifies on which slope of the signal the Reference
                Trigger occurs.
            trigger_level_array (Optional[List[float]]): Specifies at
                what threshold to trigger. Specify this value in the
                units of the measurement or generation. Use **slope** to
                specify on which slope to trigger at this threshold.
        """
        if trigger_slope_array is None:
            trigger_slope_array = []

        if trigger_level_array is None:
            trigger_level_array = []

        trigger_slope_array = numpy.array([p.value for p in trigger_slope_array], dtype=numpy.int32)
        trigger_level_array = numpy.array(trigger_level_array, dtype=numpy.float64)


        self._interpreter.cfg_anlg_multi_edge_ref_trig(
            self._handle, trigger_sources, pretrigger_samples,
            trigger_slope_array, trigger_level_array)

    def cfg_anlg_window_ref_trig(
            self, trigger_source, window_top, window_bottom,
            pretrigger_samples,
            trigger_when=WindowTriggerCondition1.ENTERING_WINDOW):
        """
        Configures the task to stop the acquisition when the device
        acquires all pretrigger samples; an analog signal enters or
        leaves a range you specify; and the device acquires all post-
        trigger samples. When you use a Reference Trigger, the default
        for the read RelativeTo property is **first_pretrigger_sample**
        with a read Offset of 0.

        Args:
            trigger_source (str): Is the name of a virtual channel or
                terminal where there is an analog signal to use as the
                source of the trigger.
            window_top (float): Is the upper limit of the window.
                Specify this value in the units of the measurement or
                generation.
            window_bottom (float): Is the lower limit of the window.
                Specify this value in the units of the measurement or
                generation.
            pretrigger_samples (int): Specifies the minimum number of
                samples to acquire per channel before recognizing the
                Reference Trigger. The number of post-trigger samples
                per channel is equal to **number of samples per
                channel** in the DAQmx Timing function minus
                **pretrigger_samples**.
            trigger_when (Optional[nidaqmx.constants.WindowTriggerCondition1]): 
                Specifies whether the Reference Trigger occurs when the
                signal enters the window or when it leaves the window.
                Use **window_bottom** and **window_top** to specify the
                limits of the window.
        """

        self._interpreter.cfg_anlg_window_ref_trig(
            self._handle, trigger_source, window_top, window_bottom,
            pretrigger_samples, trigger_when.value)

    def cfg_dig_edge_ref_trig(
            self, trigger_source, pretrigger_samples,
            trigger_edge=Edge.RISING):
        """
        Configures the task to stop the acquisition when the device
        acquires all pretrigger samples, detects a rising or falling
        edge of a digital signal, and acquires all posttrigger samples.
        When you use a Reference Trigger, the default for the read
        RelativeTo property is **first_pretrigger_sample** with a read
        Offset of 0.

        Args:
            trigger_source (str): Specifies the name of a terminal where
                there is a digital signal to use as the source of the
                trigger.
            pretrigger_samples (int): Specifies the minimum number of
                samples to acquire per channel before recognizing the
                Reference Trigger. The number of post-trigger samples
                per channel is equal to **number of samples per
                channel** in the DAQmx Timing function minus
                **pretrigger_samples**.
            trigger_edge (Optional[nidaqmx.constants.Edge]): Specifies
                on which edge of the digital signal the Reference
                Trigger occurs.
        """

        self._interpreter.cfg_dig_edge_ref_trig(
            self._handle, trigger_source, pretrigger_samples,
            trigger_edge.value)

    def cfg_dig_pattern_ref_trig(
            self, trigger_source, trigger_pattern, pretrigger_samples,
            trigger_when=DigitalPatternCondition.PATTERN_MATCHES):
        """
        Configures the task to stop the acquisition when the device
        acquires all pretrigger samples, matches a digital pattern, and
        acquires all posttrigger samples. When you use a Reference
        Trigger, the default for the read RelativeTo property is First
        PretriggerSample with a read Offset of zero.

        Args:
            trigger_source (str): Specifies the physical channels to use
                for pattern matching. The order of the physical channels
                determines the order of the pattern. If a port is
                included, the order of the physical channels within the
                port is in ascending order.
            trigger_pattern (str): Specifies the digital pattern that
                must be met for the trigger to occur.
            pretrigger_samples (int): Specifies the minimum number of
                samples to acquire per channel before recognizing the
                Reference Trigger. The number of post-trigger samples
                per channel is equal to **number of samples per
                channel** in the DAQmx Timing function minus
                **pretrigger_samples**.
            trigger_when (Optional[nidaqmx.constants.DigitalPatternCondition]): 
                Specifies the condition under which the trigger occurs.
        """

        self._interpreter.cfg_dig_pattern_ref_trig(
            self._handle, trigger_source, trigger_pattern, pretrigger_samples,
            trigger_when.value)

    def disable_ref_trig(self):
        """
        Disables reference triggering for the measurement.
        """

        self._interpreter.disable_ref_trig(
            self._handle)

