name: Generate docs

on:
  workflow_call:
  workflow_dispatch:
jobs:
  generate_docs:
    name: Generate docs
    runs-on:
      - ubuntu-latest
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Set up Python
        uses: ni/python-actions/setup-python@5286c12d65d90b2ea738bd57d452dc4366497581 # v0.4.1
        id: setup-python
      - name: Set up Poetry
        uses: ni/python-actions/setup-poetry@5286c12d65d90b2ea738bd57d452dc4366497581 # v0.4.1
      - name: Cache virtualenvs
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: |
            .venv
            .tox
          key: generate-docs-${{ runner.os }}-py${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('poetry.lock') }}
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          poetry install
      - name: Generate docs
        run: poetry run tox -e docs