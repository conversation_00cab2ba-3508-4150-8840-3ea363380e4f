# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from nidaqmx._stubs import data_moniker_pb2 as data__moniker__pb2


class DataMonikerStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.BeginSidebandStream = channel.unary_unary(
                '/ni.data_monikers.DataMoniker/BeginSidebandStream',
                request_serializer=data__moniker__pb2.BeginMonikerSidebandStreamRequest.SerializeToString,
                response_deserializer=data__moniker__pb2.BeginMonikerSidebandStreamResponse.FromString,
                )
        self.StreamReadWrite = channel.stream_stream(
                '/ni.data_monikers.DataMoniker/StreamReadWrite',
                request_serializer=data__moniker__pb2.MonikerWriteRequest.SerializeToString,
                response_deserializer=data__moniker__pb2.MonikerReadResponse.FromString,
                )
        self.StreamRead = channel.unary_stream(
                '/ni.data_monikers.DataMoniker/StreamRead',
                request_serializer=data__moniker__pb2.MonikerList.SerializeToString,
                response_deserializer=data__moniker__pb2.MonikerReadResponse.FromString,
                )
        self.StreamWrite = channel.stream_stream(
                '/ni.data_monikers.DataMoniker/StreamWrite',
                request_serializer=data__moniker__pb2.MonikerWriteRequest.SerializeToString,
                response_deserializer=data__moniker__pb2.StreamWriteResponse.FromString,
                )


class DataMonikerServicer(object):
    """Missing associated documentation comment in .proto file."""

    def BeginSidebandStream(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StreamReadWrite(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StreamRead(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StreamWrite(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DataMonikerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'BeginSidebandStream': grpc.unary_unary_rpc_method_handler(
                    servicer.BeginSidebandStream,
                    request_deserializer=data__moniker__pb2.BeginMonikerSidebandStreamRequest.FromString,
                    response_serializer=data__moniker__pb2.BeginMonikerSidebandStreamResponse.SerializeToString,
            ),
            'StreamReadWrite': grpc.stream_stream_rpc_method_handler(
                    servicer.StreamReadWrite,
                    request_deserializer=data__moniker__pb2.MonikerWriteRequest.FromString,
                    response_serializer=data__moniker__pb2.MonikerReadResponse.SerializeToString,
            ),
            'StreamRead': grpc.unary_stream_rpc_method_handler(
                    servicer.StreamRead,
                    request_deserializer=data__moniker__pb2.MonikerList.FromString,
                    response_serializer=data__moniker__pb2.MonikerReadResponse.SerializeToString,
            ),
            'StreamWrite': grpc.stream_stream_rpc_method_handler(
                    servicer.StreamWrite,
                    request_deserializer=data__moniker__pb2.MonikerWriteRequest.FromString,
                    response_serializer=data__moniker__pb2.StreamWriteResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'ni.data_monikers.DataMoniker', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class DataMoniker(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def BeginSidebandStream(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/ni.data_monikers.DataMoniker/BeginSidebandStream',
            data__moniker__pb2.BeginMonikerSidebandStreamRequest.SerializeToString,
            data__moniker__pb2.BeginMonikerSidebandStreamResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StreamReadWrite(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/ni.data_monikers.DataMoniker/StreamReadWrite',
            data__moniker__pb2.MonikerWriteRequest.SerializeToString,
            data__moniker__pb2.MonikerReadResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StreamRead(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/ni.data_monikers.DataMoniker/StreamRead',
            data__moniker__pb2.MonikerList.SerializeToString,
            data__moniker__pb2.MonikerReadResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StreamWrite(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/ni.data_monikers.DataMoniker/StreamWrite',
            data__moniker__pb2.MonikerWriteRequest.SerializeToString,
            data__moniker__pb2.StreamWriteResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
