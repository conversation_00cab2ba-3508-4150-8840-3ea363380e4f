script_info = {
    "modules": [
        {
            "relativeOutputPath": "_base_interpreter.py",
            "templateFile": "_base_interpreter.py.mako",
        },
        {
            "relativeOutputPath": "_library_interpreter.py",
            "templateFile": "_library_interpreter.py.mako",
        },
        {
            "relativeOutputPath": "_grpc_interpreter.py",
            "templateFile": "_grpc_interpreter.py.mako",
        },
        {
            "relativeOutputPath": "task\\channels\\_channel.py",
            "templateFile": "task\\channels\\_channel.py.mako",
        },
        {
            "relativeOutputPath": "task\\channels\\_ai_channel.py",
            "templateFile": "task\\channels\\_ai_channel.py.mako",
        },
        {
            "relativeOutputPath": "task\\_in_stream.py",
            "templateFile": "task\\_in_stream.py.mako",
        },
        {
            "relativeOutputPath": "task\\_out_stream.py",
            "templateFile": "task\\_out_stream.py.mako",
        },
        {
            "relativeOutputPath": "system\\_watchdog_modules\\expiration_state.py",
            "templateFile": "system\\_watchdog_modules\\expiration_state.py.mako",
        },
        {
            "relativeOutputPath": "system\\watchdog.py",
            "templateFile": "system\\watchdog.py.mako",
        },
        {
            "relativeOutputPath": "task\\channels\\_ao_channel.py",
            "templateFile": "task\\channels\\_ao_channel.py.mako",
        },
        {
            "relativeOutputPath": "task\\channels\\_ci_channel.py",
            "templateFile": "task\\channels\\_ci_channel.py.mako",
        },
        {
            "relativeOutputPath": "task\\channels\\_co_channel.py",
            "templateFile": "task\\channels\\_co_channel.py.mako",
        },
        {
            "relativeOutputPath": "task\\channels\\_di_channel.py",
            "templateFile": "task\\channels\\_di_channel.py.mako",
        },
        {
            "relativeOutputPath": "task\\channels\\_do_channel.py", 
            "templateFile": "task\\channels\\_do_channel.py.mako",
        },
        {
            "relativeOutputPath": "task\\collections\\_ai_channel_collection.py",
            "templateFile": "task\\collections\\_ai_channel_collection.py.mako",
        },
        {
            "relativeOutputPath": "task\\collections\\_ao_channel_collection.py",
            "templateFile": "task\\collections\\_ao_channel_collection.py.mako",
        },
        {
            "relativeOutputPath": "task\\collections\\_ci_channel_collection.py",
            "templateFile": "task\\collections\\_ci_channel_collection.py.mako",
        },
        {
            "relativeOutputPath": "task\\collections\\_co_channel_collection.py",
            "templateFile": "task\\collections\\_co_channel_collection.py.mako",
        },
        {
            "relativeOutputPath": "task\\collections\\_di_channel_collection.py",
            "templateFile": "task\\collections\\_di_channel_collection.py.mako",
        },
        {
            "relativeOutputPath": "task\\collections\\_do_channel_collection.py",
            "templateFile": "task\\collections\\_do_channel_collection.py.mako",
        },
        {
            "relativeOutputPath": "task\\_timing.py",
            "templateFile": "task\\_timing.py.mako",
        },  
        {
            "relativeOutputPath": "scale.py", 
            "templateFile": "scale.py.mako",
        },
        {
            "relativeOutputPath": "task\\_export_signals.py",
            "templateFile": "task\\_export_signals.py.mako",
        },
        {
            "relativeOutputPath": "task\\triggering\\_arm_start_trigger.py",
            "templateFile": "task\\triggering\\_arm_start_trigger.py.mako",
        },
        {
            "relativeOutputPath": "task\\triggering\\_handshake_trigger.py",
            "templateFile": "task\\triggering\\_handshake_trigger.py.mako",
        },
        {
            "relativeOutputPath": "task\\triggering\\_pause_trigger.py",
            "templateFile": "task\\triggering\\_pause_trigger.py.mako",
        },
        {
            "relativeOutputPath": "task\\triggering\\_reference_trigger.py",
            "templateFile": "task\\triggering\\_reference_trigger.py.mako",
        },
        {
            "relativeOutputPath": "task\\triggering\\_start_trigger.py",
            "templateFile": "task\\triggering\\_start_trigger.py.mako",
        },
        {
            "relativeOutputPath": "system\\system.py",
            "templateFile": "system\\system.py.mako",
        },
        {
            "relativeOutputPath": "system\\device.py",
            "templateFile": "system\\device.py.mako",
        },
        {
            "relativeOutputPath": "system\\physical_channel.py",
            "templateFile": "system\\physical_channel.py.mako",
        },
        {
            "relativeOutputPath": "task\\triggering\\_triggers.py",
            "templateFile": "task\\triggering\\_triggers.py.mako",
        },
    ]
}
