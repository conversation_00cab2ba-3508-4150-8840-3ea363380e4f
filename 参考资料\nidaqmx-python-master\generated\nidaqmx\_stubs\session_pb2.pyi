"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _SessionInitializationBehavior:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _SessionInitializationBehaviorEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_SessionInitializationBehavior.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    SESSION_INITIALIZATION_BEHAVIOR_UNSPECIFIED: _SessionInitializationBehavior.ValueType  # 0
    SESSION_INITIALIZATION_BEHAVIOR_INITIALIZE_NEW: _SessionInitializationBehavior.ValueType  # 1
    SESSION_INITIALIZATION_BEHAVIOR_ATTACH_TO_EXISTING: _SessionInitializationBehavior.ValueType  # 2

class SessionInitializationBehavior(_SessionInitializationBehavior, metaclass=_SessionInitializationBehaviorEnumTypeWrapper): ...

SESSION_INITIALIZATION_BEHAVIOR_UNSPECIFIED: SessionInitializationBehavior.ValueType  # 0
SESSION_INITIALIZATION_BEHAVIOR_INITIALIZE_NEW: SessionInitializationBehavior.ValueType  # 1
SESSION_INITIALIZATION_BEHAVIOR_ATTACH_TO_EXISTING: SessionInitializationBehavior.ValueType  # 2
global___SessionInitializationBehavior = SessionInitializationBehavior

@typing.final
class Session(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    name: builtins.str
    id: builtins.int
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        id: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["id", b"id", "name", b"name", "session", b"session"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["id", b"id", "name", b"name", "session", b"session"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["session", b"session"]) -> typing.Literal["name", "id"] | None: ...

global___Session = Session

@typing.final
class DeviceProperties(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    MODEL_FIELD_NUMBER: builtins.int
    VENDOR_FIELD_NUMBER: builtins.int
    SERIAL_NUMBER_FIELD_NUMBER: builtins.int
    PRODUCT_ID_FIELD_NUMBER: builtins.int
    name: builtins.str
    model: builtins.str
    vendor: builtins.str
    serial_number: builtins.str
    product_id: builtins.int
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        model: builtins.str = ...,
        vendor: builtins.str = ...,
        serial_number: builtins.str = ...,
        product_id: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["model", b"model", "name", b"name", "product_id", b"product_id", "serial_number", b"serial_number", "vendor", b"vendor"]) -> None: ...

global___DeviceProperties = DeviceProperties

@typing.final
class EnumerateDevicesRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___EnumerateDevicesRequest = EnumerateDevicesRequest

@typing.final
class EnumerateDevicesResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DEVICES_FIELD_NUMBER: builtins.int
    @property
    def devices(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DeviceProperties]: ...
    def __init__(
        self,
        *,
        devices: collections.abc.Iterable[global___DeviceProperties] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["devices", b"devices"]) -> None: ...

global___EnumerateDevicesResponse = EnumerateDevicesResponse

@typing.final
class ReserveRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESERVATION_ID_FIELD_NUMBER: builtins.int
    CLIENT_ID_FIELD_NUMBER: builtins.int
    reservation_id: builtins.str
    """client defined string representing a set of reservable resources"""
    client_id: builtins.str
    """client defined identifier for a specific client"""
    def __init__(
        self,
        *,
        reservation_id: builtins.str = ...,
        client_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["client_id", b"client_id", "reservation_id", b"reservation_id"]) -> None: ...

global___ReserveRequest = ReserveRequest

@typing.final
class ReserveResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IS_RESERVED_FIELD_NUMBER: builtins.int
    is_reserved: builtins.bool
    def __init__(
        self,
        *,
        is_reserved: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["is_reserved", b"is_reserved"]) -> None: ...

global___ReserveResponse = ReserveResponse

@typing.final
class IsReservedByClientRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESERVATION_ID_FIELD_NUMBER: builtins.int
    CLIENT_ID_FIELD_NUMBER: builtins.int
    reservation_id: builtins.str
    """client defined string representing a set of reservable resources"""
    client_id: builtins.str
    """client defined identifier for a specific client"""
    def __init__(
        self,
        *,
        reservation_id: builtins.str = ...,
        client_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["client_id", b"client_id", "reservation_id", b"reservation_id"]) -> None: ...

global___IsReservedByClientRequest = IsReservedByClientRequest

@typing.final
class IsReservedByClientResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IS_RESERVED_FIELD_NUMBER: builtins.int
    is_reserved: builtins.bool
    def __init__(
        self,
        *,
        is_reserved: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["is_reserved", b"is_reserved"]) -> None: ...

global___IsReservedByClientResponse = IsReservedByClientResponse

@typing.final
class UnreserveRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESERVATION_ID_FIELD_NUMBER: builtins.int
    CLIENT_ID_FIELD_NUMBER: builtins.int
    reservation_id: builtins.str
    """client defined string representing a set of reservable resources"""
    client_id: builtins.str
    """client defined identifier for a specific client"""
    def __init__(
        self,
        *,
        reservation_id: builtins.str = ...,
        client_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["client_id", b"client_id", "reservation_id", b"reservation_id"]) -> None: ...

global___UnreserveRequest = UnreserveRequest

@typing.final
class UnreserveResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IS_UNRESERVED_FIELD_NUMBER: builtins.int
    is_unreserved: builtins.bool
    def __init__(
        self,
        *,
        is_unreserved: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["is_unreserved", b"is_unreserved"]) -> None: ...

global___UnreserveResponse = UnreserveResponse

@typing.final
class ResetServerRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___ResetServerRequest = ResetServerRequest

@typing.final
class ResetServerResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IS_SERVER_RESET_FIELD_NUMBER: builtins.int
    is_server_reset: builtins.bool
    def __init__(
        self,
        *,
        is_server_reset: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["is_server_reset", b"is_server_reset"]) -> None: ...

global___ResetServerResponse = ResetServerResponse
