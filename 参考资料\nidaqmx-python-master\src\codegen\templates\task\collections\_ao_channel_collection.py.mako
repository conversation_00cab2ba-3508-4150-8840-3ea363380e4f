<%
    from codegen.utilities.text_wrappers import wrap
    from codegen.utilities.function_helpers import get_functions,  get_enums_used
    functions = get_functions(data, "AOChannelCollection")
    enums_used = get_enums_used(functions)
%>\
# Do not edit this file; it was automatically generated.

from nidaqmx.errors import DaqFunctionNotSupportedError
from nidaqmx.task.channels._ao_channel import AOChannel
from nidaqmx.task.collections._channel_collection import ChannelCollection
from nidaqmx.utils import unflatten_channel_string
%if enums_used:
from nidaqmx.constants import (
    ${', '.join([c for c in enums_used]) | wrap(4, 4)})
%endif


class AOChannelCollection(ChannelCollection):
    """
    Contains the collection of analog output channels for a DAQmx Task.
    """
    def __init__(self, task_handle, interpreter):
        """
        Do not construct this object directly; instead, construct a nidaqmx.Task and use the task.ao_channels property.
        """
        super().__init__(task_handle, interpreter)

    def _create_chan(self, physical_channel, name_to_assign_to_channel=''):
        """
        Creates and returns an AOChannel object.

        Args:
            physical_channel (str): Specifies the names of the physical
                channels to use to create virtual channels.
            name_to_assign_to_channel (Optional[str]): Specifies a name to
                assign to the virtual channel this method creates.
        Returns:
            nidaqmx.task.channels.AOChannel: 
            
            Specifies the newly created AOChannel object.
        """
        # Attempt to retrieve the last created channel name. This is only supported on DAQmx 24Q3+ with the library
        # interpreter.
        virtual_channel_name = None
        try:
            virtual_channel_name = self._interpreter.internal_get_last_created_chan()
        except (NotImplementedError, DaqFunctionNotSupportedError):
            pass

        # Fallback implementation is sometimes incorrect.
        if virtual_channel_name is None:
            if name_to_assign_to_channel:
                num_channels = len(unflatten_channel_string(physical_channel))

                if num_channels > 1:
                    virtual_channel_name = '{}0:{}'.format(
                        name_to_assign_to_channel, num_channels-1)
                else:
                    virtual_channel_name = name_to_assign_to_channel
            else:
                virtual_channel_name = physical_channel

        return AOChannel(self._handle, virtual_channel_name, self._interpreter)

<%namespace name="function_template" file="/function_template.py.mako"/>\
%for function_object in functions:
${function_template.script_function(function_object)}
%endfor