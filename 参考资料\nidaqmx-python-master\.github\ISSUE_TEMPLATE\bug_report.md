---
name: Bug report
about: Create a report to help us improve
title: ''
labels: 'bug,triage'
---

<!---
Thanks for filing an issue! Before you submit, please read the following:

Search open/closed issues before submitting. Someone may have reported the same issue before.
-->

# Bug Report

<!--- Provide a general summary of the issue here -->

## Repro or Code Sample

<!-- Please provide steps to reproduce the issue and/or a code repository, gist, code snippet or sample files -->

## Expected Behavior

<!--- Tell us what should happen -->

## Current Behavior

<!--- Tell us what happens instead of the expected behavior -->
<!--- If you are seeing an error, please include the full error message and stack trace -->
<!--- If applicable, provide screenshots -->

## Possible Solution

<!--- Not obligatory, but suggest a fix/reason for the bug -->
<!--- Please let us know if you'd be willing to contribute the fix; we'd be happy to work with you -->

## Context

<!--- How has this issue affected you? What are you trying to accomplish? -->
<!--- Providing context helps us come up with a solution that is most useful in the real world -->

## Your Environment

<!--- Include as many relevant details as possible about the environment you experienced the bug in -->

* Operating system and version: [e.g. Windows 11 24H2, Ubuntu Linux 24.04]
* NI-DAQmx version: [e.g. 2024 Q4]
* `nidaqmx-python` version: [e.g. 1.0.1]
* Python version [e.g. 3.9]