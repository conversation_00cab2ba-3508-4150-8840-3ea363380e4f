3.10 状态相关函数 
 
3.10.1 
MT_Get_Axis_Num 
功能描述：读取板卡的电机控制轴数 
 
VC 
INT32 
MT_Get_Axis_Num 
(INT32* pValue) 
VB 
Function 
MT_Get_Axis_Num 
(ByRef Value As Long) As Long 
Delphi 
function 
MT_Get_Axis_Num 
(pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Num 
(ref Int32 Value); 
输入参数 
无 
 
输出参数 
Value 
板卡支持的电机控制的数量0-N 
函数返回 
0 
函数执行成功，读取到的轴数有效 
非0 
函数执行失败，读取到的轴数无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
后续需要用到电机轴序号的函数需要注意不要越界 
 
3.10.2 MT_Get_Encoder_Num 
功能描述：读取板卡的闭环接口轴数（光栅尺或者编码器） 
 
VC 
INT32 
MT_Get_Encoder_Num 
(INT32* pValue) 
VB 
Function 
MT_Get_Encoder_Num 
(ByRef Value As Long) As Long 
Delphi 
function 
MT_Get_Encoder_Num 
(pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Encoder_Num 
(ref Int32 Value); 
输入参数 
无 
 
输出参数 
Value 
板卡支持的闭环接口的数量0-N 
函数返回 
0 
函数执行成功，读取到的轴数有效 
非0 
函数执行失败，读取到的轴数无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
后续需要用到闭环接口序号的函数需要注意不要越界 
 
3.10.3 MT_Get_Axis_Mode 
功能描述：读取板卡指定的工作模式 
 
VC 
INT32 
MT_Get_Axis_Mode(WORD AObj,INT32* pValue) 
VB 
Function 
MT_Get_Axis_Mode(ByVal AObj As Integer, ByRef Value As Long) As Long 
Delphi 
function MT_Get_Axis_Mode(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int MT_Get_Axis_Mode(UInt16 AObj, ref Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
板卡指定轴的当前工作模式 
0:零位模式 
1:速度模式 
2：位置模式（默认） 
3：直线插补 
4：圆弧插补 
函数返回 
0 
函数执行成功，读取到的加速度有效 
非0 
函数执行失败，读取到的加速度无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
序号越界后本次操作无效 
 
 
 
3.10.4 MT_Get_Axis_Acc[兼容] 
功能描述：读取板卡指定的电机控制轴的当前加速度 
 
VC 
INT32 
MT_Get_Axis_Acc(WORD AObj,INT32* pValue) 
VB 
Function 
MT_Get_Axis_Acc(ByVal AObj As Integer, 
ByRef Value As Long) As Long 
Delphi 
function MT_Get_Axis_Acc(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int MT_Get_Axis_Acc(UInt16 AObj, ref Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
板卡指定轴的当前加速度值，单位为Hz/S2 
>=0 
函数返回 
0 
函数执行成功，读取到的加速度有效 
非0 
函数执行失败，读取到的加速度无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
序号越界后本次操作无效 
 
3.10.5 MT_Set_Axis_Acc[兼容] 
功能描述：设置板卡指定的电机控制轴的加速度 
 
VC 
INT32 
MT_Set_Axis_Acc(WORD AObj,INT32 
Value) 
VB 
Function 
MT_Set_Axis_Acc(ByVal AObj As Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function MT_Set_Axis_Acc(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int MT_Set_Axis_Acc(UInt16 AObj, 
Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置板卡指定轴的加速度值，单位为Hz/S2 
>=0 
 
 
 
函数返回 
0 
函数执行成功，本次设置操作成功 
非0 
函数执行失败，本次设置操作失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
序号越界后本次操作虽然成，但是数据也是被丢弃无效 
 
3.10.6 MT_Get_Axis_Dec[兼容] 
功能描述：读取板卡指定的电机控制轴的当前减速度 
 
VC 
INT32 
MT_Get_Axis_Dec(WORD AObj,INT32* pValue) 
VB 
Function 
MT_Get_Axis_Dec(ByVal AObj As Integer, ByRef Value As Long) As Long 
Delphi 
function MT_Get_Axis_Dec(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int MT_Get_Axis_Dec(UInt16 AObj, ref Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
板卡指定轴的当前减速度值，单位为Hz/S2 
>=0 
函数返回 
0 
函数执行成功，读取到的减速度有效 
非0 
函数执行失败，读取到的减速度无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
序号越界后本次操作无效 
 
3.10.7 MT_Set_Axis_Dec[兼容] 
功能描述：设置板卡指定的电机控制轴的加速度 
 
VC 
INT32 
MT_Set_Axis_Dec(WORD AObj,INT32 
Value) 
VB 
Function 
MT_Set_Axis_Dec(ByVal AObj As Integer, ByVal 
Value As Long) As Long 
Delphi 
function MT_Set_Axis_Dec(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int MT_Set_Axis_Dec(UInt16 AObj, 
Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置板卡指定轴的减速度值，单位为Hz/S2 
>=0 
 
 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
序号越界后本次操作虽然成，但是数据也是被丢弃无效 
 
 
3.10.8 
MT_Get_Axis_V_Now 
功能描述：读取指定控制轴当前的速度 
 
VC 
INT32 
MT_Get_Axis_V_Now(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_V_Now (ByVal AObj As Integer, ByRef Value As Long) As Long 
Delphi 
function 
MT_Get_Axis_V_Now(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_V_Now 
(UInt16 AObj, ref Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的当前速度 
Hz/S 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.10.9 
MT_Get_Axis_Software_P 
功能描述：读取指定控制轴当前的软件位置 
 
VC 
INT32 
MT_Get_Axis_Software_P_Now(WORD AObj,INT32* pValue) 
VB 
Function 
MT_Get_Axis_Software_P_Now (ByVal AObj As Integer, ByRef Value As 
Long) As Long 
Delphi 
function 
MT_Get_Axis_Software_P 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Software_P 
(UInt16 AObj, ref Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的当前软件位置 
单位为步数 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
 
3.10.10 MT_Set_Axis_Software_P 
功能描述：设置指定控制轴当前的软件位置 
 
VC 
INT32 
MT_Set_Axis_Software_P_Now(WORD AObj,INT32 
Value) 
VB 
Function 
MT_Set_Axis_Software_P_Now 
(ByVal AObj As Integer, ByVal 
Value As Long) 
As Long 
Delphi 
function 
MT_Set_Axis_Software_P 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Software_P 
(UInt16 AObj, Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的当前软件位置 
单位为步数 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.10.11 MT_Get_Axis_Status[兼容] 
功能描述：读取指定控制轴当前的状态信息 
 
VC 
INT32 
MT_Get_Axis_Status(WORD AObj, 
 
BYTE* pRun, 
 
BYTE* pDir, 
 
BYTE* pNeg, 
 
BYTE* pPos, 
 
BYTE* pZero, 
 
BYTE* pMode); 
VB 
Function MT_Get_Axis_Status Lib "MT_API.dll" _ 
 
(ByVal AObj As Integer, ByRef Run As Byte, ByRef Dir As Byte, _ 
 
ByRef Neg As Byte, ByRef Pos As Byte, ByRef Zero As Byte, ByRef Mode As Byte) 
As Long 
Delphi 
function MT_Get_Axis_Status(AObj:Word; 
 
pRun:PByte; 
 
pDir:PByte; 
 
pNeg:PByte; 
 
pPos:PByte; 
 
pZero:PByte; 
 
pMode:PByte):Integer; 
C# 
public static extern int MT_Get_Axis_Status(WORD AObj, 
 
ref BYTE Run,ref BYTE Dir,ref BYTE Neg,ref BYTE Pos,ref BYTE Zero,ref 
BYTE Mode); 
 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Run 
指定轴的当前运动状态，1为运动，0为不运动 
Dir 
指定轴当前的方向，1为正方向，0为负方向 
Neg 
指定轴当前的负限位,1为负限位有效，0为负限位无效 
Pos 
指定轴当前的正限位,1为正限位有效，0为正限位无效 
Zero 
指定轴当前的零位,1为零位有效，0为零位无效 
Mode 
工作模式，0=零位模式，1=速度模式，2=位置模式 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.10.12 MT_Get_Axis_Status2 
功能描述：读取指定控制轴当前的状态信息 
 
VC 
INT32 
MT_Get_Axis_Status2(WORD AObj, 
 
INT32* pRun, 
 
INT32* pDir, 
 
INT32* pNeg, 
 
INT32* pPos, 
 
INT32* pZero, 
INT32* pMode); 
VB 
Function MT_Get_Axis_Status2 
Lib "MT_API.dll" _ 
 
(ByVal AObj As Integer, ByRef Run As 
Integer, ByRef Dir As 
Integer, _ 
 
ByRef Neg As 
Integer, ByRef Pos As 
Integer, ByRef Zero As 
Integer, ByRef Mode As 
Integer) As Long 
Delphi 
function MT_Get_Axis_Status2(AObj:Word; 
 
pRun:PInteger; 
 
pDir: 
PInteger; 
 
pNeg: 
PInteger; 
 
pPos: 
PInteger; 
 
pZero: 
PInteger; 
 
pMode: 
PInteger):Integer; 
C# 
public static extern int MT_Get_Axis_Status2(WORD AObj, 
 
ref 
Int32 
Run,ref 
Int32 
Dir,ref 
Int32 
Neg,ref 
Int32 
Pos,ref 
Int32 
Zero,ref 
Int32 
Mode); 
 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Run 
指定轴的当前运动状态，1为运动，0为不运动 
Dir 
指定轴当前的方向，1为正方向，0为负方向 
Neg 
指定轴当前的负限位,1为负限位有效，0为负限位无效 
Pos 
指定轴当前的正限位,1为正限位有效，0为正限位无效 
Zero 
指定轴当前的零位,1为零位有效，0为零位无效 
Mode 
工作模式，0=零位模式，1=速度模式，2=位置模式 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.10.13 MT_Get_Axis_Status_Run 
功能描述：读取指定控制轴当前的运行状态 
 
VC 
INT32 
MT_Get_Axis_Status_Run(WORD AObj, 
INT32* pRun); 
VB 
Function MT_Get_Axis_Status_Run 
Lib "MT_API.dll" _ 
 
(ByVal AObj As Integer, ByRef Run As Integer) As Long 
Delphi 
function MT_Get_Axis_Status_Run(AObj:Word; 
 
pRun:PInteger):Integer; 
C# 
public static extern int MT_Get_Axis_Status_Run(WORD AObj, 
 
ref 
Int32 
Run); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Run 
指定轴的当前运动状态，1为运动，0为不运动 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.10.14 MT_Get_Axis_Status_Dir 
功能描述：读取指定控制轴当前的运行方向 
 
VC 
INT32 
MT_Get_Axis_Status_Dir(WORD AObj, 
INT32* pDir); 
VB 
Function MT_Get_Axis_Status_Dir 
Lib "MT_API.dll" _ 
 
(ByVal AObj As Integer, ByRef 
Dir 
As Integer) As Long 
Delphi 
function MT_Get_Axis_Status_Dir(AObj:Word; 
 
pDir:PInteger):Integer; 
C# 
public static extern int MT_Get_Axis_Status_Dir(WORD AObj, 
 
ref 
Int32 
Dir); 
输入参数 
AObj 
电机控制轴序号 
 
Dir 
指定轴当前的方向，1为正方向，0为负方向 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.10.15 MT_Get_Axis_Status_Neg 
功能描述：读取指定控制轴当前的负限位状态 
 
VC 
INT32 
MT_Get_Axis_Status_Neg(WORD AObj, 
INT32* pNeg); 
VB 
Function 
MT_Get_Axis_Status_Neg 
Lib "MT_API.dll" _ 
 
(ByVal AObj As Integer, ByRef 
Neg 
As Integer) As Long 
Delphi 
function MT_Get_Axis_Status_Neg(AObj:Word; 
 
pNeg:PInteger):Integer; 
C# 
public static extern int MT_Get_Axis_Status_Neg(WORD AObj, 
 
ref 
Int32 
Neg); 
输入参数 
AObj 
电机控制轴序号 
 
Neg 
指定轴当前的负限位,1为负限位有效，0为负限位无效 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.10.16 MT_Get_Axis_Status_Pos 
功能描述：读取指定控制轴当前的正限位状态 
 
VC 
INT32 
MT_Get_Axis_Status_Pos(WORD AObj, 
INT32* pPos); 
VB 
Function MT_Get_Axis_Status_Pos 
Lib "MT_API.dll" _ 
 
(ByVal AObj As Integer, ByRef 
Pos 
As Integer) As Long 
Delphi 
function MT_Get_Axis_Status_Pos(AObj:Word; 
 
pPos:PInteger):Integer; 
C# 
public static extern int MT_Get_Axis_Status_Pos(WORD AObj, 
 
ref 
Int32 
Pos); 
输入参数 
AObj 
电机控制轴序号 
 
Pos 
指定轴当前的正限位,1为正限位有效，0为正限位无效 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.10.17 MT_Get_Axis_Status_Zero 
功能描述：读取指定控制轴当前的零位状态 
 
VC 
INT32 
MT_Get_Axis_Status_Zero(WORD AObj, 
INT32* pZero); 
VB 
Function MT_Get_Axis_Status_Zero 
Lib "MT_API.dll" _ 
 
(ByVal AObj As Integer, ByRef 
Zero 
As Integer) As Long 
Delphi 
function MT_Get_Axis_Status_Zero(AObj:Word; 
 
pZero:PInteger):Integer; 
C# 
public static extern int MT_Get_Axis_Status_Zero(WORD AObj, 
 
ref 
Int32 
Zero); 
输入参数 
AObj 
电机控制轴序号 
 
Zero 
指定轴当前的零位,1为零位有效，0为零位无效 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.10.18 MT_Get_Axis_Status_Mode 
功能描述：读取指定控制轴当前的工作模式 
 
VC 
INT32 
MT_Get_Axis_Status_Mode(WORD AObj, 
INT32* pMode); 
VB 
Function MT_Get_Axis_Status_Mode 
Lib "MT_API.dll" _ 
 
(ByVal AObj As Integer, ByRef 
Mode 
As Integer) As Long 
Delphi 
function MT_Get_Axis_Status_Mode(AObj:Word; 
 
pMode:PInteger):Integer; 
C# 
public static extern int MT_Get_Axis_Status_Mode(WORD AObj, 
 
ref 
Int32 
Mode); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Mode 
工作模式，0=零位模式，1=速度模式，2=位置模式 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
 
3.10.19 MT_Get_Axis_Encoder_Pos 
功能描述：读取指定控制轴当前的编码器/光栅尺位置(电子4倍频，为实际物理脉冲*4) 
 
VC 
INT32 
MT_Get_Axis_Encoder_Pos(WORD AObj,INT32* pValue) 
VB 
Function 
MT_Get_Axis_Encoder_Pos(ByVal AObj As Integer, ByRef Value As Long) As 
Long 
Delphi 
function 
MT_Get_Axis_Encoder_Pos 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Encoder_Pos 
(UInt16 AObj, ref Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的当前软件位置 
单位为脉冲数 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
闭环电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
闭环电机轴序号的函数需要注意不要越界 
 
 
3.10.20 MT_Set_Axis_Encoder_Pos 
功能描述：设置指定控制轴当前的编码器/光栅尺位置(电子4倍频，为实际物理脉冲*4) 
 
VC 
INT32 
MT_Set_Axis_Encoder_Pos_Now(WORD AObj,INT32 
Value) 
VB 
Function 
MT_Set_Axis_Encoder_Pos_Now 
(ByVal AObj As Integer, ByVal 
Value As 
Long) As Long 
Delphi 
function 
MT_Set_Axis_Encoder_Pos 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Encoder_Pos 
(UInt16 AObj, Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的当前软件位置 
单位为脉冲数 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
闭环电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
闭环电机轴序号的函数需要注意不要越界 
 
3.10.21 MT_Set_Axis_Halt 
功能描述：立即停止指定轴的运动，没有减速过程，对所有运动模式有效 
 
VC 
INT32 
MT_Set_Axis_Halt 
(WORD AObj) 
VB 
Function 
MT_Set_Axis_Halt 
(ByVal AObj As Integer) As Long 
Delphi 
function 
MT_Set_Axis_Halt 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Halt 
(UInt16 AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
紧急停止惯性较大的运动体时会对驱动器造成一定的冲击，紧急情况下使用，每个模式的停止是减速过程，冲击较小 
 
3.10.22 MT_Set_Axis_Halt_All 
功能描述：立即停止所有轴的运动，没有减速过程，对所有运动模式有效 
 
VC 
INT32 
MT_Set_Axis_Halt_All 
(void) 
VB 
Function 
MT_Set_Axis_Halt_All 
() As Long 
Delphi 
function 
MT_Set_Axis_Halt_All 
():Integer; 
C# 
public static extern int 
MT_Set_Axis_Halt_All 
(); 
输入参数 
无 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
紧急停止惯性较大的运动体时会对驱动器造成一定的冲击，紧急情况下使用，每个模式的停止是减速过程，冲击较小 
 
 
