3.16 圆弧插补 
 
3.16.1 
MT_Set_Axis_Circle_Axis 
功能描述：设置指定插补模块中参与插补的运动轴。不同的板卡有不同数量的插补模块，多个模块可以同时进行多个圆弧插补，每个插补模块可以随意指定任意2轴参与插补，同时进行插补的模块不可以有相同的插补轴。例如，模块0用轴2，轴3插补，模块1用轴1轴2插补，这两个模块独立动作时没有问题，不可以同时动作。 
 
VC 
INT32 
MT_Set_Axis_Circle_Axis 
(WORD AObj,INT32 
Axis_ID0,INT32 Axis_ID1) 
VB 
Function 
MT_Set_Axis_Circle_Axis 
(ByVal AObj As Integer, ByVal 
Axis_ID0 
As 
Long, 
ByVal 
Axis_ID1 
As 
Long) As Long 
Delphi 
function 
MT_Set_Axis_Circle_Axis 
(AObj:Word;Axis_ID0,Axis_ID1:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Circle_Axis 
(UInt16 AObj, 
Int32 
Axis_ID0, 
Int32 
Axis_ID1); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Axis_ID0 
本插补模块的第一个插补轴序号 
 
Axis_ID1 
本插补模块的第二个插补轴序号 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
插补轴的序号从0开始，N的轴，序号为0,1,2,3…N-1 
3.16.2 MT_Set_Axis_Circle_Acc 
功能描述：设置指定插补模块的加速度。 
 
VC 
INT32 
MT_Set_Axis_Circle_Acc 
(WORD AObj,INT32 Value) 
VB 
Function 
MT_Set_Axis_Circle_Acc 
(ByVal AObj As Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function 
MT_Set_Axis_Circle_Acc 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Circle_Acc 
(UInt16 AObj, 
Int32 
Value); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Value 
本插补模块的加速度 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.16.3 MT_Set_Axis_Circle_Dec 
功能描述：设置指定插补模块的加速度。 
 
VC 
INT32 
MT_Set_Axis_Circle_Dec 
(WORD AObj,INT32 Value) 
VB 
Function 
MT_Set_Axis_Circle_Dec 
(ByVal AObj As Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function 
MT_Set_Axis_Circle_Dec 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Circle_Dec 
(UInt16 AObj, 
Int32 
Value); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Value 
本插补模块的减速度 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.16.4 MT_Set_Axis_Circle_V 
功能描述：设置指定插补模块的速度。 
 
VC 
INT32 
MT_Set_Axis_Circle_V 
(WORD AObj,INT32 Value) 
VB 
Function 
MT_Set_Axis_Circle_V 
(ByVal AObj As Integer, ByVal 
Value As Long) As Long 
Delphi 
function 
MT_Set_Axis_Circle_V 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Circle_V 
(UInt16 AObj, 
Int32 
Value); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Value 
本插补模块的速度 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
3.16.5 MT_Set_Axis_Circle_Stop 
功能描述：减速停止插补轴 
 
VC 
INT32 
MT_Set_Axis_Circle_Stop 
(WORD AObj) 
VB 
Function 
MT_Set_Axis_Circle_Stop 
(ByVal AObj As Integer) As Long 
Delphi 
function 
MT_Set_Axis_Circle_Stop 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Circle_Stop 
(UInt16 AObj); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.16.6 MT_Set_Axis_Circle_Halt 
功能描述：立即停止插补轴 
 
VC 
INT32 
MT_Set_Axis_Circle_Halt 
(WORD AObj) 
VB 
Function 
MT_Set_Axis_Circle_Halt 
(ByVal AObj As Integer) As Long 
Delphi 
function 
MT_Set_Axis_Circle_Halt 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Circle_Halt 
(UInt16 AObj); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.16.7 MT_Set_Axis_Circle_R_CW_Run_Rel 
功能描述：以相对当前的位置的参数顺时针圆弧插补，立即启动。 
 
VC 
INT32 
MT_Set_Axis_Circle_R_CW_Run_Rel 
(WORD AObj,INT32 AR,INT32 
Axis_Target0,INT32 Axis_Target1) 
VB 
Function 
MT_Set_Axis_Circle_R_CW_Run_Rel 
(ByVal AObj As Integer,ByVal AR As 
Long, 
ByVal 
Axis_Target0 
As 
Long, ByVal 
Axis_Target1 
As 
Long) As Long 
Delphi 
Function 
MT_Set_Axis_Circle_R_CW_Run_Rel 
(AObj:Word;AR,Axis_Target0,Axis_Target1:Integer):Integer 
C# 
public static extern int 
MT_Set_Axis_Circle_R_CW_Run_Rel 
(UInt16 AObj,Int32 AR, 
Int32 
Axis_Target0, 
Int32 
Axis_Target1); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
AR 
圆弧插补的半径，正数为劣弧，负数为优弧，半径为圆弧的半径，无相对绝对之分 
 
Axis_Target0 
本插补模块的第一个插补轴相对移动的距离 
 
Axis_Target1 
本插补模块的第二个插补轴相对移动的距离 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.16.8 MT_Set_Axis_Circle_R_CW_Run_Abs 
功能描述：以绝对的位置的参数顺时针圆弧插补，立即启动。 
 
VC 
INT32 
MT_Set_Axis_Circle_R_CW_Run_Abs 
(WORD AObj,INT32 AR,INT32 
Axis_Target0,INT32 Axis_Target1) 
VB 
Function 
MT_Set_Axis_Circle_R_CW_Run_Abs 
(ByVal AObj As Integer,ByVal AR As 
Long, ByVal 
Axis_Target0 
As 
Long, ByVal 
Axis_Target1 
As 
Long) As Long 
Delphi 
Function 
MT_Set_Axis_Circle_R_CW_Run_Abs 
(AObj:Word;AR,Axis_Target0,Axis_Target1:Integer):Integer 
C# 
public static extern int 
MT_Set_Axis_Circle_R_CW_Run_Abs 
(UInt16 AObj,Int32 AR, 
Int32 
Axis_Target0, 
Int32 
Axis_Target1); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
AR 
圆弧插补的半径，正数为劣弧，负数为优弧，半径为圆弧的半径，无相对绝对之分 
 
Axis_Target0 
本插补模块的第一个插补轴需要移动到的绝对位置 
 
Axis_Target1 
本插补模块的第二个插补轴需要移动到的绝对位置 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.16.9 MT_Set_Axis_Circle_R_CCW_Run_Rel 
功能描述：以相对当前的位置的参数逆时针圆弧插补，立即启动。 
 
VC 
INT32 
MT_Set_Axis_Circle_R_CCW_Run_Rel 
(WORD AObj,INT32 AR,INT32 
Axis_Target0,INT32 Axis_Target1) 
VB 
Function 
MT_Set_Axis_Circle_R_CCW_Run_Rel 
(ByVal AObj As Integer,ByVal AR As 
Long, ByVal 
Axis_Target0 
As 
Long, ByVal 
Axis_Target1 
As 
Long) As Long 
Delphi 
Function 
MT_Set_Axis_Circle_R_CCW_Run_Rel 
(AObj:Word;AR,Axis_Target0,Axis_Target1:Integer):Integer 
C# 
public static extern int 
MT_Set_Axis_Circle_R_CCW_Run_Rel 
(UInt16 AObj,Int32 AR, 
Int32 
Axis_Target0, 
Int32 
Axis_Target1); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
AR 
圆弧插补的半径，正数为劣弧，负数为优弧，半径为圆弧的半径，无相对绝对之分 
 
Axis_Target0 
本插补模块的第一个插补轴相对移动的距离 
 
Axis_Target1 
本插补模块的第二个插补轴相对移动的距离 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.16.10 MT_Set_Axis_Circle_R_CCW_Run_Abs 
功能描述：以绝对的位置的参数逆时针圆弧插补，立即启动。 
 
VC 
INT32 
MT_Set_Axis_Circle_R_CCW_Run_Abs 
(WORD AObj,INT32 AR,INT32 
Axis_Target0,INT32 Axis_Target1) 
VB 
Function 
MT_Set_Axis_Circle_R_CCW_Run_Abs 
(ByVal AObj As Integer,ByVal AR As 
Long, ByVal 
Axis_Target0 
As 
Long, ByVal 
Axis_Target1 
As 
Long) As Long 
Delphi 
Function 
MT_Set_Axis_Circle_R_CCW_Run_Abs 
(AObj:Word;AR,Axis_Target0,Axis_Target1:Integer):Integer 
C# 
public static extern int 
MT_Set_Axis_Circle_R_CCW_Run_Abs 
(UInt16 AObj,Int32 AR, 
Int32 
Axis_Target0, 
Int32 
Axis_Target1); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
AR 
圆弧插补的半径，正数为劣弧，负数为优弧，半径为圆弧的半径，无相对绝对之分 
 
Axis_Target0 
本插补模块的第一个插补轴需要移动到的绝对位置 
 
Axis_Target1 
本插补模块的第二个插补轴需要移动到的绝对位置 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
3.16.11 MT_Get_Axis_Circle_Num 
功能描述：读取板卡上圆弧插补模块的数量 
 
VC 
INT32 
MT_Get_Axis_Circle_Num 
(INT32* pValue) 
VB 
Function 
MT_Get_Axis_Circle_Num 
(ByRef Value As Long) As Long 
Delphi 
function 
MT_Get_Axis_Circle_Num 
(pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Circle_Num 
(ref Int32 Value); 
输入参数 
无 
 
输出参数 
Value 
圆弧插补模块的数量 
函数返回 
0 
函数执行成功，读取到通道数有效 
非0 
函数执行失败，读取到通道数无效 
备注 
模块的序号从0开始，N的通道，序号为0,1,2,3…N-1 
 
3.16.12 MT_Get_Axis_Circle_Status 
功能描述：读取指定圆弧插补模块的当前插补状态 
 
VC 
INT32 
MT_Get_Axis_Circle_Status 
(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_Circle_Status 
(ByVal AObj As Integer, ByRef Value As 
Long) As 
Long 
Delphi 
function 
MT_Get_Axis_Circle_Status 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Circle_Status 
(UInt16 AObj, ref 
Int32 
Value); 
输入参数 
AObj 
插补模块的序号，不是插补轴的序号 
输出参数 
Value 
指定模块的插补状态，0为插补完后，1为正在插补中 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
 
3.16.13 MT_Get_Axis_Circle_Acc 
功能描述：读取指定圆弧插补模块的当前加速度 
 
VC 
INT32 
MT_Get_Axis_Circle_Acc 
(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_Circle_Acc 
(ByVal AObj As Integer, ByRef Value As 
Long) As 
Long 
Delphi 
function 
MT_Get_Axis_Circle_Acc 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Circle_Acc 
(UInt16 AObj, ref 
Int32 
Value); 
输入参数 
AObj 
插补模块的序号，不是插补轴的序号 
输出参数 
Value 
指定模块的加速度 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
3.16.14 MT_Get_Axis_Circle_Dec 
功能描述：读取指定圆弧插补模块的当前减速度 
 
VC 
INT32 
MT_Get_Axis_Circle_Dec 
(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_Circle_Dec 
(ByVal AObj As Integer, ByRef Value As 
Long) As 
Long 
Delphi 
function 
MT_Get_Axis_Circle_Dec 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Circle_Dec 
(UInt16 AObj, ref 
Int32 
Value); 
输入参数 
AObj 
插补模块的序号，不是插补轴的序号 
输出参数 
Value 
指定模块的减速度 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
3.16.15 MT_Get_Axis_Circle_V 
功能描述：读取指定圆弧插补模块的当前速度 
 
VC 
INT32 
MT_Get_Axis_Circle_V 
(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_Circle_V 
(ByVal AObj As Integer, ByRef Value As 
Long) As Long 
Delphi 
function 
MT_Get_Axis_Circle_V 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Circle_V 
(UInt16 AObj, ref 
Int32 
Value); 
输入参数 
AObj 
插补模块的序号，不是插补轴的序号 
输出参数 
Value 
指定模块的速度 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.16.16 MT_Get_Axis_Circle_Axis 
功能描述：读取指定圆弧插补模块的当前的插补轴 
 
VC 
INT32 
MT_Get_Axis_Circle_Axis 
(WORD AObj,INT32* pID0,INT32* pID1); 
VB 
Function 
MT_Get_Axis_Circle_Axis 
(ByVal AObj As Integer, ByRef 
pID0 
As 
Long, ByRef 
pID1 
As 
Long) As Long 
Delphi 
function 
MT_Get_Axis_Circle_Axis 
(AObj:Word; 
pID0,pID1:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Circle_Axis 
(UInt16 AObj, ref 
Int32 
ID0, ref 
Int32 
ID1); 
输入参数 
AObj 
插补模块的序号，不是插补轴的序号 
输出参数 
pID0 
指定模块的第一个插补轴 
 
pID1 
指定模块的第二个插补轴 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
插补轴的序号从0开始，N的运动轴，序号为0,1,2,3…N-1 
 
3.16.17 MT_Set_Axis_Circle_V_Start 
功能描述：设置指定插补模块的起始速度。 
 
VC 
INT32 
MT_Set_Axis_Circle_V_Start 
(WORD AObj,INT32 Value) 
VB 
Function 
MT_Set_Axis_Circle_V 
_Start(ByVal AObj As Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function 
MT_Set_Axis_Circle_V 
_Start(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Circle_V_Start 
(UInt16 AObj, 
Int32 
Value); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Value 
本插补模块的速度 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
