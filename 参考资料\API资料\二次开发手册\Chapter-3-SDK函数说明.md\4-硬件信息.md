3.4 硬件信息 
 
3.4.1 
MT_Get_Product_Resource 
功能描述：检测硬件中拥有的资源类型，包括控制的电机轴数、输入、输出等信息，再配合不同类型资源的读数量的函数，来完成最终具体资源的判断。不同的板卡，所带的资源不一样，
如果已经了解所用板卡的资源，无需调用本函数。考虑到软件兼容性的时候，可以调用本函数。 
也可通过官方工具来查看 
 
VC 
INT32 
MT_Get_Product_Resource 
(INT32* pValue) 
VB 
Function 
MT_Get_Product_Resource 
(ByRef 
Value As Long) As Long 
Delphi 
function 
MT_Get_Product_Resource 
(pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Product_Resource 
(ref Int32 Value); 
输入参数 
无 
 
输出参数 
Value 
输出硬件中资源类型，由以下资源类型进行或操作后的结果 
目前定义的资源 
#define RESOURCE_MOTOR 
0x00000001 //电机 
#define RESOURCE_TTL_IN 0x00000002 //TTL输入 
#define RESOURCE_TTL_OUT 0x00000004 //TTL输出 
#define RESOURCE_OPTIC_IN 0x00000008 //光隔输入 
#define RESOURCE_OPTIC_OUT 0x00000010 //光隔输出 
#define RESOURCE_AD 0x00000020 
//AD输入 
#define RESOURCE_OC 0x00000040 
//OC输出 
#define RESOURCE_LINE 0x00000080 
//直线插补 
#define RESOURCE_CIRCLE 0x00000100 
//圆弧插补 
#define RESOURCE_PLC 0x00000200 
//PLC功能支持 
#define RESOURCE_STREAM 0x00000400 
//流模式支持 
#define RESOURCE_ENCODER 
0x00000800 
//编码器光栅尺接口 
函数返回 
0 
函数执行成功，读取到的板卡资源有效 
非0 
函数执行失败，读取到的板卡资源无效 
备注 
 
 
3.4.2 MT_Get_Product_ID 
功能描述：读取板卡的型号。已了解无需调用本函数。也可通过官方工具来查看 
 
VC 
INT32 
MT_Get_Product_ID 
(char* sID) 
VB 
Function 
MT_Get_Product_ID 
(ByRef 
sID 
As 
String) As Long 
Delphi 
function 
MT_Get_Product_ID(sID:PAnsiChar):Integer; 
C# 
public static extern int 
MT_Get_Product_ID 
(ref 
String 
sID); 
输入参数 
无 
 
输出参数 
sID 
16长度的字符缓冲区 
函数返回 
0 
函数执行成功，读取到的型号有效 
非0 
函数执行失败，读取到的型号无效 
备注 
缓冲区需用户先申请好内存 
 
3.4.3 MT_Get_Product_SN 
功能描述：读取板卡的序列号。也可通过官方的工具来查看。序列号本司用来确认产品的相关
信息的判据。用户也可以用本唯一序列号完成用户产品的确认等工作。 
 
VC 
INT32 
MT_Get_Product_SN 
(char* sSN) 
VB 
Function 
MT_Get_Product_SN 
(ByRef 
sSN 
As 
String) As Long 
Delphi 
function 
MT_Get_Product_SN(sID:PAnsiChar):Integer; 
C# 
public static extern int 
MT_Get_Product_SN 
(ref 
String 
sSN); 
输入参数 
无 
 
输出参数 
sSN 
12长度的字符缓冲区 
函数返回 
0 
函数执行成功，读取到的序列号有效 
非0 
函数执行失败，读取到的序列号无效 
备注 
缓冲区需用户先申请好内存 
 
3.4.4 MT_Get_Product_ 
Version[兼容] 
功能描述：读取板卡固件的版本号。也可通过官方的工具来查看。版本号本司用来确认产品的相关信息的判据。 
 
VC 
INT32 
MT_Get_Product_ 
Version 
(INT32* pMajor,INT32* pMinor) 
VB 
Function 
MT_Get_Product_ 
Version 
(ByRef Major As Long, ByRef Minor As Long) As 
Long 
Delphi 
function 
MT_Get_Product_ 
Version(pMajor:PInteger;pMinor:PInteger):Integer; 
C# 
public 
static extern int MT_Get_Product_ 
Version 
(ref Int32 Major,ref Int32 
Minor); 
输入参数 
无 
 
输出参数 
Major 
大版本号 
Minor 
小版本号 
函数返回 
0 
函数执行成功，读取到的版本号有效 
非0 
函数执行失败，读取到的版本号无效 
备注 
 
3.4.5 MT_Get_Product_ 
Version2 
功能描述：读取板卡固件的版本号。也可通过官方的工具来查看。版本号本司用来确认产品的相关信息的判据。 
 
VC 
INT32 
MT_Get_Product_Version2(INT32* pMajor,INT32* pMinor,INT32* 
pRelease,INT32* pBuild) 
VB 
Function 
MT_Get_Product_ 
Version2 
(ByRef pMajor As Long,ByRef pMinor As 
Long,ByRef pRelease As Long,ByRef pBuild As Long) As Long 
Delphi 
function 
MT_Get_Product_ 
Version2(pMajor:PInteger;pMinor:PInteger; 
 
pRelease:PInteger;pBuild:PInteger):Integer; 
C# 
public 
static extern int MT_Get_Product_ 
Version 
2(ref Int32 pMajor,ref Int32 
pMinor,ref Int32 pRelease,ref Int32 pBuild); 
输入参数 
无 
 
输出参数 
pMajor 
大版本号 
pMinor 
小版本号 
pRelease 
发布次数 
pBuild 
编译次数 
函数返回 
0 
函数执行成功，读取到的版本号有效 
非0 
函数执行失败，读取到的版本号无效 
备注 
 
 
 
