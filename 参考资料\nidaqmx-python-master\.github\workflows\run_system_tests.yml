name: Run system tests

on:
  workflow_call:
  workflow_dispatch:
jobs:
  run_system_tests:
    name: Run system tests
    runs-on:
      - self-hosted
      - windows
      - x64
      - rdss-nidaqmxbot-${{ matrix.configuration }}
    strategy:
      matrix:
        configuration: ["win-10-py32", "win-10-py64"]
      # Fail-fast skews the pass/fail ratio and seems to make pytest produce
      # incomplete JUnit XML results.
      fail-fast: false
    timeout-minutes: 90
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Import DAQmx config
        run: C:\nidaqmxconfig\targets\win64U\x64\msvc-14.0\release\nidaqmxconfig.exe --eraseconfig --import tests\max_config\nidaqmxMaxConfig.ini
      - name: Cache virtualenvs
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: |
            .venv
            .tox
          key: run-system-tests-${{ runner.os }}-${{ matrix.configuration }}-${{ hashFiles('poetry.lock') }}
      - name: Install dependencies
        run: poetry install
      - name: Run system tests
        run: poetry run tox
      - name: Rename test results
        # TODO: Add Git Bash to the path on self-hosted Windows runners
        run: Get-ChildItem test_results/*.xml | Rename-Item -NewName { $_.Name -replace '.xml','-${{ matrix.configuration }}.xml' }
        if: always() && runner.os == 'Windows'
      - name: Rename test results
        run: |
          for result in test_results/*.xml; do
            mv $result ${result%.xml}-${{ matrix.configuration }}.xml
          done
        if: always() && runner.os != 'Windows'
      - name: Upload test results
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: test_results_system_${{ matrix.configuration }}
          path: test_results/*.xml
        if: always()
