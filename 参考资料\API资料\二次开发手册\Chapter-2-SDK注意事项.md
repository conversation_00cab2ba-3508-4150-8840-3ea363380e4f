2 SDK的注意事项 

2.1 线程安全性 
一般提供的版本是线程不安全的，请保证在调用本SDK的函数是在一个线程里面，或者通过定时器来实现定时调度功能。 
如果有特殊需要，V3.10(含)后的版本可以提供线程安全的，Dll内部实现了多线程保护功能，请用户注意使用，避免出现死锁，多单线程应用没有区别；单线程模式下，如果非常频繁的调用（例如非常多个定时器发生同时调用），有非常小的概率发生通信冲突（通信必须串行），V3.10(后)的版本可以提供了通信口冲突检测功能，遇到冲突会返回错误，请注意判断返回值，推荐在单线程时使用单个定时器状态机的方式进行调用。 
 
一般情况下提供的dll是线程不安全的，但是性能和可靠性好，一般情况下推荐使用。多线程和保护功能的由于dll是受控于应用程序，不能主动做动作，用户使用时需要注意，有些情况下多线程环境比较复杂，使用不当会有死锁，调试也不是很方便。 
 
一般推荐用户的使用模式：单定时器状态机调度模式（部分例程有这个功能）；一个独立的线程操作MT相关的函数，其它线程不操作。 
2.2 SET类型函数执行有效性 
0只是表示本函数执行成功（通信成功），设置的参数有可能本丢弃或者采用默认值，具体的参数限制请注意不同板卡的不同参数指标，不清楚的情况下可以联系我司或者通过对应的GET函数来获取 
2.3 GET类型函数的返回值 
只有在函数返回值为0的情况下，返回的参数才是有效值，否则为随机值 
2.4 函数的适应性 
某些函数只在特定的模式下有效，请注意查看相关的函数说明，可能函数执行返回为0，但是未设置成功。 
2.5 光栅尺和编码器的计数 
对于光栅尺和编码器，控制器计数时是进行电子4倍频的，也就是说，计数值是脉冲的4倍，在计算和设置时需要注意。本SDK提供的辅助计算函数已经考虑4倍的关系，用户自己计算时需要考虑4倍频。 

