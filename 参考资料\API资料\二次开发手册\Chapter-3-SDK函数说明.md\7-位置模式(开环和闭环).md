3.7 位置模式(开环和闭环) 

3.7.1 MT_Set_Axis_Mode_Position[兼容] 
功能描述：设置指定的轴为开环位置模式 
 
VC 
INT32 
MT_Set_Axis_Mode_Position 
(WORD AObj) 
VB 
Function MT_Set_Axis_Mode_Position 
(ByVal AObj As Integer) As Long 
Delphi 
function MT_Set_Axis_Mode_Position 
(AObj:Word):Integer; 
C# 
public static extern int MT_Set_Axis_Mode_Position 
(UInt16 AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
 
3.7.2 MT_Set_Axis_Mode_Position_Open 
功能描述：设置指定的轴为开环位置模式 
 
VC 
INT32 
MT_Set_Axis_Mode_Position_Open 
(WORD AObj) 
VB 
Function MT_Set_Axis_Mode_Position 
_Open(ByVal AObj As Integer) As Long 
Delphi 
function MT_Set_Axis_Mode_Position 
_Open(AObj:Word):Integer; 
C# 
public static extern int MT_Set_Axis_Mode_Position_Open 
(UInt16 AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
 
3.7.3 MT_Set_Axis_Mode_Position_Close 
功能描述：设置指定的轴为闭环位置模式 
 
VC 
INT32 
MT_Set_Axis_Mode_Position_Close 
(WORD AObj) 
VB 
Function 
MT_Set_Axis_Mode_Position 
_Close(ByVal AObj As Integer) As Long 
Delphi 
function MT_Set_Axis_Mode_Position 
_Close(AObj:Word):Integer; 
C# 
public static extern int MT_Set_Axis_Mode_Position_Close 
(UInt16 AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
 
3.7.4 MT_Get_Axis_Position_V_Max 
功能描述：读取位置模式下的最大速度 
 
VC 
INT32 
MT_Get_Axis_Position_V_Max 
(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_Position_V_Max 
(ByVal AObj As Integer, ByRef Value As Long) 
As Long 
Delphi 
function 
MT_Get_Axis_Position_V_Max 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Position_V_Max 
(UInt16 AObj, ref Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的位置模式下最大速度 
Hz/S 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.7.5 MT_Set_Axis_Position_V_Max 
功能描述：设置位置模式下的最大速度 
 
VC 
INT32 
MT_Set_Axis_Position_V_Max 
(WORD AObj,INT32 
Value); 
VB 
Function 
MT_Set_Axis_Position_V_Max 
(ByVal AObj As Integer, ByVal 
Value As Long) 
As Long 
Delphi 
function 
MT_Set_Axis_Position_V_Max 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Position_V_Max 
(UInt16 AObj, 
Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的位置模式下最大速度 
Hz/S 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.7.6 MT_Get_Axis_Position_P_Target 
功能描述：读取位置模式下的绝对目标位置,开环模式下为目标开环脉冲数，闭环模式下为目标闭环电子脉冲数（由于电路上进行了4倍频，单位为实际物理光栅或者编码器脉冲数*4，目标为4000的话，对应的物理上为1000脉冲） 
 
VC 
INT32 
MT_Get_Axis_Position_P_Target 
(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_Position_P_Target 
(ByVal AObj As Integer, ByRef Value As 
Long) As Long 
Delphi 
function 
MT_Get_Axis_Position_P_Target 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Position_P_Target 
(UInt16 AObj, ref Int32 
Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的位置模式下绝对目标位置 
单位为 
电机步数 
可正可负 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
 
3.7.7 
MT_Set_Axis_Position_P_Target_Abs 
功能描述：设置位置模式下的绝对目标位置, 
开环模式下为目标开环脉冲数，闭环模式下为目标闭环电子脉冲数（由于电路上进行了4倍频，单位为实际物理光栅或者编码器脉冲数*4，目标为4000的话，对应的物理上为1000脉冲） 
 
VC 
INT32 
MT_Set_Axis_Position_P_Target_Abs 
(WORD AObj,INT32 
Value); 
VB 
Function 
MT_Set_Axis_Position_P_Target_Abs 
(ByVal AObj As Integer, ByVal 
Value As 
Long) As Long 
Delphi 
function 
MT_Set_Axis_Position_P_Target_Abs 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Position_P_Target_Abs 
(UInt16 AObj,Int32 
Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的位置模式下绝对目标位置 
单位为 
电机步数 
可正可负 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.7.8 MT_Set_Axis_Position_P_Target_Rel 
功能描述：设置位置模式下的相对当前位置下运动量, 
开环模式下为目标开环脉冲数，闭环模
式下为目标闭环电子脉冲数（由于电路上进行了4倍频，单位为实际物理光栅或者编码器脉冲数*4，目标为4000的话，对应的物理上为1000脉冲） 
 
VC 
INT32 
MT_Set_Axis_Position_P_Target_Rel 
(WORD AObj,INT32 
Value); 
VB 
Function 
MT_Set_Axis_Position_P_Target_Rel 
(ByVal AObj As Integer, ByVal 
Value As 
Long) As Long 
Delphi 
function 
MT_Set_Axis_Position_P_Target_Rel 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Position_P_Target_Rel 
(UInt16 AObj,Int32 
Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的位置模式下相对当前位置的目标移动量 
单位为 
电机步数 
可正可负 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
 
3.7.9 MT_Set_Axis_Position_Stop 
功能描述：停止指定轴的位置运动模式 
 
VC 
INT32 
MT_Set_Axis_Position_Stop 
(WORD AObj) 
VB 
Function 
MT_Set_Axis_Position_Stop 
(ByVal AObj As Integer) As Long 
Delphi 
function 
MT_Set_Axis_Position_Stop 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Position_Stop 
(UInt16 AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
 
3.7.10 MT_Set_Axis_Position_V_Start 
功能描述：设置指定轴的位置模式启动初始速度，默认为50Hz/s,一般无需修改。 
 
VC 
INT32 
MT_Set_Axis_Position_V_Start 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Axis_Position_V 
_Start(ByVal AObj As Integer, ByVal 
Value As Long) 
As Long 
Delphi 
function 
MT_Set_Axis_Position_V_Start 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Position_V_Start 
(UInt16 AObj,Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的启动速度 
Hz/S 
只能正 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
较慢的速度有利于查找的精度 
 
3.7.11 MT_Set_Axis_Position_Acc 
功能描述：设置指定轴的位置模式的加速度值，默认为500Hz/s2。 
 
VC 
INT32 
MT_Set_Axis_Position_Acc 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Axis_Position_Acc(ByVal AObj As Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function 
MT_Set_Axis_Position_Acc 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Position_Acc 
(UInt16 AObj,Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的加速度 
Hz/S2 
只能正 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.7.12 MT_Set_Axis_Position_Dec 
功能描述：设置指定轴的位置模式的减速度值，默认为500Hz/s2。 
 
VC 
INT32 
MT_Set_Axis_Position_Dec 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Axis_Position_Dec(ByVal AObj As Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function 
MT_Set_Axis_Position_Dec 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Position_Dec 
(UInt16 AObj,Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的减速度 
Hz/S2 
只能正 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.7.13 MT_Get_Axis_Position_Acc 
功能描述：读取指定控制轴位置模式的加速度 
 
VC 
INT32 
MT_Get_Axis_Position_Acc(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_ 
Position_Acc 
(ByVal AObj As Integer, ByRef Value As Long) As 
Long 
Delphi 
function MT_Get_Axis_ 
Position_Acc 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_ 
Position_Acc 
(UInt16 AObj, ref Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的当前加速度 
Hz/S2 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.7.14 MT_Get_Axis_Position_Dec 
功能描述：读取指定控制轴位置模式的减速度 
 
VC 
INT32 
MT_Get_Axis_Position_Dec(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_ 
Position_Dec 
(ByVal AObj As Integer, ByRef Value As Long) As 
Long 
Delphi 
function MT_Get_Axis_ 
Position_Dec 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_ 
Position_Decc 
(UInt16 AObj, ref Int32 Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
指定轴的当前减速度 
Hz/S2 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.7.15 MT_Set_Axis_Position_Close_Dec_Factor 
功能描述：设置指定轴的闭环位置模式的减速系数，默认为1。根据负载等各种情况进行调整。可以用后面章节的估算函数进行估算。 
 
VC 
INT32 
MT_Set_Axis_Position_Close_Dec_Factor 
(WORD AObj,float 
pValue); 
VB 
Function 
MT_Set_Axis_Position_Close_Dec_Factor(ByVal 
AObj As Integer, ByVal 
Value As Single) As Long 
Delphi 
function 
MT_Set_Axis_Position_Dec 
(AObj:Word;Value:Single):Integer; 
C# 
public static extern int 
MT_Set_Axis_Position_Dec 
(UInt16 AObj,float 
Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的闭环减速系数 
只能正 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.7.16 MT_Set_Encoder_Over_Enable 
功能描述：设置指定轴在闭环运动时是否进行过冲补偿，默认不开启。在有些情况下，闭环控制可能会有几个脉冲误差的定位情况。过冲补偿可以在定位误差几个脉冲的情况下进行补偿到指定的位置。如果闭环能正确到达，无需使用本功能 
 
VC 
INT32 
MT_Set_Encoder_Over_Enable 
(WORD 
AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Encoder_Over_Enable(ByVal AObj As Integer, ByVal 
Value As Long) 
As Long 
Delphi 
function 
MT_Set_Encoder_Over_Enable 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Encoder_Over_Enable 
(UInt16 AObj,Int32 Value); 
输入参数 
AObj 
闭环控制电机控制轴序号 
输出参数 
Value 
1：开启补偿功能 
0：不开启补偿功能 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.7.17 MT_Set_Encoder_Over_Max 
功能描述：在开启闭环补偿功能后，本参数决定补偿的最大步数，默认为100 
 
VC 
INT32 
MT_Set_Encoder_Over_Max 
(WORD 
AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Encoder_Over_Max(ByVal AObj As Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function 
MT_Set_Encoder_Over_Max 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Encoder_Over_Max 
(UInt16 AObj,Int32 
Value); 
输入参数 
AObj 
闭环控制电机控制轴序号 
输出参数 
Value 
补偿步数，必须为正 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
3.7.18 MT_Set_Encoder_Over_Stable 
功能描述：在开启闭环补偿功能后，本参数决定补偿的稳定判据，默认为50 
 
VC 
INT32 
MT_Set_Encoder_Over_Stable 
(WORD AObj,INT32 
pValue); 
VB 
Function 
MT_Set_Encoder_Over_Stable(ByVal AObj As Integer, ByVal 
Value As Long) 
As Long 
Delphi 
function 
MT_Set_Encoder_Over_Stable 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Encoder_Over_Stable 
(UInt16 AObj,Int32 
Value); 
输入参数 
AObj 
闭环控制电机控制轴序号 
输出参数 
Value 
补偿步数，必须为正 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
 
