<%
errors_metadata, warnings_metadata = data['DAQmxErrors'], data['DAQmxWarnings']
%>\
# Do not edit this file; it was automatically generated.

from enum import IntEnum

__all__ = ['DAQmxErrors', 'DAQmxWarnings']


class DAQmxErrors(IntEnum):
    %for value in errors_metadata['values']:
    ${value['name']} = ${value['value']}
    %endfor
    UNKNOWN = -1


class DAQmxWarnings(IntEnum):
    UNKNOWN = -1
    %for value in warnings_metadata['values']:
    ${value['name']} = ${value['value']}
    %endfor