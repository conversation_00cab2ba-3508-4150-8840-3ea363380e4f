---
type: "always_apply"
---

<Analyzing_Rules>
以下Rules适用于波形数据分析相关功能（"sweeper400\sweeper400\analyze"目录下）的开发。
- 程序中的物理量默认使用国际单位制(SI)标准单位。
- 程序中的时间戳默认使用np.datetime64对象表示（纳秒精度）。例如，你可以使用np.datetime64("now", "ns")。
- "my_dtypes.py"模块用于定义本项目中特有的自定义数据类型/容器。
- 我们的主要操作对象是nidaqmx包输出和输入的“时域波形数据”。它的格式是一个numpy array（dtype=numpy.float64），其中每个数据都对应一个sample的具体采样值。单通道情形下，它是一维数组（shape=(samples,)）；多通道情形下，它是二维数组（shape=(channels, samples)）。
</Analyzing_Rules>