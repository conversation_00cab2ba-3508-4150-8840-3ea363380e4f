# Do not edit this file; it was automatically generated.

import numpy
import deprecation

from nidaqmx.scale import _ScaleAlternateConstructor
from nidaqmx.task.channels._channel import Channel
from nidaqmx.utils import unflatten_channel_string
from nidaqmx.constants import (
    ACExcitWireMode, ADCTimingMode, AccelChargeSensitivityUnits,
    AccelSensitivityUnits, AccelUnits, AngleUnits, AutoZeroType,
    BridgeConfiguration, BridgeElectricalUnits, BridgePhysicalUnits,
    BridgeShuntCalSource, BridgeUnits, CJCSource, ChargeUnits, Coupling,
    CurrentShuntResistorLocation, CurrentUnits, DataJustification,
    DataTransferActiveTransferMode, DigitalWidthUnits,
    EddyCurrentProxProbeSensitivityUnits, ExcitationDCorAC,
    ExcitationIdleOutputBehavior, ExcitationSource,
    ExcitationVoltageOrCurrent, FilterResponse, FilterType,
    ForceIEPESensorSensitivityUnits, ForceUnits, FrequencyUnits, Impedance1,
    InputDataTransferCondition, LVDTSensitivityUnits, LengthUnits,
    PowerIdleOutputBehavior, PowerOutputState, PressureUnits, RTDType,
    RVDTSensitivityUnits, RawDataCompressionType, ResistanceConfiguration,
    ResistanceUnits, ResolutionType, ScaleType, Sense, SensorPowerCfg,
    SensorPowerType, ShuntCalSelect, SoundPressureUnits, SourceSelection,
    StrainGageBridgeType, StrainGageRosetteMeasurementType,
    StrainGageRosetteType, StrainUnits, TemperatureUnits,
    TerminalConfiguration, ThermocoupleType, TorqueUnits, UsageTypeAI,
    VelocityIEPESensorSensitivityUnits, VelocityUnits, VoltageUnits)


class AIChannel(Channel):
    """
    Represents one or more analog input virtual channels and their properties.
    """
    __slots__ = ()

    def __repr__(self):
        return f'AIChannel(name={self._name})'

    @property
    def ai_ac_excit_freq(self):
        """
        float: Specifies the AC excitation frequency in Hertz.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x101)
        return val

    @ai_ac_excit_freq.setter
    def ai_ac_excit_freq(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x101, val)

    @ai_ac_excit_freq.deleter
    def ai_ac_excit_freq(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x101)

    @property
    def ai_ac_excit_sync_enable(self):
        """
        bool: Specifies whether to synchronize the AC excitation source
            of the channel to that of another channel. Synchronize the
            excitation sources of multiple channels to use multichannel
            sensors. Set this property to False for the master channel
            and to True for the slave channels.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x102)
        return val

    @ai_ac_excit_sync_enable.setter
    def ai_ac_excit_sync_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x102, val)

    @ai_ac_excit_sync_enable.deleter
    def ai_ac_excit_sync_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x102)

    @property
    def ai_ac_excit_wire_mode(self):
        """
        :class:`nidaqmx.constants.ACExcitWireMode`: Specifies the number
            of leads on the LVDT or RVDT. Some sensors require you to
            tie leads together to create a four- or five- wire sensor.
            Refer to the sensor documentation for more information.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x18cd)
        return ACExcitWireMode(val)

    @ai_ac_excit_wire_mode.setter
    def ai_ac_excit_wire_mode(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x18cd, val)

    @ai_ac_excit_wire_mode.deleter
    def ai_ac_excit_wire_mode(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18cd)

    @property
    def ai_accel_4_wire_dc_voltage_sensitivity(self):
        """
        float: Specifies the sensitivity of the 4 wire DC voltage
            acceleration sensor connected to the channel. This value is
            the units you specify with
            AI.Accel.4WireDCVoltage.SensitivityUnits. Refer to the
            sensor documentation to determine this value.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x3115)
        return val

    @ai_accel_4_wire_dc_voltage_sensitivity.setter
    def ai_accel_4_wire_dc_voltage_sensitivity(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x3115, val)

    @ai_accel_4_wire_dc_voltage_sensitivity.deleter
    def ai_accel_4_wire_dc_voltage_sensitivity(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3115)

    @property
    def ai_accel_4_wire_dc_voltage_sensitivity_units(self):
        """
        :class:`nidaqmx.constants.AccelSensitivityUnits`: Specifies the
            units of AI.Accel.4WireDCVoltage.Sensitivity.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x3116)
        return AccelSensitivityUnits(val)

    @ai_accel_4_wire_dc_voltage_sensitivity_units.setter
    def ai_accel_4_wire_dc_voltage_sensitivity_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x3116, val)

    @ai_accel_4_wire_dc_voltage_sensitivity_units.deleter
    def ai_accel_4_wire_dc_voltage_sensitivity_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3116)

    @property
    def ai_accel_charge_sensitivity(self):
        """
        float: Specifies the sensitivity of the charge acceleration
            sensor connected to the channel. This value is the units you
            specify with AI.Accel.Charge.SensitivityUnits. Refer to the
            sensor documentation to determine this value.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x3113)
        return val

    @ai_accel_charge_sensitivity.setter
    def ai_accel_charge_sensitivity(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x3113, val)

    @ai_accel_charge_sensitivity.deleter
    def ai_accel_charge_sensitivity(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3113)

    @property
    def ai_accel_charge_sensitivity_units(self):
        """
        :class:`nidaqmx.constants.AccelChargeSensitivityUnits`:
            Specifies the units of AI.Accel.Charge.Sensitivity.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x3114)
        return AccelChargeSensitivityUnits(val)

    @ai_accel_charge_sensitivity_units.setter
    def ai_accel_charge_sensitivity_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x3114, val)

    @ai_accel_charge_sensitivity_units.deleter
    def ai_accel_charge_sensitivity_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3114)

    @property
    def ai_accel_db_ref(self):
        """
        float: Specifies the decibel reference level in the units of the
            channel. When you read samples as a waveform, the decibel
            reference level is included in the waveform attributes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x29b2)
        return val

    @ai_accel_db_ref.setter
    def ai_accel_db_ref(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x29b2, val)

    @ai_accel_db_ref.deleter
    def ai_accel_db_ref(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x29b2)

    @property
    def ai_accel_sensitivity(self):
        """
        float: Specifies the sensitivity of the accelerometer. This
            value is in the units you specify with
            **ai_accel_sensitivity_units**. Refer to the sensor
            documentation to determine this value.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x692)
        return val

    @ai_accel_sensitivity.setter
    def ai_accel_sensitivity(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x692, val)

    @ai_accel_sensitivity.deleter
    def ai_accel_sensitivity(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x692)

    @property
    def ai_accel_sensitivity_units(self):
        """
        :class:`nidaqmx.constants.AccelSensitivityUnits`: Specifies the
            units of **ai_accel_sensitivity**.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x219c)
        return AccelSensitivityUnits(val)

    @ai_accel_sensitivity_units.setter
    def ai_accel_sensitivity_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x219c, val)

    @ai_accel_sensitivity_units.deleter
    def ai_accel_sensitivity_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x219c)

    @property
    def ai_accel_units(self):
        """
        :class:`nidaqmx.constants.AccelUnits`: Specifies the units to
            use to return acceleration measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x673)
        return AccelUnits(val)

    @ai_accel_units.setter
    def ai_accel_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x673, val)

    @ai_accel_units.deleter
    def ai_accel_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x673)

    @property
    def ai_adc_custom_timing_mode(self):
        """
        int: Specifies the timing mode of the ADC when
            **ai_adc_timing_mode** is **ADCTimingMode.CUSTOM**.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x2f6b)
        return val

    @ai_adc_custom_timing_mode.setter
    def ai_adc_custom_timing_mode(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x2f6b, val)

    @ai_adc_custom_timing_mode.deleter
    def ai_adc_custom_timing_mode(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f6b)

    @property
    def ai_adc_timing_mode(self):
        """
        :class:`nidaqmx.constants.ADCTimingMode`: Specifies the ADC
            timing mode, controlling the tradeoff between speed and
            effective resolution. Some ADC timing modes provide
            increased powerline noise rejection. On devices that have an
            AI Convert clock, this setting affects both the maximum and
            default values for **ai_conv_rate**. You must use the same
            ADC timing mode for all channels on a device, but you can
            use different ADC timing modes for different devices in the
            same task.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x29f9)
        return ADCTimingMode(val)

    @ai_adc_timing_mode.setter
    def ai_adc_timing_mode(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x29f9, val)

    @ai_adc_timing_mode.deleter
    def ai_adc_timing_mode(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x29f9)

    @property
    def ai_atten(self):
        """
        float: Specifies the amount of attenuation to use.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1801)
        return val

    @ai_atten.setter
    def ai_atten(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1801, val)

    @ai_atten.deleter
    def ai_atten(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1801)

    @property
    def ai_auto_zero_mode(self):
        """
        :class:`nidaqmx.constants.AutoZeroType`: Specifies how often to
            measure ground. NI-DAQmx subtracts the measured ground
            voltage from every sample.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x1760)
        return AutoZeroType(val)

    @ai_auto_zero_mode.setter
    def ai_auto_zero_mode(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x1760, val)

    @ai_auto_zero_mode.deleter
    def ai_auto_zero_mode(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1760)

    @property
    def ai_averaging_win_size(self):
        """
        int: Specifies the number of samples to average while acquiring
            data. Increasing the number of samples to average reduces
            noise in your measurement.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x2fee)
        return val

    @ai_averaging_win_size.setter
    def ai_averaging_win_size(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x2fee, val)

    @ai_averaging_win_size.deleter
    def ai_averaging_win_size(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2fee)

    @property
    def ai_bridge_balance_coarse_pot(self):
        """
        int: Specifies by how much to compensate for offset in the
            signal. This value can be between 0 and 127.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x17f1)
        return val

    @ai_bridge_balance_coarse_pot.setter
    def ai_bridge_balance_coarse_pot(self, val):
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x17f1, val)

    @ai_bridge_balance_coarse_pot.deleter
    def ai_bridge_balance_coarse_pot(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17f1)

    @property
    def ai_bridge_balance_fine_pot(self):
        """
        int: Specifies by how much to compensate for offset in the
            signal. This value can be between 0 and 4095.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x18f4)
        return val

    @ai_bridge_balance_fine_pot.setter
    def ai_bridge_balance_fine_pot(self, val):
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x18f4, val)

    @ai_bridge_balance_fine_pot.deleter
    def ai_bridge_balance_fine_pot(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18f4)

    @property
    def ai_bridge_cfg(self):
        """
        :class:`nidaqmx.constants.BridgeConfiguration`: Specifies the
            type of Wheatstone bridge connected to the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x87)
        return BridgeConfiguration(val)

    @ai_bridge_cfg.setter
    def ai_bridge_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x87, val)

    @ai_bridge_cfg.deleter
    def ai_bridge_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x87)

    @property
    def ai_bridge_electrical_units(self):
        """
        :class:`nidaqmx.constants.BridgeElectricalUnits`: Specifies from
            which electrical unit to scale data. Select  the same unit
            that the sensor data sheet or calibration certificate uses
            for electrical values.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2f87)
        return BridgeElectricalUnits(val)

    @ai_bridge_electrical_units.setter
    def ai_bridge_electrical_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2f87, val)

    @ai_bridge_electrical_units.deleter
    def ai_bridge_electrical_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f87)

    @property
    def ai_bridge_initial_ratio(self):
        """
        float: Specifies in volts per volt the ratio of output voltage
            from the bridge to excitation voltage supplied to the bridge
            while not under load. NI-DAQmx subtracts this value from any
            measurements before applying scaling equations. If you set
            **ai_bridge_initial_voltage**, NI-DAQmx coerces this
            property  to **ai_bridge_initial_voltage** divided by
            **ai_excit_actual_val**. If you set this property, NI-DAQmx
            coerces **ai_bridge_initial_voltage** to the value of this
            property times **ai_excit_actual_val**. If you set both this
            property and **ai_bridge_initial_voltage**, and their values
            conflict, NI-DAQmx returns an error.  To avoid this error,
            reset one property to its default value before setting the
            other.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f86)
        return val

    @ai_bridge_initial_ratio.setter
    def ai_bridge_initial_ratio(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f86, val)

    @ai_bridge_initial_ratio.deleter
    def ai_bridge_initial_ratio(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f86)

    @property
    def ai_bridge_initial_voltage(self):
        """
        float: Specifies in volts the output voltage of the bridge while
            not under load. NI-DAQmx subtracts this value from any
            measurements before applying scaling equations.  If you set
            **ai_bridge_initial_ratio**, NI-DAQmx coerces this property
            to **ai_bridge_initial_ratio** times
            **ai_excit_actual_val**. This property is set by DAQmx
            Perform Bridge Offset Nulling Calibration. If you set this
            property, NI-DAQmx coerces **ai_bridge_initial_ratio** to
            the value of this property divided by
            **ai_excit_actual_val**. If you set both this property and
            **ai_bridge_initial_ratio**, and their values conflict, NI-
            DAQmx returns an error. To avoid this error, reset one
            property to its default value before setting the other.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x17ed)
        return val

    @ai_bridge_initial_voltage.setter
    def ai_bridge_initial_voltage(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x17ed, val)

    @ai_bridge_initial_voltage.deleter
    def ai_bridge_initial_voltage(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17ed)

    @property
    def ai_bridge_nom_resistance(self):
        """
        float: Specifies in ohms the resistance of the bridge while not
            under load.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x17ec)
        return val

    @ai_bridge_nom_resistance.setter
    def ai_bridge_nom_resistance(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x17ec, val)

    @ai_bridge_nom_resistance.deleter
    def ai_bridge_nom_resistance(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17ec)

    @property
    def ai_bridge_physical_units(self):
        """
        :class:`nidaqmx.constants.BridgePhysicalUnits`: Specifies to
            which physical unit to scale electrical data. Select the
            same unit that the sensor data sheet or calibration
            certificate uses for physical values.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2f88)
        return BridgePhysicalUnits(val)

    @ai_bridge_physical_units.setter
    def ai_bridge_physical_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2f88, val)

    @ai_bridge_physical_units.deleter
    def ai_bridge_physical_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f88)

    @property
    def ai_bridge_poly_forward_coeff(self):
        """
        List[float]: Specifies an list of coefficients for the
            polynomial that converts electrical values to physical
            values. Each element of the list corresponds to a term of
            the equation. For example, if index three of the list is 9,
            the fourth term of the equation is 9x^3.
        """

        val = self._interpreter.get_chan_attribute_double_array(self._handle, self._name, 0x2f90)
        return val

    @ai_bridge_poly_forward_coeff.setter
    def ai_bridge_poly_forward_coeff(self, val):
        val = numpy.array(val, dtype=numpy.float64)
        self._interpreter.set_chan_attribute_double_array(self._handle, self._name, 0x2f90, val)

    @ai_bridge_poly_forward_coeff.deleter
    def ai_bridge_poly_forward_coeff(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f90)

    @property
    def ai_bridge_poly_reverse_coeff(self):
        """
        List[float]: Specifies an list of coefficients for the
            polynomial that converts physical values to electrical
            values. Each element of the list corresponds to a term of
            the equation. For example, if index three of the list is 9,
            the fourth term of the equation is 9x^3.
        """

        val = self._interpreter.get_chan_attribute_double_array(self._handle, self._name, 0x2f91)
        return val

    @ai_bridge_poly_reverse_coeff.setter
    def ai_bridge_poly_reverse_coeff(self, val):
        val = numpy.array(val, dtype=numpy.float64)
        self._interpreter.set_chan_attribute_double_array(self._handle, self._name, 0x2f91, val)

    @ai_bridge_poly_reverse_coeff.deleter
    def ai_bridge_poly_reverse_coeff(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f91)

    @property
    def ai_bridge_scale_type(self):
        """
        :class:`nidaqmx.constants.ScaleType`: Specifies the scaling type
            to use when scaling electrical values from the sensor to
            physical units.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2f89)
        return ScaleType(val)

    @ai_bridge_scale_type.setter
    def ai_bridge_scale_type(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2f89, val)

    @ai_bridge_scale_type.deleter
    def ai_bridge_scale_type(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f89)

    @property
    def ai_bridge_shunt_cal_enable(self):
        """
        bool: Specifies whether to enable a shunt calibration switch.
            Use **ai_bridge_shunt_cal_select** to select the switch(es)
            to enable.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x94)
        return val

    @ai_bridge_shunt_cal_enable.setter
    def ai_bridge_shunt_cal_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x94, val)

    @ai_bridge_shunt_cal_enable.deleter
    def ai_bridge_shunt_cal_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x94)

    @property
    def ai_bridge_shunt_cal_gain_adjust(self):
        """
        float: Specifies the result of a shunt calibration. This
            property is set by DAQmx Perform Shunt Calibration. NI-DAQmx
            multiplies data read from the channel by the value of this
            property. This value should be close to 1.0.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x193f)
        return val

    @ai_bridge_shunt_cal_gain_adjust.setter
    def ai_bridge_shunt_cal_gain_adjust(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x193f, val)

    @ai_bridge_shunt_cal_gain_adjust.deleter
    def ai_bridge_shunt_cal_gain_adjust(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x193f)

    @property
    def ai_bridge_shunt_cal_select(self):
        """
        :class:`nidaqmx.constants.ShuntCalSelect`: Specifies which shunt
            calibration switch(es) to enable.  Use
            **ai_bridge_shunt_cal_enable** to enable the switch(es) you
            specify with this property.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x21d5)
        return ShuntCalSelect(val)

    @ai_bridge_shunt_cal_select.setter
    def ai_bridge_shunt_cal_select(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x21d5, val)

    @ai_bridge_shunt_cal_select.deleter
    def ai_bridge_shunt_cal_select(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x21d5)

    @property
    def ai_bridge_shunt_cal_shunt_cal_a_actual_resistance(self):
        """
        float: Specifies in ohms the actual value of the internal shunt
            calibration A resistor.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f79)
        return val

    @ai_bridge_shunt_cal_shunt_cal_a_actual_resistance.setter
    def ai_bridge_shunt_cal_shunt_cal_a_actual_resistance(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f79, val)

    @ai_bridge_shunt_cal_shunt_cal_a_actual_resistance.deleter
    def ai_bridge_shunt_cal_shunt_cal_a_actual_resistance(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f79)

    @property
    def ai_bridge_shunt_cal_shunt_cal_a_resistance(self):
        """
        float: Specifies in ohms the desired value of the internal shunt
            calibration A resistor.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f78)
        return val

    @ai_bridge_shunt_cal_shunt_cal_a_resistance.setter
    def ai_bridge_shunt_cal_shunt_cal_a_resistance(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f78, val)

    @ai_bridge_shunt_cal_shunt_cal_a_resistance.deleter
    def ai_bridge_shunt_cal_shunt_cal_a_resistance(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f78)

    @property
    def ai_bridge_shunt_cal_shunt_cal_a_src(self):
        """
        :class:`nidaqmx.constants.BridgeShuntCalSource`: Specifies
            whether to use internal or external shunt when Shunt Cal A
            is selected.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30ca)
        return BridgeShuntCalSource(val)

    @ai_bridge_shunt_cal_shunt_cal_a_src.setter
    def ai_bridge_shunt_cal_shunt_cal_a_src(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30ca, val)

    @ai_bridge_shunt_cal_shunt_cal_a_src.deleter
    def ai_bridge_shunt_cal_shunt_cal_a_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30ca)

    @property
    def ai_bridge_shunt_cal_shunt_cal_b_actual_resistance(self):
        """
        float: Specifies in ohms the actual value of the internal shunt
            calibration B resistor.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f7b)
        return val

    @ai_bridge_shunt_cal_shunt_cal_b_actual_resistance.setter
    def ai_bridge_shunt_cal_shunt_cal_b_actual_resistance(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f7b, val)

    @ai_bridge_shunt_cal_shunt_cal_b_actual_resistance.deleter
    def ai_bridge_shunt_cal_shunt_cal_b_actual_resistance(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f7b)

    @property
    def ai_bridge_shunt_cal_shunt_cal_b_resistance(self):
        """
        float: Specifies in ohms the desired value of the internal shunt
            calibration B resistor.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f7a)
        return val

    @ai_bridge_shunt_cal_shunt_cal_b_resistance.setter
    def ai_bridge_shunt_cal_shunt_cal_b_resistance(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f7a, val)

    @ai_bridge_shunt_cal_shunt_cal_b_resistance.deleter
    def ai_bridge_shunt_cal_shunt_cal_b_resistance(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f7a)

    @property
    def ai_bridge_table_electrical_vals(self):
        """
        List[float]: Specifies the list of electrical values that map to
            the values in **ai_bridge_table_physical_vals**. Specify
            this value in the unit indicated by
            **ai_bridge_electrical_units**.
        """

        val = self._interpreter.get_chan_attribute_double_array(self._handle, self._name, 0x2f8e)
        return val

    @ai_bridge_table_electrical_vals.setter
    def ai_bridge_table_electrical_vals(self, val):
        val = numpy.array(val, dtype=numpy.float64)
        self._interpreter.set_chan_attribute_double_array(self._handle, self._name, 0x2f8e, val)

    @ai_bridge_table_electrical_vals.deleter
    def ai_bridge_table_electrical_vals(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f8e)

    @property
    def ai_bridge_table_physical_vals(self):
        """
        List[float]: Specifies the list of physical values that map to
            the values in **ai_bridge_table_electrical_vals**. Specify
            this value in the unit indicated by
            **ai_bridge_physical_units**.
        """

        val = self._interpreter.get_chan_attribute_double_array(self._handle, self._name, 0x2f8f)
        return val

    @ai_bridge_table_physical_vals.setter
    def ai_bridge_table_physical_vals(self, val):
        val = numpy.array(val, dtype=numpy.float64)
        self._interpreter.set_chan_attribute_double_array(self._handle, self._name, 0x2f8f, val)

    @ai_bridge_table_physical_vals.deleter
    def ai_bridge_table_physical_vals(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f8f)

    @property
    def ai_bridge_two_point_lin_first_electrical_val(self):
        """
        float: Specifies the first electrical value, corresponding to
            **ai_bridge_two_point_lin_first_physical_val**. Specify this
            value in the unit indicated by
            **ai_bridge_electrical_units**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f8a)
        return val

    @ai_bridge_two_point_lin_first_electrical_val.setter
    def ai_bridge_two_point_lin_first_electrical_val(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f8a, val)

    @ai_bridge_two_point_lin_first_electrical_val.deleter
    def ai_bridge_two_point_lin_first_electrical_val(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f8a)

    @property
    def ai_bridge_two_point_lin_first_physical_val(self):
        """
        float: Specifies the first physical value, corresponding to
            **ai_bridge_two_point_lin_first_electrical_val**. Specify
            this value in the unit indicated by
            **ai_bridge_physical_units**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f8b)
        return val

    @ai_bridge_two_point_lin_first_physical_val.setter
    def ai_bridge_two_point_lin_first_physical_val(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f8b, val)

    @ai_bridge_two_point_lin_first_physical_val.deleter
    def ai_bridge_two_point_lin_first_physical_val(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f8b)

    @property
    def ai_bridge_two_point_lin_second_electrical_val(self):
        """
        float: Specifies the second electrical value, corresponding to
            **ai_bridge_two_point_lin_second_physical_val**. Specify
            this value in the unit indicated by
            **ai_bridge_electrical_units**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f8c)
        return val

    @ai_bridge_two_point_lin_second_electrical_val.setter
    def ai_bridge_two_point_lin_second_electrical_val(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f8c, val)

    @ai_bridge_two_point_lin_second_electrical_val.deleter
    def ai_bridge_two_point_lin_second_electrical_val(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f8c)

    @property
    def ai_bridge_two_point_lin_second_physical_val(self):
        """
        float: Specifies the second physical value, corresponding to
            **ai_bridge_two_point_lin_second_electrical_val**. Specify
            this value in the unit indicated by
            **ai_bridge_physical_units**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f8d)
        return val

    @ai_bridge_two_point_lin_second_physical_val.setter
    def ai_bridge_two_point_lin_second_physical_val(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f8d, val)

    @ai_bridge_two_point_lin_second_physical_val.deleter
    def ai_bridge_two_point_lin_second_physical_val(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f8d)

    @property
    def ai_bridge_units(self):
        """
        :class:`nidaqmx.constants.BridgeUnits`: Specifies in which unit
            to return voltage ratios from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2f92)
        return BridgeUnits(val)

    @ai_bridge_units.setter
    def ai_bridge_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2f92, val)

    @ai_bridge_units.deleter
    def ai_bridge_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f92)

    @property
    def ai_charge_units(self):
        """
        :class:`nidaqmx.constants.ChargeUnits`: Specifies the units to
            use to return charge measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x3112)
        return ChargeUnits(val)

    @ai_charge_units.setter
    def ai_charge_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x3112, val)

    @ai_charge_units.deleter
    def ai_charge_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3112)

    @property
    def ai_chop_enable(self):
        """
        bool: Specifies whether the device will chop its inputs.
            Chopping removes offset voltages and other low frequency
            errors.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x3143)
        return val

    @ai_chop_enable.setter
    def ai_chop_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x3143, val)

    @ai_chop_enable.deleter
    def ai_chop_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3143)

    @property
    def ai_coupling(self):
        """
        :class:`nidaqmx.constants.Coupling`: Specifies the coupling for
            the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x64)
        return Coupling(val)

    @ai_coupling.setter
    def ai_coupling(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x64, val)

    @ai_coupling.deleter
    def ai_coupling(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x64)

    @property
    def ai_current_acrms_units(self):
        """
        :class:`nidaqmx.constants.CurrentUnits`: Specifies the units to
            use to return current RMS measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x17e3)
        return CurrentUnits(val)

    @ai_current_acrms_units.setter
    def ai_current_acrms_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x17e3, val)

    @ai_current_acrms_units.deleter
    def ai_current_acrms_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17e3)

    @property
    def ai_current_shunt_loc(self):
        """
        :class:`nidaqmx.constants.CurrentShuntResistorLocation`:
            Specifies the shunt resistor location for current
            measurements.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x17f2)
        return CurrentShuntResistorLocation(val)

    @ai_current_shunt_loc.setter
    def ai_current_shunt_loc(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x17f2, val)

    @ai_current_shunt_loc.deleter
    def ai_current_shunt_loc(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17f2)

    @property
    def ai_current_shunt_resistance(self):
        """
        float: Specifies in ohms the external shunt resistance for
            current measurements.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x17f3)
        return val

    @ai_current_shunt_resistance.setter
    def ai_current_shunt_resistance(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x17f3, val)

    @ai_current_shunt_resistance.deleter
    def ai_current_shunt_resistance(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17f3)

    @property
    def ai_current_units(self):
        """
        :class:`nidaqmx.constants.CurrentUnits`: Specifies the units to
            use to return current measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x701)
        return CurrentUnits(val)

    @ai_current_units.setter
    def ai_current_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x701, val)

    @ai_current_units.deleter
    def ai_current_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x701)

    @property
    def ai_custom_scale(self):
        """
        :class:`nidaqmx.system.scale.Scale`: Specifies the name of a
            custom scale for the channel.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x17e0)
        return _ScaleAlternateConstructor(val, self._interpreter)

    @ai_custom_scale.setter
    def ai_custom_scale(self, val):
        val = val.name
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x17e0, val)

    @ai_custom_scale.deleter
    def ai_custom_scale(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17e0)

    @property
    def ai_data_xfer_custom_threshold(self):
        """
        int: Specifies the number of samples that must be in the FIFO to
            transfer data from the device if **ai_data_xfer_req_cond**
            is
            **InputDataTransferCondition.ONBOARD_MEMORY_CUSTOM_THRESHOLD**.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x230c)
        return val

    @ai_data_xfer_custom_threshold.setter
    def ai_data_xfer_custom_threshold(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x230c, val)

    @ai_data_xfer_custom_threshold.deleter
    def ai_data_xfer_custom_threshold(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x230c)

    @property
    def ai_data_xfer_max_rate(self):
        """
        float: Specifies the rate in B/s to transfer data from the
            device. If this value is not set, then the device will
            transfer data at a rate based on the bus detected. Modify
            this value to affect performance under different
            combinations of operating system, configuration, and device.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x3117)
        return val

    @ai_data_xfer_max_rate.setter
    def ai_data_xfer_max_rate(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x3117, val)

    @ai_data_xfer_max_rate.deleter
    def ai_data_xfer_max_rate(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3117)

    @property
    def ai_data_xfer_mech(self):
        """
        :class:`nidaqmx.constants.DataTransferActiveTransferMode`:
            Specifies the data transfer mode for the device.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x1821)
        return DataTransferActiveTransferMode(val)

    @ai_data_xfer_mech.setter
    def ai_data_xfer_mech(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x1821, val)

    @ai_data_xfer_mech.deleter
    def ai_data_xfer_mech(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1821)

    @property
    def ai_data_xfer_req_cond(self):
        """
        :class:`nidaqmx.constants.InputDataTransferCondition`: Specifies
            under what condition to transfer data from the onboard
            memory of the device to the buffer.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x188b)
        return InputDataTransferCondition(val)

    @ai_data_xfer_req_cond.setter
    def ai_data_xfer_req_cond(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x188b, val)

    @ai_data_xfer_req_cond.deleter
    def ai_data_xfer_req_cond(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x188b)

    @property
    def ai_dc_offset(self):
        """
        float: Specifies the DC value to add to the input range of the
            device. Use **ai_rng_high** and **ai_rng_low** to specify
            the input range. This offset is in the native units of the
            device .
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2a89)
        return val

    @ai_dc_offset.setter
    def ai_dc_offset(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2a89, val)

    @ai_dc_offset.deleter
    def ai_dc_offset(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2a89)

    @property
    def ai_dev_scaling_coeff(self):
        """
        List[float]: Indicates the coefficients of a polynomial equation
            that NI-DAQmx uses to scale values from the native format of
            the device to volts. Each element of the list corresponds to
            a term of the equation. For example, if index two of the
            list is 4, the third term of the equation is 4x^2. Scaling
            coefficients do not account for any custom scales or sensors
            contained by the channel.
        """

        val = self._interpreter.get_chan_attribute_double_array(self._handle, self._name, 0x1930)
        return val

    @property
    def ai_dig_fltr_bandpass_center_freq(self):
        """
        float: Specifies the center frequency of the passband for the
            digital filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30c3)
        return val

    @ai_dig_fltr_bandpass_center_freq.setter
    def ai_dig_fltr_bandpass_center_freq(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30c3, val)

    @ai_dig_fltr_bandpass_center_freq.deleter
    def ai_dig_fltr_bandpass_center_freq(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30c3)

    @property
    def ai_dig_fltr_bandpass_width(self):
        """
        float: Specifies the width of the passband centered around the
            center frequency for the digital filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30c4)
        return val

    @ai_dig_fltr_bandpass_width.setter
    def ai_dig_fltr_bandpass_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30c4, val)

    @ai_dig_fltr_bandpass_width.deleter
    def ai_dig_fltr_bandpass_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30c4)

    @property
    def ai_dig_fltr_coeff(self):
        """
        List[float]: Specifies the digital filter coefficients.
        """

        val = self._interpreter.get_chan_attribute_double_array(self._handle, self._name, 0x30c7)
        return val

    @ai_dig_fltr_coeff.setter
    def ai_dig_fltr_coeff(self, val):
        val = numpy.array(val, dtype=numpy.float64)
        self._interpreter.set_chan_attribute_double_array(self._handle, self._name, 0x30c7, val)

    @ai_dig_fltr_coeff.deleter
    def ai_dig_fltr_coeff(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30c7)

    @property
    def ai_dig_fltr_enable(self):
        """
        bool: Specifies whether the digital filter is enabled or
            disabled.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x30bd)
        return val

    @ai_dig_fltr_enable.setter
    def ai_dig_fltr_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x30bd, val)

    @ai_dig_fltr_enable.deleter
    def ai_dig_fltr_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30bd)

    @property
    def ai_dig_fltr_highpass_cutoff_freq(self):
        """
        float: Specifies the highpass cutoff frequency of the digital
            filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30c2)
        return val

    @ai_dig_fltr_highpass_cutoff_freq.setter
    def ai_dig_fltr_highpass_cutoff_freq(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30c2, val)

    @ai_dig_fltr_highpass_cutoff_freq.deleter
    def ai_dig_fltr_highpass_cutoff_freq(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30c2)

    @property
    def ai_dig_fltr_lowpass_cutoff_freq(self):
        """
        float: Specifies the lowpass cutoff frequency of the digital
            filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30c1)
        return val

    @ai_dig_fltr_lowpass_cutoff_freq.setter
    def ai_dig_fltr_lowpass_cutoff_freq(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30c1, val)

    @ai_dig_fltr_lowpass_cutoff_freq.deleter
    def ai_dig_fltr_lowpass_cutoff_freq(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30c1)

    @property
    def ai_dig_fltr_notch_center_freq(self):
        """
        float: Specifies the center frequency of the stopband for the
            digital filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30c5)
        return val

    @ai_dig_fltr_notch_center_freq.setter
    def ai_dig_fltr_notch_center_freq(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30c5, val)

    @ai_dig_fltr_notch_center_freq.deleter
    def ai_dig_fltr_notch_center_freq(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30c5)

    @property
    def ai_dig_fltr_notch_width(self):
        """
        float: Specifies the width of the stopband centered around the
            center frequency for the digital filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x30c6)
        return val

    @ai_dig_fltr_notch_width.setter
    def ai_dig_fltr_notch_width(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x30c6, val)

    @ai_dig_fltr_notch_width.deleter
    def ai_dig_fltr_notch_width(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30c6)

    @property
    def ai_dig_fltr_order(self):
        """
        int: Specifies the order of the digital filter.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x30c0)
        return val

    @ai_dig_fltr_order.setter
    def ai_dig_fltr_order(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x30c0, val)

    @ai_dig_fltr_order.deleter
    def ai_dig_fltr_order(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30c0)

    @property
    def ai_dig_fltr_response(self):
        """
        :class:`nidaqmx.constants.FilterResponse`: Specifies the digital
            filter response.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30bf)
        return FilterResponse(val)

    @ai_dig_fltr_response.setter
    def ai_dig_fltr_response(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30bf, val)

    @ai_dig_fltr_response.deleter
    def ai_dig_fltr_response(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30bf)

    @property
    def ai_dig_fltr_type(self):
        """
        :class:`nidaqmx.constants.FilterType`: Specifies the digital
            filter type.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30be)
        return FilterType(val)

    @ai_dig_fltr_type.setter
    def ai_dig_fltr_type(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30be, val)

    @ai_dig_fltr_type.deleter
    def ai_dig_fltr_type(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30be)

    @property
    def ai_dither_enable(self):
        """
        bool: Specifies whether to enable dithering.  Dithering adds
            Gaussian noise to the input signal. You can use dithering to
            achieve higher resolution measurements by over sampling the
            input signal and averaging the results.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x68)
        return val

    @ai_dither_enable.setter
    def ai_dither_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x68, val)

    @ai_dither_enable.deleter
    def ai_dither_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x68)

    @property
    def ai_eddy_current_prox_sensitivity(self):
        """
        float: Specifies the sensitivity of the eddy current proximity
            probe . This value is in the units you specify with
            **ai_eddy_current_prox_sensitivity_units**. Refer to the
            sensor documentation to determine this value.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2abe)
        return val

    @ai_eddy_current_prox_sensitivity.setter
    def ai_eddy_current_prox_sensitivity(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2abe, val)

    @ai_eddy_current_prox_sensitivity.deleter
    def ai_eddy_current_prox_sensitivity(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2abe)

    @property
    def ai_eddy_current_prox_sensitivity_units(self):
        """
        :class:`nidaqmx.constants.EddyCurrentProxProbeSensitivityUnits`:
            Specifies the units of **ai_eddy_current_prox_sensitivity**.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2abf)
        return EddyCurrentProxProbeSensitivityUnits(val)

    @ai_eddy_current_prox_sensitivity_units.setter
    def ai_eddy_current_prox_sensitivity_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2abf, val)

    @ai_eddy_current_prox_sensitivity_units.deleter
    def ai_eddy_current_prox_sensitivity_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2abf)

    @property
    def ai_eddy_current_prox_units(self):
        """
        :class:`nidaqmx.constants.LengthUnits`: Specifies the units to
            use to return proximity measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2ac0)
        return LengthUnits(val)

    @ai_eddy_current_prox_units.setter
    def ai_eddy_current_prox_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2ac0, val)

    @ai_eddy_current_prox_units.deleter
    def ai_eddy_current_prox_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2ac0)

    @property
    def ai_enhanced_alias_rejection_enable(self):
        """
        bool: Specifies whether to enable enhanced alias rejection.
            Leave this property set to the default value for most
            applications.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2294)
        return val

    @ai_enhanced_alias_rejection_enable.setter
    def ai_enhanced_alias_rejection_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2294, val)

    @ai_enhanced_alias_rejection_enable.deleter
    def ai_enhanced_alias_rejection_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2294)

    @property
    def ai_excit_actual_val(self):
        """
        float: Specifies the actual amount of excitation supplied by an
            internal excitation source.  If you read an internal
            excitation source more precisely with an external device,
            set this property to the value you read.  NI-DAQmx ignores
            this value for external excitation. When performing shunt
            calibration, some devices set this property automatically.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1883)
        return val

    @ai_excit_actual_val.setter
    def ai_excit_actual_val(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1883, val)

    @ai_excit_actual_val.deleter
    def ai_excit_actual_val(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1883)

    @property
    def ai_excit_d_cor_ac(self):
        """
        :class:`nidaqmx.constants.ExcitationDCorAC`: Specifies if the
            excitation supply is DC or AC.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x17fb)
        return ExcitationDCorAC(val)

    @ai_excit_d_cor_ac.setter
    def ai_excit_d_cor_ac(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x17fb, val)

    @ai_excit_d_cor_ac.deleter
    def ai_excit_d_cor_ac(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17fb)

    @property
    def ai_excit_idle_output_behavior(self):
        """
        :class:`nidaqmx.constants.ExcitationIdleOutputBehavior`:
            Specifies whether this channel will disable excitation after
            the task is uncommitted. Setting this to Zero Volts or Amps
            disables excitation after task uncommit. Setting this
            attribute to Maintain Existing Value leaves the excitation
            on after task uncommit.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30b8)
        return ExcitationIdleOutputBehavior(val)

    @ai_excit_idle_output_behavior.setter
    def ai_excit_idle_output_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30b8, val)

    @ai_excit_idle_output_behavior.deleter
    def ai_excit_idle_output_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30b8)

    @property
    def ai_excit_sense(self):
        """
        :class:`nidaqmx.constants.Sense`: Specifies whether to use local
            or remote sense to sense excitation.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x30fd)
        return Sense(val)

    @ai_excit_sense.setter
    def ai_excit_sense(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x30fd, val)

    @ai_excit_sense.deleter
    def ai_excit_sense(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30fd)

    @property
    def ai_excit_src(self):
        """
        :class:`nidaqmx.constants.ExcitationSource`: Specifies the
            source of excitation.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x17f4)
        return ExcitationSource(val)

    @ai_excit_src.setter
    def ai_excit_src(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x17f4, val)

    @ai_excit_src.deleter
    def ai_excit_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17f4)

    @property
    def ai_excit_use_for_scaling(self):
        """
        bool: Specifies if NI-DAQmx divides the measurement by the
            excitation. You should typically set this property to True
            for ratiometric transducers. If you set this property to
            True, set **ai_max** and **ai_min** to reflect the scaling.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x17fc)
        return val

    @ai_excit_use_for_scaling.setter
    def ai_excit_use_for_scaling(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x17fc, val)

    @ai_excit_use_for_scaling.deleter
    def ai_excit_use_for_scaling(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17fc)

    @property
    def ai_excit_use_multiplexed(self):
        """
        bool: Specifies if the SCXI-1122 multiplexes the excitation to
            the upper half of the channels as it advances through the
            scan list.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2180)
        return val

    @ai_excit_use_multiplexed.setter
    def ai_excit_use_multiplexed(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2180, val)

    @ai_excit_use_multiplexed.deleter
    def ai_excit_use_multiplexed(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2180)

    @property
    def ai_excit_val(self):
        """
        float: Specifies the amount of excitation that the sensor
            requires. If **ai_excit_voltage_or_current** is
            **ExcitationVoltageOrCurrent.USE_VOLTAGE**, this value is in
            volts. If **ai_excit_voltage_or_current** is
            **ExcitationVoltageOrCurrent.USE_CURRENT**, this value is in
            amperes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x17f5)
        return val

    @ai_excit_val.setter
    def ai_excit_val(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x17f5, val)

    @ai_excit_val.deleter
    def ai_excit_val(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17f5)

    @property
    def ai_excit_voltage_or_current(self):
        """
        :class:`nidaqmx.constants.ExcitationVoltageOrCurrent`: Specifies
            if the channel uses current or voltage excitation.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x17f6)
        return ExcitationVoltageOrCurrent(val)

    @ai_excit_voltage_or_current.setter
    def ai_excit_voltage_or_current(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x17f6, val)

    @ai_excit_voltage_or_current.deleter
    def ai_excit_voltage_or_current(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17f6)

    @property
    def ai_filter_delay(self):
        """
        float: Indicates the amount of time between when the ADC samples
            data and when the sample is read by the host device. This
            value is in the units you specify with
            **ai_filter_delay_units**. You can adjust this amount of
            time using **ai_filter_delay_adjustment**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2fed)
        return val

    @property
    def ai_filter_delay_adjustment(self):
        """
        float: Specifies the amount of filter delay that gets removed if
            **ai_remove_filter_delay** is enabled. This delay adjustment
            is in addition to the value indicated by
            **ai_filter_delay**. This delay adjustment is in the units
            you specify with **ai_filter_delay_units**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x3074)
        return val

    @ai_filter_delay_adjustment.setter
    def ai_filter_delay_adjustment(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x3074, val)

    @ai_filter_delay_adjustment.deleter
    def ai_filter_delay_adjustment(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3074)

    @property
    def ai_filter_delay_units(self):
        """
        :class:`nidaqmx.constants.DigitalWidthUnits`: Specifies the
            units of **ai_filter_delay** and
            **ai_filter_delay_adjustment**.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x3071)
        return DigitalWidthUnits(val)

    @ai_filter_delay_units.setter
    def ai_filter_delay_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x3071, val)

    @ai_filter_delay_units.deleter
    def ai_filter_delay_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3071)

    @property
    def ai_filter_enable(self):
        """
        bool: Specifies the corresponding filter enable/disable state.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x3173)
        return val

    @ai_filter_enable.setter
    def ai_filter_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x3173, val)

    @ai_filter_enable.deleter
    def ai_filter_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3173)

    @property
    def ai_filter_freq(self):
        """
        float: Specifies the corresponding filter frequency (cutoff or
            center) of the filter response.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x3174)
        return val

    @ai_filter_freq.setter
    def ai_filter_freq(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x3174, val)

    @ai_filter_freq.deleter
    def ai_filter_freq(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3174)

    @property
    def ai_filter_order(self):
        """
        int: Specifies the corresponding filter order and defines the
            slope of the filter response.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x3176)
        return val

    @ai_filter_order.setter
    def ai_filter_order(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x3176, val)

    @ai_filter_order.deleter
    def ai_filter_order(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3176)

    @property
    def ai_filter_response(self):
        """
        :class:`nidaqmx.constants.FilterResponse`: Specifies the
            corresponding filter response and defines the shape of the
            filter response.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x3175)
        return FilterResponse(val)

    @ai_filter_response.setter
    def ai_filter_response(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x3175, val)

    @ai_filter_response.deleter
    def ai_filter_response(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3175)

    @property
    def ai_force_iepe_sensor_sensitivity(self):
        """
        float: Specifies the sensitivity of the IEPE force sensor
            connected to the channel. Specify this value in the unit
            indicated by **ai_force_iepe_sensor_sensitivity_units**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2f81)
        return val

    @ai_force_iepe_sensor_sensitivity.setter
    def ai_force_iepe_sensor_sensitivity(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2f81, val)

    @ai_force_iepe_sensor_sensitivity.deleter
    def ai_force_iepe_sensor_sensitivity(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f81)

    @property
    def ai_force_iepe_sensor_sensitivity_units(self):
        """
        :class:`nidaqmx.constants.ForceIEPESensorSensitivityUnits`:
            Specifies the units for
            **ai_force_iepe_sensor_sensitivity**.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2f82)
        return ForceIEPESensorSensitivityUnits(val)

    @ai_force_iepe_sensor_sensitivity_units.setter
    def ai_force_iepe_sensor_sensitivity_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2f82, val)

    @ai_force_iepe_sensor_sensitivity_units.deleter
    def ai_force_iepe_sensor_sensitivity_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f82)

    @property
    def ai_force_read_from_chan(self):
        """
        bool: Specifies whether to read from the channel if it is a
            cold-junction compensation channel. By default, DAQmx Read
            does not return data from cold-junction compensation
            channels.  Setting this property to True forces read
            operations to return the cold-junction compensation channel
            data with the other channels in the task.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x18f8)
        return val

    @ai_force_read_from_chan.setter
    def ai_force_read_from_chan(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x18f8, val)

    @ai_force_read_from_chan.deleter
    def ai_force_read_from_chan(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18f8)

    @property
    def ai_force_units(self):
        """
        :class:`nidaqmx.constants.ForceUnits`: Specifies in which unit
            to return force or load measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2f75)
        return ForceUnits(val)

    @ai_force_units.setter
    def ai_force_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2f75, val)

    @ai_force_units.deleter
    def ai_force_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f75)

    @property
    def ai_freq_hyst(self):
        """
        float: Specifies in volts a window below
            **ai_freq_thresh_voltage**. The input voltage must pass
            below **ai_freq_thresh_voltage** minus this value before NI-
            DAQmx recognizes a waveform repetition at
            **ai_freq_thresh_voltage**. Hysteresis can improve the
            measurement accuracy when the signal contains noise or
            jitter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x814)
        return val

    @ai_freq_hyst.setter
    def ai_freq_hyst(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x814, val)

    @ai_freq_hyst.deleter
    def ai_freq_hyst(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x814)

    @property
    def ai_freq_thresh_voltage(self):
        """
        float: Specifies the voltage level at which to recognize
            waveform repetitions. You should select a voltage level that
            occurs only once within the entire period of a waveform. You
            also can select a voltage that occurs only once while the
            voltage rises or falls.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x815)
        return val

    @ai_freq_thresh_voltage.setter
    def ai_freq_thresh_voltage(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x815, val)

    @ai_freq_thresh_voltage.deleter
    def ai_freq_thresh_voltage(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x815)

    @property
    def ai_freq_units(self):
        """
        :class:`nidaqmx.constants.FrequencyUnits`: Specifies the units
            to use to return frequency measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x806)
        return FrequencyUnits(val)

    @ai_freq_units.setter
    def ai_freq_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x806, val)

    @ai_freq_units.deleter
    def ai_freq_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x806)

    @property
    def ai_gain(self):
        """
        float: Specifies a gain factor to apply to the channel.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1818)
        return val

    @ai_gain.setter
    def ai_gain(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1818, val)

    @ai_gain.deleter
    def ai_gain(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1818)

    @property
    def ai_impedance(self):
        """
        :class:`nidaqmx.constants.Impedance1`: Specifies the input
            impedance of the channel.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x62)
        return Impedance1(val)

    @ai_impedance.setter
    def ai_impedance(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x62, val)

    @ai_impedance.deleter
    def ai_impedance(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x62)

    @property
    def ai_input_limits_fault_detect_enable(self):
        """
        bool: Specifies whether to enable input limits fault detection.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x318e)
        return val

    @ai_input_limits_fault_detect_enable.setter
    def ai_input_limits_fault_detect_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x318e, val)

    @ai_input_limits_fault_detect_enable.deleter
    def ai_input_limits_fault_detect_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x318e)

    @property
    def ai_input_limits_fault_detect_lower_limit(self):
        """
        float: Specifies the level of the lower limit for input limits
            detection. An input sample outside the upper and lower
            bounds causes a fault. Note: Fault detection applies to both
            positive and negative inputs. For instance, if you specify a
            lower limit of 2 mA and an upper limit of 12 mA, NI-DAQmx
            detects a fault at 15 mA and -15 mA, but not at -6 mA
            because it is in the range of -12 mA to -2 mA.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x318d)
        return val

    @ai_input_limits_fault_detect_lower_limit.setter
    def ai_input_limits_fault_detect_lower_limit(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x318d, val)

    @ai_input_limits_fault_detect_lower_limit.deleter
    def ai_input_limits_fault_detect_lower_limit(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x318d)

    @property
    def ai_input_limits_fault_detect_upper_limit(self):
        """
        float: Specifies the level of the upper limit for input limits
            detection. An input sample outside the upper and lower
            bounds causes a fault. Note: Fault detection applies to both
            positive and negative inputs. For instance, if you specify a
            lower limit of 2 mA and an upper limit of 12 mA, NI-DAQmx
            detects a fault at 15 mA and -15 mA, but not at -6 mA
            because it is in the range of -12 mA to -2 mA.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x318c)
        return val

    @ai_input_limits_fault_detect_upper_limit.setter
    def ai_input_limits_fault_detect_upper_limit(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x318c, val)

    @ai_input_limits_fault_detect_upper_limit.deleter
    def ai_input_limits_fault_detect_upper_limit(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x318c)

    @property
    def ai_input_src(self):
        """
        str: Specifies the source of the channel. You can use the signal
            from the I/O connector or one of several calibration
            signals. Certain devices have a single calibration signal
            bus. For these devices, you must specify the same
            calibration signal for all channels you connect to a
            calibration signal.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2198)
        return val

    @ai_input_src.setter
    def ai_input_src(self, val):
        self._interpreter.set_chan_attribute_string(self._handle, self._name, 0x2198, val)

    @ai_input_src.deleter
    def ai_input_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2198)

    @property
    def ai_lead_wire_resistance(self):
        """
        float: Specifies in ohms the resistance of the wires that lead
            to the sensor.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x17ee)
        return val

    @ai_lead_wire_resistance.setter
    def ai_lead_wire_resistance(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x17ee, val)

    @ai_lead_wire_resistance.deleter
    def ai_lead_wire_resistance(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17ee)

    @property
    def ai_lossy_lsb_removal_compressed_samp_size(self):
        """
        int: Specifies the number of bits to return in a raw sample when
            **ai_raw_data_compression_type** is set to
            **RawDataCompressionType.LOSSY_LSB_REMOVAL**.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x22d9)
        return val

    @ai_lossy_lsb_removal_compressed_samp_size.setter
    def ai_lossy_lsb_removal_compressed_samp_size(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x22d9, val)

    @ai_lossy_lsb_removal_compressed_samp_size.deleter
    def ai_lossy_lsb_removal_compressed_samp_size(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x22d9)

    @property
    def ai_lowpass_cutoff_freq(self):
        """
        float: Specifies the frequency in Hertz that corresponds to the
            -3dB cutoff of the filter.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1803)
        return val

    @ai_lowpass_cutoff_freq.setter
    def ai_lowpass_cutoff_freq(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1803, val)

    @ai_lowpass_cutoff_freq.deleter
    def ai_lowpass_cutoff_freq(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1803)

    @property
    def ai_lowpass_enable(self):
        """
        bool: Specifies whether to enable the lowpass filter of the
            channel.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x1802)
        return val

    @ai_lowpass_enable.setter
    def ai_lowpass_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x1802, val)

    @ai_lowpass_enable.deleter
    def ai_lowpass_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1802)

    @property
    def ai_lowpass_switch_cap_clk_src(self):
        """
        :class:`nidaqmx.constants.SourceSelection`: Specifies the source
            of the filter clock. If you need a higher resolution for the
            filter, you can supply an external clock to increase the
            resolution. Refer to the SCXI-1141/1142/1143 User Manual for
            more information.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x1884)
        return SourceSelection(val)

    @ai_lowpass_switch_cap_clk_src.setter
    def ai_lowpass_switch_cap_clk_src(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x1884, val)

    @ai_lowpass_switch_cap_clk_src.deleter
    def ai_lowpass_switch_cap_clk_src(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1884)

    @property
    def ai_lowpass_switch_cap_ext_clk_div(self):
        """
        int: Specifies the divisor for the external clock when you set
            **ai_lowpass_switch_cap_clk_src** to
            **SourceSelection.EXTERNAL**. On the SCXI-1141, SCXI-1142,
            and SCXI-1143, NI-DAQmx determines the filter cutoff by
            using the equation f/(100*n), where f is the external
            frequency, and n is the external clock divisor. Refer to the
            SCXI-1141/1142/1143 User Manual for more information.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x1886)
        return val

    @ai_lowpass_switch_cap_ext_clk_div.setter
    def ai_lowpass_switch_cap_ext_clk_div(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x1886, val)

    @ai_lowpass_switch_cap_ext_clk_div.deleter
    def ai_lowpass_switch_cap_ext_clk_div(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1886)

    @property
    def ai_lowpass_switch_cap_ext_clk_freq(self):
        """
        float: Specifies the frequency of the external clock when you
            set **ai_lowpass_switch_cap_clk_src** to
            **SourceSelection.EXTERNAL**.  NI-DAQmx uses this frequency
            to set the pre- and post- filters on the SCXI-1141,
            SCXI-1142, and SCXI-1143. On those devices, NI-DAQmx
            determines the filter cutoff by using the equation
            f/(100*n), where f is the external frequency, and n is the
            external clock divisor. Refer to the SCXI-1141/1142/1143
            User Manual for more information.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1885)
        return val

    @ai_lowpass_switch_cap_ext_clk_freq.setter
    def ai_lowpass_switch_cap_ext_clk_freq(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1885, val)

    @ai_lowpass_switch_cap_ext_clk_freq.deleter
    def ai_lowpass_switch_cap_ext_clk_freq(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1885)

    @property
    def ai_lowpass_switch_cap_out_clk_div(self):
        """
        int: Specifies the divisor for the output clock.  NI-DAQmx uses
            the cutoff frequency to determine the output clock
            frequency. Refer to the SCXI-1141/1142/1143 User Manual for
            more information.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x1887)
        return val

    @ai_lowpass_switch_cap_out_clk_div.setter
    def ai_lowpass_switch_cap_out_clk_div(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x1887, val)

    @ai_lowpass_switch_cap_out_clk_div.deleter
    def ai_lowpass_switch_cap_out_clk_div(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1887)

    @property
    def ai_lvdt_sensitivity(self):
        """
        float: Specifies the sensitivity of the LVDT. This value is in
            the units you specify with **ai_lvdt_sensitivity_units**.
            Refer to the sensor documentation to determine this value.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x939)
        return val

    @ai_lvdt_sensitivity.setter
    def ai_lvdt_sensitivity(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x939, val)

    @ai_lvdt_sensitivity.deleter
    def ai_lvdt_sensitivity(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x939)

    @property
    def ai_lvdt_sensitivity_units(self):
        """
        :class:`nidaqmx.constants.LVDTSensitivityUnits`: Specifies the
            units of **ai_lvdt_sensitivity**.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x219a)
        return LVDTSensitivityUnits(val)

    @ai_lvdt_sensitivity_units.setter
    def ai_lvdt_sensitivity_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x219a, val)

    @ai_lvdt_sensitivity_units.deleter
    def ai_lvdt_sensitivity_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x219a)

    @property
    def ai_lvdt_units(self):
        """
        :class:`nidaqmx.constants.LengthUnits`: Specifies the units to
            use to return linear position measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x910)
        return LengthUnits(val)

    @ai_lvdt_units.setter
    def ai_lvdt_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x910, val)

    @ai_lvdt_units.deleter
    def ai_lvdt_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x910)

    @property
    def ai_max(self):
        """
        float: Specifies the maximum value you expect to measure. This
            value is in the units you specify with a units property.
            When you query this property, it returns the coerced maximum
            value that the device can measure with the current settings.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x17dd)
        return val

    @ai_max.setter
    def ai_max(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x17dd, val)

    @ai_max.deleter
    def ai_max(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17dd)

    @property
    def ai_meas_type(self):
        """
        :class:`nidaqmx.constants.UsageTypeAI`: Indicates the
            measurement to take with the analog input channel and in
            some cases, such as for temperature measurements, the sensor
            to use.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x695)
        return UsageTypeAI(val)

    @property
    def ai_mem_map_enable(self):
        """
        bool: Specifies for NI-DAQmx to map hardware registers to the
            memory space of the application, if possible. Normally, NI-
            DAQmx maps hardware registers to memory accessible only to
            the kernel. Mapping the registers to the memory space of the
            application increases performance. However, if the
            application accesses the memory space mapped to the
            registers, it can adversely affect the operation of the
            device and possibly result in a system crash.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x188c)
        return val

    @ai_mem_map_enable.setter
    def ai_mem_map_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x188c, val)

    @ai_mem_map_enable.deleter
    def ai_mem_map_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x188c)

    @property
    def ai_microphone_sensitivity(self):
        """
        float: Specifies the sensitivity of the microphone. This value
            is in mV/Pa. Refer to the sensor documentation to determine
            this value.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1536)
        return val

    @ai_microphone_sensitivity.setter
    def ai_microphone_sensitivity(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1536, val)

    @ai_microphone_sensitivity.deleter
    def ai_microphone_sensitivity(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1536)

    @property
    def ai_min(self):
        """
        float: Specifies the minimum value you expect to measure. This
            value is in the units you specify with a units property.
            When you query this property, it returns the coerced minimum
            value that the device can measure with the current settings.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x17de)
        return val

    @ai_min.setter
    def ai_min(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x17de, val)

    @ai_min.deleter
    def ai_min(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17de)

    @property
    def ai_open_chan_detect_enable(self):
        """
        bool: Specifies whether to enable open channel detection.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x30ff)
        return val

    @ai_open_chan_detect_enable.setter
    def ai_open_chan_detect_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x30ff, val)

    @ai_open_chan_detect_enable.deleter
    def ai_open_chan_detect_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x30ff)

    @property
    def ai_open_thrmcpl_detect_enable(self):
        """
        bool: Specifies whether to apply the open thermocouple detection
            bias voltage to the channel. Changing the value of this
            property on a channel may require settling time before the
            data returned is valid. To compensate for this settling
            time, discard unsettled data or add a delay between
            committing and starting the task. Refer to your device
            specifications for the required settling time. When open
            thermocouple detection is enabled, use
            **open_thrmcpl_chans_exist** to determine if any channels
            were open.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2f72)
        return val

    @ai_open_thrmcpl_detect_enable.setter
    def ai_open_thrmcpl_detect_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2f72, val)

    @ai_open_thrmcpl_detect_enable.deleter
    def ai_open_thrmcpl_detect_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f72)

    @property
    def ai_overcurrent_detect_enable(self):
        """
        bool: Specifies whether to enable overcurrent detection.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x3194)
        return val

    @ai_overcurrent_detect_enable.setter
    def ai_overcurrent_detect_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x3194, val)

    @ai_overcurrent_detect_enable.deleter
    def ai_overcurrent_detect_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3194)

    @property
    def ai_power_supply_fault_detect_enable(self):
        """
        bool: Specifies whether to enable power supply fault detection.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x3191)
        return val

    @ai_power_supply_fault_detect_enable.setter
    def ai_power_supply_fault_detect_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x3191, val)

    @ai_power_supply_fault_detect_enable.deleter
    def ai_power_supply_fault_detect_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3191)

    @property
    def ai_pressure_units(self):
        """
        :class:`nidaqmx.constants.PressureUnits`: Specifies  in which
            unit to return pressure measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2f76)
        return PressureUnits(val)

    @ai_pressure_units.setter
    def ai_pressure_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2f76, val)

    @ai_pressure_units.deleter
    def ai_pressure_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f76)

    @property
    def ai_probe_atten(self):
        """
        float: Specifies the amount of attenuation provided by the probe
            connected to the channel. Specify this attenuation as a
            ratio.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2a88)
        return val

    @ai_probe_atten.setter
    def ai_probe_atten(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2a88, val)

    @ai_probe_atten.deleter
    def ai_probe_atten(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2a88)

    @property
    def ai_raw_data_compression_type(self):
        """
        :class:`nidaqmx.constants.RawDataCompressionType`: Specifies the
            type of compression to apply to raw samples returned from
            the device.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x22d8)
        return RawDataCompressionType(val)

    @ai_raw_data_compression_type.setter
    def ai_raw_data_compression_type(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x22d8, val)

    @ai_raw_data_compression_type.deleter
    def ai_raw_data_compression_type(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x22d8)

    @property
    def ai_raw_samp_justification(self):
        """
        :class:`nidaqmx.constants.DataJustification`: Indicates the
            justification of a raw sample from the device.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x50)
        return DataJustification(val)

    @property
    def ai_raw_samp_size(self):
        """
        int: Indicates in bits the size of a raw sample from the device.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x22da)
        return val

    @property
    def ai_remove_filter_delay(self):
        """
        bool: Specifies if filter delay removal is enabled on the
            device.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2fbd)
        return val

    @ai_remove_filter_delay.setter
    def ai_remove_filter_delay(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2fbd, val)

    @ai_remove_filter_delay.deleter
    def ai_remove_filter_delay(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2fbd)

    @property
    def ai_resistance_cfg(self):
        """
        :class:`nidaqmx.constants.ResistanceConfiguration`: Specifies
            the resistance configuration for the channel. NI-DAQmx uses
            this value for any resistance-based measurements, including
            temperature measurement using a thermistor or RTD.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x1881)
        return ResistanceConfiguration(val)

    @ai_resistance_cfg.setter
    def ai_resistance_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x1881, val)

    @ai_resistance_cfg.deleter
    def ai_resistance_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1881)

    @property
    def ai_resistance_units(self):
        """
        :class:`nidaqmx.constants.ResistanceUnits`: Specifies the units
            to use to return resistance measurements.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x955)
        return ResistanceUnits(val)

    @ai_resistance_units.setter
    def ai_resistance_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x955, val)

    @ai_resistance_units.deleter
    def ai_resistance_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x955)

    @property
    def ai_resolution(self):
        """
        float: Indicates the resolution of the analog-to-digital
            converter of the channel. This value is in the units you
            specify with **ai_resolution_units**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1765)
        return val

    @property
    def ai_resolution_units(self):
        """
        :class:`nidaqmx.constants.ResolutionType`: Indicates the units
            of **ai_resolution**.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x1764)
        return ResolutionType(val)

    @property
    def ai_rng_high(self):
        """
        float: Specifies the upper limit of the input range of the
            device. This value is in the native units of the device. On
            E Series devices, for example, the native units is volts.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1815)
        return val

    @ai_rng_high.setter
    def ai_rng_high(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1815, val)

    @ai_rng_high.deleter
    def ai_rng_high(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1815)

    @property
    def ai_rng_low(self):
        """
        float: Specifies the lower limit of the input range of the
            device. This value is in the native units of the device. On
            E Series devices, for example, the native units is volts.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1816)
        return val

    @ai_rng_low.setter
    def ai_rng_low(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1816, val)

    @ai_rng_low.deleter
    def ai_rng_low(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1816)

    @property
    def ai_rosette_strain_gage_gage_orientation(self):
        """
        float: Specifies gage orientation in degrees with respect to the
            X axis.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2ffc)
        return val

    @ai_rosette_strain_gage_gage_orientation.setter
    def ai_rosette_strain_gage_gage_orientation(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2ffc, val)

    @ai_rosette_strain_gage_gage_orientation.deleter
    def ai_rosette_strain_gage_gage_orientation(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2ffc)

    @property
    def ai_rosette_strain_gage_rosette_meas_type(self):
        """
        :class:`nidaqmx.constants.StrainGageRosetteMeasurementType`:
            Specifies the type of rosette measurement.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2ffd)
        return StrainGageRosetteMeasurementType(val)

    @ai_rosette_strain_gage_rosette_meas_type.setter
    def ai_rosette_strain_gage_rosette_meas_type(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2ffd, val)

    @ai_rosette_strain_gage_rosette_meas_type.deleter
    def ai_rosette_strain_gage_rosette_meas_type(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2ffd)

    @property
    def ai_rosette_strain_gage_rosette_type(self):
        """
        :class:`nidaqmx.constants.StrainGageRosetteType`: Indicates the
            type of rosette gage.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2ffe)
        return StrainGageRosetteType(val)

    @property
    def ai_rosette_strain_gage_strain_chans(self):
        """
        List[str]: Indicates the raw strain channels that comprise the
            strain rosette.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x2ffb)
        return unflatten_channel_string(val)

    @property
    def ai_rtd_a(self):
        """
        float: Specifies the 'A' constant of the Callendar-Van Dusen
            equation. NI-DAQmx requires this value when you use a custom
            RTD.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1010)
        return val

    @ai_rtd_a.setter
    def ai_rtd_a(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1010, val)

    @ai_rtd_a.deleter
    def ai_rtd_a(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1010)

    @property
    def ai_rtd_b(self):
        """
        float: Specifies the 'B' constant of the Callendar-Van Dusen
            equation. NI-DAQmx requires this value when you use a custom
            RTD.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1011)
        return val

    @ai_rtd_b.setter
    def ai_rtd_b(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1011, val)

    @ai_rtd_b.deleter
    def ai_rtd_b(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1011)

    @property
    def ai_rtd_c(self):
        """
        float: Specifies the 'C' constant of the Callendar-Van Dusen
            equation. NI-DAQmx requires this value when you use a custom
            RTD.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1013)
        return val

    @ai_rtd_c.setter
    def ai_rtd_c(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1013, val)

    @ai_rtd_c.deleter
    def ai_rtd_c(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1013)

    @property
    def ai_rtd_r0(self):
        """
        float: Specifies in ohms the sensor resistance at 0 deg C. The
            Callendar-Van Dusen equation requires this value. Refer to
            the sensor documentation to determine this value.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1030)
        return val

    @ai_rtd_r0.setter
    def ai_rtd_r0(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1030, val)

    @ai_rtd_r0.deleter
    def ai_rtd_r0(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1030)

    @property
    def ai_rtd_type(self):
        """
        :class:`nidaqmx.constants.RTDType`: Specifies the type of RTD
            connected to the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x1032)
        return RTDType(val)

    @ai_rtd_type.setter
    def ai_rtd_type(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x1032, val)

    @ai_rtd_type.deleter
    def ai_rtd_type(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1032)

    @property
    def ai_rvdt_sensitivity(self):
        """
        float: Specifies the sensitivity of the RVDT. This value is in
            the units you specify with **ai_rvdt_sensitivity_units**.
            Refer to the sensor documentation to determine this value.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x903)
        return val

    @ai_rvdt_sensitivity.setter
    def ai_rvdt_sensitivity(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x903, val)

    @ai_rvdt_sensitivity.deleter
    def ai_rvdt_sensitivity(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x903)

    @property
    def ai_rvdt_sensitivity_units(self):
        """
        :class:`nidaqmx.constants.RVDTSensitivityUnits`: Specifies the
            units of **ai_rvdt_sensitivity**.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x219b)
        return RVDTSensitivityUnits(val)

    @ai_rvdt_sensitivity_units.setter
    def ai_rvdt_sensitivity_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x219b, val)

    @ai_rvdt_sensitivity_units.deleter
    def ai_rvdt_sensitivity_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x219b)

    @property
    def ai_rvdt_units(self):
        """
        :class:`nidaqmx.constants.AngleUnits`: Specifies the units to
            use to return angular position measurements from the
            channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x877)
        return AngleUnits(val)

    @ai_rvdt_units.setter
    def ai_rvdt_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x877, val)

    @ai_rvdt_units.deleter
    def ai_rvdt_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x877)

    @property
    def ai_samp_and_hold_enable(self):
        """
        bool: Specifies whether to enable the sample and hold circuitry
            of the device. When you disable sample and hold circuitry, a
            small voltage offset might be introduced into the signal.
            You can eliminate this offset by using **ai_auto_zero_mode**
            to perform an auto zero on the channel.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x181a)
        return val

    @ai_samp_and_hold_enable.setter
    def ai_samp_and_hold_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x181a, val)

    @ai_samp_and_hold_enable.deleter
    def ai_samp_and_hold_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x181a)

    @property
    def ai_sensor_power_cfg(self):
        """
        :class:`nidaqmx.constants.SensorPowerCfg`: Specifies whether to
            turn on the sensor's power supply or to leave the
            configuration unchanged.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x316a)
        return SensorPowerCfg(val)

    @ai_sensor_power_cfg.setter
    def ai_sensor_power_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x316a, val)

    @ai_sensor_power_cfg.deleter
    def ai_sensor_power_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x316a)

    @property
    def ai_sensor_power_type(self):
        """
        :class:`nidaqmx.constants.SensorPowerType`: Specifies the type
            of power supplied to the sensor.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x316b)
        return SensorPowerType(val)

    @ai_sensor_power_type.setter
    def ai_sensor_power_type(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x316b, val)

    @ai_sensor_power_type.deleter
    def ai_sensor_power_type(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x316b)

    @property
    def ai_sensor_power_voltage(self):
        """
        float: Specifies the voltage level for the sensor's power
            supply.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x3169)
        return val

    @ai_sensor_power_voltage.setter
    def ai_sensor_power_voltage(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x3169, val)

    @ai_sensor_power_voltage.deleter
    def ai_sensor_power_voltage(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3169)

    @property
    def ai_sound_pressure_db_ref(self):
        """
        float: Specifies the decibel reference level in the units of the
            channel. When you read samples as a waveform, the decibel
            reference level is included in the waveform attributes. NI-
            DAQmx also uses the decibel reference level when converting
            **ai_sound_pressure_max_sound_pressure_lvl** to a voltage
            level.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x29b1)
        return val

    @ai_sound_pressure_db_ref.setter
    def ai_sound_pressure_db_ref(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x29b1, val)

    @ai_sound_pressure_db_ref.deleter
    def ai_sound_pressure_db_ref(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x29b1)

    @property
    def ai_sound_pressure_max_sound_pressure_lvl(self):
        """
        float: Specifies the maximum instantaneous sound pressure level
            you expect to measure. This value is in decibels, referenced
            to 20 micropascals. NI-DAQmx uses the maximum sound pressure
            level to calculate values in pascals for **ai_max** and
            **ai_min** for the channel.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x223a)
        return val

    @ai_sound_pressure_max_sound_pressure_lvl.setter
    def ai_sound_pressure_max_sound_pressure_lvl(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x223a, val)

    @ai_sound_pressure_max_sound_pressure_lvl.deleter
    def ai_sound_pressure_max_sound_pressure_lvl(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x223a)

    @property
    def ai_sound_pressure_units(self):
        """
        :class:`nidaqmx.constants.SoundPressureUnits`: Specifies the
            units to use to return sound pressure measurements from the
            channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x1528)
        return SoundPressureUnits(val)

    @ai_sound_pressure_units.setter
    def ai_sound_pressure_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x1528, val)

    @ai_sound_pressure_units.deleter
    def ai_sound_pressure_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1528)

    @property
    def ai_strain_force_read_from_chan(self):
        """
        bool: Specifies whether the data is returned by DAQmx Read when
            set on a raw strain channel that is part of a rosette
            configuration.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2ffa)
        return val

    @ai_strain_force_read_from_chan.setter
    def ai_strain_force_read_from_chan(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x2ffa, val)

    @ai_strain_force_read_from_chan.deleter
    def ai_strain_force_read_from_chan(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2ffa)

    @property
    def ai_strain_gage_cfg(self):
        """
        :class:`nidaqmx.constants.StrainGageBridgeType`: Specifies the
            bridge configuration of the strain gages.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x982)
        return StrainGageBridgeType(val)

    @ai_strain_gage_cfg.setter
    def ai_strain_gage_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x982, val)

    @ai_strain_gage_cfg.deleter
    def ai_strain_gage_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x982)

    @property
    def ai_strain_gage_gage_factor(self):
        """
        float: Specifies the sensitivity of the strain gage.  Gage
            factor relates the change in electrical resistance to the
            change in strain. Refer to the sensor documentation for this
            value.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x994)
        return val

    @ai_strain_gage_gage_factor.setter
    def ai_strain_gage_gage_factor(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x994, val)

    @ai_strain_gage_gage_factor.deleter
    def ai_strain_gage_gage_factor(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x994)

    @property
    def ai_strain_gage_poisson_ratio(self):
        """
        float: Specifies the ratio of lateral strain to axial strain in
            the material you are measuring.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x998)
        return val

    @ai_strain_gage_poisson_ratio.setter
    def ai_strain_gage_poisson_ratio(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x998, val)

    @ai_strain_gage_poisson_ratio.deleter
    def ai_strain_gage_poisson_ratio(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x998)

    @property
    def ai_strain_units(self):
        """
        :class:`nidaqmx.constants.StrainUnits`: Specifies the units to
            use to return strain measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x981)
        return StrainUnits(val)

    @ai_strain_units.setter
    def ai_strain_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x981, val)

    @ai_strain_units.deleter
    def ai_strain_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x981)

    @property
    def ai_teds_is_teds(self):
        """
        bool: Indicates if the virtual channel was initialized using a
            TEDS bitstream from the corresponding physical channel.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x2983)
        return val

    @property
    def ai_teds_units(self):
        """
        str: Indicates the units defined by TEDS information associated
            with the channel.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x21e0)
        return val

    @property
    def ai_temp_units(self):
        """
        :class:`nidaqmx.constants.TemperatureUnits`: Specifies the units
            to use to return temperature measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x1033)
        return TemperatureUnits(val)

    @ai_temp_units.setter
    def ai_temp_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x1033, val)

    @ai_temp_units.deleter
    def ai_temp_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1033)

    @property
    def ai_term_cfg(self):
        """
        :class:`nidaqmx.constants.TerminalConfiguration`: Specifies the
            terminal configuration for the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x1097)
        return TerminalConfiguration(val)

    @ai_term_cfg.setter
    def ai_term_cfg(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x1097, val)

    @ai_term_cfg.deleter
    def ai_term_cfg(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1097)

    @property
    def ai_thrmcpl_cjc_chan(self):
        """
        :class:`nidaqmx.task.channels.Channel`: Indicates the channel
            that acquires the temperature of the cold junction if
            **ai_thrmcpl_cjc_src** is **CJCSource1.SCANNABLE_CHANNEL**.
            If the channel is a temperature channel, NI-DAQmx acquires
            the temperature in the correct units. Other channel types,
            such as a resistance channel with a custom sensor, must use
            a custom scale to scale values to degrees Celsius.
        """

        val = self._interpreter.get_chan_attribute_string(self._handle, self._name, 0x1034)
        return Channel._factory(self._handle, val, self._interpreter)

    @property
    def ai_thrmcpl_cjc_src(self):
        """
        :class:`nidaqmx.constants.CJCSource`: Indicates the source of
            cold-junction compensation.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x1035)
        return CJCSource(val)

    @property
    def ai_thrmcpl_cjc_val(self):
        """
        float: Specifies the temperature of the cold junction if
            **ai_thrmcpl_cjc_src** is
            **CJCSource1.CONSTANT_USER_VALUE**. Specify this value in
            the units of the measurement.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1036)
        return val

    @ai_thrmcpl_cjc_val.setter
    def ai_thrmcpl_cjc_val(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1036, val)

    @ai_thrmcpl_cjc_val.deleter
    def ai_thrmcpl_cjc_val(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1036)

    @property
    def ai_thrmcpl_lead_offset_voltage(self):
        """
        float: Specifies the lead offset nulling voltage to subtract
            from measurements on a device. This property is ignored if
            open thermocouple detection is disabled.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2fb8)
        return val

    @ai_thrmcpl_lead_offset_voltage.setter
    def ai_thrmcpl_lead_offset_voltage(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2fb8, val)

    @ai_thrmcpl_lead_offset_voltage.deleter
    def ai_thrmcpl_lead_offset_voltage(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2fb8)

    @property
    def ai_thrmcpl_scale_type(self):
        """
        :class:`nidaqmx.constants.ScaleType`: Specifies the method or
            equation form that the thermocouple scale uses.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x29d0)
        return ScaleType(val)

    @ai_thrmcpl_scale_type.setter
    def ai_thrmcpl_scale_type(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x29d0, val)

    @ai_thrmcpl_scale_type.deleter
    def ai_thrmcpl_scale_type(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x29d0)

    @property
    def ai_thrmcpl_type(self):
        """
        :class:`nidaqmx.constants.ThermocoupleType`: Specifies the type
            of thermocouple connected to the channel. Thermocouple types
            differ in composition and measurement range.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x1050)
        return ThermocoupleType(val)

    @ai_thrmcpl_type.setter
    def ai_thrmcpl_type(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x1050, val)

    @ai_thrmcpl_type.deleter
    def ai_thrmcpl_type(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1050)

    @property
    def ai_thrmstr_a(self):
        """
        float: Specifies the 'A' constant of the Steinhart-Hart
            thermistor equation.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x18c9)
        return val

    @ai_thrmstr_a.setter
    def ai_thrmstr_a(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x18c9, val)

    @ai_thrmstr_a.deleter
    def ai_thrmstr_a(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18c9)

    @property
    def ai_thrmstr_b(self):
        """
        float: Specifies the 'B' constant of the Steinhart-Hart
            thermistor equation.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x18cb)
        return val

    @ai_thrmstr_b.setter
    def ai_thrmstr_b(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x18cb, val)

    @ai_thrmstr_b.deleter
    def ai_thrmstr_b(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18cb)

    @property
    def ai_thrmstr_c(self):
        """
        float: Specifies the 'C' constant of the Steinhart-Hart
            thermistor equation.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x18ca)
        return val

    @ai_thrmstr_c.setter
    def ai_thrmstr_c(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x18ca, val)

    @ai_thrmstr_c.deleter
    def ai_thrmstr_c(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x18ca)

    @property
    def ai_thrmstr_r1(self):
        """
        float: Specifies in ohms the value of the reference resistor for
            the thermistor if you use voltage excitation. NI-DAQmx
            ignores this value for current excitation.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x1061)
        return val

    @ai_thrmstr_r1.setter
    def ai_thrmstr_r1(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x1061, val)

    @ai_thrmstr_r1.deleter
    def ai_thrmstr_r1(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1061)

    @property
    def ai_torque_units(self):
        """
        :class:`nidaqmx.constants.TorqueUnits`: Specifies in which unit
            to return torque measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2f77)
        return TorqueUnits(val)

    @ai_torque_units.setter
    def ai_torque_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2f77, val)

    @ai_torque_units.deleter
    def ai_torque_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2f77)

    @property
    def ai_usb_xfer_req_count(self):
        """
        int: Specifies the maximum number of simultaneous USB transfers
            used to stream data. Modify this value to affect performance
            under different combinations of operating system and device.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x3000)
        return val

    @ai_usb_xfer_req_count.setter
    def ai_usb_xfer_req_count(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x3000, val)

    @ai_usb_xfer_req_count.deleter
    def ai_usb_xfer_req_count(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x3000)

    @property
    def ai_usb_xfer_req_size(self):
        """
        int: Specifies the maximum size of a USB transfer request in
            bytes. Modify this value to affect performance under
            different combinations of operating system and device.
        """

        val = self._interpreter.get_chan_attribute_uint32(self._handle, self._name, 0x2a8e)
        return val

    @ai_usb_xfer_req_size.setter
    def ai_usb_xfer_req_size(self, val):
        self._interpreter.set_chan_attribute_uint32(self._handle, self._name, 0x2a8e, val)

    @ai_usb_xfer_req_size.deleter
    def ai_usb_xfer_req_size(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2a8e)

    @property
    def ai_velocity_iepe_sensor_db_ref(self):
        """
        float: Specifies the decibel reference level in the units of the
            channel. When you read samples as a waveform, the decibel
            reference level is included in the waveform attributes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2ff5)
        return val

    @ai_velocity_iepe_sensor_db_ref.setter
    def ai_velocity_iepe_sensor_db_ref(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2ff5, val)

    @ai_velocity_iepe_sensor_db_ref.deleter
    def ai_velocity_iepe_sensor_db_ref(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2ff5)

    @property
    def ai_velocity_iepe_sensor_sensitivity(self):
        """
        float: Specifies the sensitivity of the IEPE velocity sensor
            connected to the channel. Specify this value in the unit
            indicated by **ai_velocity_iepe_sensor_sensitivity_units**.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x2ff6)
        return val

    @ai_velocity_iepe_sensor_sensitivity.setter
    def ai_velocity_iepe_sensor_sensitivity(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x2ff6, val)

    @ai_velocity_iepe_sensor_sensitivity.deleter
    def ai_velocity_iepe_sensor_sensitivity(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2ff6)

    @property
    def ai_velocity_iepe_sensor_sensitivity_units(self):
        """
        :class:`nidaqmx.constants.VelocityIEPESensorSensitivityUnits`:
            Specifies the units for
            **ai_velocity_iepe_sensor_sensitivity**.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2ff7)
        return VelocityIEPESensorSensitivityUnits(val)

    @ai_velocity_iepe_sensor_sensitivity_units.setter
    def ai_velocity_iepe_sensor_sensitivity_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2ff7, val)

    @ai_velocity_iepe_sensor_sensitivity_units.deleter
    def ai_velocity_iepe_sensor_sensitivity_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2ff7)

    @property
    def ai_velocity_units(self):
        """
        :class:`nidaqmx.constants.VelocityUnits`: Specifies in which
            unit to return velocity measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x2ff4)
        return VelocityUnits(val)

    @ai_velocity_units.setter
    def ai_velocity_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x2ff4, val)

    @ai_velocity_units.deleter
    def ai_velocity_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x2ff4)

    @property
    def ai_voltage_acrms_units(self):
        """
        :class:`nidaqmx.constants.VoltageUnits`: Specifies the units to
            use to return voltage RMS measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x17e2)
        return VoltageUnits(val)

    @ai_voltage_acrms_units.setter
    def ai_voltage_acrms_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x17e2, val)

    @ai_voltage_acrms_units.deleter
    def ai_voltage_acrms_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x17e2)

    @property
    def ai_voltage_db_ref(self):
        """
        float: Specifies the decibel reference level in the units of the
            channel. When you read samples as a waveform, the decibel
            reference level is included in the waveform attributes.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x29b0)
        return val

    @ai_voltage_db_ref.setter
    def ai_voltage_db_ref(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x29b0, val)

    @ai_voltage_db_ref.deleter
    def ai_voltage_db_ref(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x29b0)

    @property
    def ai_voltage_units(self):
        """
        :class:`nidaqmx.constants.VoltageUnits`: Specifies the units to
            use to return voltage measurements from the channel.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x1094)
        return VoltageUnits(val)

    @ai_voltage_units.setter
    def ai_voltage_units(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x1094, val)

    @ai_voltage_units.deleter
    def ai_voltage_units(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x1094)

    @property
    def pwr_current_dev_scaling_coeff(self):
        """
        List[float]: Indicates the coefficients of the polynomial
            equation that NI-DAQmx uses to scale values from the native
            format of the device to amperes. Can be read at any time
            during a task.
        """

        val = self._interpreter.get_chan_attribute_double_array(self._handle, self._name, 0x31da)
        return val

    @property
    def pwr_current_setpoint(self):
        """
        float: Specifies the output current, in amperes. If the load
            draws current greater than the specified value, the device
            will operate in Constant Current mode.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31d5)
        return val

    @pwr_current_setpoint.setter
    def pwr_current_setpoint(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31d5, val)

    @pwr_current_setpoint.deleter
    def pwr_current_setpoint(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31d5)

    @property
    def pwr_idle_output_behavior(self):
        """
        :class:`nidaqmx.constants.PowerIdleOutputBehavior`: Specifies
            whether to disable the output or maintain the existing value
            after the task is uncommitted.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x31d8)
        return PowerIdleOutputBehavior(val)

    @pwr_idle_output_behavior.setter
    def pwr_idle_output_behavior(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x31d8, val)

    @pwr_idle_output_behavior.deleter
    def pwr_idle_output_behavior(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31d8)

    @property
    def pwr_output_enable(self):
        """
        bool: Specifies whether to enable or disable power module
            output. Can be set while a task is running. Can be read at
            any time during a task. When a task is running, the output
            is enabled immediately. Otherwise, the output is not enabled
            until the task enters the Committed state.
        """

        val = self._interpreter.get_chan_attribute_bool(self._handle, self._name, 0x31d6)
        return val

    @pwr_output_enable.setter
    def pwr_output_enable(self, val):
        self._interpreter.set_chan_attribute_bool(self._handle, self._name, 0x31d6, val)

    @pwr_output_enable.deleter
    def pwr_output_enable(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31d6)

    @property
    def pwr_output_state(self):
        """
        :class:`nidaqmx.constants.PowerOutputState`: Indicates power
            channel operating state. Can be read at any time during a
            task.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x31d7)
        return PowerOutputState(val)

    @property
    def pwr_remote_sense(self):
        """
        :class:`nidaqmx.constants.Sense`: Specifies whether to use local
            or remote sense to sense the output voltage. DAQmx Read
            (Power Supply) will return remote or local voltage based on
            the Remote Sense attribute value. Reading this property will
            return the user-defined value.
        """

        val = self._interpreter.get_chan_attribute_int32(self._handle, self._name, 0x31db)
        return Sense(val)

    @pwr_remote_sense.setter
    def pwr_remote_sense(self, val):
        val = val.value
        self._interpreter.set_chan_attribute_int32(self._handle, self._name, 0x31db, val)

    @pwr_remote_sense.deleter
    def pwr_remote_sense(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31db)

    @property
    def pwr_voltage_dev_scaling_coeff(self):
        """
        List[float]: Indicates the coefficients of the polynomial
            equation that NI-DAQmx uses to scale values from the native
            format of the device to volts. Can be read at any time
            during a task.
        """

        val = self._interpreter.get_chan_attribute_double_array(self._handle, self._name, 0x31d9)
        return val

    @property
    def pwr_voltage_setpoint(self):
        """
        float: Specifies the constant output voltage, in volts. Can be
            set while a task is running. Can be read at any time during
            a task.
        """

        val = self._interpreter.get_chan_attribute_double(self._handle, self._name, 0x31d4)
        return val

    @pwr_voltage_setpoint.setter
    def pwr_voltage_setpoint(self, val):
        self._interpreter.set_chan_attribute_double(self._handle, self._name, 0x31d4, val)

    @pwr_voltage_setpoint.deleter
    def pwr_voltage_setpoint(self):
        self._interpreter.reset_chan_attribute(self._handle, self._name, 0x31d4)

    @property
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_rtd_r0 instead.")
    def ai_rtd_r_0(self):
        return self.ai_rtd_r0

    @ai_rtd_r_0.setter
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_rtd_r0 instead.")
    def ai_rtd_r_0(self, val):
        self.ai_rtd_r0 = val

    @ai_rtd_r_0.deleter
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_rtd_r0 instead.")
    def ai_rtd_r_0(self):
        del self.ai_rtd_r0

    @property
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_sound_pressure_db_ref instead.")
    def ai_sound_pressured_b_ref(self):
        return self.ai_sound_pressure_db_ref

    @ai_sound_pressured_b_ref.setter
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_sound_pressure_db_ref instead.")
    def ai_sound_pressured_b_ref(self, val):
        self.ai_sound_pressure_db_ref = val

    @ai_sound_pressured_b_ref.deleter
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_sound_pressure_db_ref instead.")
    def ai_sound_pressured_b_ref(self):
        del self.ai_sound_pressure_db_ref

    @property
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_thrmstr_r1 instead.")
    def ai_thrmstr_r_1(self):
        return self.ai_thrmstr_r1

    @ai_thrmstr_r_1.setter
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_thrmstr_r1 instead.")
    def ai_thrmstr_r_1(self, val):
        self.ai_thrmstr_r1 = val

    @ai_thrmstr_r_1.deleter
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_thrmstr_r1 instead.")
    def ai_thrmstr_r_1(self):
        del self.ai_thrmstr_r1

    @property
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_accel_db_ref instead.")
    def ai_acceld_b_ref(self):
        return self.ai_accel_db_ref

    @ai_acceld_b_ref.setter
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_accel_db_ref instead.")
    def ai_acceld_b_ref(self, val):
        self.ai_accel_db_ref = val

    @ai_acceld_b_ref.deleter
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_accel_db_ref instead.")
    def ai_acceld_b_ref(self):
        del self.ai_accel_db_ref

    @property
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_voltage_db_ref instead.")
    def ai_voltaged_b_ref(self):
        return self.ai_voltage_db_ref

    @ai_voltaged_b_ref.setter
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_voltage_db_ref instead.")
    def ai_voltaged_b_ref(self, val):
        self.ai_voltage_db_ref = val

    @ai_voltaged_b_ref.deleter
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_voltage_db_ref instead.")
    def ai_voltaged_b_ref(self):
        del self.ai_voltage_db_ref

    @property
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_velocity_iepe_sensor_db_ref instead.")
    def ai_velocity_iepe_sensord_b_ref(self):
        return self.ai_velocity_iepe_sensor_db_ref

    @ai_velocity_iepe_sensord_b_ref.setter
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_velocity_iepe_sensor_db_ref instead.")
    def ai_velocity_iepe_sensord_b_ref(self, val):
        self.ai_velocity_iepe_sensor_db_ref = val

    @ai_velocity_iepe_sensord_b_ref.deleter
    @deprecation.deprecated(deprecated_in="0.7.0", details="Use ai_velocity_iepe_sensor_db_ref instead.")
    def ai_velocity_iepe_sensord_b_ref(self):
        del self.ai_velocity_iepe_sensor_db_ref

