---
type: "manual"
---

<Measuring_Rules>
以下Rules适用于NI数据采集相关功能（"pysweep\measure"目录下）的开发。
- 我们正在python中使用nidaqmx包，控制NI数据采集卡进行数据采集工作。
- 如需查看nidaqmx包具体细节/官方例程，我已经将该包的完整代码仓库clone在本工作区的"参考资料\nidaqmx-python-master"处。但你无需使用该目录中的文件，因为nidaqmx包已经安装在我们的Python环境中，你可以直接import。
- 目前有一台型号为PXIe-1090的NI机箱（名称为"PXIChassis1"）通过雷电线连接至本PC的雷电接口（NI MAX软件显示连接正常，并可正常开始数据采集任务）。其上搭载两张PXIe-4468 DSA Analog I/O板卡，名称分别为"402Dev2Slot2"和"402Dev2Slot3"。两张板卡均各有"ai0"和"ai1"两个输入通道，以及"ao0""ao1"两个输出通道（因此整台机箱共有4个输入通道和4个输出通道）。板卡官方手册显示，其可以使用机箱中的PXIe_CLK100时钟作为硬件采样时钟源。
- 我们目前的具体工作方式是，在"pysweep\measure\measure.py"这一模块中实现所有数据采集相关的函数/类/方法/属性，并在"test.py"文件中调用"measure.py"模块，完成相关操作。
</Measuring_Rules>