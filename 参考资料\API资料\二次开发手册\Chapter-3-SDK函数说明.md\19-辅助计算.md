3.19 辅助计算 

用户编程时辅助计算的实用公式，方便用户进行物理量和实际运动控制器的输入输出之间的转换计算。 

3.19.1 MT_ 
Help_Step_Line_Real_To_Steps 
功能描述：直线移动台物理量(速度、加速度、减速度等)到开环脉冲的转换计算 
 
VC 
INT32 
MT_ 
Help_Step_Line_Real_To_Steps(double AStepAngle,INT32 ADiv,double 
APitch,double ALineRatio,double AValue) 
VB 
Function 
MT_vStep_Line_Real_To_Steps (ByVal AStepAngle As Double,ByVal ADiv As 
Long,ByVal APitch As Double,ByVal 
ALineRatio As Double,ByVal AValue As Double) 
As Long 
Delphi 
function MT_ 
Help_Step_Line_Real_To_Steps(AStepAngle:Double;ADiv:Integer; 
 
APitch:Double;ALineRatio:Double;AValue:Double):Integer 
C# 
public static extern int 
MT_ 
Help_Step_Line_Real_To_Steps(double AStepAngle,Int32 
ADiv,double APitch,double ALineRatio,double AValue) 
输入参数 
AStepAngle 
走一步电机旋转角度，例如两项步进电机一般为1.8° 
 
ADiv 
驱动器细分数，一般为2,4,8,16等等 
 
APitch 
直线台的螺距，即电机旋转一圈的位移，单位mm 
 
ALineRatio 
直线台的传动比，没有传动为1 
 
AValue 
物理量，mm,mm/s,mm/s2 
函数返回 
32位整形 
转换后的步数 
非0 
函数执行失败 
备注 
 
 
3.19.2 MT_ 
Help_Step_Circle_Real_To_Steps 
功能描述：旋转台物理量(速度、加速度、减速度等)到开环脉冲的转换计算 
 
VC 
INT32 
MT_ 
Help_Step_Circle_Real_To_Steps(double AStepAngle,INT32 ADiv,double 
ACircleRatio,double AValue) 
VB 
Function 
MT_ 
Help_Step_Circle_Real_To_Steps (ByVal AStepAngle As Double,ByVal 
ADiv As Long, ByVal 
ACircleRatio As Double,ByVal AValue As Double) As Long 
Delphi 
function MT_vStep_Circle_Real_To_Steps(AStepAngle:Double;ADiv:Integer; 
 
ACircleRatio:Double;AValue:Double):Integer 
C# 
public static extern int 
MT_ 
Help_Step_Circle_Real_To_Steps(double 
AStepAngle,Int32 ADiv, double ACircleRatio,double AValue) 
输入参数 
AStepAngle 
走一步电机旋转角度，例如两项步进电机一般为1.8° 
 
ADiv 
驱动器细分数，一般为2,4,8,16等等 
 
ACircleRatio 
旋转台的传动比 
 
AValue 
物理量，°,°/s,°/s2 
函数返回 
32位整形 
转换后的步数 
非0 
函数执行失败 
备注 
 
 
3.19.3 MT_ 
Help_Step_Line_Steps_To_Real 
功能描述：直线移动台脉冲转物理量(速度、加速度、减速度等) 
 
VC 
double 
MT_ 
Help_Step_Line_Steps_To_Real(double AStepAngle,INT32 ADiv,double 
APitch,double ALineRatio,double AValue) 
VB 
Function 
MT_ 
Help_Step_Line_Steps_To_Real 
(ByVal AStepAngle As Double,ByVal 
ADiv As Long,ByVal APitch 
As Double,ByVal 
ALineRatio As Double,ByVal AValue As 
Long) As 
Double 
Delphi 
function MT_ 
Help_Step_Line_Steps_To_Real(AStepAngle:Double;ADiv:Integer; 
 
APitch:Double;ALineRatio:Double;AValue:Integer):Double 
C# 
public static extern double 
MT_ 
Help_Step_Line_Steps_To_Real(double 
AStepAngle,Int32 ADiv,double APitch,double ALineRatio,INT32 
AValue) 
输入参数 
AStepAngle 
走一步电机旋转角度，例如两项步进电机一般为1.8° 
 
ADiv 
驱动器细分数，一般为2,4,8,16等等 
 
APitch 
直线台的螺距，即电机旋转一圈的位移，单位mm 
 
ALineRatio 
直线台的传动比，没有传动为1 
 
AValue 
脉冲数 
函数返回 
双精度 
转换后的物理量，mm,mm/s,mm/s2 
非0 
函数执行失败 
备注 
 
 
3.19.4 MT_ 
Help_Step_Circle_Steps_To_Real 
功能描述：旋转台脉冲转物理量(速度、加速度、减速度等) 
 
VC 
double 
MT_ 
Help_Step_Circle_Steps_To_Real(double AStepAngle,INT32 ADiv,double 
ACircleRatio,INT32 
AValue) 
VB 
Function 
MT_ 
Help_Step_Circle_Steps_To_Real 
(ByVal AStepAngle As Double,ByVal 
ADiv As Long, ByVal 
ACircleRatio As Double,ByVal AValue As Double) As Long 
Delphi 
function MT_ 
Help_Step_Circle_Steps_To_Real(AStepAngle:Double;ADiv:Integer; 
 
ACircleRatio:Double;AValue:Double):Integer 
C# 
public static extern int 
MT_ 
Help_Step_Circle_Steps_To_Real(double 
AStepAngle,Int32 ADiv, double ACircleRatio,double AValue) 
输入参数 
AStepAngle 
走一步电机旋转角度，例如两项步进电机一般为1.8° 
 
ADiv 
驱动器细分数，一般为2,4,8,16等等 
 
ACircleRatio 
旋转台的传动比 
 
AValue 
脉冲数 
函数返回 
双精度浮点 
转换后的物理量°,°/s,°/s2 
非0 
函数执行失败 
备注 
 
 
3.19.5 MT_ 
Help_Encoder_Line_Real_To_Steps 
功能描述：直线移动台物理量(速度、加速度、减速度等)到闭环编码器脉冲数的转换计算，编码器安装在电机上的用本转换函数，编码器的电子4倍频在转换时已经考虑，用户无需再考虑。 
 
VC 
INT32 
MT_ 
Help_Encoder_Line_Real_To_Steps(double APitch,double ALineRatio,INT32 
ALineCount,double 
AValue) 
VB 
Function 
MT_ 
Help_Encoder_Line_Real_To_Steps (ByVal APitch As Double,ByVal ALineRatio 
As Double,ByVal ALineCount As Long,ByVal AValue As Double) As Long 
Delphi 
function 
MT_ 
Help_Encoder_Line_Real_To_Steps(APitch:Double;ALineRatio:Double;ALineCount:Integer;AValue:Double):Integer 
C# 
public static extern int 
MT_ 
Help_Encoder_Line_Real_To_Steps(double APitch,double 
ALineRatio,Int32 ALineCount,double AValue) 
 
APitch 
直线台的螺距，即电机旋转一圈的位移，单位mm 
 
ALineRatio 
直线台的传动比，没有传动为1 
 
ALineCount 
编码器线数，例如1000,1024等 
 
AValue 
物理量，mm,mm/s,mm/s2 
函数返回 
32位整形 
转换后的编码器脉冲数 
非0 
函数执行失败 
备注 
 
 
3.19.6 MT_ 
Help_Encoder_Circle_Real_To_Steps 
功能描述：旋转台物理量(速度、加速度、减速度等)到闭环编码器脉冲数的转换计算，编码器安装在电机上的用本转换函数，编码器的电子4倍频在转换时已经考虑，用户无需再考虑。 
 
VC 
INT32 
MT_ 
Help_Encoder_Circle_Real_To_Steps(double ACircleRatio,INT32 
ALineCount,double AValue) 
VB 
Function 
MT_ 
Help_Encoder_Circle_Real_To_Steps(ByVal ACircleRatio As 
Double,ByVal ALineCount As Long,ByVal AValue As Double) As Long 
Delphi 
function 
MT_ 
Help_Encoder_Circle_Real_To_Steps( 
 
ACircleRatio:Double;ALineCount:Integer;AValue:Double):Integer 
C# 
public static extern int 
MT_ 
Help_Encoder_Circle_Real_To_Steps(double 
ACircleRatio,Int32 ALineCount,double AValue) 
 
ACircleRatio 
旋转台的传动比 
 
ALineCount 
编码器线数，例如1000,1024等 
 
AValue 
物理量，°,°/s,°/s2 
函数返回 
32位整形 
转换后的步数 
非0 
函数执行失败 
备注 
 
 
3.19.7 MT_ 
Help_Encoder_Line_Steps_To_Real 
功能描述：直线移动台编码器脉冲转物理量(速度、加速度、减速度等)，编码器安装在电机上的用本转换函数，编码器的电子4倍频在转换时已经考虑，用户无需再考虑，输入4倍频后的脉冲数即可。 
 
VC 
double 
MT_ 
Help_Encoder_Line_Steps_To_Real(double APitch,double 
ALineRatio,INT32 ALineCount,INT32 AValue) 
VB 
Function 
MT_ 
Help_Encoder_Line_Steps_To_Real (ByVal APitch As Double,ByVal 
ALineRatio As Double,ByVal ALineCount As Long,ByVal AValue As Long) As Long 
Delphi 
function MT_ 
Help_Encoder_Line_Steps_To_Real( 
 
APitch:Double;ALineRatio:Double;ALineCount:Integer;AValue:Integer):Double 
C# 
public static extern double 
MT_ 
Help_Encoder_Line_Steps_To_Real(double 
APitch,double ALineRatio,Int32 ALineCount,Int32 AValue) 
输入 
APitch 
直线台的螺距，即电机旋转一圈的位移，单位mm 
 
ALineRatio 
直线台的传动比，没有传动为1 
 
ALineCount 
编码器线数，例如1000,1024等 
 
AValue 
脉冲数 
函数返回 
双精度 
转换后的物理量，mm,mm/s,mm/s2 
非0 
函数执行失败 
备注 
 
 
3.19.8 MT_ 
Help_Encoder_Circle_Steps_To_Real 
功能描述：旋转台编码器脉冲转物理量(速度、加速度、减速度等) 
编码器安装在电机上的用本转换函数，编码器的电子4倍频在转换时已经考虑，用户无需再考虑，输入4倍频后的脉冲数即可。 
 
VC 
double 
MT_ 
Help_Encoder_Circle_Steps_To_Real(double ACircleRatio,INT32 
ALineCount,INT32 AValue) 
VB 
Function 
MT_ 
Help_Encoder_Circle_Steps_To_Real Lib "MT_API.dll" (ByVal 
ACircleRatio As Double,ByVal ALineCount As 
Long,ByVal AValue As Long) As Long 
Delphi 
function MT_ 
Help_Encoder_Circle_Steps_To_Real( 
 
ACircleRatio:Double;ALineCount:Integer;AValue:Integer):Double 
C# 
public static extern int 
MT_ 
Help_Encoder_Circle_Steps_To_Real(double 
ACircleRatio,Int32 ALineCount,Int32 AValue) 
 
ACircleRatio 
旋转台的传动比 
 
ALineCount 
编码器线数，例如1000,1024等 
 
AValue 
脉冲数 
函数返回 
双精度浮点 
转换后的物理量°,°/s,°/s2 
非0 
函数执行失败 
备注 
 
 
3.19.9 MT_ 
Help_Grating_Line_Real_To_Steps 
功能描述：直线移动台物理量(速度、加速度、减速度等)到闭环光栅尺脉冲数的转换计算，光栅尺安装在机械结构上，光栅尺的电子4倍频在转换时已经考虑，用户无需再考虑。 
 
VC 
INT32 
MT_ 
Help_Grating_Line_Real_To_Steps(double AUnit_um,double AValue) 
VB 
Function 
MT_ 
Help_Grating_Line_Real_To_Steps (ByVal AUnit_um As Double,ByVal AValue 
As Double) As Long 
Delphi 
function 
MT_ 
Help_Grating_Line_Real_To_Steps(AUnit_um:Double;AValue:Double):Integer 
C# 
public static extern int 
MT_ 
Help_Grating_Line_Real_To_Steps(double AUnit_um,double 
AValue) 
 
AUnit_um 
光栅尺的栅距，单位为um 
 
AValue 
物理量，mm,mm/s,mm/s2 
函数返回 
32位整形 
转换后的光栅尺脉冲数 
非0 
函数执行失败 
备注 
 
 
3.19.10 MT_ 
Help_Grating_Circle_Real_To_Steps 
功能描述：旋转台物理量(速度、加速度、减速度等)到光栅尺脉冲数的转换计算，光栅尺安装在机械结构上，光栅尺的电子4倍频在转换时已经考虑，用户无需再考虑。 
 
VC 
INT32 
MT_ 
Help_Grating_Circle_Real_To_Steps(INT32 ALineCount,double AValue) 
VB 
Function 
MT_ 
Help_Grating_Circle_Real_To_Steps (ByVal 
ALineCount As Long,ByVal 
AValue As Double) As Long 
Delphi 
function MT_ 
Help_Grating_Circle_Real_To_Steps(ALineCount:Integer;AValue:Double):Integer 
C# 
public static extern int 
MT_ 
Help_Grating_Circle_Real_To_Steps(Int32 
ALineCount,double AValue) 
 
ALineCount 
光栅尺整圈的光栅线数，例如64800等 
 
AValue 
物理量，°,°/s,°/s2 
函数返回 
32位整形 
转换后的步数 
非0 
函数执行失败 
备注 
 
 
3.19.11 MT_ 
Help_Grating_Line_Steps_To_Real 
功能描述：直线移动台光栅尺脉冲转物理量(速度、加速度、减速度等)，光栅尺安装在机械上，光栅尺的电子4倍频在转换时已经考虑，用户无需再考虑，输入4倍频后的脉冲数即可。 
 
VC 
double 
MT_ 
Help_Grating_Line_Steps_To_Real(double AUnit_um,INT32 AValue) 
VB 
Function 
MT_ 
Help_Grating_Line_Steps_To_Real (ByVal AUnit_um As Double,ByVal 
AValue As Long) As Long 
Delphi 
function MT_ 
Help_Grating_Line_Steps_To_Real(AUnit_um:Double;AValue:Integer):Double 
C# 
public static extern double 
MT_ 
Help_Grating_Line_Steps_To_Real(double 
AUnit_um,Int32 AValue) 
输入 
AUnit_um 
光栅尺的栅距，单位为um 
 
AValue 
光栅尺脉冲数 
函数返回 
双精度 
转换后的物理量，mm,mm/s,mm/s2 
非0 
函数执行失败 
备注 
 
 
3.19.12 MT_ 
Help_Grating_Circle_Steps_To_Real 
功能描述：光栅尺脉冲转物理量(速度、加速度、减速度等) 
光栅尺安装在机械结构用本转换函数，编码器的电子4倍频在转换时已经考虑，用户无需再考虑，输入4倍频后的脉冲数即可。 
 
VC 
double 
MT_ 
Help_Grating_Circle_Steps_To_Real(INT32 ALineCount,INT32 AValue) 
VB 
Function 
MT_ 
Help_Grating_Circle_Steps_To_Real (ByVal ALineCount As Long,ByVal 
AValue As Long) As Long 
Delphi 
function 
MT_ 
Help_Grating_Circle_Steps_To_Real(ALineCount:Integer;AValue:Integer):Double 
C# 
public static extern int 
MT_ 
Help_Grating_Circle_Steps_To_Real(Int32 
ALineCount,Int32 AValue) 
 
ALineCount 
光栅尺整圈的光栅线数，例如64800等 
 
AValue 
脉冲数 
函数返回 
双精度浮点 
转换后的物理量°,°/s,°/s2 
非0 
函数执行失败 
备注 
 
 
3.19.13 MT_ 
Help_Encoder_Factor 
功能描述：编码器闭环减速系数估算。 

VC 
float 
MT_ 
Help_Encoder_Factor(double AStepAngle,INT32 ADiv,INT32 ALineCount) 
VB 
Function 
MT_ 
Help_Encoder_Factor(ByVal AStepAngle As Double,ByVal ADiv As 
Long,ByVal ALineCount As 
Long) As Single 
Delphi 
function 
MT_ 
Help_Encoder_Factor(AStepAngle:Double;ADiv:Integer; 
 
ALineCount:Integer):Single 
C# 
public static extern 
Single 
MT 
Help__Grating_Line_Steps_To_Real(double 
AUnit_um,Int32 AValue) 
输入 
AStepAngle 
走一步电机旋转角度，例如两项步进电机一般为1.8° 
 
ADiv 
驱动器细分数，一般为2,4,8,16等等 
 
ALineCount 
编码器线数，例如1000,1024等 
函数返回 
单精度 
减速系数 
非0 
函数执行失败 
备注 
 
 
3.19.14 MT_ 
Help_Grating_Line_Factor 
功能描述：光栅尺直线运动闭环减速系数估算。 
 
VC 
float 
MT_ 
Help_Grating_Line_Factor(double AStepAngle,INT32 ADiv,double 
APitch,double ALineRatio,double 
AUnit_um) 
VB 
Function 
MT_ 
Help_Grating_Line_Factor (ByVal AStepAngle As Double,ByVal ADiv As 
Long,ByVal APitch As Double,ByVal 
ALineRatio As Double,ByVal AUnit_um As 
Double) As 
Single 
Delphi 
function MT_ 
Help_Grating_Line_Factor(AStepAngle:Double;ADiv:Integer; 
 
APitch:Double;ALineRatio:Double;AUnit_um:Double):Single 
C# 
public static extern Single 
MT_ 
Help_Grating_Line_Factor(double AStepAngle,Int32 
ADiv,double APitch,double ALineRatio,double AUnit_um) 
输入 
AStepAngle 
走一步电机旋转角度，例如两项步进电机一般为1.8° 
 
ADiv 
驱动器细分数，一般为2,4,8,16等等 
 
APitch 
直线台的螺距，即电机旋转一圈的位移，单位mm 
 
ALineRatio 
直线台的传动比，没有传动为1 
 
AUnit_um 
光栅尺的珊距，单位为um 
函数返回 
单精度 
减速系数 
非0 
函数执行失败 
备注 
 
 
3.19.15 MT_Help_Grating_Circle_Factor 
功能描述：光栅尺旋转运动闭环减速系数估算。 
 
VC 
float 
MT_ 
Help_Grating_Circle_Factor(double AStepAngle,INT32 
ADiv,double 
ACircleRatio,INT32 ALineCount) 
VB 
Function 
MT_ 
Help_Grating_Circle_Factor (ByVal AStepAngle As Double,ByVal ADiv 
As Long,ByVal ACircleRatio As Double,ByVal ALineCount As Long) As 
Single 
Delphi 
function 
MT_ 
Help_Grating_Circle_Factor(AStepAngle:Double;ADiv:Integer; 
 
ACircleRatio:Double;ALineCount:Integer):Single 
C# 
public static extern Single 
MT_ 
Help_Grating_Circle_Factor(double AStepAngle,Int32 
ADiv,double ACircleRatio,Int32 ALineCount) 
输入 
AStepAngle 
走一步电机旋转角度，例如两项步进电机一般为1.8° 
 
ADiv 
驱动器细分数，一般为2,4,8,16等等 
 
ACircleRatio 
旋转台的传动比 
 
ALine_Count 
光栅尺整圈的光栅数目 
函数返回 
单精度 
减速系数 
非0 
函数执行失败 
备注 
 
 
 
 
 

