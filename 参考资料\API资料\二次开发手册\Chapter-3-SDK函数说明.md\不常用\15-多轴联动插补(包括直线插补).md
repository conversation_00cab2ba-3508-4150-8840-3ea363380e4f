3.15 多轴联动插补(包括直线插补) 

备注：推荐优先使用带X的多轴插补函数 

3.15.1 
MT_Set_Axis_Line_Axis 
功能描述：设置指定插补模块中参与插补的运动轴。不同的板卡有不同数量的插补模块，多个模块可以同时进行多个直线插补，每个插补模块可以随意指定任意2轴参与插补，同时进行插补的模块不可以有相同的插补轴。例如，模块0用轴2，轴3插补，模块1用轴1轴2插补，这两个模块独立动作时没有问题，不可以同时动作。 
 
VC 
INT32 
MT_Set_Axis_Line_Axis 
(WORD 
AObj,INT32 
Axis_ID0,INT32 Axis_ID1) 
VB 
Function 
MT_Set_Axis_Line_Axis 
(ByVal AObj As Integer, ByVal 
Axis_ID0 
As 
Long, 
ByVal 
Axis_ID1 
As 
Long) As Long 
Delphi 
function 
MT_Set_Axis_Line_Axis 
(AObj:Word;Axis_ID0,Axis_ID1:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_Axis 
(UInt16 AObj, 
Int32 
Axis_ID0, 
Int32 
Axis_ID1); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Axis_ID0 
本插补模块的第一个插补轴序号 
 
Axis_ID1 
本插补模块的第二个插补轴序号 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
插补轴的序号从0开始，N的轴，序号为0,1,2,3…N-1 
3.15.2 MT_Set_Axis_Line_Acc 
功能描述：设置指定插补模块的加速度。 
 
VC 
INT32 
MT_Set_Axis_Line_Acc 
(WORD AObj,INT32 
Value) 
VB 
Function 
MT_Set_Axis_Line_Acc 
(ByVal AObj As Integer, ByVal 
Value 
As Long) As Long 
Delphi 
function 
MT_Set_Axis_Line_Acc 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_Acc 
(UInt16 AObj, 
Int32 
Value); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Value 
本插补模块的加速度 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.15.3 MT_Set_Axis_Line_Dec 
功能描述：设置指定插补模块的加速度。 
 
VC 
INT32 
MT_Set_Axis_Line_Dec 
(WORD AObj,INT32 Value) 
VB 
Function 
MT_Set_Axis_Line_Dec 
(ByVal AObj As Integer, ByVal 
Value 
As Long) 
As Long 
Delphi 
function 
MT_Set_Axis_Line_Dec 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_Dec 
(UInt16 AObj, 
Int32 
Value); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Value 
本插补模块的减速度 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.15.4 MT_Set_Axis_Line_V 
功能描述：设置指定插补模块的速度。 
 
VC 
INT32 
MT_Set_Axis_Line_V 
(WORD AObj,INT32 Value) 
VB 
Function 
MT_Set_Axis_Line_V 
(ByVal AObj As Integer, ByVal 
Value 
As Long) As Long 
Delphi 
function 
MT_Set_Axis_Line_V 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_V 
(UInt16 AObj, 
Int32 
Value); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Value 
本插补模块的速度 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.15.5 MT_Set_Axis_Line_Run 
功能描述：在设置插补的参数后，启动指定的插补模块，在MT_Set_Axis_Line_Rel和MT_Set_Axis_Line_Abs后执行，可用组合指令MT_Set_Axis_Line_Run_Rel和MT_Set_Axis_Line_Run_Abs代替 
 
VC 
INT32 
MT_Set_Axis_Line_Run 
(WORD AObj) 
VB 
Function 
MT_Set_Axis_Line_Run 
(ByVal AObj As Integer) As Long 
Delphi 
function 
MT_Set_Axis_Line_Run 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_Run 
(UInt16 AObj); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.15.6 MT_Set_Axis_Line_Stop 
功能描述：减速停止插补轴 
 
VC 
INT32 
MT_Set_Axis_Line_Stop 
(WORD AObj) 
VB 
Function 
MT_Set_Axis_Line_Stop 
(ByVal AObj As Integer) As Long 
Delphi 
function 
MT_Set_Axis_Line_Stop 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_Stop 
(UInt16 AObj); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.15.7 MT_Set_Axis_Line_Halt 
功能描述：立即停止插补轴 
 
VC 
INT32 
MT_Set_Axis_Line_Halt 
(WORD AObj) 
VB 
Function 
MT_Set_Axis_Line_Halt 
(ByVal AObj As Integer) As Long 
Delphi 
function 
MT_Set_Axis_Line_Halt 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_Halt 
(UInt16 AObj); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.15.8 MT_Set_Axis_Line_Rel 
功能描述：相对当前的位置设置直线插补的目标位置，设置目标后插补轴不动作，需要用MT_Set_Axis_Line_Run启动。 
 
VC 
INT32 
MT_Set_Axis_Line_Rel 
(WORD AObj,INT32 
Axis_Target0,INT32 Axis_Target1) 
VB 
Function 
MT_Set_Axis_Line_Rel 
(ByVal AObj As Integer, ByVal 
Axis_Target0 
As 
Long, 
ByVal 
Axis_Target1 
As 
Long) As Long 
Delphi 
Function 
MT_Set_Axis_Line_Rel(AObj:Word;Axis_Target0,Axis_Target1:Integer):Integer 
C# 
public static extern int 
MT_Set_Axis_Line_Rel(UInt16 AObj, 
Int32 
Axis_Target0, 
Int32 
Axis_Target1); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Axis_Target0 
本插补模块的第一个插补轴相对移动的距离 
 
Axis_Target1 
本插补模块的第二个插补轴相对移动的距离 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.15.9 MT_Set_Axis_Line_Abs 
功能描述：设置直线插补的绝对目标位置，设置目标后插补轴不动作，需要用MT_Set_Axis_Line_Run启动。 
 
VC 
INT32 
MT_Set_Axis_Line_Abs 
(WORD AObj,INT32 
Axis_Target0,INT32 Axis_Target1) 
VB 
Function 
MT_Set_Axis_Line_Abs 
(ByVal AObj As 
Integer, ByVal 
Axis_Target0 
As 
Long, 
ByVal 
Axis_Target1 
As 
Long) As Long 
Delphi 
Function 
MT_Set_Axis_Line_Abs(AObj:Word;Axis_Target0,Axis_Target1:Integer):Integer 
C# 
public static extern int 
MT_Set_Axis_Line_Abs(UInt16 AObj, 
Int32 
Axis_Target0, 
Int32 
Axis_Target1); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Axis_Target0 
本插补模块的第一个插补轴需要移动到的绝对位置 
 
Axis_Target1 
本插补模块的第二个插补轴需要移动到的绝对位置 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.15.10 MT_Set_Axis_Line_Run_Rel 
功能描述：相对当前的位置设置直线插补的目标位置,插补轴立即动作。 
 
VC 
INT32 
MT_Set_Axis_Line_Run_Rel 
(WORD AObj,INT32 
Axis_Target0,INT32 
Axis_Target1) 
VB 
Function 
MT_Set_Axis_Line_Run_Rel 
(ByVal AObj As Integer, ByVal 
Axis_Target0 
As 
Long, ByVal 
Axis_Target1 
As 
Long) As Long 
Delphi 
Function 
MT_Set_Axis_Line_Run_Rel(AObj:Word;Axis_Target0,Axis_Target1:Integer):Integer 
C# 
public static extern int 
MT_Set_Axis_Line_Run_Rel(UInt16 AObj, 
Int32 
Axis_Target0, 
Int32 
Axis_Target1); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Axis_Target0 
本插补模块的第一个插补轴相对移动的距离 
 
Axis_Target1 
本插补模块的第二个插补轴相对移动的距离 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.15.11 MT_Set_Axis_Line_Run_Abs 
功能描述：设置直线插补的绝对目标位置,插补轴立即动作。 
 
VC 
INT32 
MT_Set_Axis_Line_Run_Abs 
(WORD AObj,INT32 
Axis_Target0,INT32 
Axis_Target1) 
VB 
Function 
MT_Set_Axis_Line_Run_Abs 
(ByVal AObj As 
Integer, ByVal 
Axis_Target0 
As 
Long, ByVal 
Axis_Target1 
As 
Long) As Long 
Delphi 
Function 
MT_Set_Axis_Line_Run_Abs(AObj:Word;Axis_Target0,Axis_Target1:Integer):Integer 
C# 
public static extern int 
MT_Set_Axis_Line_Run_Abs(UInt16 AObj, 
Int32 
Axis_Target0, 
Int32 
Axis_Target1); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Axis_Target0 
本插补模块的第一个插补轴需要移动到的绝对位置 
 
Axis_Target1 
本插补模块的第二个插补轴需要移动到的绝对位置 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.15.12 MT_Get_Axis_Line_Num 
功能描述：读取板卡上直线插补模块的数量 
 
VC 
INT32 
MT_Get_Axis_Line_Num 
(INT32* pValue) 
VB 
Function 
MT_Get_Axis_Line_Num 
(ByRef Value As Long) As Long 
Delphi 
function 
MT_Get_Axis_Line_Num 
(pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Line_Num 
(ref Int32 Value); 
输入参数 
无 
 
输出参数 
Value 
直线插补模块的数量 
函数返回 
0 
函数执行成功，读取到通道数有效 
非0 
函数执行失败，读取到通道数无效 
备注 
模块的序号从0开始，N的通道，序号为0,1,2,3…N-1 
 
3.15.13 MT_Get_Axis_Line_Status 
功能描述：读取指定直线插补模块的当前插补状态 
 
VC 
INT32 
MT_Get_Axis_Line_Status 
(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_Line_Status 
(ByVal AObj As Integer, ByRef Value As 
Long) As 
Long 
Delphi 
function 
MT_Get_Axis_Line_Status 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Line_Status 
(UInt16 AObj, ref 
Int32 
Value); 
输入参数 
AObj 
插补模块的序号，不是插补轴的序号 
输出参数 
Value 
指定模块的插补状态，0为插补结束，1为正在插补中 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
 
3.15.14 MT_Get_Axis_Line_Acc 
功能描述：读取指定直线插补模块的当前加速度 
 
VC 
INT32 
MT_Get_Axis_Line_Acc 
(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_Line_Acc 
(ByVal AObj As Integer, ByRef Value As 
Long) As 
Long 
Delphi 
function 
MT_Get_Axis_Line_Acc 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Line_Acc 
(UInt16 AObj, ref 
Int32 
Value); 
输入参数 
AObj 
插补模块的序号，不是插补轴的序号 
输出参数 
Value 
指定模块的加速度 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
3.15.15 MT_Get_Axis_Line_Dec 
功能描述：读取指定直线插补模块的当前减速度 
 
VC 
INT32 
MT_Get_Axis_Line_Dec 
(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_Line_Dec 
(ByVal AObj As Integer, ByRef Value As 
Long) As 
Long 
Delphi 
function 
MT_Get_Axis_Line_Dec 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Line_Dec 
(UInt16 AObj, ref 
Int32 
Value); 
输入参数 
AObj 
插补模块的序号，不是插补轴的序号 
输出参数 
Value 
指定模块的减速度 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
3.15.16 MT_Get_Axis_Line_V 
功能描述：读取指定直线插补模块的当前速度 
 
VC 
INT32 
MT_Get_Axis_Line_V 
(WORD AObj,INT32* pValue); 
VB 
Function 
MT_Get_Axis_Line_V 
(ByVal AObj As Integer, ByRef Value As 
Long) As Long 
Delphi 
function 
MT_Get_Axis_Line_V 
(AObj:Word;pValue:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Line_V 
(UInt16 AObj, ref 
Int32 
Value); 
输入参数 
AObj 
插补模块的序号，不是插补轴的序号 
输出参数 
Value 
指定模块的速度 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
3.15.17 MT_Get_Axis_Line_Axis 
功能描述：读取指定直线插补模块的当前的插补轴 
 
VC 
INT32 
MT_Get_Axis_Line_Axis 
(WORD AObj,INT32* pID0,INT32* pID1); 
VB 
Function 
MT_Get_Axis_Line_Axis 
(ByVal AObj As Integer, ByRef 
pID0 
As 
Long, ByRef 
pID1 
As 
Long) As Long 
Delphi 
function 
MT_Get_Axis_Line_Axis 
(AObj:Word; 
pID0,pID1:PInteger):Integer; 
C# 
public static extern int 
MT_Get_Axis_Line_Axis 
(UInt16 AObj, ref 
Int32 
ID0, ref 
Int32 
ID1); 
输入参数 
AObj 
插补模块的序号，不是插补轴的序号 
输出参数 
pID0 
指定模块的第一个插补轴 
 
pID1 
指定模块的第二个插补轴 
函数返回 
0 
函数执行成功，读取到的数据有效 
非0 
函数执行失败，读取到的数据无效 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
插补轴的序号从0开始，N的运动轴，序号为0,1,2,3…N-1 
 
3.15.18 MT_Set_Axis_Line_V_Start 
功能描述：设置指定插补模块的起始速度(最快轴速度)。 
 
VC 
INT32 
MT_Set_Axis_Line_V_Start 
(WORD AObj,INT32 Value) 
VB 
Function 
MT_Set_Axis_Line_V 
_Start(ByVal AObj As Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function 
MT_Set_Axis_Line_V 
_Start(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_V_Start 
(UInt16 AObj, 
Int32 
Value); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
Value 
本插补模块的速度 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
3.15.19 MT_Set_Axis_Line_X_Count 
功能描述：设置参与联动插补的轴数。 
 
VC 
INT32 
MT_Set_Axis_Line_X_Count 
(WORD AObj,INT32 Value) 
VB 
Function 
MT_Set_Axis_Line_X_Count 
(ByVal AObj As 
Integer, ByVal 
Value As Long) As 
Long 
Delphi 
function 
MT_Set_Axis_Line_X_Count(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_X_Count(UInt16 AObj, 
Int32 
Value); 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 

Value 
参数插补的轴的数量，范围2-X，X为控制卡的轴数 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
3.15.20 MT_Set_Axis_Line_X_Axis 
功能描述：设置参与联动插补的轴和在插补组中的序号,设置完序号和插补目标后，用MT_Set_Axis_Line_Run启动。 
 
VC 
INT32 
MT_Set_Axis_Line_X_Axis(WORD AObj,INT32 AAxisID,INT32 AAxis) 
VB 
Function 
MT_Set_Axis_Line_X_Axis (ByVal AObj As Integer,ByVal AAxisID As 
Long,ByVal AAxis As Long) As Long 
Delphi 
function 
MT_Set_Axis_Line_X_ 
Axis 
(AObj:Word;AAxisID:Integer;AAxis:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_X_Axis(UInt16 
AObj,Int32 AAxisID,Int32 
AAxis) 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
AAxisID 
插补模式的参与轴在插补模式中的序号，不是插补轴的序号，范围为MT_Set_Axis_Line_X_Count中设置的0..Count-1 
 
AAxis 
插补轴的序号，0..M-1 M为轴数 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
3.15.21 MT_Set_Axis_Line_X_Target_Rel 
功能描述：设置参与联动插补的轴相对的插补目标, 
设置完序号和插补目标后，用MT_Set_Axis_Line_Run启动。 
 
VC 
INT32 
MT_Set_Axis_Line_X_ 
Target_Rel 
(WORD AObj,INT32 AAxisID,INT32 ATarget) 
VB 
Function 
MT_Set_Axis_Line_X_ 
Target_Rel 
(ByVal AObj As Integer,ByVal AAxisID As 
Long,ByVal ATarget 
As Long) As Long 
Delphi 
function 
MT_Set_Axis_Line_X_ Target_Rel 
(AObj:Word; 
AAxisID:Integer;ATarget:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_X_ 
Target_Rel 
(UInt16 AObj,Int32 
AAxisID,Int32 ATarget) 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
AAxisID 
插补模式的参与轴在插补模式中的序号，不是插补轴的序号，范围为MT_Set_Axis_Line_X_Count中设置的0..Count-1 
 
ATarget 
相对插补位置 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
3.15.22 MT_Set_Axis_Line_X_Target_Abs 
功能描述：设置参与联动插补的轴绝对的插补目标, 
设置完序号和插补目标后，用MT_Set_Axis_Line_Run启动。 
 
VC 
INT32 
MT_Set_Axis_Line_X_ 
Target_Abs 
(WORD AObj,INT32 AAxisID,INT32 ATarget) 
VB 
Function 
MT_Set_Axis_Line_X_ 
Target_Abs 
(ByVal AObj As Integer,ByVal AAxisID As 
Long,ByVal ATarget 
As Long) As Long 
Delphi 
function 
MT_Set_Axis_Line_X_ Target_Abs 
(AObj:Word; 
AAxisID:Integer;ATarget:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_X_ 
Target_Abs 
(UInt16 AObj,Int32 
AAxisID,Int32 ATarget) 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
AAxisID 
插补模式的参与轴在插补模式中的序号，不是插补轴的序号，范围为MT_Set_Axis_Line_X_Count中设置的0..Count-1 
 
ATarget 
绝对插补位置 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
3.15.23 MT_Set_Axis_Line_X_Run_Rel 
功能描述：相对方式启动联动插补，相当于MT_Set_Axis_Line_X_Count,MT_Set_Axis_Line_X_Axis,MT_Set_Axis_Line_X_Target_Rel和MT_Set_Axis_Line_Run的组合。 
 
VC 
INT32 
MT_Set_Axis_Line_X_Run_Rel(WORD AObj,INT32 AAxis_Num,INT32* 
pAxis,INT32* pTarget) 
VB 
Function 
MT_Set_Axis_Line_X_Run_Rel (ByVal 
AObj As Integer,ByVal AAxis_Num As 
Long,ByRef pAxis As Long,ByRef pTarget As Long) As Long 
Delphi 
function 
MT_Set_Axis_Line_X_Run_Rel 
(AObj:Word; 
AAxis_Num:Integer;pAxis:PInteger;pTarget:PInteger):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_X_Run_Rel(UInt16 AObj,Int32 
AAxis_Num,ref Int32 pAxis,ref Int32 pTarget) 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
AAxis_Num 
参与插补的轴的数量2-X 
 
pAxis 
参与插补轴的序号的数组，需要传入一个数组，这个数组的长度要大于等于参与插补轴的数量 
 
ATarget 
参与插补轴的相对目标的数组，需要传入一个数组，这个数组
的长度要大于等于参与插补轴的数量 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
3.15.24 MT_Set_Axis_Line_X_Run_Abs 
功能描述：绝对方式启动联动插补，相当于MT_Set_Axis_Line_X_Count,MT_Set_Axis_Line_X_Axis,MT_Set_Axis_Line_X_Target_Abs和MT_Set_Axis_Line_Run的组合。 
 
VC 
INT32 
MT_Set_Axis_Line_X_Run_Abs(WORD 
AObj,INT32 AAxis_Num,INT32* 
pAxis,INT32* pTarget) 
VB 
Function 
MT_Set_Axis_Line_X_Run_Abs 
(ByVal AObj As Integer,ByVal AAxis_Num As 
Long,ByRef pAxis As Long,ByRef pTarget As Long) As Long 
Delphi 
function 
MT_Set_Axis_Line_X_Run_Abs 
(AObj:Word; 
AAxis_Num:Integer;pAxis:PInteger;pTarget:PInteger):Integer; 
C# 
public static extern int 
MT_Set_Axis_Line_X_Run_Abs(UInt16 AObj,Int32 
AAxis_Num,ref Int32 pAxis,ref Int32 pTarget) 
输入参数 
AObj 
插补模块序号，不是插补轴的序号 
 
AAxis_Num 
参与插补的轴的数量2-X 
 
pAxis 
参与插补轴的序号的数组，需要传入一个数组，这个数组的长度要大于等于参与插补轴的数量 
 
ATarget 
参与插补轴的绝对目标的数组，需要传入一个数组，这个数组的长度要大于等于参与插补轴的数量 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
插补模块的序号从0开始，N的插补模块，序号为0,1,2,3…N-1 
 
