3.9 软件限位 
 
3.9.1 MT_Set_Axis_Software_Limit_Neg_Value 
功能描述：设置位置模式下的软件限位功能负限位的值 
 
VC 
INT32 
MT_Set_Axis_Software_Limit_Neg_Value 
(WORD AObj,INT32 
Value); 
VB 
Function 
MT_Set_Axis_Software_Limit_Neg_Value 
(ByVal AObj As Integer, ByVal 
Value As Long) As Long 
Delphi 
function 
MT_Set_Axis_Software_Limit_Neg_Value 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Software_Limit_Neg_Value 
(UInt16 AObj,Int32 
Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的位置模式下软件负限位值 
单位为 
电机步数 
可正可负 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
在软件限位有效的情况下本值才起作用 
负软限位值要小于正软限位值 
 
3.9.2 MT_Set_Axis_Software_Limit_Pos_Value 
功能描述：设置位置模式下的软件限位功能正限位的值 
 
VC 
INT32 
MT_Set_Axis_Software_Limit_Pos_Value 
(WORD AObj,INT32 
Value); 
VB 
Function 
MT_Set_Axis_Software_Limit_Pos_Value 
(ByVal AObj As Integer, ByVal 
Value As Long) As Long 
Delphi 
function 
MT_Set_Axis_Software_Limit_Pos_Value 
(AObj:Word;Value:Integer):Integer; 
C# 
public static extern int 
MT_Set_Axis_Software_Limit_Pos_Value 
(UInt16 AObj,Int32 
Value); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
Value 
设置指定轴的位置模式下软件正限位值 
单位为 
电机步数 
可正可负 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
电机轴序号的函数需要注意不要越界 
在软件限位有效的情况下本值才起作用 
负软限位值要小于正软限位值 
 
3.9.3 MT_Set_Axis_Software_Limit_Enable 
功能描述：使能指定轴的软件限位模式 
 
VC 
INT32 
MT_Set_Axis_Software_Limit_Enable 
(WORD AObj) 
VB 
Function 
MT_Set_Axis_Software_Limit_Enable 
(ByVal AObj As Integer) As Long 
Delphi 
function 
MT_Set_Axis_Software_Limit_Enable 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Software_Limit_Enable 
(UInt16 AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
 
3.9.4 MT_Set_Axis_Software_Limit_Disable 
功能描述：禁止指定轴的软件限位模式 
 
VC 
INT32 
MT_Set_Axis_Software_Limit_Disable 
(WORD AObj) 
VB 
Function 
MT_Set_Axis_Software_Limit_Disable 
(ByVal AObj As Integer) 
As Long 
Delphi 
function 
MT_Set_Axis_Software_Limit_Disable 
(AObj:Word):Integer; 
C# 
public static extern int 
MT_Set_Axis_Software_Limit_Disable 
(UInt16 AObj); 
输入参数 
AObj 
电机控制轴序号 
输出参数 
无 
 
函数返回 
0 
函数执行成功 
非0 
函数执行失败 
备注 
电机轴的序号从0开始，N轴的卡，序号为0,1,2,3…N-1 
用到电机轴序号的函数需要注意不要越界 
 
