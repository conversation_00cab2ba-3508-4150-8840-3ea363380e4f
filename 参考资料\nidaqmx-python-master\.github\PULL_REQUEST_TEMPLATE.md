- [ ] This contribution adheres to [CONTRIBUTING.md](https://github.com/ni/nidaqmx-python/blob/master/CONTRIBUTING.md).

TODO: Check the above box with an 'x' indicating you've read and followed [CONTRIBUTING.md](https://github.com/ni/nidaqmx-python/blob/master/CONTRIBUTING.md).

- [ ] I've updated [CHANGELOG.md](https://github.com/ni/nidaqmx-python/blob/master/CHANGELOG.md) if applicable.

TODO: Check the above box with an 'x' if considered if there were any and then documented client facing changes in [CHANGELOG.md](https://github.com/ni/nidaqmx-python/blob/master/CHANGELOG.md). Strike through if this is not a relevant client facing change.

- [ ] I've added tests applicable for this pull request

TODO: Check the above box with an 'x' indicating you have considered and added any applicable system or unit tests. Strike through if you considered and decided additional tests were not warranted.

### What does this Pull Request accomplish?

TODO: Include high-level description of the changes in this pull request.

### Why should this Pull Request be merged?

TODO: Justify why this contribution should be part of the project.

### What testing has been done?

TODO: Detail what testing has been done to ensure this submission meets requirements.
