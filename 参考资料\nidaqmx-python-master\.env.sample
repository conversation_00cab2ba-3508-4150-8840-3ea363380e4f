# This is a sample nidaqmx-python configuration file.

# To use it:
# - Copy this file to your application's directory or one of its parent directories
#   (such as the root of your Git repository).
# - Rename it to `.env`.
# - Uncomment and edit the options you want to change.
# - Restart any affected applications or services.

# By default, nidaqmx-python on Windows uses nicai_utf8, the UTF-8 version of the NI-DAQmx C library.
# If that is not available, it falls back to nicaiu, the MBCS (multibyte character set) version. You can override
# this behavior by uncommenting the following option:

# NIDAQMX_C_LIBRARY=nicaiu