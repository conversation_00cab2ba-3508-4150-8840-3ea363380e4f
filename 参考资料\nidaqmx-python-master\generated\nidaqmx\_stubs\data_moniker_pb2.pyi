"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import google.protobuf.any_pb2
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _SidebandStrategy:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _SidebandStrategyEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_SidebandStrategy.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    UNKNOWN: _SidebandStrategy.ValueType  # 0
    GRPC: _SidebandStrategy.ValueType  # 1
    SHARED_MEMORY: _SidebandStrategy.ValueType  # 2
    DOUBLE_BUFFERED_SHARED_MEMORY: _SidebandStrategy.ValueType  # 3
    SOCKETS: _SidebandStrategy.ValueType  # 4
    SOCKETS_LOW_LATENCY: _SidebandStrategy.ValueType  # 5
    HYPERVISOR_SOCKETS: _SidebandStrategy.ValueType  # 6
    RDMA: _SidebandStrategy.ValueType  # 7
    RDMA_LOW_LATENCY: _SidebandStrategy.ValueType  # 8

class SidebandStrategy(_SidebandStrategy, metaclass=_SidebandStrategyEnumTypeWrapper): ...

UNKNOWN: SidebandStrategy.ValueType  # 0
GRPC: SidebandStrategy.ValueType  # 1
SHARED_MEMORY: SidebandStrategy.ValueType  # 2
DOUBLE_BUFFERED_SHARED_MEMORY: SidebandStrategy.ValueType  # 3
SOCKETS: SidebandStrategy.ValueType  # 4
SOCKETS_LOW_LATENCY: SidebandStrategy.ValueType  # 5
HYPERVISOR_SOCKETS: SidebandStrategy.ValueType  # 6
RDMA: SidebandStrategy.ValueType  # 7
RDMA_LOW_LATENCY: SidebandStrategy.ValueType  # 8
global___SidebandStrategy = SidebandStrategy

@typing.final
class BeginMonikerSidebandStreamRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STRATEGY_FIELD_NUMBER: builtins.int
    MONIKERS_FIELD_NUMBER: builtins.int
    strategy: global___SidebandStrategy.ValueType
    @property
    def monikers(self) -> global___MonikerList: ...
    def __init__(
        self,
        *,
        strategy: global___SidebandStrategy.ValueType = ...,
        monikers: global___MonikerList | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["monikers", b"monikers"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["monikers", b"monikers", "strategy", b"strategy"]) -> None: ...

global___BeginMonikerSidebandStreamRequest = BeginMonikerSidebandStreamRequest

@typing.final
class BeginMonikerSidebandStreamResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STRATEGY_FIELD_NUMBER: builtins.int
    CONNECTION_URL_FIELD_NUMBER: builtins.int
    SIDEBAND_IDENTIFIER_FIELD_NUMBER: builtins.int
    BUFFER_SIZE_FIELD_NUMBER: builtins.int
    strategy: global___SidebandStrategy.ValueType
    connection_url: builtins.str
    sideband_identifier: builtins.str
    buffer_size: builtins.int
    def __init__(
        self,
        *,
        strategy: global___SidebandStrategy.ValueType = ...,
        connection_url: builtins.str = ...,
        sideband_identifier: builtins.str = ...,
        buffer_size: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["buffer_size", b"buffer_size", "connection_url", b"connection_url", "sideband_identifier", b"sideband_identifier", "strategy", b"strategy"]) -> None: ...

global___BeginMonikerSidebandStreamResponse = BeginMonikerSidebandStreamResponse

@typing.final
class Moniker(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SERVICE_LOCATION_FIELD_NUMBER: builtins.int
    DATA_SOURCE_FIELD_NUMBER: builtins.int
    DATA_INSTANCE_FIELD_NUMBER: builtins.int
    service_location: builtins.str
    data_source: builtins.str
    data_instance: builtins.int
    def __init__(
        self,
        *,
        service_location: builtins.str = ...,
        data_source: builtins.str = ...,
        data_instance: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["data_instance", b"data_instance", "data_source", b"data_source", "service_location", b"service_location"]) -> None: ...

global___Moniker = Moniker

@typing.final
class MonikerWriteRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MONIKERS_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    @property
    def monikers(self) -> global___MonikerList: ...
    @property
    def data(self) -> global___MonikerValues: ...
    def __init__(
        self,
        *,
        monikers: global___MonikerList | None = ...,
        data: global___MonikerValues | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data", "monikers", b"monikers", "write_data", b"write_data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data", b"data", "monikers", b"monikers", "write_data", b"write_data"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["write_data", b"write_data"]) -> typing.Literal["monikers", "data"] | None: ...

global___MonikerWriteRequest = MonikerWriteRequest

@typing.final
class MonikerReadResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> global___MonikerValues: ...
    def __init__(
        self,
        *,
        data: global___MonikerValues | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data", b"data"]) -> None: ...

global___MonikerReadResponse = MonikerReadResponse

@typing.final
class MonikerList(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    READ_MONIKERS_FIELD_NUMBER: builtins.int
    WRITE_MONIKERS_FIELD_NUMBER: builtins.int
    @property
    def read_monikers(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Moniker]: ...
    @property
    def write_monikers(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Moniker]: ...
    def __init__(
        self,
        *,
        read_monikers: collections.abc.Iterable[global___Moniker] | None = ...,
        write_monikers: collections.abc.Iterable[global___Moniker] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["read_monikers", b"read_monikers", "write_monikers", b"write_monikers"]) -> None: ...

global___MonikerList = MonikerList

@typing.final
class MonikerValues(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VALUES_FIELD_NUMBER: builtins.int
    @property
    def values(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[google.protobuf.any_pb2.Any]: ...
    def __init__(
        self,
        *,
        values: collections.abc.Iterable[google.protobuf.any_pb2.Any] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["values", b"values"]) -> None: ...

global___MonikerValues = MonikerValues

@typing.final
class SidebandWriteRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CANCEL_FIELD_NUMBER: builtins.int
    VALUES_FIELD_NUMBER: builtins.int
    cancel: builtins.bool
    @property
    def values(self) -> global___MonikerValues: ...
    def __init__(
        self,
        *,
        cancel: builtins.bool = ...,
        values: global___MonikerValues | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["values", b"values"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cancel", b"cancel", "values", b"values"]) -> None: ...

global___SidebandWriteRequest = SidebandWriteRequest

@typing.final
class SidebandReadResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CANCEL_FIELD_NUMBER: builtins.int
    VALUES_FIELD_NUMBER: builtins.int
    cancel: builtins.bool
    @property
    def values(self) -> global___MonikerValues: ...
    def __init__(
        self,
        *,
        cancel: builtins.bool = ...,
        values: global___MonikerValues | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["values", b"values"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cancel", b"cancel", "values", b"values"]) -> None: ...

global___SidebandReadResponse = SidebandReadResponse

@typing.final
class StreamWriteResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___StreamWriteResponse = StreamWriteResponse
