<%
import codegen.utilities.enum_helpers as enum_helpers

enums = enum_helpers.get_enums(data)
%>\
# Do not edit this file; it was automatically generated.

from enum import Enum

# Constants
AUTO = -1
CFG_DEFAULT = -1
DEFAULT = -1
READ_ALL_AVAILABLE = -1
WAIT_INFINITELY = -1.0


# Enums
% for name, enum_metadata in enums.items():
class ${name}(Enum):
%   for value in enum_metadata['values']:
    ${value['name']} = ${value['value']}${enum_helpers.get_enum_value_docstring(value['documentation']['description'])}
%   endfor


% endfor
