<%
    from codegen.utilities.text_wrappers import wrap
    from codegen.utilities.function_helpers import get_functions,  get_enums_used
    functions = get_functions(data, "DIChannelCollection")
    enums_used = get_enums_used(functions)
%>\
# Do not edit this file; it was automatically generated.

from nidaqmx.errors import DaqFunctionNotSupportedError
from nidaqmx.task.channels._di_channel import DIChannel
from nidaqmx.task.collections._channel_collection import ChannelCollection
from nidaqmx.utils import unflatten_channel_string
%if enums_used:
from nidaqmx.constants import (
    ${', '.join([c for c in enums_used]) | wrap(4, 4)})
%endif


class DIChannelCollection(ChannelCollection):
    """
    Contains the collection of digital input channels for a DAQmx Task.
    """
    def __init__(self, task_handle, interpreter):
        """
        Do not construct this object directly; instead, construct a nidaqmx.Task and use the task.di_channels property.
        """
        super().__init__(task_handle, interpreter)

    def _create_chan(self, lines, line_grouping, name_to_assign_to_lines=''):
        """
        Creates and returns a DIChannel object.

        Args:
            lines (str): Specifies the names of the lines to use to 
                create virtual channels.
            line_grouping (Optional[nidaqmx.constants.LineGrouping]):
                Specifies how to group digital lines into one or more
                virtual channels.
            name_to_assign_to_lines (Optional[str]): Specifies a name to 
                assign to the virtual channel this method creates.
        Returns:
            nidaqmx.task.channels.DIChannel: 
            
            Specifies the newly created DIChannel object.
        """
        # Attempt to retrieve the last created channel name. This is only supported on DAQmx 24Q3+ with the library
        # interpreter.
        virtual_channel_name = None
        try:
            virtual_channel_name = self._interpreter.internal_get_last_created_chan()
        except (NotImplementedError, DaqFunctionNotSupportedError):
            pass

        # Fallback implementation is sometimes incorrect.
        if virtual_channel_name is None:
            unflattened_lines = unflatten_channel_string(lines)
            num_lines = len(unflattened_lines)
            
            if line_grouping == LineGrouping.CHAN_FOR_ALL_LINES:
                if name_to_assign_to_lines:
                    virtual_channel_name = name_to_assign_to_lines
                elif num_lines == 1:
                    virtual_channel_name = lines
                else:
                    virtual_channel_name = unflattened_lines[0] + '...'
            else:
                if name_to_assign_to_lines:
                    if num_lines > 1:
                        virtual_channel_name = '{}0:{}'.format(
                            name_to_assign_to_lines, num_lines-1)
                    else:
                        virtual_channel_name = name_to_assign_to_lines
                else:
                    virtual_channel_name = lines

        return DIChannel(self._handle, virtual_channel_name, self._interpreter)

<%namespace name="function_template" file="/function_template.py.mako"/>\
%for function_object in functions:
${function_template.script_function(function_object)}
%endfor